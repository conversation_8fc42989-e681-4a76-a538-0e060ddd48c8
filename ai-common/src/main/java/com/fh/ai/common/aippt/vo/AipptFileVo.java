package com.fh.ai.common.aippt.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 用于aippt文件的vo信息，主要会解析成json格式存储到会话和historyApp里面的businessJson里面
 * <AUTHOR>
 * @date 2024/11/15 15:48
 */
@Data
public class AipptFileVo implements Serializable {
    /**
     * 文件oid
     */
    private String fileOid;

    /**
     * aippt的地址
     */
    private String aipptUrl;

    /**
     * 小图
     */
    private String smallUrl;

    /**
     * 图片地址
     */
    private String viewPath;

    /**
     * 下载地址
     */
    private String downloadUrl;
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 封面地址-download
     */
    private String coverDownloadUrl;
    /**
     * 封面地址-view
     */
    private String coverViewPath;

}
