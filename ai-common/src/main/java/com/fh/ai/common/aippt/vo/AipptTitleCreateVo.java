package com.fh.ai.common.aippt.vo;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 创建title返回的vo
 * 
 * <AUTHOR>
 * @date 2024/11/22 15:01
 */
@Data
public class AipptTitleCreateVo implements Serializable {
    /**
     * 任务id
     */
    private Long id;
    private String title;
    private Integer type;
    @JSONField(name = "api_key")
    private String apiKey;
    @JSONField(name = "created_at")
    private String createdAt;
}
