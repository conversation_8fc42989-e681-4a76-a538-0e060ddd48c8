package com.fh.ai.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 类FormToken.java的实现描述：FormToken注解
 *
 * <AUTHOR> 2017年x月xx日 下午8:51:20
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Token {
    /**
     * 需要防重复功能的表单入口URL对应的controller方法需要添加的注解，用于生成token（默认为uuid）
     * @return
     */
    boolean save() default false;
    /**
     * 防重复表单提交表单到后台对应的URL的controller方法需要添加的注解，用于第一次成功提交后remove掉token
     * @return
     */
    boolean remove() default false;
    /**
     * 是否让token防重复拦截器放过token校验，为true时一般用于ajax提交放过到controller中处理，此功能可以在提交失败后恢复token
     * @return
     */
    boolean pass() default false;//如果拦截到位表单重复提交是否放过让controller处理,默认被拦截器处理返回false;
}