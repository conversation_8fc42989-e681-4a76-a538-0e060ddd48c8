package com.fh.ai.common.coze.bo;

import java.io.Serializable;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * coze的请求workflow的bo（非流式）
 * 
 * <AUTHOR>
 * @date 2024/10/8 17:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CozeWorkflowBo implements Serializable {
    /**
     * 必选，待执行的 Workflow ID，此工作流应已发布。
     */
    private String workflow_id;
    /**
     * 可选，工作流开始节点的输入参数及取值，你可以在指定工作流的编排页面查看参数列表。
     */
    private Map<String,Object> parameters;
    /**
     * 可选，需要关联的智能体ID。 部分工作流执行时需要指定关联的智能体，例如存在数据库节点、变量节点等节点的工作流
     */
    private String bot_id;
    /**
     * 可选，用于指定一些额外的字段，以 Map[String][String] 格式传入。例如某些插件 会隐式用到的经纬度等字段
     */
    private Map<String,String> ext;
    /**
     * 可选，工作流所在的应用 ID
     */
    private String app_id;
    /**
     * 可选，是否异步执行工作流。默认为 false，即同步执行。
     */
    private Boolean is_async;

    /**
     * coze工作流查询异步工作流结果使用，工作流执行id
     */
    private String execute_id;
}
