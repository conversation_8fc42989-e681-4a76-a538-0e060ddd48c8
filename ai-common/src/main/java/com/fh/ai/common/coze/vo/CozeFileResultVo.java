package com.fh.ai.common.coze.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * coze的文件上传返回的结果：https://www.coze.cn/docs/developer_guides/upload_files
 * <AUTHOR>
 * @date 2024/12/23 18:09
 */
@Data
public class CozeFileResultVo implements Serializable {
    /**
     * 状态码。
     * 0 代表调用成功。
     */
    private Integer code;
    /**
     * 状态信息。API 调用失败时可通过此字段查看详细错误信息。
     */
    private String message;
    /**
     * 本次对话的基本信息。详细说明可参考 CozeFileVo
     */
    private CozeFileVo data;
    /**
     * 发送请求的实际参数，用于后面的日志记录
     */
    private String params;
    /**
     * 消息（是否是后来改为了此字段）
     */
    private String msg;
}
