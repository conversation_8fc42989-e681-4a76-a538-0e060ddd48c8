package com.fh.ai.common.dingding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fh.ai.common.utils.HttpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.time.LocalDate;

@Data
@Service
@Slf4j
public class DingDingUtil {

    private String DING_TALK_SERVER_URL = "https://oapi.dingtalk.com/robot/send?access_token=45209974deb04d821ab04061f0ca367af6c35fa21425b8e63dfd33af188b5c09";

    public JSONObject sendRobotMsg(String msg) {
        try {
            Long timestamp = System.currentTimeMillis();
            String signature = getSignature(timestamp);

            StringBuilder requestUrlSb = new StringBuilder(DING_TALK_SERVER_URL);
            requestUrlSb.append("&timestamp=" + timestamp);
            requestUrlSb.append("&sign=" + signature);

            String requestUrl = requestUrlSb.toString();

            // 参数
            StringBuffer msgBuffer = new StringBuffer();
//            msgBuffer.append(msg).append("[").append(LocalDate.now().toString()).append("]");
            msgBuffer.append(msg);
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("msgtype", "text");
            JSONObject jsonParamContent = new JSONObject();
            jsonParamContent.put("content", msgBuffer);
            jsonParam.put("text", jsonParamContent);

            String params = JSON.toJSONString(jsonParam);
            JSONObject result = HttpUtil.doPost(requestUrl, params);
            return result;
        } catch (Exception e) {
            log.error("发送机器人消息失败" + e.getMessage());
        }

        return null;
    }

    public String getSignature(Long timestamp) {
        String secret = "SEC8e3bd0bcd5e3ffef19be940b64f8dc9ae55e731603f36da96d154a1c21f71912";

        String stringToSign = timestamp + "\n" + secret;
        Mac mac = null;
        try {
            mac = Mac.getInstance("HmacSHA256");

            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            System.out.println(sign);
            return sign;
        } catch (Exception e) {
            log.error("获取签名失败" + e.getMessage());
        }

        return null;
    }

    public static void main(String[] args) {

        DingDingUtil dingDingUtil = new DingDingUtil();
        dingDingUtil.sendRobotMsg("测试测试，有个商品库存不足");


    }
}

