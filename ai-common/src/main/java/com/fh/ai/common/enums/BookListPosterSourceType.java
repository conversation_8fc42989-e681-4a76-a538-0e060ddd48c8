package com.fh.ai.common.enums;

/**
 * 书单海报来源类型：1-提示词 2-图片 3-代码 4-其他
 */
public enum BookListPosterSourceType {
    // 1-提示词 2-图片 3-代码 4-其他
    PROMPT(1, "prompt"),
    IMAGE(2, "image"),
    CODE(3, "code"),
    OTHER(4, "other");

    private int value;

    private String name;

    BookListPosterSourceType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static BookListPosterSourceType fromValue(int value) {
        for (BookListPosterSourceType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown book list poster source type value: " + value);
    }
}
