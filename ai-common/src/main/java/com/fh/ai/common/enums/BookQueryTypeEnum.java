package com.fh.ai.common.enums;

/**
 * @Classname BookQueryTypeEnum
 * @Description TODO
 * @Date 2024/12/11 15:49
 * @Created by admin
 */
public enum BookQueryTypeEnum {
    // 1日 2周 3月
    Day(1),
    Week(2),
    Month(3);

    private int value;

    BookQueryTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BookQueryTypeEnum fromValue(int value) {
        for (BookQueryTypeEnum type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown source type value: " + value);
    }

}
