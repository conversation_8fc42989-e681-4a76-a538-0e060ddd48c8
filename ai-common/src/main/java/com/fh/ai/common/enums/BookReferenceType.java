package com.fh.ai.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 营销参考类型 1-新闻热点 2-节假日
 */
public enum BookReferenceType {

    NEWS(1, "新闻"), HOLIDAY(2, "节假日"),;

    private Integer code;

    private String msg;

    BookReferenceType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsg(Integer code) {
        if (code == null) {
            return "";
        }
        BookReferenceType[] values = values();
        for (BookReferenceType oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getMsg();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        BookReferenceType[] values = values();
        for (BookReferenceType oneEnum : values) {
            if (oneEnum.getMsg().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
}
