package com.fh.ai.common.enums;

/**
 * 提问内容类型的枚举
 */
public enum ContentTypeEnum {


    /**
     * 文本
     */
    TEXT("text"),

    /**
     * 多模态，多模态内容，即文本和文件的组合、文本和图片的组合。
     */
    OBJECT_STRING("object_string"),

    /**
     * card,卡片。此枚举值仅在接口响应中出现，不支持作为入参。
     */
    CARD("card"),
    ;

    private String type;

    ContentTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
