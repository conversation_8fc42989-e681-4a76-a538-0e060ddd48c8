package com.fh.ai.common.enums;

import java.util.Objects;

/**
 * @Classname FZContentJobStateEnum
 * @Description 方正内容审校状态枚举
 * @Date 2025/2/10 20:58
 * @Created by admin
 */
public enum FZContentJobStateEnum {
    SEND_TASK_FAILURE("-1", "发送任务失败"), SEND_TASK_SUCCESS("0", "发送任务成功"), TASK_IN_PROGRESS("2", "任务处理中"),
    TASK_COMPLETED("3", "任务处理完成"), TASK_FAILED("4", "任务处理失败"), TASK_TIMEOUT("5", "任务超时");

    private final String code;
    private final String value;

    FZContentJobStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 获取功能点的代码
     *
     * @return 功能点代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取功能点的描述
     *
     * @return 功能点描述
     */
    public String getValue() {
        return value;
    }

    public static String getValue(String code) {
        FZContentJobStateEnum[] values = values();
        for (FZContentJobStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getValue();
            }
        }
        return null;
    }

    public static String getCode(String value) {
        FZContentJobStateEnum[] values = values();
        for (FZContentJobStateEnum oneEnum : values) {
            if (oneEnum.getValue().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }

    public static FZContentJobStateEnum getByCode(String code) {
        FZContentJobStateEnum[] values = values();
        for (FZContentJobStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum;
            }
        }
        return null;
    }

    public static FZContentJobStateEnum getFZContentJobStateEnumResult(String code ) {
        FZContentJobStateEnum stateEnum = getByCode(code);
        if (Objects.isNull(stateEnum)){
            return TASK_FAILED;
        }
        if (stateEnum.equals(FZContentJobStateEnum.SEND_TASK_FAILURE)||stateEnum.equals(FZContentJobStateEnum.TASK_FAILED)||stateEnum.equals(FZContentJobStateEnum.TASK_TIMEOUT)){
            return TASK_FAILED;
        }
        if (stateEnum.equals(FZContentJobStateEnum.SEND_TASK_SUCCESS)||stateEnum.equals(FZContentJobStateEnum.TASK_IN_PROGRESS)){
            return TASK_IN_PROGRESS;
        }
        if (stateEnum.equals(FZContentJobStateEnum.TASK_COMPLETED)){
            return TASK_COMPLETED;
        }

        return TASK_FAILED;
    }
}
