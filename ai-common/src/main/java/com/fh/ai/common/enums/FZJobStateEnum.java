package com.fh.ai.common.enums;

import java.util.Objects;

/**
 * @Classname FZJobStateEnum
 * @Description 方正任务状态枚举
 * @Date 2025/2/7 14:39
 * @Created by admin
 */
public enum FZJobStateEnum {
    FAILURE("-1", "任务失败"),
    PENDING("0", "未处理"),
    EXTRACT_COMPLETED("1", "抽取处理完成"),
    EXTRACT_IN_PROGRESS("11", "抽取处理中"),
    EXTRACT_FAILED("12", "抽取处理失败"),
    REVIEW_COMPLETED("2", "审校处理完成"),
    REVIEW_IN_PROGRESS("21", "审校处理中"),
    REVIEW_FAILED("22", "审校处理失败"),
    DECRYPTION_FAILED("24", "文档解密失败"),
    EXCEEDS_REVIEW_LIMIT("25", "文档超出审校限制"),
    EMPTY_CONTENT("26", "提取文档内容为空"),
    SPECIAL_CHARACTERS("10002", "稿件中特殊字符较多无法进行审校"),
    REVIEW_RESULT_FAILED("10009", "审校结果生成失败"),
    SAMPLE_CONVERSION_FAILED("10010", "大样转换失败"),
    PDF_CONVERSION_FAILED("10011", "PDF转换失败"),
    ENCRYPTED_PDF_FAILED("10013", "加密的pdf文件审校后无法生成结果");

    private final String code;
    private final String value;

    FZJobStateEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 获取功能点的代码
     *
     * @return 功能点代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取功能点的描述
     *
     * @return 功能点描述
     */
    public String getValue() {
        return value;
    }

    public static String getValue(String code) {
        FZJobStateEnum[] values = values();
        for (FZJobStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getValue();
            }
        }
        return null;
    }

    public static String getCode(String value) {
        FZJobStateEnum[] values = values();
        for (FZJobStateEnum oneEnum : values) {
            if (oneEnum.getValue().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
    public static FZJobStateEnum getByCode(String code){
        FZJobStateEnum[] values = values();
        for (FZJobStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum;
            }
        }
        return null;
    }

    // 根据任务状态返回相应的处理结果
    public static FZJobStateEnum getFZJobStateEnumResult(String code ) {
        FZJobStateEnum status = getByCode(code);
        if (Objects.isNull(status)){
            return FAILURE;
        }
        // 如果枚举值为失败的状态，返回 -1（失败）
        if (status.equals(FZJobStateEnum.FAILURE) || status.equals(FZJobStateEnum.EXTRACT_FAILED) ||
                status.equals(FZJobStateEnum.REVIEW_FAILED) || status.equals(FZJobStateEnum.DECRYPTION_FAILED) ||
                status.equals(FZJobStateEnum.EXCEEDS_REVIEW_LIMIT) || status.equals(FZJobStateEnum.EMPTY_CONTENT) ||
                status.equals(FZJobStateEnum.SPECIAL_CHARACTERS) || status.equals(FZJobStateEnum.REVIEW_RESULT_FAILED) ||
                status.equals(FZJobStateEnum.SAMPLE_CONVERSION_FAILED) || status.equals(FZJobStateEnum.PDF_CONVERSION_FAILED) ||
                status.equals(FZJobStateEnum.ENCRYPTED_PDF_FAILED)) {
            return FAILURE; // 失败
        }

        // 如果枚举值为处理中状态，返回 21（审校处理中）
        if (status.equals(FZJobStateEnum.PENDING) || status.equals(FZJobStateEnum.EXTRACT_IN_PROGRESS) ||
                status.equals(FZJobStateEnum.REVIEW_IN_PROGRESS)) {
            return REVIEW_IN_PROGRESS; // 审校处理中
        }

        // 如果枚举值为已处理完成状态，返回 2（审校处理完成）
        if (status.equals(FZJobStateEnum.REVIEW_COMPLETED)) {
            return REVIEW_COMPLETED; // 审校处理完成
        }

        // 默认返回 -1，如果没有匹配到任何状态
        return FAILURE; // 失败
    }

    @Override
    public String toString() {
        return "FZJobStateEnum{" +
                "code='" + code + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
