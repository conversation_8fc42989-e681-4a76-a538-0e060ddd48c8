package com.fh.ai.common.enums;

/**
 * 状态：1未查阅 2已查阅 3已采纳
 */
public enum FeedbackStateEnum {

    UNREVIEWED(1, "未查阅"),
    REVIEWED(2, "已查阅"),
    ACCEPTED(3, "已采纳"),
    ;

    private Integer code;

    private String msg;

    FeedbackStateEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsg(Integer code) {
        FeedbackStateEnum[] values = values();
        for (FeedbackStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getMsg();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        FeedbackStateEnum[] values = values();
        for (FeedbackStateEnum oneEnum : values) {
            if (oneEnum.getMsg().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
}
