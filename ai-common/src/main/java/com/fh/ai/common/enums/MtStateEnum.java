package com.fh.ai.common.enums;

/**
 * 状态：1处理中，2处理成功，3处理失败
 */
public enum MtStateEnum {

    DEALING(1, "处理中"),
    FINISH(2, "处理成功"),
    FAIL(3, "处理失败");

    private Integer code;

    private String msg;

    MtStateEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsg(Integer code) {
        MtStateEnum[] values = values();
        for (MtStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getMsg();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        MtStateEnum[] values = values();
        for (MtStateEnum oneEnum : values) {
            if (oneEnum.getMsg().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
}
