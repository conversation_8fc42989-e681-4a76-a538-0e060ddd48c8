package com.fh.ai.common.enums;

/**
 * @Classname PPMCategoryTypeEnum
 * @Description 凤凰错误类型
 * @Date 2025/2/14 15:47
 * @Created by admin
 */
public enum PPMCategoryTypeEnum {

    WORD_ERROR("1-1", "错别字"),
    NON_STANDARD_WORD("1-2", "不规范字"),
    WORD_ORDER_ERROR("1-3", "多字、漏字、倒字"),
    REDUNDANT_COMPONENT("2-1", "成分多余/残缺"),
    IMPROPER_COLLOCATION("2-2", "搭配不当"),
    SPECIFICATION_MISTAKE("3-1", "规范错误"),
    LEVEL_NUMBER_ERROR("3-2", "层级序号错误"),
    PERSON_NAME_ERROR("4-1", "人名差错"),
    PLACE_NAME_ERROR("4-2", "地名差错"),
    LITERATURE_ERROR("4-3", "书名、诗词等文学差错"),
    HISTORICAL_CHRONOLOGY_ERROR("4-4", "历史纪年错误"),
    FACTUAL_ERROR("4-6", "事实差错"),
    SUBJECT_TERM_ERROR("4-5", "学科术语错误"),
    ENGLISH_SPELLING_ERROR("5-1", "英文拼写错误"),
    CHINESE_PINYIN_ERROR("5-2", "汉语拼音错误"),
    POLITICALLY_SENSITIVE_ERROR("6-1","政治敏感内容差错"),
    INTERNATIONAL_ERROR("6-2","涉国际差错"),
    HK_MC_TW_ERROR("6-3","涉港澳台差错"),
    ETHNICITY_RELIGION_ERROR("6-4","涉民族宗教差错"),
    VIOLENCE_ERROR("6-5","暴恐违禁"),
    VULGAR_PORNOGRAPHYL_ERROR("6-6","低俗色情"),
    DUPLICATE_ERROR("7-1","重复句检查");

    private String code;

    private String value;


    PPMCategoryTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 获取功能点的代码
     *
     * @return 功能点代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取功能点的描述
     *
     * @return 功能点描述
     */
    public String getValue() {
        return value;
    }

    public static String getValue(String code) {
        PPMCategoryTypeEnum[] values = values();
        for (PPMCategoryTypeEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getValue();
            }
        }
        return null;
    }

    public static String getCode(String value) {
        PPMCategoryTypeEnum[] values = values();
        for (PPMCategoryTypeEnum oneEnum : values) {
            if (oneEnum.getValue().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
}
