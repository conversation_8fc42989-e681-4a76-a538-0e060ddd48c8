package com.fh.ai.common.enums;

/**
 * @Classname ProofreadingExcutedEnum
 * @Description 审校记录
 * @Date 2025/2/20 16:54
 * @Created by admin
 */
public enum ProofreadingExcutedEnum {
    OLINE_PPM_WORD(111, "凤凰在线文本审校"),
    ONLINE_FZ_WORD(121, "方正在线文本审校"),
    ONLINE_FZ_CONTENT(122, "方正在线重要讲话审校"),
    FILE_PPM_WORD(211,"凤凰文件审校"),
    FILE_FZ_WORD(221,"方正文件审校"),
    FILE_FZ_CONTENT(222,"方正内容审校"),
    FILE_FZ_DUP(223,"方正上下文查重审校"),
    FILE_FZ_REFERENCE(224,"方正参考文献审校");

    private Integer code;

    private String value;

    ProofreadingExcutedEnum(Integer code, String msg) {
        this.code = code;
        this.value = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(Integer code) {
        ProofreadingExcutedEnum[] values = values();
        for (ProofreadingExcutedEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getValue();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        ProofreadingExcutedEnum[] values = values();
        for (ProofreadingExcutedEnum oneEnum : values) {
            if (oneEnum.getValue().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }


}


