package com.fh.ai.common.enums;

/**
 * @Classname ProofreadingTaskType
 * @Description 具体审校任务的类型
 * @Date 2025/1/20 15:31
 * @Created by admin
 */
public enum ProofreadingTaskType {
    /**
     * 字词字符审校
     */
    WORDS_CHECK(1, "字词字符审校"),
    /**
     * 内容审校，含重要讲话、法律法规和党内法规政策审校
     */
    CONTENT_CHECK(2, "内容审校"),
    /**
     * 上下文查重审校
     */
    DUPLICATE_CHECK(3,"上下文查重审校"),
    /**
     * 参考文献审校
     */
    REFERENCES(4,"参考文献审校");

    private Integer code;

    private String value;

    ProofreadingTaskType(Integer code, String msg) {
        this.code = code;
        this.value = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(Integer code) {
        ProofreadingTaskType[] values = values();
        for (ProofreadingTaskType oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getValue();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        ProofreadingTaskType[] values = values();
        for (ProofreadingTaskType oneEnum : values) {
            if (oneEnum.getValue().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
}
