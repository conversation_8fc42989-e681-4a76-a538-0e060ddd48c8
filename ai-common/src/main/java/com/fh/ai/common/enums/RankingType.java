package com.fh.ai.common.enums;

/**
 * 榜单数据类型
 */
public enum RankingType {
    /**
     * 总榜，公共
     */
    ALL(1),
    /**
     * 飙升，公共
     */
    RISING(2),
    /**
     * 新书，公共
     */
    NEWBOOK(3),

    // 1畅销月榜单，2畅销季榜单，3好书月榜单，4好书季度榜单
    /**
     * 书苑，畅销书，月榜
     */
    PPMBOOK_CX_MONTH(1),
    /**
     * 书苑，好书，月榜
     */
    PPMBOOK_HS_MONTH(3),
    ;

    private int value;

    RankingType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static RankingType fromValue(int value) {
        for (RankingType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown Book Input type value: " + value);
    }
}
