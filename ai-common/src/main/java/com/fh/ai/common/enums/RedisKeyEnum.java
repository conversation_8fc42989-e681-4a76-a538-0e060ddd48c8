package com.fh.ai.common.enums;

/**
 * RedisKeyEnum
 */
public enum RedisKeyEnum {
    //角色
    ROLE("role"),

    // 管理员缓存
    ADMIN("admin:cache:"),

    // 管理员的
    ADMIN_TOKEN("admin:token:"),

    // 管理员登录错误次数
    ADMIN_LOGIN_ERRORS("admin:login:errors"),

    // 用户缓存
    USER("user:cache:"),

    // 用户token
    USER_TOKEN("user:token:"),

    // 兑换码
    REDEEM_CODE_LIST("redeem:code:list:"),
    /**
     * 用户用户名密码错误计数key
     */
    USER_FAIL_LOGIN_COUNT_KEY("user:fail:login:count"),

    /**
     * 锁定用户key
     */
    USER_LOCK_KEY("user:lock:"),

    CONFIG_LOCK_KEY("config:lock:key"),
    CONFIG_KEY("fh:init:config"),
    /**
     * 角色对应菜单配置
     */
    ROLE_MENU_KET("role:menu:config")
    ;

    private final String value;

    RedisKeyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
