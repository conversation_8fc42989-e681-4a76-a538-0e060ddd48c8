package com.fh.ai.common.enums;

/**
 * 状态，1：上传成功，2：处理中，3：处理成功，4：处理失败
 */
public enum XfStateEnum {
    UPLOADED(1, "上传成功"),
    DEALING(2, "处理中"),
    FINISH(3, "处理成功"),
    FAIL(4, "处理失败");

    private Integer code;

    private String msg;

    XfStateEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsg(Integer code) {
        XfStateEnum[] values = values();
        for (XfStateEnum oneEnum : values) {
            if (oneEnum.getCode().equals(code)) {
                return oneEnum.getMsg();
            }
        }
        return null;
    }

    public static Integer getCode(String value) {
        XfStateEnum[] values = values();
        for (XfStateEnum oneEnum : values) {
            if (oneEnum.getMsg().equals(value)) {
                return oneEnum.getCode();
            }
        }
        return null;
    }
}
