package com.fh.ai.common.guiji;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 硅基定制模特vo
 * <AUTHOR>
 * @date 2024/10/31 10:08
 */
@Data
public class GuijiDzmtVo implements Serializable {
    private Long id;
    private String robotName;
    private String robotDesc;
    private String coverUrl;
    private String configJson;
    private Long gender;
    private Long age;
    private String starSigns;
    private String face;
    private Long version;
    private Object scene;
    private Long type;
    private String expireTime;
    private Long popularity;
    private String speakerId;
    private String ttsSpeaker;
    private Object labelBaseDTOList;
    private List<GuijiDzmtsenceVo> sceneList;
}
