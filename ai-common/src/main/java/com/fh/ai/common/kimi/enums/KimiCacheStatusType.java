package com.fh.ai.common.kimi.enums;

/**
 * "pending", "ready", "error", "inactive"
 * <AUTHOR>
 * @date 2025/1/10 10:25
 */
public enum KimiCacheStatusType {

    PENDING("pending"),
    READY("ready"),
    ERROR("error"),
    INACTIVE("inactive"),

    ;

    private String value;

    KimiCacheStatusType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
