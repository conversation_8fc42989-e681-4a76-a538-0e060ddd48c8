package com.fh.ai.common.meitu;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ImageCdnUtil {

    @Value("${image.cdn.enable:false}")
    private boolean cdnEnabled;

    @Value("${image.cdn.orihost:}")
    private String originalHost;

    @Value("${image.cdn.targethost:}")
    private String targetHost;

    /**
     * Replace image URLs with CDN URLs if CDN is enabled
     * 
     * @param content Content containing image URLs
     * @return Content with replaced CDN URLs
     */
    public String replaceCdnUrls(String content) {
        if (!cdnEnabled || content == null || StringUtils.isBlank(originalHost) || StringUtils.isBlank(targetHost)) {
            return content;
        }

        return content.replace(originalHost, targetHost);
    }
}
