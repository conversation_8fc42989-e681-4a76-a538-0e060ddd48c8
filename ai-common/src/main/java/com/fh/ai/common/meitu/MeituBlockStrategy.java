package com.fh.ai.common.meitu;

import com.github.rholder.retry.BlockStrategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义阻塞策略（暂时未用到），注意，默认是：.withBlockStrategy(BlockStrategies.threadSleepStrategy())
 * 
 * <AUTHOR>
 * @date 2024/1/8 17:16
 */
@Slf4j
public class MeituBlockStrategy implements BlockStrategy {
    /**
     * 自旋的毫秒值
     * @param sleepTime the computed sleep duration in milliseconds
     * @throws InterruptedException
     */
    @Override
    public void block(long sleepTime) throws InterruptedException {

        long start = System.currentTimeMillis();
        long end = start;

        // 在此自旋，直到超时
        while (end - start <= sleepTime) {
            end = System.currentTimeMillis();
        }
    }
}
