package com.fh.ai.common.meitu;

import java.util.concurrent.Callable;
import java.util.function.Function;

import com.fh.ai.common.meitu.model.MtInvokeParamsDto;
import com.fh.ai.common.meitu.vo.MtResult;

/**
 * 任务体
 * 
 * <AUTHOR>
 * @date 2024/1/8 16:26
 */
public class MeituRequestCallableTask implements Callable<MtResult> {

    /**
     * 重试次数（默认3，也可每个任务自定义）
     */
    private Integer attemptNumber;

    /**
     * 参数
     */
    private MtInvokeParamsDto param;

    /**
     * 任务体
     */
    private Function<MtInvokeParamsDto, MtResult> function;

    public MeituRequestCallableTask(MtInvokeParamsDto param, Function<MtInvokeParamsDto, MtResult> function) {
        this.param = param;
        this.function = function;
    }

    public MeituRequestCallableTask(Integer attemptNumber, MtInvokeParamsDto param,
        Function<MtInvokeParamsDto, MtResult> function) {
        this.attemptNumber = attemptNumber;
        this.param = param;
        this.function = function;
    }

    @Override
    public MtResult call() throws Exception {
        MtResult result = function.apply(param);
        return result;
    }

    public Integer getAttemptNumber() {
        return attemptNumber;
    }
}
