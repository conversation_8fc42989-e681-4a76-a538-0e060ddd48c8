package com.fh.ai.common.openbookclient.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Classname SmartClientConfig
 * @Description TODO
 * @Date 2024/12/5 11:05
 * @Created by admin
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "smart.client")
public class SmartClientConfig {
    /**
     * 查询时间 需按照 yyyyMM的格式配置值，如果不配置值，则查询上个月的数据。
     */
    private String cycleValue;
    /**
     * 查询类型，month、year，默认按照年查询
     */
    private String queryType = "year";
    /**
     * 每次每页查询条数
     */
    private Integer pageSize = 10;
    /**
     * api前缀地址
     */
    private String baseAddress = "http://webapi.openbookscan.com.cn";

}
