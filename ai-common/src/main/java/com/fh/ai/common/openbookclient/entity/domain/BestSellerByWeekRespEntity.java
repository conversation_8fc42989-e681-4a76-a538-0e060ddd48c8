package com.fh.ai.common.openbookclient.entity.domain;

/**
 * 周榜单返回主实体
 */
public class BestSellerByWeekRespEntity extends BestSellerRespBaseEntity {
    
        /// <summary>
        /// 周销售
        /// </summary>
        private int sales;

        /// <summary>
        /// 近4周销量
        /// </summary>
        private int salesBy4Weeks;

        /**
         * 周销售
         * @return
         */
        public int getSales() {
            return sales;
        }

        public void setSales(int sales) {
            this.sales = sales;
        }

        /**
         * 近4周销售
         * @return
         */
        public int getSalesBy4Weeks() {
            return salesBy4Weeks;
        }

        public void setSalesBy4Weeks(int salesBy4Weeks) {
            this.salesBy4Weeks = salesBy4Weeks;
        }
}
