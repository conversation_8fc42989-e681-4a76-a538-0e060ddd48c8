package com.fh.ai.common.openbookclient.entity.domain;

/**
 * 零售各个渠道对比榜单返回主实体
 */
public class BestSellerRespForChnCompareEntity extends BestSellerRespBaseEntity {
    
         /// <summary>
        /// 零售销量
        /// </summary>
        private int sales_Mix;

        /// <summary>
        /// 网店销量
        /// </summary>
        private int sales_Web;

        /// <summary>
        /// 实体店销量
        /// </summary>
        private int sales_Loc;
        /// <summary>
        /// 平台电商销量
        /// </summary>
        private int sales_ST31;
        /// <summary>
        /// 短视频电商 销量
        /// </summary>
        private int sales_ST32;
        /// <summary>
        /// 垂直及其他电商 销量
        /// </summary>
        private int sales_ST33;

        /**
         * 零售渠道销量
         * @return
         */
        public int getSales_Mix() {
            return sales_Mix;
        }
        public void setSales_Mix(int sales_Mix) {
            this.sales_Mix = sales_Mix;
        }
        /**
         * 网店渠道销量
         * @return
         */
        public int getSales_Web() {
            return sales_Web;
        }
        public void setSales_Web(int sales_Web) {
            this.sales_Web = sales_Web;
        }

        /**
         * 实体店渠道销量
         * @return
         */
        public int getSales_Loc() {
            return sales_Loc;
        }
        public void setSales_Loc(int sales_Loc) {
            this.sales_Loc = sales_Loc;
        }

        /**
         * 平台电商渠道销量
         * @return
         */
        public int getSales_ST31() {
            return sales_ST31;
        }
        public void setSales_ST31(int sales_ST31) {
            this.sales_ST31 = sales_ST31;
        }
        /**
         * 短视频渠道销量
         * @return
         */
        public int getSales_ST32() {
            return sales_ST32;
        }
        public void setSales_ST32(int sales_ST32) {
            this.sales_ST32 = sales_ST32;
        }
        /**
         * 垂直电商及其他渠道销量
         * @return
         */
        public int getSales_ST33() {
            return sales_ST33;
        }
        public void setSales_ST33(int sales_ST33) {
            this.sales_ST33 = sales_ST33;
        }

}
