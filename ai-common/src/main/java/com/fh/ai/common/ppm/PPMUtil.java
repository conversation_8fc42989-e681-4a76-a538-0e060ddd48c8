package com.fh.ai.common.ppm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fh.ai.common.utils.HttpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;

@Slf4j
@Component
@Data
public class PPMUtil {

    @Value("${ppm.url}")
    private String url;

    private static final Integer NORMAL = 1;


    /**
     * 获取用户信息
     *
     * @param token
     * @return
     */
    public PPMUser getUserInfo(String token) {
        try {
            String encode = URLEncoder.encode(token, "UTF-8");
//            token = decode.replaceAll(" ", "+");
            String urlStr = url + encode;
            log.info("获取PPM用户信息url：" + urlStr);
            JSONObject result = HttpUtil.doGet(urlStr);

            String resultJson = JSON.toJSONString(result);

            log.info("获取PPM用户信息：" + resultJson);
            if (null != result
                    && Boolean.TRUE.equals(result.getBoolean("validate"))
                    && NORMAL.equals(result.getInteger("code"))) {
                // 成功
                PPMUser user = JSON.parseObject(resultJson, PPMUser.class);
                return user;
            }
        } catch (Exception e) {
            log.error("获取PPM用户信息失败" + e.getMessage());
        }

        return null;
    }

    /**
     * 获取app用户信息
     *
     * @param userId
     * @return
     */
    public PPMAppUser getUserInfoByUserId(String userId) {
        try {
            String urlStr = "http://oa.ppm.cn/indishare/xmyw.nsf/agtgetuserdep?openagent&userId=" + userId;
            log.info("获取PPMApp用户信息url：" + urlStr);
            JSONObject result = HttpUtil.doGet(urlStr);

            String resultJson = JSON.toJSONString(result);

            log.info("获取PPMApp用户信息：" + resultJson);
            if (null != result) {
                // 成功
                PPMAppUser user = JSON.parseObject(resultJson, PPMAppUser.class);
                return user;
            }
        } catch (Exception e) {
            log.error("获取PPMApp用户信息失败" + e.getMessage());
        }

        return null;
    }
}
