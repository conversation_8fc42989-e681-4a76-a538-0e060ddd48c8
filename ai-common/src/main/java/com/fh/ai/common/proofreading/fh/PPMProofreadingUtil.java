package com.fh.ai.common.proofreading.fh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.constants.ConstantsInteger;
import com.fh.ai.common.enums.FileTypeEnum;
import com.fh.ai.common.enums.PPMProofreadOnlineSyncType;
import com.fh.ai.common.proofreading.PPMTokenParam;
import com.fh.ai.common.proofreading.ProofreadConstants;
import com.fh.ai.common.proofreading.fh.vo.*;
import com.fh.ai.common.utils.HttpUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.utils.StringKit;
import com.fh.ai.common.vo.AjaxResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * @Classname fhProofreadingUtil
 * @Description 凤凰报刊审校工具
 * @Date 2025/1/10 11:28
 * @Created by admin
 */
@Data
@Service
@Slf4j
public class PPMProofreadingUtil {

    /**
     * redis
     */
    @Resource
    private RedisComponent redisComponent;
    /**
     * 凤凰审校token配置
     */
    @Resource
    private PPMTokenParam ppmTokenParam;

    /**
     * rpc获取凤凰审校接口token
     * @return
     */
    public AjaxResult getPPMToken(){
        String url = ProofreadConstants.PPM_CREATE_TOKEN_URL;
        Map<String, Object> requestParamMap = new HashMap<>();
        requestParamMap.put("appid",ppmTokenParam.getAppid());
        requestParamMap.put("secret",ppmTokenParam.getSecret());
        url = StringKit.urlConcat(url, requestParamMap);
        JSONObject jsonObject = HttpUtil.doGet(url);
        if (Objects.isNull(jsonObject)){
            log.error("调用创建凤凰审校接口token接口失败，返回空");
            return AjaxResult.fail("接口调用失败，返回空");
        }
        if (checkIsSuccess(jsonObject)){
            log.info("调用凤凰创建token接口成功，返回体：{}",jsonObject.toJSONString());
            PPMProofreadBaseResult<CreateTokenVo> tokenResult = JSON.parseObject(jsonObject.toJSONString(),
                    new TypeReference<PPMProofreadBaseResult<CreateTokenVo>>() {
                    });
            return AjaxResult.success(tokenResult.getData(),"凤凰获取token成功");
        }else {
            log.error("调用凤凰创建token接口返回失败，失败原因：{}",jsonObject.getString("msg"));
            return AjaxResult.fail(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    /**
     * 从缓存获取token
     * @return
     */
    public String getPPMTokenFromCache(){
        if (redisComponent.hasKey(ProofreadConstants.PPM_REDIS_TOKEN_KEY)){
            Object tokenObject = redisComponent.get(ProofreadConstants.PPM_REDIS_TOKEN_KEY);
            if (tokenObject != null) {
                log.info("从缓存获取的key为：{}",tokenObject);
                return (String)tokenObject;
            }
            log.error("凤凰审校从redis获取token失败-tokenObject is null");
            return "";
        }else {
            AjaxResult ppmTokenResult = getPPMToken();
            if (!ppmTokenResult.getSuccess()){
                log.error("凤凰审校调用接口获取token失败，原因：{}",ppmTokenResult.getMsg());
                return "";
            }
            CreateTokenVo data = (CreateTokenVo)ppmTokenResult.getData();
            if (Objects.isNull(data) || StringUtils.isBlank(data.getToken())){
                log.error("凤凰审校获取token接口返回成功，但返回数据为空，返回体：{}",JSON.toJSONString(ppmTokenResult));
                return "";
            }
            redisComponent.set(ProofreadConstants.PPM_REDIS_TOKEN_KEY,data.getToken(),ProofreadConstants.PPM_REDIS_TOKEN_KEY_EXPIRE_TIME);
            return data.getToken();
        }
    }

    /**
     * 创建在线审校同步任务
     * @param pathParam 路径参数 包括token 23小时有效token
     * @param formData 表单信息，包括type：同步/异步。functions，
     * @return
     */
    public AjaxResult createOnlineSyncTask(Map<String,Object> pathParam, Map<String,String> formData){
        String url = ProofreadConstants.PPM_CREATE_ONLINE_TASK;
        url = StringKit.urlConcat(url, pathParam);
        // 同步
        formData.put(ProofreadConstants.PPM_TYPE,String.valueOf(PPMProofreadOnlineSyncType.SYNC.getValue()));
        JSONObject jsonObject = HttpUtil.doPost(url, null, formData, null);
        if (Objects.isNull(jsonObject)){
            log.error("调用凤凰创建在线审校同步任务接口失败，返回空");
            return AjaxResult.fail("接口调用失败，返回空");
        }
        if (checkIsSuccess(jsonObject)){
            PPMProofreadBaseResult<PPMProofreadResultTaskVo> onlineSyncResult = JSON.parseObject(jsonObject.toJSONString(),
                    new TypeReference<PPMProofreadBaseResult<PPMProofreadResultTaskVo>>() {
                    });
            return AjaxResult.success(onlineSyncResult.getData(),"凤凰在线同步文本审校成功");
        }else {
            return AjaxResult.fail(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    /**
     * 创建在线审校异步任务
     * @param pathParam 路径参数 包括token 24小时有效token
     * @param formData 表单信息，包括type：同步/异步。functions，
     * @return
     */
    public AjaxResult createOnlineAsyncTask(Map<String,Object> pathParam, Map<String,String> formData){
        String url = ProofreadConstants.PPM_CREATE_ONLINE_TASK;
        url = StringKit.urlConcat(url, pathParam);
        // 同步
        formData.put(ProofreadConstants.PPM_TYPE,String.valueOf(PPMProofreadOnlineSyncType.ASYNC.getValue()));
        JSONObject jsonObject = HttpUtil.doPost(url, null, formData, null);
        if (Objects.isNull(jsonObject)){
            log.error("调用凤凰创建在线审校异步任务接口失败，返回空");
            return AjaxResult.fail("接口调用失败，返回空");
        }
        if (checkIsSuccess(jsonObject)){
            PPMProofreadBaseResult<PPMCreateAsyncTaskVo> onlineAsyncResult = JSON.parseObject(jsonObject.toJSONString(),
                    new TypeReference<PPMProofreadBaseResult<PPMCreateAsyncTaskVo>>() {
                    });
            return AjaxResult.success(onlineAsyncResult.getData());
        }else {
            return AjaxResult.fail(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    /**
     * 创建文件审校任务
     * @param fileType 1 word ; 2 pdf
     * @param pathParam
     * @param formData
     * @param fileMap
     * @return
     */
    public AjaxResult createFileTask(Map<String,Object> pathParam, Map<String,String> formData,Map<String, File> fileMap,Integer fileType){
        String url = null;
        if (FileTypeEnum.WORD.getValue().equals(fileType)){
            url = ProofreadConstants.PPM_CREATE_WORD_TASK;
        }else if (FileTypeEnum.PDF.getValue().equals(fileType)){
            url = ProofreadConstants.PPM_CREATE_PDF_URL;
        }else {
            return AjaxResult.fail("文件类型不支持");
        }
        url = StringKit.urlConcat(url, pathParam);
        JSONObject jsonObject = HttpUtil.doPost(url, null, formData, fileMap);
        if (Objects.isNull(jsonObject)){
            log.error("调用凤凰创建文件审校任务接口失败，返回空");
            return AjaxResult.fail("接口调用失败，返回空");
        }
        if (checkIsSuccess(jsonObject)){
            PPMProofreadBaseResult<PPMCreateAsyncTaskVo> onlineAsyncResult = JSON.parseObject(jsonObject.toJSONString(),
                    new TypeReference<PPMProofreadBaseResult<PPMCreateAsyncTaskVo>>() {
                    });
            return AjaxResult.success(onlineAsyncResult.getData());
        }else {
            return AjaxResult.fail(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }

    /**
     * 查询校对结果
     * @param taskId 任务id
     * @return
     */
    public AjaxResult getPPmProofreadResult(String taskId,String token){
        String url = ProofreadConstants.PPM_GET_RESULT_URL;
        Map<String,Object> pathParam = new HashMap<>();
        pathParam.put(ProofreadConstants.PPM_TASK_ID,taskId);
        pathParam.put(ProofreadConstants.TOKEN,token);
        url = StringKit.urlConcat(url, pathParam);
        JSONObject jsonObject = HttpUtil.doGet(url);
        if (Objects.isNull(jsonObject)){
            log.error("查询凤凰审校结果接口失败，返回空");
            return AjaxResult.fail("接口调用失败，返回空");
        }
        if (checkIsSuccess(jsonObject)){
            PPMProofreadBaseResult<PPMProofreadResultTaskVo> onlineAsyncResult = JSON.parseObject(jsonObject.toJSONString(),
                    new TypeReference<PPMProofreadBaseResult<PPMProofreadResultTaskVo>>() {
                    });
            return AjaxResult.success(onlineAsyncResult.getData());
        }else {
            return AjaxResult.fail(jsonObject.getInteger("code"),jsonObject.getString("msg"));
        }
    }



    /**
     * 校验返回结果是否正确
     * @param jsonObject
     * @return
     */
    private Boolean checkIsSuccess(JSONObject jsonObject){
        if (Objects.isNull(jsonObject)){
            return Boolean.FALSE;
        }
        return Objects.equals(jsonObject.getInteger("code"), ConstantsInteger.NUM_1) && Constants.SUCCESS_STR.equals(jsonObject.getString("msg"));
    }


    /**
     * 处理凤凰返回的结果
     * @param resultStr
     * @return
     */
    public static List<PPMRpcResultVo> handleResult(String resultStr){
        JSONArray resJsonArray = JSONArray.parseArray(resultStr);
        List<PPMRpcResultVo> ppmRpcResultVos = new ArrayList<>();
        for (int i = 0;i<resJsonArray.size();i++){
            PPMRpcResultVo ppmRpcResultVo = new PPMRpcResultVo();
            JSONObject singleJsonObject = resJsonArray.getJSONObject(i);
            ppmRpcResultVo.setYuanju(singleJsonObject.getString("yuanju"));
            ppmRpcResultVo.setYema(singleJsonObject.getString("yema"));
            Object o = singleJsonObject.get("jiucuo");
            if (o instanceof JSONObject){
                Map<String, PPMJiuCuoDetailVo> jiuCuoDetailVoMap = JSON.parseObject(JSON.toJSONString(o), new TypeReference<Map<String, PPMJiuCuoDetailVo>>() {
                });
                ppmRpcResultVo.setJiucuoMap(jiuCuoDetailVoMap);
            }else if (o instanceof JSONArray){
                List<PPMJiuCuoDetailVo> ppmJiuCuoDetailVos = JSON.parseObject(JSON.toJSONString(o), new TypeReference<List<PPMJiuCuoDetailVo>>() {
                });
                ppmRpcResultVo.setJiucuoList(ppmJiuCuoDetailVos);
            }
            ppmRpcResultVos.add(ppmRpcResultVo);
        }
        return ppmRpcResultVos;
    }


}
