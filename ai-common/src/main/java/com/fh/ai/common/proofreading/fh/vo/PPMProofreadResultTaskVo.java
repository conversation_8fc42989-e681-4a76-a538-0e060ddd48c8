package com.fh.ai.common.proofreading.fh.vo;

import lombok.Data;

/**
 * @Classname createPPMProofreadTask
 * @Description 凤凰审校接口返回的对象结构。需要将result进行转换，详见 PPMResultVo类
 * @Date 2025/1/10 14:15
 * @Created by admin
 */
@Data
public class PPMProofreadResultTaskVo {
    /**
     * 任务id
     */
    private String taskid;
    /**
     * 审校结果
     */
    private String result;
    /**
     * 审校结果文件第三方下载地址。只有上传文件校验，才有值，如果是在线校验，则为空。
     */
    private String fileUrl;
}
