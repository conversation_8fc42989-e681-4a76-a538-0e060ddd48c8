package com.fh.ai.common.proofreading.fz.req;

import lombok.Data;

/**
 * @Classname FZReviewTxtReq
 * @Description 方正文本/文件审校入参
 * @Date 2025/1/22 10:42
 * @Created by admin
 */
@Data
public class FZReviewTxtReq {
    /**
     * 稿件 id，取审校文件上传接口返回的文件 id
     */
    private String id;
    /**
     * 必填，所要校对的文本内容，同步接口文本字数不能超过5000。注意：多个段落用换行符（\n 或 \r 或 \r\n）分隔。
     */
    private String text;
    /**
     * 选填，如果对域名单独做过授权，则需要传递该参数。
     */
    private String domain;
    /**
     * 选填，回调地址
     */
    private String callbackUrl;
    /**
     * 选填，不填此项默认为授权的审校项，也可以指定具体的审校类型进行校对，当前包含审校项有：
     *
     * 易错词检查 errorwords、 敏感词检查 sentivewords、简繁误用 traditionalchinese、 异体字检查 variantchar、领导人排序检查 leadersort、
     * 领导人职务检查 leadertitle、重点词检查 keywords 、译文检查 translation、 纪年检查 historytime、不规范名词检查 terms、地名检查 place、
     * 标点符号检查 punctuation、领导人姓名检查 leadername、 非推荐词 nonrecommender、语义重复semrepetition、标准检查 standardscheck、
     * 古诗文检查 citation、一致性检查 consistency、多余空格errorspace。
     *
     * 默认该参数不传为检查所有项（标准检查、古诗文检查、一致性检查除外），也可以通过下面的方式针对某一项进行审校。
     *
     * errorwords|sentivewords
     */
    private String etype;

    /**
     * 敏感内容检查严格程度，默认为 M。该参数同时作用于敏感词检查和领导人职务检查。
     * 不同严格程度说明：
     * H（严格）：仅检查敏感程度为高的敏感词及插入特殊符号的敏感词变体、重点关注国家级以上领导人职务表述问题
     * M（一般）：检查敏感程度为中或高的敏感词及混淆文字与插入符号类的敏感词变体、重点关注省部级以上领导人职务表述问题
     * L（宽松）：检查所有敏感词及所有类型的敏感词变体、严格检查所有领导人职务表述问题
     */
    private String strictness;
    /**
     * 学科,多个用英文逗号,分隔，默认为空，对不规范名词、译文检查功能有影响，传入学科则检查对应学科的不规范名词，如果不传入学科则不进行检查。
     */
    private String subject;
    /**
     * 标注文件是否生成统计页和封面页 0：否 1:是
     */
    private String coverPage;
    /**
     * 【选填】 业务id/流水id，方便对账。
     */
    private String bid;
    /**
     * 【选填】，如果没有多租户情况，该参数可以不传，默认取当前授权用户对应的机构词库进行审校。
     * 如果是多租户情况，需要传orgId参数才能使用到对应的租户（机构）词库，该值为同步租户信息接口返回的id属性，具体请参考同步租户信息接口。
     */
    private String orgId;

    /**
     * 易错词检查模式，
     * 默认：H。
     * 查全（H）：检查过程中关注结果的查全率，检查结果数量多，准确率相对低；
     * 查准（L）：检查过程中关注结果的准确率，检查结果数量少，查全率相对低。
     */
    private String precision;

    /**
     * 【选填】是否生成标注文件、报告文件， 0：不生成， 1：生成标注和报告文件，2：只生成标注文件，3：只生成报告文件
     *  默认1；输出标注文件或报告文件会影响审校速度（建议按需输出）
     */
    private String reportFile;
}
