package com.fh.ai.common.proofreading.fz.vo;

import lombok.Data;

import java.util.List;

/**
 * @Classname ContentverifyTaskOutPut
 * @Description 内容审校结果，重要讲话审校、法律法规审校、党内法规政策审校、参考文献审校接口都可使用该返回结构
 * @Date 2025/1/24 15:55
 * @Created by admin
 */
@Data
public class ContentverifyTaskOutPut {
    /**
     * 任务 id
     */
    private String jobId;
    /**
     * 任务完成进度
     */
    private String processRate;
    /**
     * 任务状态，-1 为发送任务失败，0 为发送任务成功，2 为任务处理中，3 为任务处理完成，4 为任务处理失败，5 为任务超时
     */
    private String state;
    /**
     * 描述信息，对应上述任务状态
     */
    private String message;
    /**
     * 审 校 结 果 列 表 ， 数 据 类 型List<Base>
     */
    private List<Base> detail;

}
