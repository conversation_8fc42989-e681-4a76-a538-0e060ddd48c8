/**
 * Copyright 2024 bejson.com
 */
package com.fh.ai.common.qwen.bo;

import lombok.Data;

import java.util.List;

/**
 * Auto-generated: 2024-06-12 16:37:0
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
@Data
public class QwenChatBo {

    private String model;
    private List<QwenMessage> messages;
    private float top_p;
    private float temperature;
    private boolean stream;

    private float repetition_penalty;

}