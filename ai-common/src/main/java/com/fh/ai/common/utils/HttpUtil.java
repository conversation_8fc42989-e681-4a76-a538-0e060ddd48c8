package com.fh.ai.common.utils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fh.ai.common.constants.Constants;

/**
 * HTTP request utility with modern Java features
 *
 * <AUTHOR>
 */
public class HttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);
    private static final String UTF_8 = "UTF-8";
    private static final Charset UTF8_CHARSET = StandardCharsets.UTF_8;

    // Standard timeouts in milliseconds
    private static final int DEFAULT_TIMEOUT = 60_000;
    private static final int EXTENDED_TIMEOUT = 300_000;

    // 异步执行线程池
    private static final ExecutorService ASYNC_EXECUTOR = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "HttpUtil-Async-" + System.currentTimeMillis());
        thread.setDaemon(true);
        return thread;
    });

    /**
     * Creates a default request configuration with specified timeout
     */
    private static RequestConfig createRequestConfig(int timeout) {
        return RequestConfig.custom().setConnectTimeout(timeout).setConnectionRequestTimeout(timeout)
            .setSocketTimeout(timeout).build();
    }

    /**
     * Applies headers to any HTTP request
     */
    private static void applyHeaders(HttpRequestBase request, Map<String, String> headers) {
        Optional.ofNullable(headers).ifPresent(h -> h.forEach(request::setHeader));
    }

    /**
     * Executes HTTP request with resource management and error handling
     */
    private static <T> T executeRequest(Function<CloseableHttpClient, CloseableHttpResponse> requestExecutor,
        Function<CloseableHttpResponse, T> responseHandler, String url, String requestParams) {

        try (CloseableHttpClient client = HttpClients.createDefault();
            CloseableHttpResponse response = requestExecutor.apply(client)) {

            return responseHandler.apply(response);

        } catch (Exception e) {
            String logMessage =
                Optional.ofNullable(requestParams).map(params -> JSON.toJSONString(params) + " ").orElse("");

            LOGGER.error(logMessage + "请求第三方接口失败！！接口地址:" + url, e);
            return null;
        }
    }

    /**
     * Handles JSON response
     */
    private static JSONObject handleJsonResponse(CloseableHttpResponse response, String url) throws IOException {
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            String resultString = EntityUtils.toString(entity, UTF_8);
            JSONObject result = JSONObject.parseObject(resultString);
            LOGGER.info("请求第三方接口地址：{} 结果：{}", url, resultString);
            return result;
        }
        return null;
    }

    /**
     * Handles InputStream response
     */
    private static InputStream handleStreamResponse(CloseableHttpResponse response, String url, String params)
        throws IOException {
        if (Constants.SYSTEM_ERROR_CODE_SUCCESS.equals(response.getStatusLine().getStatusCode())) {
            return response.getEntity().getContent();
        } else {
            String errorInfo = EntityUtils.toString(response.getEntity());
            String paramInfo = Optional.ofNullable(params).orElse("");
            LOGGER.error("请求第三方接口失败！！接口地址: {} 参数：{} 返回值：{} {}", url, paramInfo,
                response.getStatusLine().getStatusCode(), errorInfo);
            return null;
        }
    }

    /**
     * Performs HTTP GET request and returns parsed JSON
     */
    public static JSONObject doGet(String url) {
        return doGet(url, null);
    }

    /**
     * Performs HTTP GET request with headers and returns parsed JSON
     */
    public static JSONObject doGet(String url, Map<String, String> headers) {
        return executeRequest(client -> {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setConfig(createRequestConfig(DEFAULT_TIMEOUT));
            applyHeaders(httpGet, headers);
            try {
                return client.execute(httpGet);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, response -> {
            try {
                return handleJsonResponse(response, url);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, url, null);
    }

    /**
     * Performs HTTP GET request with headers and returns parsed JSON
     */
    public static JSONObject doGet(String url, Map<String, String> headers, int timeout) {
        return executeRequest(client -> {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setConfig(createRequestConfig(timeout));
            applyHeaders(httpGet, headers);
            try {
                return client.execute(httpGet);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, response -> {
            try {
                return handleJsonResponse(response, url);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, url, null);
    }

    /**
     * Performs HTTP GET request and returns an InputStream
     */
    public static InputStream doGetStream(String url, Map<String, String> headers) {
        try{
            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
            HttpGet httpGet = new HttpGet(url);
            httpGet.setConfig(createRequestConfig(EXTENDED_TIMEOUT));
            applyHeaders(httpGet, headers);

            CloseableHttpResponse response = httpClient.execute(httpGet);
            return handleStreamResponse(response, url, null);

        } catch (Exception e) {
            LOGGER.error("请求第三方接口失败！！接口地址:" + url, e);
            return null;
        }
        // 不在finally中关闭资源，因为我们需要将流传递出去
    }

    /**
     * Performs HTTP POST request with JSON body and returns parsed JSON
     */
    public static JSONObject doPost(String url, String params) {
        return doPost(url, null, params);
    }

    /**
     * Performs HTTP POST request with headers and JSON body
     */
    public static JSONObject doPost(String url, Map<String, String> headers, String params) {
        return executeRequest(client -> {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(
                createRequestConfig(Optional.ofNullable(headers).map(h -> EXTENDED_TIMEOUT).orElse(DEFAULT_TIMEOUT)));

            // Add JSON body if params provided
            Optional.ofNullable(params).ifPresent(p -> {
                StringEntity entity = new StringEntity(p, ContentType.APPLICATION_JSON);
                httpPost.setEntity(entity);
            });

            applyHeaders(httpPost, headers);

            try {
                return client.execute(httpPost);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, response -> {
            try {
                return handleJsonResponse(response, url);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, url, params);
    }

    /**
     * Performs HTTP POST request and returns an InputStream
     */
    public static InputStream doPostStream(String url, Map<String, String> headers, String params) {
        try{
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(createRequestConfig(EXTENDED_TIMEOUT));

            StringEntity entity = new StringEntity(params, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            applyHeaders(httpPost, headers);

            CloseableHttpResponse response = httpClient.execute(httpPost);
            return handleStreamResponse(response, url, params);

        } catch (Exception e) {
            LOGGER.error(JSON.toJSONString(params) + "请求第三方接口失败！！接口地址:" + url, e);
            return null;
        }
        // 不在finally中关闭资源，因为我们需要将流传递出去
    }

    /**
     * Performs HTTP POST request asynchronously and returns an InputStream wrapped in CompletableFuture
     * 使用线程池实现异步，避免复杂的异步HTTP客户端依赖问题
     */
    public static CompletableFuture<InputStream> doPostStreamAsync(String url, Map<String, String> headers, String params) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                CloseableHttpClient httpClient = HttpClients.createDefault();
                HttpPost httpPost = new HttpPost(url);
                httpPost.setConfig(createRequestConfig(EXTENDED_TIMEOUT));

                StringEntity entity = new StringEntity(params, ContentType.APPLICATION_JSON);
                httpPost.setEntity(entity);
                applyHeaders(httpPost, headers);

                CloseableHttpResponse response = httpClient.execute(httpPost);
                return handleStreamResponse(response, url, params);

            } catch (Exception e) {
                LOGGER.error(JSON.toJSONString(params) + "异步请求第三方接口失败！！接口地址:" + url, e);
                throw new RuntimeException("异步HTTP请求失败", e);
            }
        }, ASYNC_EXECUTOR);
    }

    /**
     * Performs HTTP POST request with multipart form data including files
     */
    public static JSONObject doPost(String url, Map<String, String> headers, Map<String, String> params,
        Map<String, File> fileMap) {

        return executeRequest(client -> {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(createRequestConfig(DEFAULT_TIMEOUT));

            // Build multipart entity
            MultipartEntityBuilder builder =
                MultipartEntityBuilder.create().setMode(HttpMultipartMode.BROWSER_COMPATIBLE).setCharset(UTF8_CHARSET);

            // Add string parameters
            Optional.ofNullable(params).ifPresent(p -> p.forEach((key, value) -> builder.addPart(key,
                new StringBody(value, ContentType.create("text/plain", UTF8_CHARSET)))));

            // Add file parameters
            Optional.ofNullable(fileMap).ifPresent(
                fm -> fm.forEach((key, file) -> builder.addPart(key, new FileBody(file, ContentType.DEFAULT_BINARY))));

            httpPost.setEntity(builder.build());
            applyHeaders(httpPost, headers);

            try {
                return client.execute(httpPost);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, response -> {
            try {
                return handleJsonResponse(response, url);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, url, params != null ? JSON.toJSONString(params) : null);
    }

    /**
     * Performs HTTP POST request with multipart form data including files
     */
    public static JSONObject doPost(String url, Map<String, String> headers, Map<String, String> params,
                                    Map<String, File> fileMap, int timeout) {

        return executeRequest(client -> {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(createRequestConfig(timeout));

            // Build multipart entity
            MultipartEntityBuilder builder =
                    MultipartEntityBuilder.create().setMode(HttpMultipartMode.BROWSER_COMPATIBLE).setCharset(UTF8_CHARSET);

            // Add string parameters
            Optional.ofNullable(params).ifPresent(p -> p.forEach((key, value) -> builder.addPart(key,
                    new StringBody(value, ContentType.create("text/plain", UTF8_CHARSET)))));

            // Add file parameters
            Optional.ofNullable(fileMap).ifPresent(
                    fm -> fm.forEach((key, file) -> builder.addPart(key, new FileBody(file, ContentType.DEFAULT_BINARY))));

            httpPost.setEntity(builder.build());
            applyHeaders(httpPost, headers);

            try {
                return client.execute(httpPost);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, response -> {
            try {
                return handleJsonResponse(response, url);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, url, params != null ? JSON.toJSONString(params) : null);
    }

    /**
     * Performs HTTP DELETE request
     */
    public static JSONObject doDelete(String url, Map<String, String> headers) {
        return executeRequest(client -> {
            HttpDelete httpDelete = new HttpDelete(url);
            httpDelete.setConfig(createRequestConfig(DEFAULT_TIMEOUT));
            applyHeaders(httpDelete, headers);

            try {
                return client.execute(httpDelete);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, response -> {
            try {
                return handleJsonResponse(response, url);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, url, null);
    }

    /**
     * Alternative HTTP DELETE implementation
     *
     * @deprecated Use doDelete instead
     */
    @Deprecated
    public static JSONObject httpDelete(String url, Map<String, String> headers) {
        return doDelete(url, headers);
    }
}