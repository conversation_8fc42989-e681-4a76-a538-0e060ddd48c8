package com.fh.ai.common.utils;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

import lombok.Data;

@Data
// 用于保存每个事件和数据的结构
public class SSEEvent implements Serializable {
    private String id;
    private String event;
    private Object data;
    private boolean json;

    public SSEEvent(String event, Object data) {
        this.event = event;
        this.data = data;
        this.json = SSEParser.isJSONValid(data.toString());
    }

    @Override
    public String toString() {
        String data = this.data.toString();
        if (!SSEParser.isJSONValid(data)) {
            return "Event: " + event + ", Data: " + data;
        }
        return "Event: " + event + ", Data: " + JSONObject.parseObject(data).toJSONString();
    }

}