package com.fh.ai.common.zego;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 即构返回的形象vo
 * <AUTHOR>
 * @date 2024/10/16 13:59
 */
@Data
public class ModelVo implements Serializable {
    @JSONField(name = "AvatarUrl")
    private String avatarUrl;
    @JSONField(name = "MetaHumanModelName")
    private String metaHumanModelName;
    @JSONField(name = "Visibility")
    private Integer visibility;
    @JSONField(name = "Gender")
    private Integer gender;
    @JSONField(name = "MetaHumanModelId")
    private String metaHumanModelId;
}
