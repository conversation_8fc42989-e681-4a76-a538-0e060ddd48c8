package com.fh.ai.common.zhipuai;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.servlet.ServletOutputStream;

import com.alibaba.fastjson.JSONObject;
import com.fh.ai.common.utils.HttpUtil;
import com.fh.ai.common.zhipuai.bo.MessageBo;
import com.fh.ai.common.zhipuai.bo.WebSearchProBo;
import com.fh.ai.common.zhipuai.vo.WebSearchProVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.zhipu.oapi.ClientV4;
import com.zhipu.oapi.Constants;
import com.zhipu.oapi.service.v4.embedding.EmbeddingApiResponse;
import com.zhipu.oapi.service.v4.embedding.EmbeddingRequest;
import com.zhipu.oapi.service.v4.file.FileApiResponse;
import com.zhipu.oapi.service.v4.file.QueryFileApiResponse;
import com.zhipu.oapi.service.v4.file.QueryFilesRequest;
import com.zhipu.oapi.service.v4.file.UploadFileRequest;
import com.zhipu.oapi.service.v4.fine_turning.CreateFineTuningJobApiResponse;
import com.zhipu.oapi.service.v4.fine_turning.FineTuningJobRequest;
import com.zhipu.oapi.service.v4.fine_turning.QueryFineTuningEventApiResponse;
import com.zhipu.oapi.service.v4.fine_turning.QueryFineTuningJobApiResponse;
import com.zhipu.oapi.service.v4.fine_turning.QueryFineTuningJobRequest;
import com.zhipu.oapi.service.v4.fine_turning.QueryPersonalFineTuningJobApiResponse;
import com.zhipu.oapi.service.v4.fine_turning.QueryPersonalFineTuningJobRequest;
import com.zhipu.oapi.service.v4.image.CreateImageRequest;
import com.zhipu.oapi.service.v4.image.ImageApiResponse;
import com.zhipu.oapi.service.v4.model.ChatCompletionRequest;
import com.zhipu.oapi.service.v4.model.ChatFunction;
import com.zhipu.oapi.service.v4.model.ChatFunctionParameters;
import com.zhipu.oapi.service.v4.model.ChatMessage;
import com.zhipu.oapi.service.v4.model.ChatMessageAccumulator;
import com.zhipu.oapi.service.v4.model.ChatMessageRole;
import com.zhipu.oapi.service.v4.model.ChatTool;
import com.zhipu.oapi.service.v4.model.ChatToolType;
import com.zhipu.oapi.service.v4.model.Choice;
import com.zhipu.oapi.service.v4.model.Delta;
import com.zhipu.oapi.service.v4.model.ModelApiResponse;
import com.zhipu.oapi.service.v4.model.ModelData;
import com.zhipu.oapi.service.v4.model.QueryModelResultRequest;
import com.zhipu.oapi.service.v4.model.QueryModelResultResponse;
import com.zhipu.oapi.service.v4.model.WebSearch;
import com.zhipu.oapi.service.v4.model.ZhiPuAiHttpException;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import io.reactivex.Flowable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * release-V4-2.3.2参考:https://github.com/MetaGLM/zhipuai-sdk-java-v4/blob/main/src/test/java/com/zhipu/oapi/V4Test.java
 */
@Data
@Service
@Slf4j
public class ZhipuAiUtil {

    private static final String API_KEY = "76e3b52600ff28ef24ef59b325e10ade.KfilSO8EqwBWkNMX";

    private static final ClientV4 client =
        new ClientV4.Builder(API_KEY).enableTokenCache().networkConfig(300, 100, 100, 100, TimeUnit.SECONDS)
            .connectionPool(new okhttp3.ConnectionPool(5, 5, TimeUnit.MINUTES)).build();

    private static final ObjectMapper mapper = defaultObjectMapper();

    // 智谱的模型选择
    private static final String MODEL_SELECT = Constants.ModelChatGLM4Plus;
    // 智谱web_search是否打开
    private static final boolean WEB_SEARCH_ENABLE = true;
    // maxTokens:https://bigmodel.cn/dev/api/normal-model/glm-4#glm-4
    private static final int MAX_TOKENS = 4095;

    // web-search-pro联网搜索
    private static final String WEB_SEARCH_PRO_URL = "https://open.bigmodel.cn/api/paas/v4/tools";
    private static final String WEB_SEARCH_PRO_TOOL = "web-search-pro";

    public static ObjectMapper defaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        // 最新版：release-V4-2.3.2不再需要
        // mapper.addMixIn(ChatFunction.class,ChatFunctionMixIn.class);
        // mapper.addMixIn(ChatCompletionRequest.class, ChatCompletionRequestMixIn.class);
        // mapper.addMixIn(ChatFunctionCall.class, ChatFunctionCallMixIn.class);
        return mapper;
    }

    // 请自定义自己的业务id
    private static final String requestIdTemplate = "fh-%d";

    /**
     * main方法测试
     * 
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        // System.setProperty("org.slf4j.simpleLogger.logFile", "System.out");
        // String filePath = "C:\\Users\\<USER>\\Desktop\\项目\\凤凰ai\\202404180506.xlsx";
        // StringBuilder sb = readExcel(filePath);
        // sb.append(" 这是大运河相关的知识，请此为背景回答问题。");
        //
        // String question = "根据以上内容，请问泰伯渎的由来是什么？";
        // testInvoke(question, null, sb.toString());

        // 1. sse-invoke调用模型，使用标准Listener，直接返回结果
        // sseInvoke(question);

        // sseInvoke();

        // webSearchInvoke(question);

        // 2. invoke调用模型,直接返回结果
        // testInvoke(sb.toString());

        // 3. 异步调用
        // String taskId = testAsyncInvoke();
        // 4.异步查询
        // testQueryResult("579416905285701758563124066216681206");

        // 5.文生图
        // testCreateImage();

        // 6. 图生文
        // testImageToWord();

        // 7. 向量模型
        // testEmbeddings();

        // 8.微调-上传微调数据集
        // testUploadFile();

        // 9.微调-查询上传文件列表
        // testQueryUploadFileList();

        // 10.微调-创建微调任务
        // testCreateFineTuningJob();

        // 11.微调-查询微调任务事件
        // testQueryFineTuningJobsEvents();

        // 12.微调-查询微调任务
        // testRetrieveFineTuningJobs();

        // 13.微调-查询个人微调任务
        // testQueryPersonalFineTuningJobs();

        // 14.微调-调用微调模型（参考模型调用接口，并替换成要调用模型的编码model）

        // 15.调用web-search-pro
//        webSearchProInvoke("明天在南京应该穿什么衣服？");
    }

    // ---------------------- 正在使用的方法-begin ----------------------
    /**
     * sse调用
     *
     * @param question
     * @param prompt
     * @param contextBos
     * @return
     */
    public static ModelApiResponse sseInvoke(String question, String prompt, List<ContextBo> contextBos,
        String systemContext, Map<String, String> fileIdTaskResultMap) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(systemContext)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), systemContext);
            messages.add(chatSystemMessage);
        }

        // 附件内容
        if (CollectionUtil.isNotEmpty(fileIdTaskResultMap)) {
            for (Map.Entry<String, String> entry : fileIdTaskResultMap.entrySet()) {
                ChatMessage chatFileMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), entry.getValue());
                messages.add(chatFileMessage);
            }
        }

        if (StringUtils.isNotBlank(prompt)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.USER.value(), prompt);
            messages.add(chatSystemMessage);
        }

        if (CollectionUtil.isNotEmpty(contextBos)) {
            // 上下文
            for (ContextBo contextBo : contextBos) {
                // 问
                ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(),
                        Optional.ofNullable(contextBo.getQuestion()).orElse(""));
                // 答
                ChatMessage chatAssMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), contextBo.getAnswer());

                messages.add(chatUserMessage);
                messages.add(chatAssMessage);
            }
        }

        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatMessage);

        // 请求的唯一标识
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 启用搜索
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);
        log.info("=========messages=======" + messages);
        ChatCompletionRequest chatCompletionRequest =
            ChatCompletionRequest.builder().model(MODEL_SELECT).stream(Boolean.TRUE).messages(messages)
                .requestId(requestId).tools(chatToolList).toolChoice("auto").maxTokens(MAX_TOKENS).build();
        ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
        sseModelApiResp.setMsg(JSONUtil.toJsonStr(messages));
        return sseModelApiResp;
    }

    /**
     * 临时使用，长文本会议使用。和上面方法类似，只不过支持model传参。
     * 
     * @param model
     * @param question
     * @param prompt
     * @param contextBos
     * @param systemContext
     * @param text
     * @return
     */
    public static ModelApiResponse sseInvoke(String model, String question, String prompt, List<ContextBo> contextBos,
        String systemContext, String text) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(systemContext)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), systemContext);
            messages.add(chatSystemMessage);
        }

        if (StringUtils.isNotBlank(prompt)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.USER.value(), prompt);
            messages.add(chatSystemMessage);
        }

        if (StringUtils.isNotBlank(text)) {
            ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(), text);
            messages.add(chatUserMessage);
        }

        if (CollectionUtil.isNotEmpty(contextBos)) {
            // 上下文
            for (ContextBo contextBo : contextBos) {
                // 问
                ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(),
                        Optional.ofNullable(contextBo.getQuestion()).orElse(""));
                // 答
                ChatMessage chatAssMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), contextBo.getAnswer());

                messages.add(chatUserMessage);
                messages.add(chatAssMessage);
            }
        }

        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatMessage);

        // 请求的唯一标识
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 启用搜索
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);
        log.info("=========messages=======" + messages);
        if (StringUtils.isEmpty(model)) {
            model = MODEL_SELECT;
        }
        ChatCompletionRequest chatCompletionRequest =
            ChatCompletionRequest.builder().model(model).stream(Boolean.TRUE).messages(messages).requestId(requestId)
                .tools(chatToolList).maxTokens(MAX_TOKENS).toolChoice("auto").build();
        ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
        sseModelApiResp.setMsg(JSONUtil.toJsonStr(messages));
        return sseModelApiResp;
    }

    /**
     * 同步调用，支持传上下文
     *
     * @param question
     * @param prompt
     * @param contextBos
     * @return
     */
    public static ModelApiResponse testInvoke(String question, String prompt, List<ContextBo> contextBos) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(prompt)) {
            // 系统角色
            ChatMessage chatPromptMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt);
            messages.add(chatPromptMessage);
        }

        if (CollectionUtil.isNotEmpty(contextBos)) {
            // 上下文
            for (ContextBo contextBo : contextBos) {
                // 问
                ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(),
                        Optional.ofNullable(contextBo.getQuestion()).orElse(""));
                // 答
                ChatMessage chatAssMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), contextBo.getAnswer());

                messages.add(chatUserMessage);
                messages.add(chatAssMessage);
            }
        }

        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatMessage);

        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 关闭搜索
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        // webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(MODEL_SELECT)
            .stream(Boolean.FALSE).invokeMethod(Constants.invokeMethod).messages(messages).requestId(requestId)
            .tools(chatToolList).maxTokens(MAX_TOKENS).toolChoice("auto").build();
        ModelApiResponse invokeModelApiResp = client.invokeModelApi(chatCompletionRequest);
        String output = JSON.toJSONString(invokeModelApiResp);
        log.info("同步调用返回： {}", output);
        // try {
        // System.out.println("model output:" + mapper.writeValueAsString(invokeModelApiResp));
        // } catch (JsonProcessingException e) {
        // e.printStackTrace();
        // }
        return invokeModelApiResp;
    }

    /**
     * 同步调用
     *
     * @param question
     * @param prompt
     * @return
     */
    public static ModelApiResponse testInvoke(String question, String prompt) {
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(prompt)) {
            // 系统角色
            ChatMessage chatPromptMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt);
            messages.add(chatPromptMessage);
        }

        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatMessage);

        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 启用搜索
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(MODEL_SELECT)
            .stream(Boolean.FALSE).invokeMethod(Constants.invokeMethod).messages(messages).requestId(requestId)
            .tools(chatToolList).maxTokens(MAX_TOKENS).toolChoice("auto").build();
        ModelApiResponse invokeModelApiResp = client.invokeModelApi(chatCompletionRequest);
        String output = JSON.toJSONString(invokeModelApiResp);
        log.info("同步调用返回： {}", output);
        // try {
        // System.out.println("model output:" + mapper.writeValueAsString(invokeModelApiResp));
        // } catch (JsonProcessingException e) {
        // e.printStackTrace();
        // }
        return invokeModelApiResp;
    }

    /**
     * 转换对象
     * 
     * @param flowable
     * @return
     */
    public static Flowable<ChatMessageAccumulator> mapStreamToAccumulator(Flowable<ModelData> flowable) {
        return flowable.map(chunk -> {
            return new ChatMessageAccumulator(chunk.getChoices().get(0).getDelta(), null, chunk.getChoices().get(0),
                chunk.getUsage(), chunk.getCreated(), chunk.getId());
        });
    }

    /**
     * 返回封装对象
     * 
     * @param sseModelApiResp
     * @param outputStream
     * @return
     * @throws Exception
     */
    public static ZhipuData makeStreamResponse(ModelApiResponse sseModelApiResp, ServletOutputStream outputStream)
        throws Exception {
        ZhipuData zhipuData = new ZhipuData();

        StringBuilder answerSb = new StringBuilder();
        StringBuilder responseData = new StringBuilder();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);

        final String[] requestId = {null};

        try {
            if (sseModelApiResp.isSuccess()) {
                // AtomicBoolean isFirst = new AtomicBoolean(true);
                ChatMessageAccumulator chatMessageAccumulator =
                    mapStreamToAccumulator(sseModelApiResp.getFlowable()).doOnNext(accumulator -> {
                        {
                            // if (isFirst.getAndSet(false)) {
                            // System.out.print("Response: ");
                            // }
                            // if (accumulator.getDelta() != null && accumulator.getDelta().getTool_calls() != null) {
                            // String jsonString = mapper.writeValueAsString(accumulator.getDelta().getTool_calls());
                            // System.out.println("tool_calls: " + jsonString);
                            // }
                            // if (accumulator.getDelta() != null && accumulator.getDelta().getContent() != null) {
                            // System.out.print(accumulator.getDelta().getContent());
                            // }

                            // ModelApiResponse response = new ModelApiResponse();
                            responseData.append(JSONUtil.toJsonStr(accumulator));
                            answerSb.append(accumulator.getDelta().getContent());

                            String id = accumulator.getId();
                            requestId[0] = id;

                            Choice choice = accumulator.getChoice();
                            List<Choice> choices = new ArrayList<>();
                            choices.add(choice);
                            ModelData data = new ModelData();
                            data.setChoices(choices);
                            data.setUsage(accumulator.getUsage());
                            data.setId(accumulator.getId());
                            data.setCreated(accumulator.getCreated());
                            // data.setRequestId(chatCompletionRequest.getRequestId());
                            // response.setFlowable(null);
                            // response.setData(data);

                            if (accumulator.getUsage() != null) {
                                zhipuData.setUsageTotal(Long.parseLong(accumulator.getUsage().getTotalTokens() + ""));
                                zhipuData.setUsageIn(Long.parseLong(accumulator.getUsage().getPromptTokens() + ""));
                                zhipuData
                                    .setUsageOut(Long.parseLong(accumulator.getUsage().getCompletionTokens() + ""));
                            }

                            String output = JSON.toJSONString(data);

                            writer.write("data: " + output + "\n\n");
                            writer.flush();

                            log.info("返回： {}", output);
                            // outputStream.write(output.getBytes());
                        }
                    }).doOnComplete(System.out::println).lastElement().blockingGet();
            }
        } catch (Exception e) {
            String defaultAnswer = com.fh.ai.common.constants.Constants.DEFAULT_ERR_ANSWER;
            // 修正prompt超出后的提示
            if (e instanceof ZhiPuAiHttpException && e.getLocalizedMessage() != null
                && e.getLocalizedMessage().contains("超长")) {
                defaultAnswer = com.fh.ai.common.constants.Constants.DEFAULT_PROMPT_OUTFLOW_ANSWER;
            }
            writeDefaultAnswer(defaultAnswer, writer);
            answerSb.append(defaultAnswer);
            zhipuData.setResponseData(responseData.toString());
            zhipuData.setAnswer(answerSb.toString());
            log.error("ZhipuAiUtil makeStreamResponse error:：", e);
            return zhipuData;
        }

        if (StringUtils.isBlank(answerSb)) {
            // data:
            // {"choices":[{"delta":{"content":"。","role":"assistant"},"index":0}],"created":1716977398,"id":"8701002480761299758"}
            // data:
            // {"choices":[{"delta":{"content":"","role":"assistant"},"finishReason":"stop","index":0}],"created":1716977398,"id":"8701002480761299758","usage":{"completionTokens":29,"promptTokens":1565,"totalTokens":1594}}
            writeDefaultAnswer(com.fh.ai.common.constants.Constants.DEFAULT_ANSWER, writer);

            answerSb.append(com.fh.ai.common.constants.Constants.DEFAULT_ANSWER);
        }
        zhipuData.setResponseData(responseData.toString());
        zhipuData.setRequestId(requestId[0]);
        zhipuData.setAnswer(answerSb.toString());
        return zhipuData;
    }

    /**
     * 返回默认数据
     * 
     * @param content
     * @param writer
     * @throws Exception
     */
    public static void writeDefaultAnswer(String content, OutputStreamWriter writer) throws Exception {
        for (int i = 0; i < content.length(); i++) {
            Choice choice = new Choice();
            Delta delta = new Delta();
            delta.setContent(String.valueOf(content.charAt(i)));
            delta.setRole(ChatMessageRole.ASSISTANT.value());
            choice.setDelta(delta);
            choice.setIndex(0L);

            List<Choice> choices = new ArrayList<>();
            choices.add(choice);
            ModelData data = new ModelData();
            data.setChoices(choices);

            String output = JSON.toJSONString(data);
            writer.write("data: " + output + "\n\n");
            writer.flush();
        }

        Choice stopChoice = new Choice();
        Delta stopDelta = new Delta();
        stopDelta.setContent("");
        stopDelta.setRole(ChatMessageRole.ASSISTANT.value());
        stopChoice.setDelta(stopDelta);
        stopChoice.setIndex(0L);
        stopChoice.setFinishReason("stop");

        List<Choice> stopChoices = new ArrayList<>();
        stopChoices.add(stopChoice);
        ModelData stopData = new ModelData();
        stopData.setChoices(stopChoices);

        String stopOutput = JSON.toJSONString(stopData);
        writer.write("data: " + stopOutput + "\n\n");
        writer.flush();
    }

    /**
     * 封装返回对象
     * 
     * @param sseModelApiResp
     * @return
     */
    public static ZhipuData makeStreamResponse(ModelApiResponse sseModelApiResp) {
        ZhipuData zhipuData = new ZhipuData();
        StringBuilder responseData = new StringBuilder();
        StringBuilder answerSb = new StringBuilder();
        // OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);

        final String[] requestId = {null};

        try {
            if (sseModelApiResp.isSuccess()) {
                // AtomicBoolean isFirst = new AtomicBoolean(true);
                ChatMessageAccumulator chatMessageAccumulator =
                    mapStreamToAccumulator(sseModelApiResp.getFlowable()).doOnNext(accumulator -> {
                        {
                            // if (isFirst.getAndSet(false)) {
                            // System.out.print("Response: ");
                            // }
                            // if (accumulator.getDelta() != null && accumulator.getDelta().getTool_calls() != null) {
                            // String jsonString = mapper.writeValueAsString(accumulator.getDelta().getTool_calls());
                            // System.out.println("tool_calls: " + jsonString);
                            // }
                            // if (accumulator.getDelta() != null && accumulator.getDelta().getContent() != null) {
                            // System.out.print(accumulator.getDelta().getContent());
                            // }

                            // ModelApiResponse response = new ModelApiResponse();
                            responseData.append(JSONUtil.toJsonStr(accumulator));
                            answerSb.append(accumulator.getDelta().getContent());

                            String id = accumulator.getId();
                            requestId[0] = id;

                            Choice choice = accumulator.getChoice();
                            List<Choice> choices = new ArrayList<>();
                            choices.add(choice);
                            ModelData data = new ModelData();
                            data.setChoices(choices);
                            data.setUsage(accumulator.getUsage());
                            data.setId(accumulator.getId());
                            data.setCreated(accumulator.getCreated());
                            // data.setRequestId(chatCompletionRequest.getRequestId());
                            // response.setFlowable(null);
                            // response.setData(data);

                            String output = JSON.toJSONString(data);

                            // writer.write("data: " + output + "\n\n");
                            // writer.flush();

                            log.info("返回： {}", output);
                            // outputStream.write(output.getBytes());
                        }
                    }).doOnComplete(System.out::println).lastElement().blockingGet();
            }
        } catch (Exception e) {
            // writeDefaultAnswer(com.fh.ai.common.constants.Constants.DEFAULT_ERR_ANSWER, writer);
            answerSb.append(com.fh.ai.common.constants.Constants.DEFAULT_ERR_ANSWER);
            zhipuData.setResponseData(responseData.toString());
            zhipuData.setAnswer(answerSb.toString());
            log.error("ZhipuAiUtil makeStreamResponse error:：", e);
            return zhipuData;
        }

        if (StringUtils.isBlank(answerSb)) {
            // data:
            // {"choices":[{"delta":{"content":"。","role":"assistant"},"index":0}],"created":1716977398,"id":"8701002480761299758"}
            // data:
            // {"choices":[{"delta":{"content":"","role":"assistant"},"finishReason":"stop","index":0}],"created":1716977398,"id":"8701002480761299758","usage":{"completionTokens":29,"promptTokens":1565,"totalTokens":1594}}
            // writeDefaultAnswer(com.fh.ai.common.constants.Constants.DEFAULT_ANSWER, writer);

            answerSb.append(com.fh.ai.common.constants.Constants.DEFAULT_ANSWER);
        }
        zhipuData.setResponseData(responseData.toString());
        zhipuData.setRequestId(requestId[0]);
        zhipuData.setAnswer(answerSb.toString());
        return zhipuData;
    }

    // ---------------------- 正在使用的方法-end ------------------------

    // ##################### 下面的方法废弃，全部都是历史的测试方法 #####################

    @Deprecated
    private static void testQueryPersonalFineTuningJobs() {
        QueryPersonalFineTuningJobRequest queryPersonalFineTuningJobRequest = new QueryPersonalFineTuningJobRequest();
        queryPersonalFineTuningJobRequest.setLimit(1);
        QueryPersonalFineTuningJobApiResponse queryPersonalFineTuningJobApiResponse =
            client.queryPersonalFineTuningJobs(queryPersonalFineTuningJobRequest);
        System.out.println("model output:" + JSON.toJSONString(queryPersonalFineTuningJobApiResponse));

    }

    @Deprecated
    private static void testQueryFineTuningJobsEvents() {
        QueryFineTuningJobRequest queryFineTuningJobRequest = new QueryFineTuningJobRequest();
        queryFineTuningJobRequest.setJobId("ftjob-20240119114544390-zkgjb");
        // queryFineTuningJobRequest.setLimit(1);
        // queryFineTuningJobRequest.setAfter("1");
        QueryFineTuningEventApiResponse queryFineTuningEventApiResponse =
            client.queryFineTuningJobsEvents(queryFineTuningJobRequest);
        System.out.println("model output:" + JSON.toJSONString(queryFineTuningEventApiResponse));
    }

    /**
     * 查询微调任务
     */
    @Deprecated
    private static void testRetrieveFineTuningJobs() {
        QueryFineTuningJobRequest queryFineTuningJobRequest = new QueryFineTuningJobRequest();
        queryFineTuningJobRequest.setJobId("ftjob-20240119114544390-zkgjb");
        // queryFineTuningJobRequest.setLimit(1);
        // queryFineTuningJobRequest.setAfter("1");
        QueryFineTuningJobApiResponse queryFineTuningJobApiResponse =
            client.retrieveFineTuningJobs(queryFineTuningJobRequest);
        System.out.println("model output:" + JSON.toJSONString(queryFineTuningJobApiResponse));
    }

    /**
     * 创建微调任务
     */
    @Deprecated
    private static void testCreateFineTuningJob() {
        FineTuningJobRequest request = new FineTuningJobRequest();
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());
        request.setRequestId(requestId);
        request.setModel("chatglm3-6b");
        request.setTraining_file("file-20240118082608327-kp8qr");
        CreateFineTuningJobApiResponse createFineTuningJobApiResponse = client.createFineTuningJob(request);
        System.out.println("model output:" + JSON.toJSONString(createFineTuningJobApiResponse));
    }

    /**
     * 微调文件上传列表查询
     */
    @Deprecated
    private static void testQueryUploadFileList() {
        QueryFilesRequest queryFilesRequest = new QueryFilesRequest();
        QueryFileApiResponse queryFileApiResponse = client.queryFilesApi(queryFilesRequest);
        System.out.println("model output:" + JSON.toJSONString(queryFileApiResponse));
    }

    /**
     * 微调上传数据集
     */
    @Deprecated
    private static void testUploadFile() {
        String filePath = "/Users/<USER>/Downloads/transaction-data.jsonl";
        String purpose = "fine-tune";
        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setPurpose(purpose);
        uploadFileRequest.setFilePath(filePath);
        FileApiResponse fileApiResponse = client.invokeUploadFileApi(uploadFileRequest);
        System.out.println("model output:" + JSON.toJSONString(fileApiResponse));
    }

    @Deprecated
    private static void testEmbeddings() {
        EmbeddingRequest embeddingRequest = new EmbeddingRequest();
        embeddingRequest.setInput("hello world");
        embeddingRequest.setModel(Constants.ModelEmbedding2);
        EmbeddingApiResponse apiResponse = client.invokeEmbeddingsApi(embeddingRequest);
        System.out.println("model output:" + JSON.toJSONString(apiResponse));
    }

    /**
     * 图生文
     */
    @Deprecated
    private static void testImageToWord() {
        List<ChatMessage> messages = new ArrayList<>();
        List<Map<String, Object>> contentList = new ArrayList<>();
        Map<String, Object> textMap = new HashMap<>();
        textMap.put("type", "text");
        textMap.put("text", "图里有什么");
        Map<String, Object> typeMap = new HashMap<>();
        typeMap.put("type", "image_url");
        Map<String, Object> urlMap = new HashMap<>();
        urlMap.put("url", "https://sfile.chatglm.cn/testpath/275ae5b6-5390-51ca-a81a-60332d1a7cac_0.png");
        typeMap.put("image_url", urlMap);
        contentList.add(textMap);
        contentList.add(typeMap);
        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), contentList);
        messages.add(chatMessage);
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(Constants.ModelChatGLM4V)
            .stream(Boolean.FALSE).invokeMethod(Constants.invokeMethod).messages(messages).requestId(requestId).build();
        ModelApiResponse modelApiResponse = client.invokeModelApi(chatCompletionRequest);
        System.out.println("model output:" + JSON.toJSONString(modelApiResponse));

    }

    @Deprecated
    private static void testCreateImage() {
        CreateImageRequest createImageRequest = new CreateImageRequest();
        createImageRequest.setModel(Constants.ModelCogView);
        // createImageRequest.setPrompt("画一个温顺可爱的小狗");
        ImageApiResponse imageApiResponse = client.createImage(createImageRequest);
        System.out.println("imageApiResponse:" + JSON.toJSONString(imageApiResponse));
    }

    /**
     * sse调用，单纯测试使用。
     *
     * @param question
     * @param prompt
     * @return
     */
    @Deprecated
    public static ModelApiResponse sseInvoke(String question, String prompt) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(prompt)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt);
            messages.add(chatSystemMessage);
        }

        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatMessage);

        // 请求的唯一标识
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 启用搜索
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);
        log.info("=========messages=======" + messages);
        ChatCompletionRequest chatCompletionRequest =
            ChatCompletionRequest.builder().model(MODEL_SELECT).stream(Boolean.TRUE).messages(messages)
                .requestId(requestId).tools(chatToolList).toolChoice("auto").build();
        ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
        return sseModelApiResp;
    }

    /**
     * sse调用
     *
     * @param question
     * @param prompt
     * @param contextBos
     * @param systemContext
     * @param text
     * @return
     */
    @Deprecated
    public static ModelApiResponse sseInvoke(String question, String prompt, List<ContextBo> contextBos,
        String systemContext, String text) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(systemContext)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), systemContext);
            messages.add(chatSystemMessage);
        }

        if (StringUtils.isNotBlank(prompt)) {
            ChatMessage chatSystemMessage = new ChatMessage(ChatMessageRole.USER.value(), prompt);
            messages.add(chatSystemMessage);
        }

        if (StringUtils.isNotBlank(text)) {
            ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(), text);
            messages.add(chatUserMessage);
        }

        if (CollectionUtil.isNotEmpty(contextBos)) {
            // 上下文
            for (ContextBo contextBo : contextBos) {
                // 问
                ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(),
                        Optional.ofNullable(contextBo.getQuestion()).orElse(""));
                // 答
                ChatMessage chatAssMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), contextBo.getAnswer());

                messages.add(chatUserMessage);
                messages.add(chatAssMessage);
            }
        }

        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatMessage);

        // 请求的唯一标识
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 启用搜索
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);
        log.info("=========messages=======" + messages);
        ChatCompletionRequest chatCompletionRequest =
            ChatCompletionRequest.builder().model(MODEL_SELECT).stream(Boolean.TRUE).messages(messages)
                .requestId(requestId).tools(chatToolList).toolChoice("auto").build();
        ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
        sseModelApiResp.setMsg(JSONUtil.toJsonStr(messages));
        return sseModelApiResp;
    }

    /**
     * sse长文本调用
     *
     * @param question
     * @param prompt
     * @param text
     * @return
     */
    @Deprecated
    public static ModelApiResponse sseInvoke(String question, String prompt, String text) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(prompt)) {
            // 系统角色
            ChatMessage chatPromptMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt);
            messages.add(chatPromptMessage);
        }

        // 系统角色
        ChatMessage chatTextMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), text);
        messages.add(chatTextMessage);

        // 用户角色
        ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatUserMessage);

        // 请求的唯一标识
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // // 启用搜索
        // List<ChatTool> chatToolList = new ArrayList<>();
        // ChatTool chatTool = new ChatTool();
        // chatTool.setType(ChatToolType.WEB_SEARCH.value());
        //
        // WebSearch webSearch = new WebSearch();
        // webSearch.setEnable(true);
        // webSearch.setSearch_query(question);
        // chatTool.setWeb_search(webSearch);
        //
        // chatToolList.add(chatTool);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(MODEL_SELECT)
            .stream(Boolean.TRUE).messages(messages).requestId(requestId)
            // .tools(chatToolList)
            .toolChoice("auto").build();
        ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
        return sseModelApiResp;
    }

    /**
     * sse调用
     */
    @Deprecated
    public static void sseInvoke() {
        List<ChatMessage> messages = new ArrayList<>();
        // ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), "ChatGLM和你哪个更强大");
        // ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), "你能帮我查询2024年1月1日从北京南站到上海的火车票吗？");
        messages.add(chatMessage);
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());
        // 函数调用参数构建部分
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.FUNCTION.value());
        ChatFunctionParameters chatFunctionParameters = new ChatFunctionParameters();
        chatFunctionParameters.setType("object");
        Map<String, Object> properties = new HashMap<>();
        properties.put("departure", new HashMap<String, Object>() {
            {
                put("type", "string");
                put("description", "出发城市或车站");
            }
        });
        properties.put("destination", new HashMap<String, Object>() {
            {
                put("type", "string");
                put("description", "目的地城市或车站");
            }
        });
        properties.put("date", new HashMap<String, Object>() {
            {
                put("type", "string");
                put("description", "要查询的车次日期");
            }
        });
        List<String> required = new ArrayList<>();
        required.add("departure");
        required.add("destination");
        required.add("date");
        chatFunctionParameters.setProperties(properties);
        ChatFunction chatFunction = ChatFunction.builder().name("query_train_info").description("根据用户提供的信息，查询对应的车次")
            .parameters(chatFunctionParameters).required(required).build();
        chatTool.setFunction(chatFunction);
        chatToolList.add(chatTool);

        ChatCompletionRequest chatCompletionRequest =
            ChatCompletionRequest.builder().model(MODEL_SELECT).stream(Boolean.TRUE).messages(messages)
                .requestId(requestId).tools(chatToolList).toolChoice("auto").build();
        ModelApiResponse sseModelApiResp = client.invokeModelApi(chatCompletionRequest);
        if (sseModelApiResp.isSuccess()) {
            AtomicBoolean isFirst = new AtomicBoolean(true);
            ChatMessageAccumulator chatMessageAccumulator =
                mapStreamToAccumulator(sseModelApiResp.getFlowable()).doOnNext(accumulator -> {
                    {
                        if (isFirst.getAndSet(false)) {
                            System.out.print("Response: ");
                        }
                        if (accumulator.getDelta() != null && accumulator.getDelta().getTool_calls() != null) {
                            String jsonString = mapper.writeValueAsString(accumulator.getDelta().getTool_calls());
                            System.out.println("tool_calls: " + jsonString);
                        }
                        if (accumulator.getDelta() != null && accumulator.getDelta().getContent() != null) {
                            System.out.print(accumulator.getDelta().getContent());
                        }
                    }
                }).doOnComplete(System.out::println).lastElement().blockingGet();

            Choice choice = chatMessageAccumulator.getChoice();
            List<Choice> choices = new ArrayList<>();
            choices.add(choice);
            ModelData data = new ModelData();
            data.setChoices(choices);
            data.setUsage(chatMessageAccumulator.getUsage());
            data.setId(chatMessageAccumulator.getId());
            data.setCreated(chatMessageAccumulator.getCreated());
            data.setRequestId(chatCompletionRequest.getRequestId());
            sseModelApiResp.setFlowable(null);
            sseModelApiResp.setData(data);
        }
        System.out.println("model output:" + JSON.toJSONString(sseModelApiResp));
    }

    /**
     * 同步调用
     *
     * @param question
     * @param prompt
     * @param text
     * @return
     */
    @Deprecated
    public static ModelApiResponse testInvoke(String question, String prompt, String text) {
        // 消息
        List<ChatMessage> messages = new ArrayList<>();

        if (StringUtils.isNotBlank(prompt)) {
            // 系统角色
            ChatMessage chatPromptMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt);
            messages.add(chatPromptMessage);
        }

        // 系统角色
        ChatMessage chatSysMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), text);
        // 用户角色
        ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        messages.add(chatSysMessage);
        messages.add(chatUserMessage);

        // 请求的唯一标识
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // // 启用搜索
        // List<ChatTool> chatToolList = new ArrayList<>();
        // ChatTool chatTool = new ChatTool();
        // chatTool.setType(ChatToolType.WEB_SEARCH.value());
        //
        // WebSearch webSearch = new WebSearch();
        // webSearch.setEnable(true);
        // webSearch.setSearch_query(question);
        // chatTool.setWeb_search(webSearch);
        //
        // chatToolList.add(chatTool);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(MODEL_SELECT)
            .stream(Boolean.FALSE).invokeMethod(Constants.invokeMethod).messages(messages).requestId(requestId)
            // .tools(chatToolList)
            .toolChoice("auto").build();
        ModelApiResponse invokeModelApiResp = client.invokeModelApi(chatCompletionRequest);
        String output = JSON.toJSONString(invokeModelApiResp);
        log.info("同步调用返回： {}", output);
        // try {
        // System.out.println("model output:" + mapper.writeValueAsString(invokeModelApiResp));
        // } catch (JsonProcessingException e) {
        // e.printStackTrace();
        // }
        return invokeModelApiResp;
    }

    /**
     * 异步调用
     */
    @Deprecated
    private static String testAsyncInvoke() {
        List<ChatMessage> messages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), "ChatLM和你哪个更强大");
        messages.add(chatMessage);
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());
        // 函数调用参数构建部分
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.FUNCTION.value());
        ChatFunctionParameters chatFunctionParameters = new ChatFunctionParameters();
        chatFunctionParameters.setType("object");
        Map<String, Object> properties = new HashMap<>();
        properties.put("location", new HashMap<String, Object>() {
            {
                put("type", "string");
                put("description", "城市，如：北京");
            }
        });
        properties.put("unit", new HashMap<String, Object>() {
            {
                put("type", "string");
                put("enum", new ArrayList<String>() {
                    {
                        add("celsius");
                        add("fahrenheit");
                    }
                });
            }
        });
        chatFunctionParameters.setProperties(properties);
        ChatFunction chatFunction = ChatFunction.builder().name("get_weather")
            .description("Get the current weather of a location").parameters(chatFunctionParameters).build();
        chatTool.setFunction(chatFunction);
        chatToolList.add(chatTool);
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(MODEL_SELECT)
            .stream(Boolean.FALSE).invokeMethod(Constants.invokeMethodAsync).messages(messages).requestId(requestId)
            .tools(chatToolList).toolChoice("auto").build();
        ModelApiResponse invokeModelApiResp = client.invokeModelApi(chatCompletionRequest);
        System.out.println("model output:" + JSON.toJSONString(invokeModelApiResp));
        return invokeModelApiResp.getData().getId();
    }

    /**
     * 查询异步结果
     *
     * @param taskId
     */
    @Deprecated
    private static void testQueryResult(String taskId) {
        QueryModelResultRequest request = new QueryModelResultRequest();
        request.setTaskId(taskId);
        QueryModelResultResponse queryResultResp = client.queryModelResult(request);
        try {
            System.out.println("model output:" + mapper.writeValueAsString(queryResultResp));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 联网查询调用
     *
     * @param question
     * @return
     */
    @Deprecated
    public static ModelApiResponse webSearchInvoke(String question) {
        List<ChatMessage> messages = new ArrayList<>();
        // ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), "ChatGLM和你哪个更强大");
        ChatMessage chatUserMessage = new ChatMessage(ChatMessageRole.USER.value(), question);
        // ChatMessage chatAssMessage = new ChatMessage(ChatMessageRole.ASSISTANT.value(), "进行联网搜索，是否需要返回联网搜索的网站信息");
        // ChatMessage chatUserMessage2 = new ChatMessage(ChatMessageRole.USER.value(), "查询的网站地址是什么");

        // List<ToolCalls> tool_calls = new ArrayList<>();
        // ToolCalls toolCalls = new ToolCalls();
        // toolCalls.setId("toolId"+ System.currentTimeMillis());
        // toolCalls.setType("web_search");
        // tool_calls.add(toolCalls);

        // chatAssMessage.setTool_calls(tool_calls);

        // ChatMessage chatToolMessage = new ChatMessage("tool", question);
        // chatToolMessage.setTool_call_id("toolId"+ System.currentTimeMillis());

        messages.add(chatUserMessage);
        // messages.add(chatAssMessage);
        // messages.add(chatUserMessage2);
        // messages.add(chatToolMessage);
        // messages.add(chatAssMessage);
        String requestId = String.format(requestIdTemplate, System.currentTimeMillis());

        // 函数调用参数构建部分
        List<ChatTool> chatToolList = new ArrayList<>();
        ChatTool chatTool = new ChatTool();
        chatTool.setType(ChatToolType.WEB_SEARCH.value());

        WebSearch webSearch = new WebSearch();
        webSearch.setEnable(WEB_SEARCH_ENABLE);
        webSearch.setSearch_query(question);
        chatTool.setWeb_search(webSearch);

        chatToolList.add(chatTool);

        ChatCompletionRequest chatCompletionRequest =
            ChatCompletionRequest.builder().model(MODEL_SELECT).stream(Boolean.FALSE)
                .invokeMethod(Constants.invokeMethod).messages(messages).requestId(requestId).tools(chatToolList)
                // .toolChoice("auto")
                .build();
        ModelApiResponse invokeModelApiResp = client.invokeModelApi(chatCompletionRequest);
        String output = JSON.toJSONString(invokeModelApiResp);
        log.info("同步调用返回： {}", output);
        // try {
        // System.out.println("model output:" + mapper.writeValueAsString(invokeModelApiResp));
        // } catch (JsonProcessingException e) {
        // e.printStackTrace();
        // }
        return invokeModelApiResp;
    }

    @Deprecated
    public static StringBuilder readExcel(String filePath) throws IOException {
        FileInputStream fileInputStream = new FileInputStream(new File(filePath));
        Workbook workbook = new XSSFWorkbook(fileInputStream);
        Sheet sheet = workbook.getSheetAt(0);

        StringBuilder sb = new StringBuilder();

        for (Row row : sheet) {
            for (Cell cell : row) {
                switch (cell.getCellTypeEnum()) {
                    case STRING:
                        // System.out.print(cell.getStringCellValue() + "\t");

                        sb.append(cell.getStringCellValue() + "\t");
                        break;
                    case NUMERIC:
                        // System.out.print(cell.getNumericCellValue() + "\t");
                        sb.append(cell.getNumericCellValue() + "\t");
                        break;
                    case BOOLEAN:
                        // System.out.print(cell.getBooleanCellValue() + "\t");
                        sb.append(cell.getBooleanCellValue() + "\t");
                        break;
                    case FORMULA:
                        // System.out.print(cell.getCellFormula() + "\t");
                        sb.append(cell.getCellFormula() + "\t");
                        break;
                    default:
                        break;
                }
            }
            // System.out.println();
            sb.append("\n");
        }

        workbook.close();
        fileInputStream.close();

        return sb;
    }

    /**
     * 联网查询调用
     *
     * @param question
     * @return
     */
    public static WebSearchProVo webSearchProInvoke(String question) {
        try {
            // 请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", API_KEY);
            // 入参
            WebSearchProBo webSearchProBo = new WebSearchProBo();
            webSearchProBo.setTool(WEB_SEARCH_PRO_TOOL);
            webSearchProBo.setRequestId(String.format(requestIdTemplate, System.currentTimeMillis()));
            webSearchProBo.setStream(Boolean.FALSE);
            webSearchProBo.setMessages(Collections.singletonList(new MessageBo(ChatMessageRole.USER.value(), question)));

            JSONObject jsonObject = HttpUtil.doPost(WEB_SEARCH_PRO_URL, headers, defaultObjectMapper().writeValueAsString(webSearchProBo));
            return JSON.parseObject(jsonObject.toJSONString(), WebSearchProVo.class);
        } catch (Exception e) {
            log.error("zhipu webSearchProInvoke error,question:" + question + ",e:" + e);
        }
        return null;
    }



}
