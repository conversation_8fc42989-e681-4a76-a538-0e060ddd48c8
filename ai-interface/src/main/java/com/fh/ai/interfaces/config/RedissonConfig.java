package com.fh.ai.interfaces.config;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String password;

    @Value("${spring.redis.database}")
    private int database;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        if (StringUtils.isBlank(password)) {
            config.useSingleServer().setAddress("redis://" + redisHost + ":" + redisPort).setDatabase(database);
        } else {
            config.useSingleServer().setAddress("redis://" + redisHost + ":" + redisPort).setPassword(password).setDatabase(database);
        }
        return Redisson.create(config);
    }
}



