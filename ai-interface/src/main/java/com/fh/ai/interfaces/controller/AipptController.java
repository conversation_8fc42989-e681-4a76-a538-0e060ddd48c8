package com.fh.ai.interfaces.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.vo.historyApp.HistoryAppVo;
import com.fh.ai.common.enums.OrganizationQuotaType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.ai.business.entity.bo.conversationFile.ConversationFileBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.vo.conversationFile.ConversationFileVo;
import com.fh.ai.business.service.IConversationFileService;
import com.fh.ai.business.service.IHistoryAppService;
import com.fh.ai.common.aippt.AipptUtil;
import com.fh.ai.common.aippt.bo.AipptBo;
import com.fh.ai.common.aippt.vo.AipptCodeVo;
import com.fh.ai.common.aippt.vo.AipptTitleCreateVo;
import com.fh.ai.common.enums.ConversationStateTypeEnum;
import com.fh.ai.common.enums.ConversationTaskTypeEnum;
import com.fh.ai.common.vo.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * aippt的接口
 * 
 * <AUTHOR>
 * @date 2024/11/4 14:48
 */
@Slf4j
@RestController
@RequestMapping("/aippt")
@Validated
@Api(value = "", tags = "aippt接口")
public class AipptController extends BaseController {
    @Resource
    private AipptUtil aipptUtil;
    @Resource
    private IConversationFileService conversationFileService;
    @Resource
    private IHistoryAppService historyAppService;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 获取aippt的code
     *
     * @return aippt code
     */
    @ApiOperation(value = "查询aippt的code", httpMethod = "GET")
    @GetMapping(value = "/code")
    public AjaxResult getAipptCode() {
        String userOid = getCurrentUser().getOid();
        AipptCodeVo aipptCodeVo = aipptUtil.getCodeOfUidWithCache(userOid);
        return AjaxResult.success(aipptCodeVo);
    }

    /**
     * 获取aippt的token
     *
     * @return aippt token
     */
    @ApiOperation(value = "查询aippt的token", httpMethod = "GET")
    @GetMapping(value = "/token")
    public AjaxResult getAipptToken() {
        String userOid = getCurrentUser().getOid();
        AipptCodeVo aipptCodeVo = aipptUtil.getTokenOfUidWithCache(userOid);
        return AjaxResult.success(aipptCodeVo);
    }

    /**
     * 将ppt上传我们系统并返回文件信息，提交一个异步任务到aippt
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-13 17:19:12
     */
    @ApiOperation(value = "提交aippt作品的下载任务", httpMethod = "POST")
    @PostMapping(value = "/submit-download-task")
    public AjaxResult submitDownloadTask(@RequestBody AipptBo aipptBo) {
        // 提交aippt作品id的下载任务到aippt
        String userOid = getCurrentUser().getOid();
        String taskKey = aipptUtil.submitDownloadTask(userOid, aipptBo.getId());
        if (StringUtils.isBlank(taskKey)) {
            return AjaxResult.fail("提交aippt作品的下载任务失败");
        }

        // 插入任务到数据库，conversionFile表
        ConversationFileBo conversationFileBo = new ConversationFileBo();
        conversationFileBo.setConversationCode(aipptBo.getConversationCode());
        conversationFileBo.setBusinessId(String.valueOf(aipptBo.getId()));
        conversationFileBo.setTaskId(taskKey);
        conversationFileBo.setTaskType(ConversationTaskTypeEnum.AIPPT_DOWNLOAD.getCode());
        conversationFileBo.setTaskState(ConversationStateTypeEnum.UPLOADED.getCode());
        conversationFileBo.setHistoryId(aipptBo.getHistoryId());
        conversationFileService.saveConversationFileByConversationCode(conversationFileBo);

        // 更新历史会话中的businessId
        if (aipptBo.getHistoryId() != null && aipptBo.getId() != null) {
            HistoryAppBo historyAppBo = new HistoryAppBo();
            historyAppBo.setId(aipptBo.getHistoryId());
            historyAppBo.setBusinessId(String.valueOf(aipptBo.getId()));
            historyAppService.updateHistoryApp(historyAppBo);

            // 扣减配额
            if (ORGANIZATION_QUOTA_REDUCE_SWITCH) {
                HistoryAppVo historyAppVo = historyAppService.getById(aipptBo.getHistoryId());
                applicationContext.publishEvent(new OrganizationReduceQuotaBo(getCurrentUser().getOid(),
                        getCurrentUser().getOrganizationId(),
                        1L,
                        historyAppVo.getType(),
                        OrganizationQuotaType.PPT.getValue()));
            }
        }

        // 【定时任务做的事情】
        // 定时任务查询aippt作品id的下载任务状态并记录到数据库
        // 记录到数据库(可以复用p_conversation_file)：aippt作品id，用户id，生成状态，关联会话id，创建时间，更新时间
        // 更新会话中的业务json信息，里面存储我们的文件信息

        // 返回taskId
        return AjaxResult.success(taskKey);
    }

    /**
     * 查询aippt下载任务
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-13 17:19:12
     */
    @ApiOperation(value = "提交aippt作品的下载任务", httpMethod = "POST")
    @PostMapping(value = "/query-download-task")
    public AjaxResult queryDownloadTask(@RequestBody AipptBo aipptBo) {
        // 提交aippt作品id的下载任务到aippt
        String userOid = getCurrentUser().getOid();
        List<String> taskResults = aipptUtil.queryDownloadTask(userOid, aipptBo.getTaskKey());
        if (taskResults == null) {
            return AjaxResult.fail("提交aippt作品的下载任务失败");
        }
        return AjaxResult.success(taskResults);
    }

    /**
     * 查询提交任务的在系统里面的状态，根据会话码和历史会话id查询
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-13 17:19:12
     */
    @ApiOperation(value = "查询提交任务的在系统里面的状态", httpMethod = "POST")
    @PostMapping(value = "/query-task")
    public AjaxResult queryTask(@RequestBody AipptBo aipptBo) {
        // 提交aippt作品id的下载任务到aippt
        String userOid = getCurrentUser().getOid();
        ConversationFileBo conversationFileBo = new ConversationFileBo();
        conversationFileBo.setUserOid(userOid);
        conversationFileBo.setConversationCode(aipptBo.getConversationCode());
        conversationFileBo.setHistoryId(aipptBo.getHistoryId());
        conversationFileBo.setTaskType(ConversationTaskTypeEnum.AIPPT_DOWNLOAD.getCode());
        ConversationFileVo detail = conversationFileService.getDetail(conversationFileBo);
        return AjaxResult.success(detail);
    }

    /**
     * 根据标题内容创建任务
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-13 17:19:12
     */
    @ApiOperation(value = "根据标题内容创建任务", httpMethod = "POST")
    @PostMapping(value = "/create-title")
    public AjaxResult createTitle(@RequestBody AipptBo aipptBo) {
        String userOid = getCurrentUser().getOid();
        aipptBo.setUserOid(userOid);
        AipptTitleCreateVo aipptTitleCreateVo = aipptUtil.createTitleTask(aipptBo);
        return AjaxResult.success(aipptTitleCreateVo);
    }

    /**
     * 根据标题内容创建大纲-流式
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -11-13 17:19:12
     */
    @ApiOperation(value = "根据标题内容创建大纲-流式", httpMethod = "POST")
    @PostMapping(value = "/create-outline")
    public void createOutline(@RequestBody AipptBo aipptBo, HttpServletResponse response) {
        String userOid = getCurrentUser().getOid();
        aipptBo.setUserOid(userOid);
        response.setHeader("Content-Type", "text/event-stream");
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Pragma", "no-cache");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            aipptUtil.createOutline(aipptBo, outputStream);
        } catch (Exception e) {
            log.error("根据标题内容创建大纲-流式失败", e);
        }
    }
}
