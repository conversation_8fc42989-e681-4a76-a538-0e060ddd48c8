package com.fh.ai.interfaces.controller;

import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeBo;
import com.fh.ai.business.entity.bo.excellentWorksPrize.ExcellentWorksPrizeConditionBo;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.service.IExcellentWorksPrizeService;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 父类或者接口链接：@see BaseController
 * <p>
 * 描述：优秀作品控制器
 * 创建人: 杨圣君
 * 创建时间: 2024/11/15
 */

@RestController
@RequestMapping("/excellentWorksPrize")
@Slf4j
@Api("优秀作品获奖记录接口")
public class ExcellentWorksPrizeController extends BaseController {
    @Resource
    private IExcellentWorksPrizeService excellentWorksPrizeService;

    @ApiOperation(value = "优秀作品获奖记录列表", httpMethod = "POST")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody ExcellentWorksPrizeBo condition) {
        String userOid = getCurrentUser().getOid();
        condition.setUserOid(userOid);
        return AjaxResult.success(excellentWorksPrizeService.getExcellentWorksPrizeListByCondition(condition));
    }


}
