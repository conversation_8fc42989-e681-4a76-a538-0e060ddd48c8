package com.fh.ai.interfaces.controller;

import com.fh.ai.business.entity.bo.help.HelpConditionBo;
import com.fh.ai.business.service.IHelpService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;


/**
 * 帮助中心
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-06-17 16:56:44
 */
@RestController
@RequestMapping("/help")
@Validated
@Api(value = "/help", tags = "帮助中心接口" )
public class HelpController extends BaseController {
	
    @Autowired
    private IHelpService helpService;


	@PostMapping("/list")
	@ApiOperation(value = "分页查询帮助中心表", httpMethod = "POST")
    public AjaxResult getHelpListByCondition(@RequestBody HelpConditionBo condition){
		if(condition == null){
			condition = new HelpConditionBo();
		}
		condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
		return AjaxResult.success(helpService.getHelpListByCondition(condition));
	}

	@GetMapping("/detail")
	@ApiOperation(value = "查询帮助中心详情", httpMethod = "GET")
	public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
		Map<String, Object> map = helpService.getDetail(id);
		return AjaxResult.success(map);
	}
}
