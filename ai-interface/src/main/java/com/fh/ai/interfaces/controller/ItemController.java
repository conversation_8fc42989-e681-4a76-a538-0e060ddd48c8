package com.fh.ai.interfaces.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.interfaces.bo.ItemCacheBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 出题相关接口
 * 
 * <AUTHOR>
 * @date 2025/2/21 9:58
 */
@Slf4j
@RestController
@RequestMapping("/item")
@Validated
public class ItemController extends BaseController {

    @Resource
    private RedisComponent redisComponent;
    /**
     * 知识点缓存前缀 exam_cache_prefix
     */
    public final static String KW_CACHE_KEY_PREFIX = "e_c_prefix_";

    /**
     * 知识点缓存服务，临时，放在redis里面
     */
    @PostMapping("/cache/put")
    public AjaxResult setCache(@RequestBody ItemCacheBo itemCacheBo) {
        String cacheKey = KW_CACHE_KEY_PREFIX + itemCacheBo.getKey();
        redisComponent.set(cacheKey, itemCacheBo.getValue());
        return AjaxResult.success();
    }

    /**
     * 获取缓存
     * 
     * @param key
     * @param value
     * @return
     */
    @PostMapping("/cache/get")
    public AjaxResult getCache(@RequestBody ItemCacheBo itemCacheBo) {
        String cacheKey = KW_CACHE_KEY_PREFIX + itemCacheBo.getKey();
        Object o = redisComponent.get(cacheKey);
        return AjaxResult.success(o);
    }

}
