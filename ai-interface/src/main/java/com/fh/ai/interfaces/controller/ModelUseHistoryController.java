package com.fh.ai.interfaces.controller;

import com.fh.ai.business.entity.bo.modelUseHistory.ModelUseHistoryBo;
import com.fh.ai.business.service.IModelUseHistoryService;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-03-20  17:58
 */
@RestController
@RequestMapping("/modelUseHistory")
@Slf4j
@Api(tags = "用户模型使用记录")
public class ModelUseHistoryController extends BaseController {
    @Resource
    private IModelUseHistoryService modelUseHistoryService;

    /**
     * 新增用户模型使用记录
     *
     * @param modelUseHistoryBo
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增用户模型使用记录", httpMethod = "POST")
    public AjaxResult addModelUseHistory(@RequestBody ModelUseHistoryBo modelUseHistoryBo) {
        modelUseHistoryBo.setUserOid(getCurrentUser().getOid());
        return modelUseHistoryService.addModelUseHistory(modelUseHistoryBo);
    }
}
