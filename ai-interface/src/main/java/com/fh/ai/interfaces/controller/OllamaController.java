package com.fh.ai.interfaces.controller;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.fh.ai.business.entity.bo.chat.ChatBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;
import com.fh.ai.business.service.IConversationService;
import com.fh.ai.business.service.IHistoryAppService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IUserService;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.ollama.OllamaUtil;
import com.fh.ai.common.ollama.vo.OllamaData;
import com.fh.ai.common.zhipuai.ContextBo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * ollama接口
 * 
 * <AUTHOR>
 * @date 2025/2/10 10:24
 */
@Slf4j
@RestController
@RequestMapping("/ollama")
@Validated
@Api(value = "", tags = "ollama模型接口")
public class OllamaController extends BaseController {

    @Resource
    private IUserService userService;
    @Resource
    private IHistoryAppService historyAppService;
    @Resource
    private IOrganizationService organizationService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private OllamaUtil ollamaUtil;
    @Resource
    private IConversationService conversationService;

    /**
     * 流式对话-会话分离模式（调用流程：1需要先调用conversatin/add ; 2调用本接口执行ollama流式对话）
     *
     * @param chatBo
     * @param res
     */
    @ApiOperation("流式对话")
    @PostMapping(value = "/chat/stream")
    public void streamChat(@RequestBody ChatBo chatBo, HttpServletResponse res) {
        if (ORGANIZATION_QUOTA_REDUCE_SWITCH
            && !organizationService.checkOrganizationQuota(getCurrentUser().getOrganizationId())) {
            throw new BusinessException("余额不足，请反馈企业管理员处理哦！");
        }
        if (StringUtils.isBlank(chatBo.getConversationCode())) {
            throw new BusinessException("参数conversationCode不能为空");
        }
        if (StringUtils.isBlank(chatBo.getModel())) {
            throw new BusinessException("参数model不能为空");
        }

        String userOid = getCurrentUser().getOid();
        String model = chatBo.getModel();

        // 会话信息
        ConversationVo conversationVo =
            conversationService.getDetail(chatBo.getConversationCode(), chatBo.getConversationType());
        if (null == conversationVo) {
            throw new BusinessException("对话信息不存在");
        }

        String question = StringUtils.isNotBlank(conversationVo.getOriginalQuestion())
            ? conversationVo.getOriginalQuestion() : conversationVo.getMessage();
        // 上下文 - 优先使用前端传参的context，否则使用数据库查询的逻辑
        List<ContextBo> contextBos;
        if (StringUtils.isNotBlank(chatBo.getContext())) {
            // 前端传参context有值，优先使用前端传参的context
            List<ContextBo> frontendContextBos = JSONArray.parseArray(chatBo.getContext(), ContextBo.class);
            contextBos = CollectionUtils.isNotEmpty(frontendContextBos) ? frontendContextBos
                : historyAppService.getConversation(chatBo.getConversationCode(), chatBo.getConversationType(), 10);
        } else {
            // 前端传参context为空，使用原有的从数据库查询的逻辑
            contextBos = Optional.ofNullable(conversationVo.getContext()).filter(StringUtils::isNotBlank).map(context -> {
                List<ContextBo> arr = JSONArray.parseArray(context, ContextBo.class);
                return CollectionUtils.isNotEmpty(arr) ? arr
                    : historyAppService.getConversation(chatBo.getConversationCode(), chatBo.getConversationType(), 10);
            }).orElseGet(() -> historyAppService.getConversation(chatBo.getConversationCode(),
                chatBo.getConversationType(), 10));
        }
        OllamaData ollamaData = ollamaUtil.streamChat(question, "", model, contextBos);

        log.info("stream 开始返回");
        // 响应流
        res.setHeader("Content-Type", "text/event-stream");
        res.setContentType("text/event-stream");
        res.setCharacterEncoding("UTF-8");
        res.setHeader("Pragma", "no-cache");

        try (ServletOutputStream outputStream = res.getOutputStream()) {
            OllamaData ollamaDataResult = ollamaUtil.makeStreamResponse(ollamaData.getInputStream(), outputStream);
            HistoryAppBo historyAppBo = new HistoryAppBo();
            historyAppBo.setConversationCode(conversationVo.getConversationCode());
            historyAppBo.setUserOid(userOid);
            historyAppBo.setOrganizationId(getCurrentUser().getOrganizationId());
            historyAppBo.setType(conversationVo.getType());
            historyAppBo.setAskType(conversationVo.getAskType());
            historyAppBo.setPrompt(conversationVo.getPrompt());
            historyAppBo.setOriginalQuestion(conversationVo.getOriginalQuestion());
            historyAppBo.setQuestion(conversationVo.getMessage());
            historyAppBo.setParameterJson(ollamaData.getParams());
            historyAppBo.setRequestId(ollamaDataResult.getId());
            historyAppBo.setResult(ollamaDataResult.getAnswer());
            historyAppBo.setResponseData(ollamaDataResult.getResponseData());
            historyAppBo.setChannel(conversationVo.getChannel());
            historyAppBo.setCreateBy(userOid);
            historyAppBo.setCustomParameterJson(conversationVo.getCustomParameterJson());
            // ollama的用量
            if (ollamaDataResult.getUsage() != null) {
                historyAppBo.setUsageTotal(Long.parseLong(ollamaDataResult.getUsage().getTotal_tokens() + ""));
                historyAppBo.setUsageIn(Long.parseLong(ollamaDataResult.getUsage().getPrompt_tokens() + ""));
                historyAppBo.setUsageOut(Long.parseLong(ollamaDataResult.getUsage().getCompletion_tokens() + ""));
            }
            historyAppBo.setMessageUUID(chatBo.getMessageUUID());
            historyAppService.addHistoryApp(historyAppBo);

            // 扣减配额
            if (ORGANIZATION_QUOTA_REDUCE_SWITCH) {
                applicationContext.publishEvent(new OrganizationReduceQuotaBo(getCurrentUser().getOid(),
                    getCurrentUser().getOrganizationId(), historyAppBo.getUsageTotal(), conversationVo.getType()));
            }
        } catch (Exception e) {
            log.error("ollama模型接口流式对话异常" + e.getMessage());
            throw new BusinessException("流式对话异常");
        }
    }
}
