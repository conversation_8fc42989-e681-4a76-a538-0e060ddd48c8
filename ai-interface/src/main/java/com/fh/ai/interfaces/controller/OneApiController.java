package com.fh.ai.interfaces.controller;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fh.ai.business.entity.bo.chat.ChatBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.bo.tip.TipConditionBo;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;
import com.fh.ai.business.entity.vo.tip.TipRemarkVo;
import com.fh.ai.business.entity.vo.tip.TipVo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.business.service.IConversationService;
import com.fh.ai.business.service.IHistoryAppService;
import com.fh.ai.business.service.ITipService;
import com.fh.ai.common.constants.Constants;
import com.fh.ai.common.enums.ConversationTaskTypeEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.TypeEnum;
import com.fh.ai.common.enums.WebSearchEnabled;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.llmbase.dto.Choices;
import com.fh.ai.common.llmbase.dto.LLMData;
import com.fh.ai.common.llmbase.dto.PromptBo;
import com.fh.ai.common.oneapi.OneApiService;
import com.fh.ai.common.utils.ThreadUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zhipuai.ContextBo;
import com.google.common.collect.Maps;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * one-api接口
 *
 * <AUTHOR>
 * @date 2025/2/10 10:24
 */
@Slf4j
@RestController
@RequestMapping("/one-api")
@Validated
@Api(value = "", tags = "one-api模型接口")
public class OneApiController extends BaseController {

    @Resource
    private IHistoryAppService historyAppService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private OneApiService oneApiService;
    @Resource
    private IConversationService conversationService;
    @Resource
    private ITipService tipService;

    /**
     * 流式对话-会话分离模式（调用流程：1需要先调用conversatin/add ; 2调用本接口执行one-api流式对话）
     *
     * @param chatBo
     */
    @ApiOperation("流式对话")
    @PostMapping(value = "/chat/stream")
    public ResponseBodyEmitter streamChat(@RequestBody ChatBo chatBo, HttpServletResponse response) {
        // 创建ResponseBodyEmitter，设置超时时间
        ResponseBodyEmitter emitter = new ResponseBodyEmitter(300_000L); // 5分钟超时

        // 设置response头
        setupResponseHeaders(response);

        // 校验
        validateRequest(chatBo);

        // 使用自定义线程池异步处理
        UserVo currentUser = getCurrentUser();
        emitter.onTimeout(() -> {
            log.warn("流式对话超时");
            completeEmitterSafely(emitter);
        });
        emitter.onError((throwable) -> {
            log.error("流式对话发生错误", throwable);
            completeEmitterSafely(emitter);
        });
        emitter.onCompletion(() -> {
            log.debug("流式对话完成");
        });
        CompletableFuture.runAsync(() -> {
            try {
                processStreamChat(chatBo, emitter, currentUser);
            } catch (Exception e) {
                log.error("异步处理异常", e);
                handleEmitterError(emitter, e);
            } finally {
                completeEmitterSafely(emitter);
            }
        }, ThreadUtil.getStreamChatExecutor()).exceptionally(throwable -> {
            log.error("流式对话发生错误", throwable);
            handleEmitterError(emitter, throwable);
            return null;
        });

        return emitter;
    }

    private void completeEmitterSafely(ResponseBodyEmitter emitter) {
        try {
            emitter.complete();
        } catch (IllegalStateException e) {
            // emitter已经完成，忽略此异常
            log.debug("Emitter已经完成:", e.getMessage());
        } catch (Exception ex) {
            log.error("完成emitter时发生异常", ex);
        }
    }

    private void handleEmitterError(ResponseBodyEmitter emitter, Throwable throwable) {
        try {
            emitter.completeWithError(throwable);
        } catch (IllegalStateException e) {
            // emitter已经完成，忽略此异常
            log.debug("Emitter已经完成: ", e.getMessage());
        } catch (Exception ex) {
            log.error("处理emitter错误时发生异常", ex);
        }
    }

    /**
     * 异步处理流式对话并返回
     *
     * @param chatBo the chat bo
     * @param emitter the emitter
     * @param createUser the create user
     * <AUTHOR>
     * @date 2025 -07-22 11:46:10
     */
    private void processStreamChat(ChatBo chatBo, ResponseBodyEmitter emitter, UserVo createUser) {
        try {
            String userOid = createUser.getOid();
            String model = chatBo.getModel();
            Boolean webSearchEnabled = chatBo.getWebSearchEnabled();

            // 会话信息
            ConversationVo conversationVo =
                conversationService.getDetail(chatBo.getConversationCode(), chatBo.getConversationType());
            if (null == conversationVo) {
                throw new BusinessException("对话信息不存在");
            }

            // 构建请求数据
            // 文件问答内容
            Map<String, String> fileIdTaskResultMap = buildFileContentMap(conversationVo);
            // 图片地址（多模态问答使用-仅支持图片）
            Map<String, String> imagesMap = buildImagesMap(conversationVo);
            // 本次问题
            String question = Optional.ofNullable(conversationVo.getOriginalQuestion()).filter(StringUtils::isNotBlank)
                .orElse(conversationVo.getMessage());

            // 上下文 - 优先使用前端传参的context，否则使用数据库查询的逻辑
            List<ContextBo> contextBos = getContextBos(chatBo, conversationVo);
            // 网络搜索prompt
            String webSearchPrompt = getWebSearchPrompt(webSearchEnabled, model);
            // user和system提示词
            PromptBo promptBo = getPromptBo(chatBo);
            String userPrompt = promptBo.getUserPrompt();
            String systemPrompt = promptBo.getSystemPrompt();

            // 流式问答请求
            LLMData llmData = oneApiService.streamChat(question, systemPrompt, userPrompt, model, chatBo.getMaxTokens(),
                chatBo.getThinkingType(), contextBos, fileIdTaskResultMap, imagesMap, webSearchEnabled,
                webSearchPrompt);
            // 处理流式响应数据
            processStreamResponse(emitter, llmData, createUser, conversationVo, chatBo);
        } catch (BusinessException e) {
            log.error("流式对话异步处理异常", e);
            throw new RuntimeException("流式对话异步处理异常");
        }
    }

    /**
     * 获取用户和系统提示词
     * 
     * @param chatBo
     * @return
     */
    private PromptBo getPromptBo(ChatBo chatBo) {
        PromptBo promptBo = new PromptBo();
        String userPrompt = chatBo.getPrompt();
        String systemPrompt = chatBo.getSystemPrompt();
        promptBo.setUserPrompt(userPrompt);
        promptBo.setSystemPrompt(systemPrompt);
        // 使用数据库里面的系统提示词
        if ((chatBo.getUseSystemTips() || chatBo.getUseUserTips()) && chatBo.getConversationType() != null) {
            TipConditionBo tipConditionBo = new TipConditionBo();
            tipConditionBo.setAppType(String.valueOf(chatBo.getConversationType()));
            tipConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            Map<String, Object> map = tipService.getTipListByCondition(tipConditionBo);
            if (map.containsKey("list") && map.get("list") != null) {
                List<TipVo> tipVos = JSONArray.parseArray(JSONArray.toJSONString(map.get("list")), TipVo.class);
                TipVo tipVo = tipVos.get(0);
                TipRemarkVo tipRemarkVo = JSON.parseObject(tipVo.getRemark(), TipRemarkVo.class);
                if (chatBo.getUseSystemTips() && tipRemarkVo != null) {
                    promptBo.setSystemPrompt(tipRemarkVo.getContent());
                }
                if (chatBo.getUseUserTips() && tipRemarkVo != null) {
                    promptBo.setUserPrompt(tipRemarkVo.getContent());
                }
            }
        }
        return promptBo;
    }

    /**
     * 获取上下文
     * 
     * @param chatBo
     * @param conversationVo
     * @return
     */
    private List<ContextBo> getContextBos(ChatBo chatBo, ConversationVo conversationVo) {
        List<ContextBo> contextBos;
        if (StringUtils.isNotBlank(chatBo.getContext())) {
            // 前端传参context有值，优先使用前端传参的context
            List<ContextBo> frontendContextBos = JSONArray.parseArray(chatBo.getContext(), ContextBo.class);
            contextBos = CollectionUtils.isNotEmpty(frontendContextBos) ? frontendContextBos
                : historyAppService.getConversation(chatBo.getConversationCode(), chatBo.getConversationType(),
                    Constants.CONTEXT_FROM_HISTORY_SIZE_DEFAULT);
        } else {
            // 前端传参context为空，使用原有的从数据库查询的逻辑
            contextBos =
                Optional.ofNullable(conversationVo.getContext()).filter(StringUtils::isNotBlank).map(context -> {
                    List<ContextBo> arr = JSONArray.parseArray(context, ContextBo.class);
                    return CollectionUtils.isNotEmpty(arr) ? arr
                        : historyAppService.getConversation(chatBo.getConversationCode(), chatBo.getConversationType(),
                            Constants.CONTEXT_FROM_HISTORY_SIZE_DEFAULT);
                }).orElseGet(() -> historyAppService.getConversation(chatBo.getConversationCode(),
                    chatBo.getConversationType(), Constants.CONTEXT_FROM_HISTORY_SIZE_DEFAULT));
        }
        return contextBos;
    }

    /**
     * 校验请求参数
     * 
     * @param chatBo 请求参数
     */
    private void validateRequest(ChatBo chatBo) {
        // 用量校验
        checkOrganizationQuota();
        if (StringUtils.isBlank(chatBo.getConversationCode())) {
            throw new BusinessException("参数conversationCode不能为空");
        }
        if (StringUtils.isBlank(chatBo.getModel())) {
            throw new BusinessException("参数model不能为空");
        }
    }

    /**
     * 构建文件内容映射
     * 
     * @param conversationVo 会话信息
     * @return 文件内容映射map
     */
    private Map<String, String> buildFileContentMap(ConversationVo conversationVo) {
        Map<String, String> fileMap = Maps.<String, String>newHashMap();
        Optional.ofNullable(conversationVo.getFileVos()).ifPresent(
            files -> files.stream().filter(file -> StringUtils.isNotBlank(file.getTaskId())).forEach(file -> {
                String content = String.format("【文档】《%s》 的内容是：【文档-开始】%s【文档-结束】", file.getFileName(),
                    Optional.ofNullable(file.getTaskResult()).orElse(""));
                fileMap.put(file.getTaskId(), content);
            }));
        return fileMap;
    }

    /**
     * 构建地址内容映射
     *
     * @param conversationVo 会话信息
     * @return 地址内容映射map
     */
    private Map<String, String> buildImagesMap(ConversationVo conversationVo) {
        Map<String, String> imagesMap = Maps.<String, String>newHashMap();
        Optional.ofNullable(conversationVo.getFileVos())
            .ifPresent(
                images -> images.stream()
                    .filter(image -> StringUtils.isNotBlank(image.getFileOid()) && image.getTaskType() != null
                        && image.getTaskType().equals(ConversationTaskTypeEnum.FILE_QA_MULTI.getCode()))
                    .forEach(image -> {
                        IAttachmentService attachmentService = SpringUtil.getBean(IAttachmentService.class);
                        AttachmentVo detail = attachmentService.getDetail(image.getFileOid());
                        if (detail == null) {
                            return;
                        }
                        imagesMap.put(image.getFileOid(), detail.getViewPath());
                    }));
        return imagesMap;
    }

    /**
     * 处理流式响应数据
     * 
     * @param emitter 响应
     * @param llmData 流式响应数据
     * @param currentUser 用户
     * @param conversationVo 会话信息
     * @param chatBo 请求参数
     */
    private void processStreamResponse(ResponseBodyEmitter emitter, LLMData llmData, UserVo currentUser,
        ConversationVo conversationVo, ChatBo chatBo) {
        try {
            // 处理结果返回数据后的LLMData，与入参llmData不是同一个对象。
            LLMData llmDataResult = oneApiService.makeStreamResponse(llmData.getInputStream(), emitter,
                llmData.getQuestionWithPromptAndContext(), llmData.getWebSearchResult());
            HistoryAppBo historyApp = createHistoryApp(currentUser, conversationVo, llmDataResult, llmData, chatBo);
            historyAppService.addHistoryApp(historyApp);
            // 扣减配额
            reduceOrganizationQuota(() -> {
                applicationContext.publishEvent(new OrganizationReduceQuotaBo(currentUser.getOid(),
                    currentUser.getOrganizationId(), historyApp.getUsageTotal(), conversationVo.getType()));
                return null;
            });
        } catch (Exception e) {
            log.error("one-api模型接口流式对话异常", e);
            throw new BusinessException("流式对话异常");
        }
    }

    /**
     * 设置响应头 produces注解有问题。
     * 
     * @param res 响应
     */
    private void setupResponseHeaders(HttpServletResponse res) {
        res.setHeader("Content-Type", "text/event-stream");
        res.setContentType("text/event-stream");
        res.setCharacterEncoding("UTF-8");
        res.setHeader("Pragma", "no-cache");
    }

    /**
     * 创建历史应用记录
     * 
     * @param currentUser 用户
     * @param conversationVo 会话信息
     * @param llmDataResult 处理结果返回数据后的LLMData
     * @param llmData 流式响应数据返回的LLMDate
     * @param chatBo 请求参数
     * @return 历史应用记录
     */
    private HistoryAppBo createHistoryApp(UserVo currentUser, ConversationVo conversationVo, LLMData llmDataResult,
        LLMData llmData, ChatBo chatBo) {
        HistoryAppBo historyApp = new HistoryAppBo();
        // Set basic properties
        historyApp.setConversationCode(conversationVo.getConversationCode());
        historyApp.setUserOid(currentUser.getOid());
        historyApp.setOrganizationId(currentUser.getOrganizationId());
        // Set conversation properties
        historyApp.setType(conversationVo.getType());
        historyApp.setAskType(conversationVo.getAskType());
        // Set chat data
        historyApp.setPrompt(conversationVo.getPrompt());
        historyApp.setOriginalQuestion(conversationVo.getOriginalQuestion());
        historyApp.setQuestion(conversationVo.getMessage());
        // Set response data
        historyApp.setParameterJson(llmData.getParams());
        historyApp.setRequestId(llmDataResult.getId());
        historyApp.setResult(llmDataResult.getAnswer());
        historyApp.setResultReasoning(llmDataResult.getAnswerReasoning());
        historyApp.setResponseData(llmDataResult.getResponseData());
        // Set webSearch data
        historyApp.setWebSearchEnabled(WebSearchEnabled.getByValue(chatBo.getWebSearchEnabled()));
        historyApp.setWebSearchParams(llmData.getWebSearchParams());
        String webSearchResult = StringUtils.isNotBlank(llmData.getWebSearchResult()) ? llmData.getWebSearchResult()
            : llmDataResult.getWebSearchResult();
        historyApp.setWebSearchResult(webSearchResult);
        // Set chat model
        historyApp.setModel(chatBo.getModel());
        // Set messageUUID
        historyApp.setMessageUUID(chatBo.getMessageUUID());
        // Set customParameterJson
        historyApp.setCustomParameterJson(conversationVo.getCustomParameterJson());
        // Set other properties
        Optional.ofNullable(llmDataResult.getUsage()).ifPresent(usage -> {
            historyApp.setUsageTotal(usage.getTotal_tokens());
            historyApp.setUsageIn(usage.getPrompt_tokens());
            historyApp.setUsageOut(usage.getCompletion_tokens());
        });
        return historyApp;
    }

    /**
     * 创建历史应用记录
     *
     * @param currentUser 用户
     * @param llmData 流式响应数据返回的LLMDate
     * @param chatBo 请求参数
     * @return 历史应用记录
     */
    private HistoryAppBo createHistoryApp(UserVo currentUser, LLMData llmData, ChatBo chatBo, String userPrompt) {
        HistoryAppBo historyApp = new HistoryAppBo();
        // Set basic properties
        historyApp.setUserOid(currentUser.getOid());
        historyApp.setOrganizationId(currentUser.getOrganizationId());
        // Set conversation properties
        historyApp.setType(chatBo.getConversationType());
        historyApp.setAskType(1);
        // Set chat data
        historyApp.setPrompt(userPrompt);
        historyApp.setOriginalQuestion(chatBo.getQuestion());
        historyApp.setQuestion(chatBo.getQuestion());
        // Set response data
        historyApp.setParameterJson(llmData.getParams());
        historyApp.setRequestId(llmData.getId());
        historyApp.setResult(llmData.getAnswer());
        historyApp.setResultReasoning(llmData.getAnswerReasoning());
        historyApp.setResponseData(llmData.getResponseData());
        // Set chat model
        historyApp.setModel(chatBo.getModel());
        // Set messageUUID
        historyApp.setMessageUUID(chatBo.getMessageUUID());
        // Set other properties
        Optional.ofNullable(llmData.getUsage()).ifPresent(usage -> {
            historyApp.setUsageTotal(usage.getTotal_tokens());
            historyApp.setUsageIn(usage.getPrompt_tokens());
            historyApp.setUsageOut(usage.getCompletion_tokens());
        });
        return historyApp;
    }

    /**
     * 获取web搜索提示词
     * 
     * @param webSearchEnabled 是否开启web搜索
     * @param model 模型（判断模型本身是否支持联网）
     * @return 返回web搜索提示词
     */
    private String getWebSearchPrompt(Boolean webSearchEnabled, String model) {
        if (!Boolean.TRUE.equals(webSearchEnabled)) {
            return "";
        }
        if (Constants.SUPPORT_WEB_SEARCH_MODEL.contains(model)) {
            return "";
        }

        TipConditionBo tipConditionBo = new TipConditionBo();
        tipConditionBo.setAppType(TypeEnum.WEB_SEARCH.getCode().toString());
        tipConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        Map<String, Object> map = tipService.getTipListByCondition(tipConditionBo);

        if (!map.containsKey("list") || map.get("list") == null) {
            return "";
        }

        List<TipVo> tipVos = JSONArray.parseArray(JSONArray.toJSONString(map.get("list")), TipVo.class);
        if (CollectionUtils.isEmpty(tipVos)) {
            return "";
        }

        TipVo tipVo = tipVos.get(0);
        TipRemarkVo tipRemarkVo = JSON.parseObject(tipVo.getRemark(), TipRemarkVo.class);
        return tipRemarkVo.getContent();
    }

    /**
     * 非流式对话
     *
     * @param chatBo
     */
    @ApiOperation("非流式对话")
    @PostMapping(value = "/chat-no-conversation")
    public AjaxResult chatNoConversation(@RequestBody ChatBo chatBo) {
        // 校验
        if (StringUtils.isBlank(chatBo.getModel())) {
            return AjaxResult.fail("参数model不能为空");
        }

        UserVo currentUser = getCurrentUser();
        String model = chatBo.getModel();

        String userPrompt = chatBo.getPrompt();
        String systemPrompt = chatBo.getSystemPrompt();
        // 使用数据库里面的系统提示词
        if ((chatBo.getUseSystemTips() || chatBo.getUseUserTips()) && chatBo.getConversationType() != null) {
            TipConditionBo tipConditionBo = new TipConditionBo();
            tipConditionBo.setAppType(String.valueOf(chatBo.getConversationType()));
            tipConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
            Map<String, Object> map = tipService.getTipListByCondition(tipConditionBo);
            if (map.containsKey("list") && map.get("list") != null) {
                List<TipVo> tipVos = JSONArray.parseArray(JSONArray.toJSONString(map.get("list")), TipVo.class);
                TipVo tipVo = tipVos.get(0);
                TipRemarkVo tipRemarkVo = JSON.parseObject(tipVo.getRemark(), TipRemarkVo.class);
                if (chatBo.getUseSystemTips()) {
                    systemPrompt = tipRemarkVo.getContent();
                }
                if (chatBo.getUseUserTips()) {
                    userPrompt = tipRemarkVo.getContent();
                }
            }
        }

        LLMData llmData = oneApiService.chatNoConversation(chatBo.getQuestion(), systemPrompt, userPrompt, model);
        String res = makeUnStreamResponse(llmData);

        // 保存historyApp记录
        HistoryAppBo historyAppBo = createHistoryApp(currentUser, llmData, chatBo, userPrompt);
        historyAppService.addHistoryApp(historyAppBo);

        return AjaxResult.success(res);
    }

    /**
     * 仅模型调用时候的返回的结果
     *
     * @param
     * @return
     */
    public static String makeUnStreamResponse(LLMData llmData) {
        StringBuilder answerSb = new StringBuilder();
        try {
            List<Choices> choices = llmData.getChoices();
            if (CollUtil.isNotEmpty(choices)) {
                choices.forEach(o -> {
                    answerSb.append(o.getMessage().getContent());
                });
            }
        } catch (Exception e) {
            log.error("one-api makeStreamResponse error:", e);
        }
        String answer = answerSb.toString();
        llmData.setAnswer(answer);
        return answer;
    }
}
