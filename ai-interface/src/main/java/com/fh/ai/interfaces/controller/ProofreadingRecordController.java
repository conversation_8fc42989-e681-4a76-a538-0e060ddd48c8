package com.fh.ai.interfaces.controller;

import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadRetryBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordBo;
import com.fh.ai.business.entity.bo.proofreading.ProofreadingRecordConditionBo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.business.service.IProofreadingRecordService;
import com.fh.ai.business.service.impl.ProofreadingRecordRetryService;
import com.fh.ai.common.enums.ChannelEnum;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.ProofreadingRecordType;
import com.fh.ai.common.utils.ExcelUtils;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.vo.SheetData;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname ProofreadingRecordController
 * @Description 审校记录接口
 * @Date 2025/1/17 17:36
 * @Created by admin
 */
@Slf4j
@RestController
@RequestMapping("/proofreadRecord")
@Validated
@Api(value = "", tags = "审校记录接口")
public class ProofreadingRecordController extends BaseController {
    /**
     * 审校记录服务
     */
    @Resource
    private IProofreadingRecordService proofreadingRecordService;

    @Resource
    private ProofreadingRecordRetryService proofreadingRecordRetryService;

    /**
     * 创建在线审校记录
     * 
     * @return 返回审校记录id，前端调用凤凰/方正审校接口需携带该id。
     */
    @RequestMapping("/addOnlineRecord")
    public AjaxResult createProofreadingRecord(@RequestBody ProofreadingRecordBo proofreadingRecordBo) {
        Assert.notNull(proofreadingRecordBo.getChannel(), "渠道不能为空");
        Assert.hasText(proofreadingRecordBo.getOriginalFileName(), "文件名称不能为空");
        Assert.notEmpty(proofreadingRecordBo.getExecutedTaskInfoList(), "执行的审校任务列表不能为空");
        UserVo currentUser = getCurrentUser();
        // 在线同步审校
        proofreadingRecordBo.setRecordType(ProofreadingRecordType.ONLINE_SYNC.getCode());
        proofreadingRecordBo.setUserOid(currentUser.getOid());
        proofreadingRecordBo.setOrganizationId(currentUser.getOrganizationId());
        AjaxResult result = proofreadingRecordService.addProofreadingRecord(proofreadingRecordBo);
        return result;
    }

    /**
     * 创建文件审校记录
     * 
     * @return 返回审校记录id，前端调用凤凰/方正审校接口需携带该id。
     */
    @RequestMapping("/addFileRecord")
    public AjaxResult createFileProofreadingRecord(@RequestBody ProofreadingRecordBo proofreadingRecordBo) {
        Assert.notNull(proofreadingRecordBo.getChannel(), "渠道不能为空");
        Assert.hasText(proofreadingRecordBo.getFileOid(), "上传后的文件id不能为空");
        Assert.notEmpty(proofreadingRecordBo.getExecutedTaskInfoList(), "执行的审校任务列表不能为空");
        UserVo currentUser = getCurrentUser();
        // 上传文件审校
        proofreadingRecordBo.setRecordType(ProofreadingRecordType.UPLOAD_FILE.getCode());
        proofreadingRecordBo.setUserOid(currentUser.getOid());
        proofreadingRecordBo.setOrganizationId(currentUser.getOrganizationId());
        AjaxResult result = proofreadingRecordService.addProofreadingRecord(proofreadingRecordBo);
        return result;
    }

    /**
     * 重试审校记录
     * 
     * @param retryBo
     * @return
     */
    @RequestMapping("/retryProofread")
    public AjaxResult retryProofread(@RequestBody ProofreadRetryBo retryBo) {
        Assert.notNull(retryBo.getRecordId(), "审校记录id不能为空");
        AjaxResult checkRecordLegal = null;
        try {
            checkRecordLegal = proofreadingRecordRetryService.retryProofread(retryBo);

        }catch (Exception e){
            return AjaxResult.fail(e.getMessage());
        }

        return checkRecordLegal;
    }

    /**
     * 获取当前用户的审校记录列表
     * 
     * @param conditionBo
     * @return
     */
    @RequestMapping("/getMyProofreadList")
    public AjaxResult getMyProofreadList(@RequestBody ProofreadingRecordConditionBo conditionBo) {
        UserVo currentUser = getCurrentUser();
        conditionBo.setUserOid(currentUser.getOid());
        conditionBo.setOrganizationId(currentUser.getOrganizationId());
        return proofreadingRecordService.getMyProofreadRecord(conditionBo);
    }

    /**
     * 获取审校记录详情
     * @param recordId
     * @return
     */
    @RequestMapping("/getMyProofreadDetail/{recordId}")
    public AjaxResult getMyProofreadDetail(@PathVariable("recordId") Long recordId) {
        return proofreadingRecordService.getRecordDetail(recordId);
    }


    /**
     * 删除审校任务
     * 
     * @param recordId
     * @return
     */
    @RequestMapping("/deleteMyProofread")
    public AjaxResult deleteMyProofread(@RequestParam("recordId") Long recordId) {
        ProofreadingRecordBo recordBo = new ProofreadingRecordBo();
        recordBo.setId(recordId);
        recordBo.setIsDelete(IsDeleteEnum.ISDELETE.getCode());
        proofreadingRecordService.updateProofreadingRecord(recordBo);
        return AjaxResult.success("删除成功");
    }

    /**
     * 校验审校记录是否需要下载excel
     *
     * @param recordId
     * @return
     */
    @RequestMapping("/checkIsNeedDownloadExcel")
    public AjaxResult checkIsNeedDownloadExcel(@RequestParam("recordId") Long recordId){
        return proofreadingRecordService.checkIsNeedDownloadExcel(recordId);
    }

    /**
     * 下载在线审校结果
     * 
     * @param recordId
     * @return
     */
    @RequestMapping("/downloadOlineProofreadExcel")
    public void downloadOlineProofreadExcel(@RequestParam("recordId") Long recordId,
        @RequestParam("fileName") String fileName, HttpServletResponse response) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        // 获取下载对象
        AjaxResult result = proofreadingRecordService.getProofreadSheetData(recordId);

        if (result.getSuccess()){
            List<SheetData> sheetDataList = (List<SheetData>)result.getData();
            if (CollectionUtils.isNotEmpty(sheetDataList)){
                // 排序
                List<SheetData> sortedSheets = sheetDataList.stream()
                        .sorted(Comparator.comparingInt(SheetData::getSort))
                        .collect(Collectors.toList());
                // 导出结果
                ExcelUtils.exportExcelRichTextSheets(sortedSheets, fileName, response);
            }
        }

    }

}
