package com.fh.ai.interfaces.controller;

import com.fh.ai.business.entity.bo.scene.SceneConditionBo;
import com.fh.ai.business.service.ISceneService;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-11-20  15:30
 */
@Api("场景接口")
@RestController
@RequestMapping("/scene")
@Slf4j
public class SceneController {
    @Resource
    private ISceneService sceneService;

    @PostMapping("/list")
    @ApiOperation("获取场景推荐列表")
    public AjaxResult getSceneList(@RequestBody SceneConditionBo sceneConditionBo) {
        return AjaxResult.success(sceneService.getSceneListWithDetailList(sceneConditionBo));
    }

}
