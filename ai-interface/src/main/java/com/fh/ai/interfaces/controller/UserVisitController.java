package com.fh.ai.interfaces.controller;

import com.fh.ai.business.entity.bo.userVisit.UserVisitBo;
import com.fh.ai.business.entity.bo.userVisit.UserVisitConditionBo;
import com.fh.ai.business.service.IUserVisitService;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 用户访问记录表
 *
 * <AUTHOR>
 * @date 2024-05-15 13:56:34
 */
@RestController
@RequestMapping("/userVisit")
@Validated
@Api(value = "", tags = "用户访问记录表接口")
public class UserVisitController extends BaseController {

    @Autowired
    private IUserVisitService userVisitService;

    /**
     * 查询用户访问记录表列表
     *
     * <AUTHOR>
     * @date 2024-05-15 13:56:34
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询用户访问记录表", httpMethod = "POST")
    public AjaxResult getUserVisitListByCondition(@RequestBody UserVisitConditionBo condition) {
        return AjaxResult.success(userVisitService.getUserVisitListByCondition(condition));
    }

    /**
     * 新增用户访问记录表
     *
     * <AUTHOR>
     * @date 2024-05-15 13:56:34
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增用户访问记录表", httpMethod = "POST")
    public AjaxResult addUserVisit(@Validated @RequestBody UserVisitBo userVisitBo) {
        userVisitBo.setUserOid(getCurrentUser().getOid());
        userVisitBo.setOrganizationId(getCurrentUser().getOrganizationId());
        userVisitBo.setCreateBy(getCurrentUser().getOid());
        return userVisitService.addUserVisit(userVisitBo);
    }

    /**
     * 修改用户访问记录表
     *
     * @param userVisitBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-05-15 13:56:34
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改用户访问记录表", httpMethod = "POST")
    public AjaxResult updateUserVisit(@Validated @RequestBody UserVisitBo userVisitBo) {
        if (null == userVisitBo.getId()) {
            return AjaxResult.fail("用户访问记录表id不能为空");
        }
        return userVisitService.updateUserVisit(userVisitBo);
    }

    /**
     * 查询用户访问记录表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-05-15 13:56:34
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询用户访问记录表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return userVisitService.getDetail(id);
    }

}