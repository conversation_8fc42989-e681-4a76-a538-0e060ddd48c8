package com.fh.ai.interfaces.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.fh.ai.business.entity.bo.chat.ChatBo;
import com.fh.ai.business.entity.bo.historyApp.HistoryAppBo;
import com.fh.ai.business.entity.bo.organization.OrganizationReduceQuotaBo;
import com.fh.ai.business.entity.vo.conversation.ConversationVo;
import com.fh.ai.business.service.IConversationService;
import com.fh.ai.business.service.IHistoryAppService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IUserService;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.common.zhipuai.ContextBo;
import com.fh.ai.common.zhipuai.ZhipuAiUtil;
import com.fh.ai.common.zhipuai.ZhipuData;
import com.google.common.collect.Maps;
import com.zhipu.oapi.service.v4.model.ModelApiResponse;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/zpAi")
@Validated
@Api(value = "", tags = "智谱AI接口")
public class ZhipuAiController extends BaseController {

    @Autowired
    private IConversationService conversationService;

    @Autowired
    private IHistoryAppService historyAppService;

    @Autowired
    private IUserService userService;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private IOrganizationService organizationService;

    @Autowired
    private ApplicationContext applicationContext;

    @ApiOperation(value = "对话", httpMethod = "POST")
    @PostMapping(value = "/chat")
    public AjaxResult chat(@Validated @RequestBody ChatBo chatBo) {
        if (StringUtils.isBlank(chatBo.getQuestion())) {
            return AjaxResult.success("问题不能为空");
        }

        ModelApiResponse modelApiResponse;
        if (StringUtils.isNotBlank(chatBo.getContext())) {
            List<ContextBo> contextBos = JSONArray.parseArray(chatBo.getContext(), ContextBo.class);
            modelApiResponse = ZhipuAiUtil.testInvoke(chatBo.getQuestion(), chatBo.getPrompt(), contextBos);
        } else {
            modelApiResponse = ZhipuAiUtil.testInvoke(chatBo.getQuestion(), chatBo.getPrompt());
        }

        return AjaxResult.success(modelApiResponse);
    }

    /**
     * 不知道干什么的，by sunqb at 20241009
     * 
     * @param chatBo
     * @param res
     */
    @ApiOperation("单纯流式对话")
    @PostMapping(value = "/streamPureChat")
    @Deprecated
    public void streamPureChat(@RequestBody ChatBo chatBo, HttpServletResponse res) {
        if (StringUtils.isBlank(chatBo.getQuestion())) {
            throw new BusinessException("参数question不能为空");
        }

        ModelApiResponse sseModelApiResp;
        if (StringUtils.isNotBlank(chatBo.getContext())) {
            List<ContextBo> contextBos = JSONArray.parseArray(chatBo.getContext(), ContextBo.class);
            sseModelApiResp = ZhipuAiUtil.sseInvoke(chatBo.getQuestion(), chatBo.getPrompt(), contextBos, null, Maps.newHashMap());
        } else {
            sseModelApiResp = ZhipuAiUtil.sseInvoke(chatBo.getQuestion(), chatBo.getPrompt());
        }

        // 响应流
        res.setHeader("Content-Type", "text/event-stream");
        res.setContentType("text/event-stream");
        res.setCharacterEncoding("UTF-8");
        res.setHeader("Pragma", "no-cache");

        try (ServletOutputStream outputStream = res.getOutputStream()) {
            ZhipuAiUtil.makeStreamResponse(sseModelApiResp, outputStream);
        } catch (Exception e) {
            log.error("单纯流式对话异常" + e.getMessage());
            throw new BusinessException("单纯流式对话异常");
        }
    }

    /**
     * 只有流式支持附件问答
     * @param chatBo
     * @param res
     */
    @ApiOperation("流式对话")
    @PostMapping(value = "/streamChat")
    public void streamChat(@RequestBody ChatBo chatBo, HttpServletResponse res) {
        if (ORGANIZATION_QUOTA_REDUCE_SWITCH
            && !organizationService.checkOrganizationQuota(getCurrentUser().getOrganizationId())) {
            throw new BusinessException("余额不足，请反馈企业管理员处理哦！");
        }

        if (StringUtils.isBlank(chatBo.getConversationCode())) {
            throw new BusinessException("参数conversationCode不能为空");
        }

        // 判断配额
        String userOid = getCurrentUser().getOid();
        AjaxResult userResult = userService.getDetail(userOid);
        if (userResult.failed() || null == userResult.getData()) {
            throw new BusinessException("获取用户信息失败");
        }
        // UserVo userVo = (UserVo) userResult.getData();
        // if (null == userVo.getQuota() || Constants.ZERO.compareTo(userVo.getQuota()) >= 0) {
        // throw new BusinessException("抱歉，您今天的体验次数已满，请明天再来体验");
        // }

        ConversationVo conversationVo =
            conversationService.getDetail(chatBo.getConversationCode(), chatBo.getConversationType());
        // 本地问答内容
        Map<String, String> fileIdTaskResultMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(conversationVo.getFileVos())) {
            conversationVo.getFileVos().stream()
                .filter(conversationFileVo -> StringUtils.isNotBlank(conversationFileVo.getTaskId()))
                .forEach(fileVo -> {
                    String fileNamePrefix = "【文档】《" + fileVo.getFileName() + "》 的内容是：【文档-开始】";
                    String taskResult = StringUtils.isBlank(fileVo.getTaskResult()) ? "" : fileVo.getTaskResult();
                    String fileNameSuffix = "【文档-结束】";
                    fileIdTaskResultMap.put(fileVo.getTaskId(), fileNamePrefix + taskResult + fileNameSuffix);
                });
        }

        List<ContextBo> arr =
            historyAppService.getConversation(chatBo.getConversationCode(), chatBo.getConversationType(), 10);
        if (null == conversationVo) {
            throw new BusinessException("对话信息不存在");
        }

        String question = StringUtils.isNotBlank(conversationVo.getOriginalQuestion())
            ? conversationVo.getOriginalQuestion() : conversationVo.getMessage();

        // List<ContextBo> arr = new ArrayList();
        boolean hasKey = redisComponent.hasKey("system_context_zhipu");
        String systemContext = "";
        if (hasKey) {
            systemContext = (String)redisComponent.get("system_context_zhipu");
        }

        // ModelApiResponse sseModelApiResp;
        // if (StringUtils.isNotBlank(conversationVo.getContext())) {
        // List<ContextBo> contextBos = JSONArray.parseArray(conversationVo.getContext(), ContextBo.class);
        // arr.addAll(contextBos);
        // sseModelApiResp = ZhipuAiUtil.sseInvoke(question, conversationVo.getPrompt(), arr,systemContext);
        // } else {
        // sseModelApiResp = ZhipuAiUtil.sseInvoke(question, conversationVo.getPrompt(),arr,systemContext);
        // }

        // 后台通过历史记录去取
        ModelApiResponse sseModelApiResp =
            ZhipuAiUtil.sseInvoke(question, conversationVo.getPrompt(), arr, systemContext, fileIdTaskResultMap);

        log.info("stream 开始返回");

        // 响应流
        res.setHeader("Content-Type", "text/event-stream");
        res.setContentType("text/event-stream");
        res.setCharacterEncoding("UTF-8");
        res.setHeader("Pragma", "no-cache");

        try (ServletOutputStream outputStream = res.getOutputStream()) {
            // if (sseModelApiResp.isSuccess()) {
            // // 减少配额
            // userService.reduceQuota(userOid);
            // }

            ZhipuData zhipuData = ZhipuAiUtil.makeStreamResponse(sseModelApiResp, outputStream);

            HistoryAppBo historyAppBo = new HistoryAppBo();
            historyAppBo.setConversationCode(conversationVo.getConversationCode());
            historyAppBo.setUserOid(userOid);
            historyAppBo.setOrganizationId(getCurrentUser().getOrganizationId());
            historyAppBo.setType(conversationVo.getType());
            historyAppBo.setAskType(conversationVo.getAskType());
            historyAppBo.setPrompt(conversationVo.getPrompt());
            historyAppBo.setOriginalQuestion(conversationVo.getOriginalQuestion());
            historyAppBo.setQuestion(conversationVo.getMessage());
            historyAppBo.setParameterJson(sseModelApiResp.getMsg());
            historyAppBo.setRequestId(zhipuData.getRequestId());
            historyAppBo.setResult(zhipuData.getAnswer());
            historyAppBo.setResponseData(zhipuData.getResponseData());
            historyAppBo.setChannel(conversationVo.getChannel());
            historyAppBo.setCreateBy(userOid);
            historyAppBo.setUsageTotal(zhipuData.getUsageTotal());
            historyAppBo.setUsageIn(zhipuData.getUsageIn());
            historyAppBo.setUsageOut(zhipuData.getUsageOut());
            historyAppBo.setMessageUUID(chatBo.getMessageUUID());
            historyAppBo.setCustomParameterJson(conversationVo.getCustomParameterJson());
            historyAppService.addHistoryApp(historyAppBo);

            // 扣减配额
            if (ORGANIZATION_QUOTA_REDUCE_SWITCH) {
                applicationContext.publishEvent(new OrganizationReduceQuotaBo(getCurrentUser().getOid(),
                    getCurrentUser().getOrganizationId(), historyAppBo.getUsageTotal(), conversationVo.getType()));
            }

        } catch (Exception e) {
            log.error("流式对话异常:", e);
            throw new BusinessException("流式对话异常");
        }
    }

}
