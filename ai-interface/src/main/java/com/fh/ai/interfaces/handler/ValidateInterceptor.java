package com.fh.ai.interfaces.handler;

import java.util.Date;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.alibaba.fastjson.JSONObject;
import com.fh.ai.business.entity.bo.user.UserBo;
import com.fh.ai.business.entity.vo.user.UserVo;
import com.fh.ai.business.service.IOrganizationPackageService;
import com.fh.ai.business.service.IUserService;
import com.fh.ai.common.constants.ConstHttp;
import com.fh.ai.common.utils.DateKit;
import com.fh.ai.common.vo.AjaxR<PERSON>ult;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户tokens和用户机构有效期校验，tokens用完或者机构过期，需要重新登录
 */
@Slf4j
@Component
public class ValidateInterceptor implements HandlerInterceptor {

    @Value("${validate.organdtokens.enable:false}")
    private Boolean validateOrgandtokensEnable;
    @Resource
    private IUserService userService;
    @Resource
    private IOrganizationPackageService organizationPackageService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        if (!validateOrgandtokensEnable) {
            return true;
        }
        UserVo currentUserVo = getCurrentUser(request);
        // 没登录直接返回true，说明接口不需要登录
        if (currentUserVo == null) {
            return true;
        }

        // 实时查询出用户的信息(不从缓存获取，因为用户信息可能会变动)
        UserBo detailBo = new UserBo();
        detailBo.setOid(currentUserVo.getOid());
        AjaxResult detailResult = userService.getDetail(detailBo);
        if (detailResult.failed() || null == detailResult.getData()) {
            // 没查到交给后面的拦截器处理
            return true;
        }
        UserVo userVo = (UserVo)detailResult.getData();
        // 校验机构
        Date orgAuthStartTime = userVo.getOrgAuthStartTime();
        Date orgAuthEndTime = userVo.getOrgAuthEndTime();
        // 是否有效期内
        boolean orgValidateResult = DateKit.ifDateInSlot(new Date(), orgAuthStartTime, orgAuthEndTime);
        if (!orgValidateResult) {
            response.setStatus(HttpStatus.OK.value());
            response.setCharacterEncoding("UTF-8");
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            AjaxResult ajaxResult = AjaxResult.fail(ConstHttp.NOT_VALIDATE_USER_ORG,"机构过期，需要重新登录");
            response.getWriter().write(JSONObject.toJSONString(ajaxResult));
            log.error("机构过期，需要重新登录,organizationId:"+userVo.getOrganizationId());
            return false;
        }

        // 校验token。暂时使用用户的tokens校验，后续改为使用机构的tokens校验。后续改为具体每个功能校验
        // 查询当前企业的企业用量
        // Long organizationQuota =
        // organizationPackageService.getOrganizationQuotaByCondition(userVo.getOrganizationId());
        // if (organizationQuota == null || organizationQuota <= 0) {
        // response.setStatus(HttpStatus.OK.value());
        // response.setCharacterEncoding("UTF-8");
        // response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        // AjaxResult ajaxResult = AjaxResult.fail(ConstHttp.NOT_VALIDATE_USER_TOKENS,"用户tokens耗尽，需要重新登录");
        // response.getWriter().write(JSONObject.toJSONString(ajaxResult));
        // log.error("用户tokens耗尽，需要重新登录,oid:"+userVo.getOid());
        // return false;
        // }

        return true;
    }

    /*
     * 获取当前登录的用户
     * */
    private UserVo getCurrentUser(HttpServletRequest request) {
        UserVo userVo = null;
        userVo = (UserVo)request.getAttribute("currentUser");
        return userVo;
    }
}