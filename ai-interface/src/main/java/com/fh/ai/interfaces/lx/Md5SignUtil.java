package com.fh.ai.interfaces.lx;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/3/11
 */
@Slf4j
public class Md5SignUtil  {

    private static String signStr(Map<String, Object> params){
        return params.keySet().stream().sorted()
                .filter(k -> ObjectUtil.isNotEmpty(params.get(k)))
                .map(k -> k + "=" + params.get(k)).collect(Collectors.joining("&"));
    }


    public static String encrypt(Map<String, Object> param, String key) {
        String paramStr = signStr(param).concat("&secretKey=").concat(key);
        log.info("【签名】 需要签名的 字符串：{}", paramStr);
        final String sign = SecureUtil.md5(paramStr);
        log.info("【签名】MD5签名：{}, 需要签名的 字符串：{}",sign, paramStr);
        return sign;
    }


    public static boolean verify(Map<String, Object> param, String key, String sign) {
        final String encrypt = encrypt(param, key);
        log.info("【签名】 系统加签：{}， 待比对签名：{}", encrypt, sign);
        return encrypt.equals(sign);
    }

    public static boolean verifyIgnoreCase(Map<String, Object> param, String key, String sign) {
        final String encrypt = encrypt(param, key);
        log.info("【签名】 系统加签：{}， 待比对签名：{}", encrypt, sign);
        return encrypt.equalsIgnoreCase(sign);
    }


    public static void main(String[] args) {
        Map<String, Object> param = new HashMap<>();
        param.put("userId",7);
        param.put("role",1);
        param.put("timestamp",1729493055000L);
        param.put("name","张三");
        param.put("nonce","j7atpjjpgyxl054c0klk99038tyqtd47");
        System.out.println(param);
        String encrypt = encrypt(param, "s5l4sQPK592Y0BjzqXFlix9uY3CRDnhkkyZwngAN5bnmAFdxxPVrkDSp5W94");
        System.out.println(encrypt);
    }
}
