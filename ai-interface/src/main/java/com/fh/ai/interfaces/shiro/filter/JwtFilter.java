package com.fh.ai.interfaces.shiro.filter;

import com.alibaba.fastjson.JSON;
import com.fh.ai.common.exception.AuthenticationException;
import com.fh.ai.common.utils.FileSignUtil;
import com.fh.ai.common.utils.RedisComponent;
import com.fh.ai.common.vo.AjaxResult;
import com.fh.ai.interfaces.shiro.token.JwtToken;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.web.filter.authc.BasicHttpAuthenticationFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.AntPathMatcher;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class JwtFilter extends BasicHttpAuthenticationFilter {

    private final String ACCESS_TOKEN = "Authorization";
    private final String TRUE = "true";

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Value("${shiro.unfilter.path}")
    private String unfilter;

    @Value("${anonymity.on-off}")
    private String on_off;

    private final static String SYSTEM_CONFIG_KEY = "system:config";//系统参数 redis key
    private final static String ANONYMITY_URI_PARAM_KEY = "ANONYMITY_URI";//匿名uri系统参数key
    private final static String PARAM_DESCRIPTION = "paramDescription";

    @Value("${fileSign.tokenFromCookie:false}")
    private Boolean tokenFromCookie;

    @Resource
    @Lazy
    private RedisComponent redisComponent;

    @Value("${fileSign.needCheckFileSignUrl.path:/attachment/getFile/**}")
    private String needCheckFileSignUrl;

    /**
     * 执行登录认证(判断请求头是否带上token)
     *
     * @param httpServletRequest
     * @param httpServletResponse
     * @param mappedValue
     * @return
     */
    @SneakyThrows
    @Override
    protected boolean isAccessAllowed(ServletRequest httpServletRequest, ServletResponse httpServletResponse, Object mappedValue) {
        HttpServletRequest request = (HttpServletRequest) httpServletRequest;
        HttpServletResponse response = (HttpServletResponse) httpServletResponse;
        //如果存在,则进入executeLogin方法执行登入,检查token 是否正确
        try {
            if (isLoginAttempt(request, response)) {
                executeLogin(request, response);
                return true;
            }
        } catch (AuthenticationException e) {
            ServletOutputStream outputStream = response.getOutputStream();
            log.warn("token验证失败!",e);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.setCode(e.getCode());
            ajaxResult.setMsg(e.getMessage());
            String errorMsg = JSON.toJSONString(ajaxResult);
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpStatus.OK.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            outputStream.write(errorMsg.getBytes(StandardCharsets.UTF_8));
            outputStream.flush();
            outputStream.close();
            return false;
        } catch (Exception e) {
            ServletOutputStream outputStream = response.getOutputStream();
            log.warn("token验证失败!",e);
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.setCode(406);
            ajaxResult.setMsg("哎呀～您的登录通行证过期了，先去OA系统登陆，点击“凤凰智灵”的入口，就能重返平台啦！");
            String errorMsg = JSON.toJSONString(ajaxResult);
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpStatus.OK.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            outputStream.write(errorMsg.getBytes(StandardCharsets.UTF_8));
            outputStream.flush();
            outputStream.close();
            return false;
        }
        return true;
    }

    @Override
    protected boolean isLoginAttempt(ServletRequest request, ServletResponse response) {
        HttpServletRequest req = (HttpServletRequest) request;
        String requestURI = req.getRequestURI();
        if (requestURI.contains("ureport")) {
            return false;
        }
        if (TRUE.equals(on_off)) {//如果开启匿名
            Map<String, Object> config = (Map<String, Object>) redisComponent.hget(SYSTEM_CONFIG_KEY, ANONYMITY_URI_PARAM_KEY);
            String uri = (null == config ? null : config.get(PARAM_DESCRIPTION).toString());
            if (!StringUtils.isEmpty(uri)) {
                String[] uris = uri.split(",");
                for (String path : uris) {
                    if (!StringUtils.isEmpty(path)) {
                        if (antPathMatcher.match(path, requestURI)) {
                            return false;
                        }
                    }
                }
            }
        } else {
            if (!StringUtils.isEmpty(unfilter)) {
                String[] paths = unfilter.split(",");
                for (String path : paths) {
                    boolean match = antPathMatcher.match(path, requestURI);
                    if (match) {
                        return false;
                    }
                }
            }
            // 判断访问文件签名是否正确，签名正确不进行token校验
            if (!StringUtils.isEmpty(needCheckFileSignUrl)) {
                String[] paths = needCheckFileSignUrl.split(",");
                for (String path : paths) {
                    boolean match = antPathMatcher.match(path, requestURI);
                    if (match) {
                        String fileSign = req.getParameter("fileSign");
                        String timestamp = req.getParameter("noncestr");
                        String fileName = requestURI.substring(requestURI.lastIndexOf("/") + 1);
                        if (FileSignUtil.checkFileSign(fileName, fileSign, timestamp)) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 重写AuthenticatingFilter的executeLogin方法丶执行登陆操作
     */
    @Override
    protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        //先从请求头中获取token
        String token = httpServletRequest.getHeader(ACCESS_TOKEN);//Authorization
        //如果请求头中没有token,则从表单信息中获取
        if (StringUtils.isBlank(token)) {
            token = httpServletRequest.getParameter(ACCESS_TOKEN);
        }
        // 从cookie中获取
        if (tokenFromCookie && StringUtils.isBlank(token)) {
            Cookie[] cookies = httpServletRequest.getCookies();
            if (null != cookies) {
                for (Cookie cookie : cookies) {
                    if (ACCESS_TOKEN.equalsIgnoreCase(cookie.getName())) {
                        token = cookie.getValue();
                        break;
                    }
                }
            }
        }
        JwtToken jwtToken = new JwtToken(token);
        // 提交给realm进行登入,如果错误他会抛出异常并被捕获, 反之则代表登入成功,返回true
        getSubject(request, response).login(jwtToken);
        return true;
    }

    /**
     * 对跨域提供支持
     */
    @Override
    protected boolean preHandle(ServletRequest httpServletRequest, ServletResponse httpServletResponse) throws Exception {
        HttpServletRequest request = (HttpServletRequest) httpServletRequest;
        HttpServletResponse response = (HttpServletResponse) httpServletResponse;
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Max-Age", "86400");
        response.setHeader("Access-Control-Allow-Headers", "*,Content-Type,Authorization,x-nonce,x-signature,x-timestamp");
        // 如果是OPTIONS则结束请求
        if (HttpMethod.OPTIONS.toString().equals(request.getMethod())) {
            response.setStatus(HttpStatus.OK.value());
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write("".getBytes(StandardCharsets.UTF_8));
            outputStream.flush();
            outputStream.close();
            return false;
        }
        try {
            return super.preHandle(request, response);
        } catch (RuntimeException e) {
            return false;
        }
    }
}
