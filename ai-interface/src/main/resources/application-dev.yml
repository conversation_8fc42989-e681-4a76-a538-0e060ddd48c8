spring:
  datasource:
    url: *********************************************************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5 #初始化连接大小
      minIdle: 5  #最小连接池数量
      maxActive: 50 #最大连接池数量
  #redis
  redis:
    database: 5
    host: ************
    port: 6379
    password:
    timeout: 60000
    jedis:
      pool:
        max-active: 10
        max-wait: -1
        max-idle: 10
        min-idle: 1

#日志输出路径和级别
logging:
  level:
    root: debug
  path: ./logs/

ftp:
  host: ***********
  port: 21
  username: sjwxtest
  password: sjwx123456
  clientTimeout: 0
  connectTimeout: 0
  encoding: UTF-8
  bufferSize: 1024
  passiveMode: true
  basepath: /
  viewRootUrl: http://***********/ydpt-dev/file
  downRootUrl: http://***********/ydpt-dev/file
  rootPath: /data/ftp/ydpt-dev/
  root: /

ftpPool:
  maxTotal: 50
  minIdle: 0
  maxIdle: 50
  maxWait: -1
  blockWhenExhausted: true
  testOnBorrow: true
  testOnReturn: false
  testOnCreate: true
  testWhileIdle: false
  lifo: false

#初始密码
initPassword: gkqhkiG9w0!
#passwordReg: '^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{6,15}$'
passwordReg: '^(?=.*[0-9])(?=.*[a-z])(?=.*[!@#$%^&*,\\.])[0-9a-zA-Z!@#$%^&*,\\.]{8,16}$'
#passwordRegAlert: '密码6-15个字符，必须包含大小写字母、数字'
passwordRegAlert: '密码格式不正确,密码长度为8-16位，包含数字、大小写字母及特殊字符'

gaoding:
  ak: B3CF99000A746AFB964775810E331ECB
  sk: 315ECF85CBEAAEA2F2716D4D0361C3DA

filepath:
  windows: D:\file
  linux: /data/upload/ai
  # 静态地址
  viewPrefix: http://suzhouoffice.4c888.com:62031/files
    # 经过接口的下载地址
  webPrefix: http://10.2.128.17:8898/attachment/download/
  loadPrefix: http://suzhouoffice.4c888.com:62031/attachment/load/

ppm:
#  url: http://my.ppm.cn/wps/LtpaSsoRest/LtpaService?LtpaToken=
#  url: http://myppm.fhsljy.com/wps/LtpaSsoRest/LtpaService?MoreInfo=true&LtpaToken=
  url: http://my.ppm.cn/wps/LtpaSsoRest/LtpaService?MoreInfo=true&LtpaToken=


mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


sms:
  douyin:
    access-key-id: AKLTMzg3MWQ2NDJkYWU3NGRlNGI5Y2Y5MGE0MmEzYzQ0YTA
    access-key-secret: WWpJNU5HVm1aREZqTm1VMk5HVmlZV0ppWkRnMU1USmtNak5oTXpjMllUWQ==
    sign-name: 江苏凤凰出版传媒股份有限公司
    account: 7fd6c2a6
    notice: 7fd637e5
    template-code:
      code: SPT_09a29a26
      margin_remind: ST_84435adb
      arrears_remind: ST_8443526b

# 乐学默认账号所在组织配置
lx:
  default:
    organizationId: 1

# validate校验，校验机构有效期、人员tokens余量。如果true则会金国ValidateInterceptor拦截器中校验。默认false
validate:
  organdtokens:
    enable: true

# aippt
aippt:
  ak: 6721ede06999b
  sk: H5vOyKSIIiRNm4PU1ljugzwA7V9eAwor

# 请求fileextract服务的host
fileextract:
  url: http://localhost:8897

# ollama地址
ollama:
  address:
    chat: http://zktz.4c888.com:62011/api/chat
  authorization: Bearer nvbon5h0YTX4fl0szH57fXpQ62tP5OydY+hsVzWqh+o=

# 凤凰审校接口测试环境 appid
ppmproofread:
  token:
    appid: chatppmtest
    secret: 41769ed040182baf59b671fd2a1b9c8f

# one-api地址
one-api:
  address:
    chat: http://************:3000/v1/chat/completions
  authorization: sk-lMO7P1IMqNqgekecF9Ba7bB70fA14763A7F414CaF9A964F5

# 学伴默认账号所在组织配置
sso:
  default:
    organizationId: 1
# 学伴对外服务的基础地址和应用
wisdom:
  book:
    open:
      base:
        url: "http://127.0.0.1:9001/basic/info/open"
    app:
      code: "eVh4JeYRy3PGBVeH"

# 安全相关：
#是否开启上传文件格式校验
uploadCheck:
  open: true
# 签名校验
sign:
  # 是否开启签名配置
  open: false
  # 请求有效性校验（基于签名有效时间）
  requestExpiredCheck: true
  # 重复请求校验（如果开启 有效性必须开启 ，要不然 没有意义）
  requestRepeatCheck: false
  # 排除的url
  ignoreUrl: /attachment/**,/conversationFile/upload,/conversationFile/uploadByBase64,/coze/upload,/qwen/uploadFile,/open/**,/bookCopywriting/upload/excel
  # 密钥
  secret: c677049b-cb54-4400-9234-40efbd448e7d
  # 签名失效时间，单位秒
  expire: 7200
# xss和sql注入过滤
xssFilter:
  # 是否开启
  open: true
  # 忽略的url
  ignoreUrl: /login/**,/open/**,/zpAi/chat,/conversation/add,/conversation/update,/bookListPoster/add,/bookListPoster/update
sqlFilter:
  # 是否开启
  open: true
  # 忽略的url
  ignoreUrl: /login/**,/open/**,/zpAi/chat
fileSign:
  # 文件签名校验url
  needCheckFileSignUrl:
    path: /attachment/getFile/**
  # 是否从cookie中获取token
  tokenFromCookie: true

# ffmpeg转换开关（智能会议转换音频采样率）
ffmpeg:
  switch: false

# 美图图片替换为cdn地址的开关和地址配置image.cdn.enable
image:
  cdn:
    enable: true
    orihost: http://hdq.fhsljy.com:62031
    targethost: http://hdq.fhsljy.com:62031