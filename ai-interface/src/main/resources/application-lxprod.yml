spring:
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************
    username: educationsprod
    password: edju#VrCH3n1BDDq9b@M
    druid:
      initial-size: 5 #初始化连接大小
      minIdle: 5  #最小连接池数量
      maxActive: 50 #最大连接池数量
  #redis
  redis:
    database: 3
    host: r-uf6gq4ub6c72dighfc.redis.rds.aliyuncs.com
    port: 6379
    password: zlQw!K84aKy2
    timeout: 60000
    jedis:
      pool:
        max-active: 10
        max-wait: -1
        max-idle: 10
        min-idle: 1

#日志输出路径和级别
logging:
  level:
    root: error
  path: ./logs/

ftp:
  host: ************
  port: 21
  username: yuedupingtai
  password: 123456
  clientTimeout: 0
  connectTimeout: 0
  encoding: UTF-8
  bufferSize: 1024
  passiveMode: true
  basepath: /
  viewRootUrl: https://ssl10.test.fhsljy.com/files
  downRootUrl: https://ssl10.test.fhsljy.com/files
  rootPath: /www/wwwroot/ssl10.test.fhsljy.com/files/
  root: /

ftpPool:
  maxTotal: 50
  minIdle: 0
  maxIdle: 50
  maxWait: -1
  blockWhenExhausted: true
  testOnBorrow: true
  testOnReturn: false
  testOnCreate: true
  testWhileIdle: false
  lifo: false

#初始密码
initPassword: gkqhkiG9w0!
#passwordReg: '^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{6,15}$'
passwordReg: '^(?=.*[0-9])(?=.*[a-z])(?=.*[!@#$%^&*,\\.])[0-9a-zA-Z!@#$%^&*,\\.]{8,16}$'
#passwordRegAlert: '密码6-15个字符，必须包含大小写字母、数字'
passwordRegAlert: '密码格式不正确,密码长度为8-16位，包含数字、大小写字母及特殊字符'

gaoding:
  ak: B3CF99000A746AFB964775810E331ECB
  sk: 315ECF85CBEAAEA2F2716D4D0361C3DA

filepath:
  windows: D:\file
  linux: /data/upload/ai
  viewPrefix: https://chat.ppm.cn/file
  webPrefix: https://chat.ppm.cn/fhzl/attachment/download/
  loadPrefix: https://chat.ppm.cn/fhzl/attachment/load/

ppm:
  #  url: http://my.ppm.cn/wps/LtpaSsoRest/LtpaService?LtpaToken=
  url: http://myppm.fhsljy.com/wps/LtpaSsoRest/LtpaService?MoreInfo=true&LtpaToken=

# 乐学默认账号所在组织配置
lx:
  default:
    organizationId: 1
  evaluating:
    returnUrl: https://esp.jsfhlx.com/mall/speechEvaluationMain/result
    task:
      enabled: true # 乐学测评定时任务开关 true开启任务 false关闭任务

# validate校验，校验机构有效期、人员tokens余量。如果true则会金国ValidateInterceptor拦截器中校验
validate:
  organdtokens:
    enable: false