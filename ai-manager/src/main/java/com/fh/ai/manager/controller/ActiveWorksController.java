package com.fh.ai.manager.controller;

import com.fh.ai.business.entity.bo.worksActive.WorksActiveBo;
import com.fh.ai.business.entity.bo.worksActive.WorksActiveConditionBo;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.service.IWorksActiveService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.enums.IsSupportVoteEnum;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 作品
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-11-07  17:10
 */
@RestController
@RequestMapping("/activeWorks")
@Slf4j
@Api("作品活动接口")
public class ActiveWorksController extends BaseController {
    @Resource
    private IWorksActiveService worksActiveService;

    /**
     * 按条件获取活动作品列表
     * <p>
     * 传入参数: @param condition 条件
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/11/22
     */

    @PostMapping("/list")
    @ApiOperation(value = "分页查询活动作品表", httpMethod = "POST")
    public AjaxResult getActivityWorksListByCondition(@RequestBody WorksActiveConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return AjaxResult.success(worksActiveService.getWorksActiveListByConditionPage(condition));
    }


    /**
     * 添加活动作品
     * <p>
     * 传入参数: @param activityBo 活动 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/11/22
     */

    @PostMapping("/add")
    @ApiOperation(value = "新增活动作品表", httpMethod = "POST")
    public AjaxResult addActivityWorks(@Validated @RequestBody WorksActiveBo activityBo) {
        if (IsSupportVoteEnum.YES.getValue().equals(activityBo.getIsSupportVote())) {
            // 校验投票开始时间需大于等于活动结束时间
            if (activityBo.getVoteStartTime().before(activityBo.getActiveEndTime())) {
                return AjaxResult.fail("投票开始时间需大于活动结束时间");
            }
        }
        AdminVo currentAdmin = getCurrentAdmin();
        activityBo.setUserOid(currentAdmin.getOid());
        activityBo.setCreateBy(currentAdmin.getOid());
        activityBo.setOrganizationId(currentAdmin.getOrganizationId());
        return worksActiveService.addWorksActive(activityBo);
    }


    /**
     * 修改 活动作品
     * <p>
     * 传入参数: @param activityBo 活动 bo
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/11/22
     */

    @PostMapping("/update")
    @ApiOperation(value = "修改活动作品表", httpMethod = "POST")
    public AjaxResult updateActivityWorks(@Validated @RequestBody WorksActiveBo activityBo) {
        if (null == activityBo.getId()) {
            return AjaxResult.fail("活动表id不能为空");
        }
        if (IsSupportVoteEnum.YES.getValue().equals(activityBo.getIsSupportVote())) {
            // 校验投票开始时间需大于等于活动结束时间
            if (activityBo.getVoteStartTime().before(activityBo.getActiveEndTime())) {
                return AjaxResult.fail("投票开始时间需大于活动结束时间");
            }
        }
        activityBo.setUpdateBy(getCurrentAdmin().getOid());
        return worksActiveService.updateWorksActive(activityBo);
    }

    /**
     * 查询活动表详情
     * <p>
     * 传入参数: @param id
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/11/22
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询活动作品表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return worksActiveService.getDetail(id);
    }


    /**
     * 删除活动表
     * <p>
     * 传入参数: @param id
     * 返回值: @return {@link AjaxResult }
     * 创建人: 杨圣君
     * 创建时间: 2024/11/22
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除活动作品表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id) {
        WorksActiveBo activityBo = new WorksActiveBo();
        activityBo.setId(id);
        activityBo.setUpdateBy(getCurrentAdmin().getOid());
        return worksActiveService.deleteActivity(activityBo);
    }


}
