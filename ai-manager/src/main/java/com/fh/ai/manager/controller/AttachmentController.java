package com.fh.ai.manager.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import com.fh.ai.business.entity.bo.attachment.AttachmentBo;
import com.fh.ai.business.entity.bo.attachment.AttachmentConditionBo;
import com.fh.ai.business.entity.vo.attachment.AttachmentVo;
import com.fh.ai.business.service.IAttachmentService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.exception.BusinessException;
import com.fh.ai.common.utils.SystemUtil;
import com.fh.ai.common.vo.AjaxResult;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.util.List;

/**
 * 附件表
 *
 * <AUTHOR>
 * @date 2024-03-06 16:23:34
 */
@Slf4j
@RestController
@RequestMapping("/attachment")
@Validated
@Api(value = "", tags = "附件表接口")
public class AttachmentController extends BaseController {

    @Autowired
    private IAttachmentService attachmentService;

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    /**
     * 查询附件表列表
     *
     * <AUTHOR>
     * @date 2024-03-06 16:23:34
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询附件表", httpMethod = "POST")
    public AjaxResult getAttachmentListByCondition(@RequestBody AttachmentConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return AjaxResult.success(attachmentService.getAttachmentListByCondition(condition));
    }

    /**
     * 新增附件表
     *
     * <AUTHOR>
     * @date 2024-03-06 16:23:34
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增附件表", httpMethod = "POST")
    public AjaxResult addAttachment(@Validated @RequestBody AttachmentBo attachmentBo) {
        return attachmentService.addAttachment(attachmentBo);
    }

    /**
     * 修改附件表
     *
     * @param attachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-06 16:23:34
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改附件表", httpMethod = "POST")
    public AjaxResult updateAttachment(@Validated @RequestBody AttachmentBo attachmentBo) {
        if (null == attachmentBo.getId()) {
            return AjaxResult.fail("附件表id不能为空");
        }
        return attachmentService.updateAttachment(attachmentBo);
    }

    /**
     * 查询附件表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-06 16:23:34
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询附件表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return attachmentService.getDetail(id);
    }

    /**
     * 更新状态
     *
     * @param attachmentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-04 15:14:34
     */
    @PostMapping("/updateState")
    @ApiOperation(value = "更新状态", httpMethod = "POST")
    public AjaxResult updateState(@Validated @RequestBody AttachmentBo attachmentBo) {
        if (null == attachmentBo.getId()) {
            return AjaxResult.fail("附件表id不能为空");
        }

        if (null == attachmentBo.getState()) {
            return AjaxResult.fail("状态不能为空");
        }

        attachmentBo.setUpdateBy(getCurrentAdmin().getOid());
        return attachmentService.updateState(attachmentBo);
    }

    /**
     * 删除附件表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-03-06 16:23:34
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除附件表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id) {
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setId(id);
        attachmentBo.setUpdateBy(getCurrentAdmin().getOid());
        return attachmentService.deleteAttachment(attachmentBo);
    }

    /**
     * 上传附件
     *
     * @param file
     * @return
     */
    @ApiOperation("上传附件")
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file,Boolean scalePath) {
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setCreateBy(getCurrentAdmin().getOid());
        attachmentBo.setScalePath(scalePath);
        return attachmentService.uploadFile(file, attachmentBo);
    }

    /**
     * 上传附件(第二天失效，运维脚本在服务上面删除前一天的临时文件夹数据)
     *
     * @param file
     * @return
     */
    @ApiOperation("上传附件(第二天失效，运维脚本在服务上面删除前一天的临时文件夹数据)")
    @PostMapping("/upload-temp")
    public AjaxResult uploadTemp(@RequestParam("file") MultipartFile file) {
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setCreateBy(getCurrentAdmin().getOid());
        attachmentBo.setTempFile(true);
        return attachmentService.uploadFile(file, attachmentBo);
    }

    /**
     * 批量上传文件
     *
     * @param files
     * @return
     */
    @ApiOperation("批量上传文件")
    @PostMapping("/uploadFiles")
    public AjaxResult uploadFiles(@RequestParam(value = "files") MultipartFile[] files) {
        if (null == files || files.length == 0) {
            return AjaxResult.fail("文件不能为空");
        }

        List<AttachmentVo> attachmentVos = Lists.newArrayList();
        for (MultipartFile file : files) {
            AjaxResult upload = null;
            try {
                upload = upload(file,Boolean.FALSE);
            } catch (Exception e) {
                log.error("文件{}上传出错{}", file.getOriginalFilename(), e.getMessage());
            }

            if (!upload.getSuccess()) {
                continue;
            }

            AttachmentVo attachmentVo = (AttachmentVo) upload.getData();
            attachmentVos.add(attachmentVo);
        }

        return AjaxResult.success(attachmentVos);
    }

    /**
     * 下载文件
     *
     * @param fileOid
     * @param response
     */
    @GetMapping("/download/{fileOid}")
    @ApiOperation(value = "下载文件")
    public void download(@PathVariable("fileOid") String fileOid, HttpServletResponse response) {

        // 获取附件信息
        AttachmentVo attachment = attachmentService.getDetail(fileOid);
        if (attachment == null) {
            throw new BusinessException("附件不存在");
        }

        // 文件名
        String fileName = attachment.getOriginalName();
        // 文件相对路径
        String fileRelativePath = attachment.getOriginPath();
        fileRelativePath = SystemUtil.isWindows() ? windowsPath + fileRelativePath : linuxPath + fileRelativePath;

        Long fileSize = attachment.getSize();
        fileSize = fileSize * 1024;

        // 进行下载
        response.setContentLengthLong(new File(fileRelativePath).length());
        //response.setHeader(HttpHeaders.CONTENT_LENGTH,fileSize+"");
        response.setContentType("application/octet-stream; charset=utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLUtil.encode(fileName));

        InputStream inputStream = null;
        final ServletOutputStream outputStream;
        try {
            inputStream = new FileInputStream(fileRelativePath);
            outputStream = response.getOutputStream();

            //写出
            byte[] b = new byte[1024];
            int len;
            //从输入流中读取一定数量的字节，并将其存储在缓冲区字节数组中，读到末尾返回-1
            while ((len = inputStream.read(b)) > 0) {
                outputStream.write(b, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new BusinessException("文件下载失败");
        } finally {
            IoUtil.close(inputStream);
        }
    }

    /**
     * 文件预览
     *
     * @param fileOid
     * @throws IOException
     */
    @GetMapping("/preview/{fileOid}")
    @ApiOperation(value = "文件预览")
    public void preview(@PathVariable("fileOid") String fileOid, HttpServletResponse response) {
        final byte[] preview = filePreview(fileOid);
        if (preview == null) {
            throw new BusinessException("文件预览失败");
        }
        response.setContentLength(preview.length);
        response.setHeader("Accept-Ranges", "bytes");
        final ServletOutputStream outputStream;
        try {
            outputStream = response.getOutputStream();
        } catch (IOException e) {
            throw new BusinessException("文件预览失败");
        }

        IoUtil.write(outputStream, false, preview);
    }

    /**
     * 读取文件
     *
     * @param fileOid
     * @return
     */
    private byte[] filePreview(String fileOid) {
        AttachmentVo attachment = attachmentService.getDetail(fileOid);
        if (null == attachment) {
            return null;
        }
        String fileRelativePath = attachment.getOriginPath();

        String filePath = SystemUtil.isWindows() ? windowsPath + fileRelativePath : linuxPath + fileRelativePath;
        BufferedInputStream inputStream = FileUtil.getInputStream(filePath);
        //进行读取数据流 内部强制关闭流
        final byte[] bytes = IoUtil.readBytes(inputStream, true);
        return bytes;
    }

    @ApiOperation("上传视频返回第一帧")
    @PostMapping("/uploadVideo")
    public AjaxResult uploadVideo(@RequestParam("file") MultipartFile file) {
        AttachmentBo attachmentBo = new AttachmentBo();
        attachmentBo.setCreateBy(getCurrentAdmin().getOid());
        return attachmentService.uploadVideo(file, attachmentBo);
    }
}