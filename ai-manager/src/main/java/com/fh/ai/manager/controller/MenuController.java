package com.fh.ai.manager.controller;

import com.fh.ai.business.entity.bo.menu.MenuConditionBo;
import com.fh.ai.business.service.IMenuService;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 菜单接口
 *
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
@Slf4j
@RestController
@RequestMapping("/menu")
@Validated
@Api(value = "", tags = "菜单接口")
public class MenuController {

    @Autowired
    private IMenuService menuService;

    /**
     * 查询列表
     *
     * <AUTHOR>
     * @date 2023-05-04 10:58:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询", httpMethod = "POST")
    public AjaxResult getMenuListByCondition(@RequestBody MenuConditionBo condition) {
        return AjaxResult.success(menuService.getMenuListByCondition(condition));
    }

}
