package com.fh.ai.manager.controller;

import com.fh.ai.business.entity.bo.organization.OrganizationBo;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.bo.organizationUsageStatistic.OrganizationUsageStatisticConditionBo;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.business.service.IOrganizationUsageStatisticService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 组织机构表
 *
 * <AUTHOR>
 * @date 2023-08-29 18:01:13
 */
@RestController
@RequestMapping("/organization")
@Validated
@Api(value = "", tags = "组织机构表接口")
public class OrganizationController extends BaseController {

    @Autowired
    private IOrganizationService organizationService;
    @Autowired
    private IOrganizationUsageStatisticService organizationUsageStatisticService;

    /**
     * 查询组织机构表列表
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询组织机构表", httpMethod = "POST")
    public AjaxResult getOrganizationListByCondition(@RequestBody OrganizationConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return AjaxResult.success(organizationService.getOrganizationListByCondition(condition));
    }

    /**
     * 查询需要统计的组织机构
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @GetMapping("/getStatisticsOrganizationList")
    @ApiOperation(value = "查询需要统计的组织机构", httpMethod = "GET")
    public AjaxResult getStatisticsOrganizationList() {
        return AjaxResult.success(organizationService.getStatisticsOrganizationList());
    }

    /**
     * 查询组织树
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/getOrganizationTree")
    @ApiOperation(value = "查询组织树", httpMethod = "POST")
    public AjaxResult getOrganizationTree(@RequestBody OrganizationBo organizationBo) {
        return AjaxResult.success(organizationService.getOrganizationTree(organizationBo.getIds()));
    }

    /**
     * 查询单个组织子树
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/getSubOrganizationTree")
    @ApiOperation(value = "查询单个组织子树", httpMethod = "POST")
    public AjaxResult getSubOrganizationTree(@RequestBody OrganizationBo organizationBo) {
        if (null == organizationBo.getId()) {
            return AjaxResult.fail("组织机构id不能为空");
        }

        return AjaxResult.success(organizationService.getSubOrganizationTree(organizationBo.getId()));
    }

    /**
     * 查询单个组织含有人员信息子树
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/getSubOrganizationTreeWithAdmin")
    @ApiOperation(value = "查询单个组织含有人员信息子树", httpMethod = "POST")
    public AjaxResult getSubOrganizationTreeWithAdmin(@RequestBody OrganizationBo organizationBo) {
        if (null == organizationBo.getId()) {
            return AjaxResult.fail("组织机构id不能为空");
        }

        return AjaxResult.success(organizationService.getSubOrganizationTreeWithAdmin(organizationBo.getId()));
    }

    /**
     * 查询单个家族组织树
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/getFamilyOrganizationTree")
    @ApiOperation(value = "查询单个家族组织树", httpMethod = "POST")
    public AjaxResult getFamilyOrganizationTree(@RequestBody OrganizationBo organizationBo) {
        if (null == organizationBo.getBottomOrganizationId()) {
            return AjaxResult.fail("下级组织机构id不能为空");
        }

        if (null == organizationBo.getTopOrganizationId()) {
            return AjaxResult.fail("上级组织机构id不能为空");
        }

        return AjaxResult.success(organizationService.getFamilyOrganizationTree(organizationBo.getBottomOrganizationId(), organizationBo.getTopOrganizationId()));
    }

    /**
     * 新增组织机构表
     *
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增组织机构表", httpMethod = "POST")
    public AjaxResult addOrganization(@Validated @RequestBody OrganizationBo organizationBo) {
        return organizationService.addOrganization(organizationBo);
    }

    /**
     * 修改组织机构表
     *
     * @param organizationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改组织机构表", httpMethod = "POST")
    public AjaxResult updateOrganization(@Validated @RequestBody OrganizationBo organizationBo) {
        if (null == organizationBo.getId()) {
            return AjaxResult.fail("组织机构表id不能为空");
        }
        return organizationService.updateOrganization(organizationBo);
    }

    /**
     * 查询组织机构表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询组织机构表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return organizationService.getDetail(id);
    }

    /**
     * 更新状态
     *
     * @param organizationBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-04 15:14:34
     */
    @PostMapping("/updateState")
    @ApiOperation(value = "更新状态", httpMethod = "POST")
    public AjaxResult updateState(@Validated @RequestBody OrganizationBo organizationBo) {
        if (null == organizationBo.getId()) {
            return AjaxResult.fail("组织机构表id不能为空");
        }

        if (null == organizationBo.getState()) {
            return AjaxResult.fail("状态不能为空");
        }

        organizationBo.setUpdateBy(getCurrentAdmin().getOid());
        return organizationService.updateState(organizationBo);
    }

    /**
     * 删除组织机构表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-29 18:01:13
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除组织机构表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id) {
        OrganizationBo organizationBo = new OrganizationBo();
        organizationBo.setId(id);
        organizationBo.setUpdateBy(getCurrentAdmin().getOid());
        return organizationService.deleteOrganization(organizationBo);
    }

    /**
     * 获取组织信息（包含当前套餐信息）
     *
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/23 11:14
     **/
    @GetMapping("/organizationDetailWithPackage")
    @ApiOperation(value = "获取组织信息（包含当前套餐信息）", httpMethod = "GET")
    public AjaxResult getOrganizationWithPackage(@RequestParam(value = "id", required = false) Long organizationId) {
        if (organizationId == null) {
            AdminVo adminVo = getCurrentAdmin();
            organizationId = adminVo.getOrganizationId();
        }
        return organizationService.organizationDetailWithPackage(organizationId);
    }

    /**
     * 获取组织信息列表（包含当前套餐信息）
     *
     * @param conditionBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/28 17:32
     **/
    @PostMapping("/organizationListWithPackage")
    @ApiOperation(value = "获取组织信息列表（包含当前套餐信息）", httpMethod = "POST")
    public AjaxResult getOrganizationListWithPackage(@RequestBody OrganizationConditionBo conditionBo) {
        return AjaxResult.success(organizationService.organizationListWithPackage(conditionBo));
    }

    /**
     * 组织用量统计
     *
     * @param
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/29 9:08
     **/
    @GetMapping("/accumulateUsageStatistic")
    public AjaxResult getOrganizationAccumulateUsageStatistic() {
        return AjaxResult.success(organizationService.getOrganizationAccumulateUsageStatistic());
    }

    /**
     * 企业每天用量统计列表
     *
     * @param conditionBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2024/10/31 14:31
     **/
    @PostMapping("/usageListByStatisticDay")
    public AjaxResult getOrganizationUsageListByStatisticDay(@RequestBody OrganizationUsageStatisticConditionBo conditionBo) {
        return AjaxResult.success(organizationUsageStatisticService.getOrganizationUsageStatisticListByCondition(conditionBo));
    }
}