package com.fh.ai.manager.controller;

import com.fh.ai.business.entity.bo.prize.PrizeBo;
import com.fh.ai.business.entity.bo.prize.PrizeConditionBo;
import com.fh.ai.business.service.IPrizeService;
import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 奖品表
 *
 * <AUTHOR>
 * @date 2024-02-20 17:21:24
 */
@RestController
@RequestMapping("/prize")
@Validated
@Api(value = "", tags = "奖品表接口")
public class PrizeController extends BaseController {

    @Autowired
    private IPrizeService prizeService;

    /**
     * 查询奖品表列表
     *
     * <AUTHOR>
     * @date 2024-02-20 17:21:24
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询奖品表", httpMethod = "POST")
    public AjaxResult getPrizeListByCondition(@RequestBody PrizeConditionBo condition) {
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return AjaxResult.success(prizeService.getPrizeListByCondition(condition));
    }

    /**
     * 新增奖品表
     *
     * <AUTHOR>
     * @date 2024-02-20 17:21:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增奖品表", httpMethod = "POST")
    public AjaxResult addPrize(@Validated @RequestBody PrizeBo prizeBo) {
        return prizeService.addPrize(prizeBo);
    }

    /**
     * 修改奖品表
     *
     * @param prizeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-02-20 17:21:24
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改奖品表", httpMethod = "POST")
    public AjaxResult updatePrize(@Validated @RequestBody PrizeBo prizeBo) {
        if (null == prizeBo.getId()) {
            return AjaxResult.fail("奖品表id不能为空");
        }
        return prizeService.updatePrize(prizeBo);
    }

    /**
     * 查询奖品表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-02-20 17:21:24
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询奖品表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return prizeService.getDetail(id);
    }

    /**
     * 更新状态
     *
     * @param prizeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-04 15:14:34
     */
    @PostMapping("/updateState")
    @ApiOperation(value = "更新状态", httpMethod = "POST")
    public AjaxResult updateState(@Validated @RequestBody PrizeBo prizeBo) {
        if (null == prizeBo.getId()) {
            return AjaxResult.fail("奖品表id不能为空");
        }

        if (null == prizeBo.getState()) {
            return AjaxResult.fail("状态不能为空");
        }

        prizeBo.setUpdateBy(getCurrentAdmin().getOid());
        return prizeService.updateState(prizeBo);
    }

    /**
     * 删除奖品表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-02-20 17:21:24
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除奖品表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id) {
        PrizeBo prizeBo = new PrizeBo();
        prizeBo.setId(id);
        prizeBo.setUpdateBy(getCurrentAdmin().getOid());
        return prizeService.deletePrize(prizeBo);
    }

    /**
     * 获取兑换码
     *
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-02-20 17:21:24
     */
    @GetMapping("/code")
    @ApiOperation(value = "获取兑换码", httpMethod = "GET")
    public AjaxResult code() {
        return prizeService.code();
    }
}