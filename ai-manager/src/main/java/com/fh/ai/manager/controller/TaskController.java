package com.fh.ai.manager.controller;

import com.fh.ai.business.entity.bo.task.TaskConditionBo;
import com.fh.ai.business.entity.bo.task.TaskBo;
import com.fh.ai.business.service.ITaskService;

import com.fh.ai.common.enums.IsDeleteEnum;
import com.fh.ai.common.vo.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 任务表
 *
 * <AUTHOR>
 * @date 2024-06-04 13:49:38
 */
@RestController
@RequestMapping("/task")
@Validated
@Api(value = "", tags = "任务表接口" )
public class TaskController extends BaseController {

    @Autowired
    private ITaskService taskService;

    /**
     * 查询任务表列表
     * <AUTHOR>
     * @date 2024-06-04 13:49:38
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询任务表", httpMethod = "POST")
    public AjaxResult getTaskListByCondition(@RequestBody TaskConditionBo condition){
        condition.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
        return AjaxResult.success(taskService.getTaskListByCondition(condition));
    }

    /**
     * 新增任务表
     * <AUTHOR>
     * @date 2024-06-04 13:49:38
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增任务表", httpMethod = "POST")
    public AjaxResult addTask(@Validated @RequestBody TaskBo taskBo){
        return taskService.addTask(taskBo);
    }

    /**
     * 修改任务表
     * @param taskBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-06-04 13:49:38
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改任务表", httpMethod = "POST")
    public AjaxResult updateTask(@Validated @RequestBody TaskBo taskBo) {
        if(null == taskBo.getId()) {
            return AjaxResult.fail("任务表id不能为空");
        }
        return taskService.updateTask(taskBo);
    }

    /**
     * 查询任务表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-06-04 13:49:38
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询任务表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return taskService.getDetail(id,null);
    }

    /**
     * 更新状态
     *
     * @param taskBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-05-04 15:14:34
     */
    @PostMapping("/updateState")
    @ApiOperation(value = "更新状态", httpMethod = "POST")
    public AjaxResult updateState(@Validated @RequestBody TaskBo taskBo) {
        if(null == taskBo.getId()) {
            return AjaxResult.fail("任务表id不能为空");
        }

        if (null == taskBo.getState()) {
            return AjaxResult.fail("状态不能为空");
        }

        taskBo.setUpdateBy(getCurrentAdmin().getOid());
        return taskService.updateState(taskBo);
    }

    /**
     * 删除任务表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-06-04 13:49:38
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除任务表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id) {
        TaskBo taskBo = new TaskBo();
        taskBo.setId(id);
        taskBo.setUpdateBy(getCurrentAdmin().getOid());
        return taskService.deleteTask(taskBo);
    }

    /**
     * 更新任务列表排序
     *
     * @param taskBo
     * @return com.fh.ai.common.vo.AjaxResult
     * <AUTHOR>
     * @date 2025/5/12 14:58
     **/
    @PostMapping("/updateSort")
    @ApiOperation(value = "更新列表排序", httpMethod = "POST")
    public AjaxResult updateSort(@RequestBody TaskBo taskBo) {
        return taskService.updateSort(taskBo);
    }
}