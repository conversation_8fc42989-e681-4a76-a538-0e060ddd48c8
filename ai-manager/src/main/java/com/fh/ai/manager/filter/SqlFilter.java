package com.fh.ai.manager.filter;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.fh.ai.common.constants.WebFilterConstants;
import com.fh.ai.common.utils.WebFluxUtil;
import com.fh.ai.common.vo.AjaxResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * @Author: liuzeyu
 * @CreateTime: 2025-04-17 14:34
 */
@Component
public class SqlFilter implements Filter, Ordered {
    @Value("${sqlFilter.open:false}")
    private Boolean open;

    @Value("${sqlFilter.ignoreUrl:/login/**}")
    private String ignoreUrl;

    private static Pattern sqlPattern = Pattern.compile("(?i)(?:" +
    // 匹配形如： ' or 'xxx' = 'xxx'
        "(?:\\s*'\\s*or\\s*'[^']+'\\s*=\\s*'[^']+')" + "|" +
        // 匹配形如： = 'xxx'
        "(?:\\s*=\\s*'[^']+')" + "|" +
        // 匹配 union select
        "\\bunion\\s+select\\b" + "|" +
        // 匹配 drop table
        "\\bdrop\\s+table\\b" + "|" +
        // 匹配 truncate table
        "\\btruncate\\s+table\\b" + "|" +
        // 匹配 exec 命令
        "\\bexec\\s+\\w+\\b" + "|" +
        // 新增匹配 select sleep( 语句
        "\\bselect\\s+sleep\\s*\\(" + "|" +
        // 匹配 AND 1=1 -- 注入（数字）
        "\\band\\s+1\\s*=\\s*1\\s*--" + "|" +
        // 匹配 AND "1"="1" -- 注入（带引号）
        "\\band\\s+\"1\"\\s*=\\s*\"1\"\\s*--" + "|" +
        // 匹配 OR 1=1 -- 注入（数字）
        "\\bor\\s+1\\s*=\\s*1\\s*--" + "|" +
        // 匹配 OR "1"="1" -- 注入（带引号）
        "\\bor\\s+\"1\"\\s*=\\s*\"1\"\\s*--" +

        ")", Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL);
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        HttpServletRequest httpRequest = null;
        CachedBodyHttpServletRequest cachedRequest = null;
        if (request instanceof HttpServletRequest) {// 判断左边的对象是否是它右边对象的实例
            httpRequest = (HttpServletRequest)request;
            cachedRequest = new CachedBodyHttpServletRequest(httpRequest);
        }
        String requestURI = httpRequest.getRequestURI();

        // 是否开启校验
        if (!open) {
            chain.doFilter(cachedRequest, response);
            return;
        }

        // 检查是否在排除列表中
        if (matchesExcludes(requestURI)) {
            chain.doFilter(cachedRequest, response);
            return;
        }

        Map<String, String> parameterMap = WebFluxUtil.getParams(cachedRequest);
        if (CollUtil.isEmpty(parameterMap)) {
            chain.doFilter(cachedRequest, response);
            return;
        }
        String paramterJson = JSONObject.toJSONString(parameterMap);
        if (sqlPattern.matcher(paramterJson).find()) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            out.write(JSONObject.toJSONString(AjaxResult.fail(500, "您所访问的页面请求中有违反安全规则元素存在，拒绝访问!")));
            return;
        }

        chain.doFilter(cachedRequest, response);
    }

    private boolean matchesExcludes(String requestURI) {
        // 获取忽略校验的url
        List<String> xssIgnoreUrls = Lists.newArrayList();
        if (StringUtils.isNotBlank(ignoreUrl)) {
            xssIgnoreUrls = Arrays.asList(ignoreUrl.split(","));
        } else {
            xssIgnoreUrls = Arrays.asList(); // 默认为空列表
        }
        for (String xssIgnoreUrl : xssIgnoreUrls) {
            if (antPathMatcher.match(xssIgnoreUrl.trim(), requestURI)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int getOrder() {
        return WebFilterConstants.ORDER_BY_RANGE_FILTER_ORDER;
    }
}
