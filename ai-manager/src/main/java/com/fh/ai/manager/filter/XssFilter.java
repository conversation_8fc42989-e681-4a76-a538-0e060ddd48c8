package com.fh.ai.manager.filter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.fh.ai.common.constants.WebFilterConstants;
import com.fh.ai.common.utils.WebFluxUtil;
import com.fh.ai.common.utils.XSSUtil;
import com.fh.ai.common.vo.AjaxResult;

import cn.hutool.core.collection.CollUtil;

/**
 * 防止XSS 攻击和SQL注入攻击的Filter
 * @ClassName:  XssAndSqlFilter
 * @Description:
 * @author:
 * @date:   2021年3月22日 下午2:29:12
 *
 * @Copyright: 2021 www.digipower.cn
 * 注意：
 */
@Component
public class XssFilter implements Filter , Ordered {

    @Value("${xssFilter.open:false}")
    private Boolean open;

    @Value("${xssFilter.ignoreUrl:/login/**}")
    private String ignoreUrl;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = null;
        CachedBodyHttpServletRequest cachedRequest = null;
        if (request instanceof HttpServletRequest) {//判断左边的对象是否是它右边对象的实例
            httpRequest = (HttpServletRequest) request;
            cachedRequest = new CachedBodyHttpServletRequest(httpRequest);
        }
        String requestURI = httpRequest.getRequestURI();

        // 是否开启校验
        if (!open) {
            chain.doFilter(cachedRequest, response);
            return;
        }

        // 检查是否在排除列表中
        if (matchesExcludes(requestURI)) {
            chain.doFilter(cachedRequest, response);
            return;
        }

        Map<String, String> parameterMap = WebFluxUtil.getParams(cachedRequest);
        if (CollUtil.isEmpty(parameterMap)) {
            chain.doFilter(cachedRequest, response);
            return;
        }
        String parameterJson = JSONObject.toJSONString(parameterMap);
        if (XSSUtil.hasXSSContent(parameterJson)) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            out.write(JSONObject.toJSONString(AjaxResult.fail(500, "您所访问的页面请求中有违反安全规则元素存在，拒绝访问!")));
            return;
        }

        chain.doFilter(cachedRequest, response);

    }

    private boolean matchesExcludes(String requestURI) {
        // 获取忽略校验的url
        List<String> xssIgnoreUrls = Lists.newArrayList();
        if (StringUtils.isNotBlank(ignoreUrl)) {
            xssIgnoreUrls = Arrays.asList(ignoreUrl.split(","));
        } else {
            xssIgnoreUrls = Arrays.asList(); // 默认为空列表
        }
        for (String xssIgnoreUrl : xssIgnoreUrls) {
            if (antPathMatcher.match(xssIgnoreUrl.trim(), requestURI)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int getOrder() {
        return WebFilterConstants.BODY_XSS_FILTER_ORDER;
    }
}