package com.fh.ai.manager.task;

import com.fh.ai.business.service.IBookService;
import com.fh.ai.business.service.IFzBookService;
import com.fh.ai.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 图书库同步task
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-11-20  17:44
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "tasks.fzBookSync", name = "enabled", havingValue = "true")
public class FzBookSyncTask {

    @Resource
    private IBookService bookService;

    /**
     * 图书库同步
     *
     * @param
     * @return void
     * <AUTHOR>
     * @date 2024/11/20 17:45
     **/
    @Scheduled(cron = "0 0 3 * * ?")
    public void fzBookSync() {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date nowDay = new Date();
            Date yesterday = DateUtil.getYesterday(nowDay);
            String startTime = format.format(yesterday);
            String endTime = format.format(nowDay);
            bookService.fzBookSync(startTime, endTime);
        } catch (Exception e) {
            log.error("fzBookSync error, e:" + e);
        }
    }

}
