package com.fh.ai.manager.task;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.ai.business.entity.bo.admin.AdminConditionBo;
import com.fh.ai.business.service.IAdminService;
import com.fh.ai.common.enums.IsDeleteEnum;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.beust.jcommander.internal.Lists;
import com.fh.ai.business.entity.bo.organization.OrganizationConditionBo;
import com.fh.ai.business.entity.dto.organizationPackage.OrganizationPackageDto;
import com.fh.ai.business.entity.vo.admin.AdminVo;
import com.fh.ai.business.entity.vo.organization.OrganizationVo;
import com.fh.ai.business.service.IOrganizationPackageService;
import com.fh.ai.business.service.IOrganizationService;
import com.fh.ai.common.enums.SmsRemindType;
import com.fh.ai.common.sms.douyin.DouyinSmsService;
import com.fh.ai.common.sms.enums.SmsSendTypeEnum;

import lombok.extern.slf4j.Slf4j;
/**
 * 企业余量提醒task
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-10-29  13:59
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "tasks.organization-quota-remind", name = "enabled", havingValue = "true")
public class OrganizationQuotaRemindTask {
    @Resource
    private IOrganizationService organizationService;
    @Resource
    private IOrganizationPackageService organizationPackageService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private DouyinSmsService douyinSmsService;
    @Resource
    private IAdminService adminService;
    /**
     * 企业管理员角色id
     */
    private static final Long ORG_ADMIN_ROLE_ID = 10L;

    @Scheduled(cron = "0 0/1 * * * ?")
    public void organizationQuotaRemind() {
        log.info("企业余量提醒定时器开始......");
        RLock lock = redissonClient.getLock("lock:organization:quotaRemind");
        try {
            lock.lock(5, TimeUnit.MINUTES);
            log.info("get lock");
            // 查询企业
            OrganizationConditionBo organizationConditionBo = new OrganizationConditionBo();
            Map<String, Object> organizationMap = organizationService.organizationListWithPackage(organizationConditionBo);
            List<OrganizationVo> organizationVos = Lists.newArrayList();
            if (organizationMap.containsKey("list") && organizationMap.get("list") != null) {
                organizationVos = JSONArray.parseArray(JSONArray.toJSONString(organizationMap.get("list")), OrganizationVo.class);
            }
            if (CollectionUtils.isEmpty(organizationVos)) {
                return;
            }
            // 过滤未短信提醒，且余量小于百分之10的企业
            organizationVos = organizationVos.stream().filter(o -> {
                return o.getOrganizationPackage() != null
                        && SmsRemindType.NOT_REMIND.getValue() == o.getOrganizationPackage().getSmsMarginRemind()
                        && o.getOrganizationPackage().getBalance().add(o.getOrganizationPackage().getConsumeAmount()).compareTo(new BigDecimal(0)) > 0
                        && o.getOrganizationPackage().getBalance()
                        .multiply(new BigDecimal(100))
                        .divide((o.getOrganizationPackage().getBalance()
                                .add(o.getOrganizationPackage().getConsumeAmount())), 2, RoundingMode.HALF_UP)
                        .compareTo(new BigDecimal("10")) <= 0;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(organizationVos)) {
                return;
            }
            // 短信提醒
            List<OrganizationPackageDto> organizationPackageDtos = Lists.newArrayList();
            for (OrganizationVo organizationVo : organizationVos) {
                // 查询企业管理员
                AdminConditionBo adminConditionBo = new AdminConditionBo();
                adminConditionBo.setOrganizationId(organizationVo.getId());
                adminConditionBo.setRoleId(ORG_ADMIN_ROLE_ID);
                adminConditionBo.setIsDelete(IsDeleteEnum.NOTDELETE.getCode());
                Map<String, Object> adminVoMap = adminService.getAdminListByCondition(adminConditionBo);
                if (CollectionUtil.isNotEmpty(adminVoMap) && adminVoMap.containsKey("list")) {
                    List<AdminVo> adminVos = JSONArray.parseArray(JSONArray.toJSONString(adminVoMap.get("list")), AdminVo.class);
                    organizationVo.setOrganizationAdmin(adminVos.get(0));
                }
                AdminVo adminVo = organizationVo.getOrganizationAdmin();
                String phone = "";
                if (adminVo!= null) {
                    phone = adminVo.getPhone();
                }
                if (StringUtils.isBlank(phone)) {
                    log.error("企业：{} 未找到管理员手机号", organizationVo.getName());
                    continue;
                }

                // 发送短信
                final boolean sendSuccess = this.douyinSmsService.sendNoticeMessage(phone, SmsSendTypeEnum.ORG_MARGIN_REMIND, null);
                if (sendSuccess) {
                    // 设置已提醒
                    OrganizationPackageDto organizationPackageDto = new OrganizationPackageDto();
                    organizationPackageDto.setId(organizationVo.getOrganizationPackage().getId());
                    organizationPackageDto.setSmsMarginRemind(SmsRemindType.IS_REMIND.getValue());
                    organizationPackageDtos.add(organizationPackageDto);
                }
            }
            if (CollectionUtils.isNotEmpty(organizationPackageDtos)) {
                organizationPackageService.updateBatchById(organizationPackageDtos);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            lock.unlock();
            log.info("release lock");
        }
    }

}
