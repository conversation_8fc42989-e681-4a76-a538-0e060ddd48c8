package com.fh.ai.manager.task;

import java.util.Calendar;
import java.util.Date;

import javax.annotation.Resource;

import com.beust.jcommander.internal.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.fh.ai.business.service.IOrganizationUsageStatisticService;

/**
 * 企业账单统计task（每天凌晨3点执行，统计前一天使用量）
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-10-29  10:19
 */
@Component
@ConditionalOnProperty(prefix = "tasks.organization-usage-statistic", name = "enabled", havingValue = "true")
@Slf4j
public class OrganizationUsageStatisticTask {
    @Resource
    private IOrganizationUsageStatisticService organizationUsageStatisticService;

    @Scheduled(cron = "0 0 3 * * ?")
    public void organizationUsageStatistic() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        Date statisticDay = calendar.getTime();
        organizationUsageStatisticService.organizationUsageStatisticTask(statisticDay, null);
    }
}
