package com.fh.ai.manager.task;

import com.fh.ai.business.service.IPrizeService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@ConditionalOnProperty(prefix = "tasks.prize-supply-remind", name = "enabled", havingValue = "true")
public class PrizeSupplyRemindTask {

    @Resource
    private IPrizeService prizeService;

    /**
     * 商品库存不足提醒
     */
    @Scheduled(cron = "0 */10 * * * *")
    public void prizeSupplyRemind() {
        prizeService.prizeSupplyRemind();
    }
}
