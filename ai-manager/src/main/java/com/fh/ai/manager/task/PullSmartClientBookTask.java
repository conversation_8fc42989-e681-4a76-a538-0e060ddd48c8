package com.fh.ai.manager.task;

import com.fh.ai.business.service.IBookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Classname PullSmartClientBookTask
 * @Description 拉取开卷数据定时任务
 * @Date 2024/12/9 9:06
 * @Created by admin
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "tasks.pullSmartBook", name = "enabled", havingValue = "true")
public class PullSmartClientBookTask {

    @Resource
    private IBookService iBookService;

    @Scheduled(cron = "0 11 2 15 * ?")
    public void pullSmartBook(){
        try {
            iBookService.getBookRpcFromSmartClient();
        }catch (Exception e){
            log.error("拉取开卷数据定时任务出错，错误信息：{}，异常堆栈",e.getMessage(),e);
            throw e;
        }
    }

}
