package com.fh.ai.business.entity.bo.activity;

import com.fh.ai.business.entity.bo.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 活动表
 * 
 * <AUTHOR>
 * @date 2024-05-13 14:48:18
 */
@Data
public class ActivityConditionBo extends PageLimitBo{

	/**
	 * 活动id
	 */
	@ApiModelProperty("活动id")
	private Long id;

	/**
	 * 位置，1历史记录，2积分中心-文字，3积分中心-赚积分
	 */
	@ApiModelProperty("位置，1历史记录，2积分中心-文字，3积分中心-赚积分")
	private Integer position;

	/**
	 * 类型 1图片 2文本
	 */
	@ApiModelProperty("类型 1图片 2文本")
	private Integer type;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String name;

	/**
	 * 内容
	 */
	@ApiModelProperty("内容")
	private String content;

	/**
	 * 详情
	 */
	@ApiModelProperty("详情")
	private String details;

	/**
	 * 状态 1上架 2下架
	 */
	@ApiModelProperty("状态 1上架 2下架")
	private Integer state;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	private String jumpUrl;
	private Integer frequency;
	private String readDate;
	private Date publishTime;

	private String userOid;

	/**
	 * 展示类型 1-web 2-h5 3-web和h5
	 */
	@ApiModelProperty("展示类型 1-web 2-h5 3-web和h5")
	private Integer showType;

	/**
	 * h5内容
	 */
	@ApiModelProperty("h5内容")
	private String h5Content;

	/**
	 * h5跳转地址
	 */
	@ApiModelProperty("h5跳转地址")
	private String h5JumpUrl;

	@ApiModelProperty("广告详情页开关 1-开启 2-关闭")
	private Integer detailSwitch;

	@ApiModelProperty("广告详情页内容")
	private String detailContent;

	@ApiModelProperty("跳转类型 1-无跳转 2-链接 3-意见反馈")
	private Integer jumpType;
}