package com.fh.ai.business.entity.bo.book;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 书籍信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Data
public class BookBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@ApiModelProperty("ID")
	private Long id;

	/**
	 * 书籍数据来源来行：1其他，2当当，3微信，4豆瓣，5书苑，6开卷，7图书库，8抖音，9京东
	 */
	@ApiModelProperty("书籍数据来源来行：1其他，2当当，3微信，4豆瓣，5书苑，6开卷，7图书库，8抖音，9京东")
	private Integer sourceType;

	/**
	 * 书籍排序
	 */
	@ApiModelProperty("书籍排序")
	private Long sortIndex;

	/**
	 * 书籍名称
	 */
	@ApiModelProperty("书籍名称")
	private String bookName;

	/**
	 * 书籍介绍
	 */
	@ApiModelProperty("书籍介绍")
	private String bookDescription;

	/**
	 * 书籍作者
	 */
	@ApiModelProperty("书籍作者")
	private String bookAuthor;

	/**
	 * 书籍分类
	 */
	@ApiModelProperty("书籍分类")
	private String bookCategory;

	/**
	 * 书籍分类，按照层级
	 */
	@ApiModelProperty("书籍分类，按照层级")
	private String bookCategoryAll;

	/**
	 * 书籍标签，多个用英文逗号
	 */
	@ApiModelProperty("书籍标签，多个用英文逗号")
	private String bookLabel;

	/**
	 * 出版时间，格式为yyyy年MM月
	 */
	@ApiModelProperty("出版时间，格式为yyyy年MM月")
	private String publishTimeStr;

	/**
	 * 出版社名称
	 */
	@ApiModelProperty("出版社名称")
	private String publishName;

	/**
	 * 书籍isbn
	 */
	@ApiModelProperty("书籍isbn")
	private String bookIsbn;

	/**
	 * 书籍推荐语
	 */
	@ApiModelProperty("书籍推荐语")
	private String bookRecommendation;

	/**
	 * 书籍封面图地址
	 */
	@ApiModelProperty("书籍封面图地址")
	private String bookCover;

	/**
	 * 书籍在第三方系统的id，与source_type结合使用
	 */
	@ApiModelProperty("书籍在第三方系统的id，与source_type结合使用")
	private String bookThirdId;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;


	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private String createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private String updateTime;


	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	/**
	 * 书籍推荐值格式为：87.0，前端要拼上%
	 */
	private String bookRecommendRating;

	/**
	 * 书籍价格
	 */
	private String bookPrice;
	/**
	 * 店铺
	 */
	private String bookShop;
	/**
	 * 销售数量
	 */
	private String salesCount;

	/**
	 * 录入方式：1程序爬取，2后台手动录入
	 */
	private Integer inputType;

	/**
	 * 封面类型：1url地址，2本地上传文件的url地址
	 */
	private Integer coverType;

	/**
	 * 出版集团
	 */
	@ApiModelProperty("出版集团")
	private String publishGroup;

	@ApiModelProperty("出版日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date publishTime;

	@ApiModelProperty("描述处理备注（用字符串备注：例如y）")
	private String bookDescriptionHandleRmark;

	@ApiModelProperty("关键字标签-从介绍总结")
	private String keywords;

	@ApiModelProperty("删除原因")
	private String deleteReason;
}
