package com.fh.ai.business.entity.bo.calendarEvent;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 日历事件表
 * 
 * <AUTHOR>
 * @date 2024-07-02 14:21:19
 */
@Data
public class CalendarEventBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 日历事件表id
	 */
	@ApiModelProperty("日历事件表id")
	private Long calendarEventId;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 日期
	 */
	@ApiModelProperty("日期")
	private String day;

	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private Long year;

	/**
	 * 类型：1公历、2农历、3节气、4国际节日
	 */
	@ApiModelProperty("类型：1公历、2农历、3节气、4国际节日")
	private Integer type;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String name;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	/**
	 * 是否推荐展示 1-推荐 2-不推荐
	 */
	@ApiModelProperty("是否推荐展示 1-推荐 2-不推荐")
	private Integer recommendType;

	/**
	 * 介绍
	 */
	@ApiModelProperty("介绍")
	private String introduce;

	/**
	 * 大模型智能分析后的结果
	 */
	@ApiModelProperty("大模型智能分析后的结果")
	private String modelAnalysis;
}