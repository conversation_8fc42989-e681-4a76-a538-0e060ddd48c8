package com.fh.ai.business.entity.bo.calendarEvent;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 营销节点
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-09 11:42:41
 */
@Data
public class MarketingNodeBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 年月日。yyyy-MM-dd
	 */
	@ApiModelProperty("年月日。yyyy-MM-dd")
	private String day;

	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private Long year;

	/**
	 * 月份
	 */
	@ApiModelProperty("月份")
	private Long month;

	/**
	 * 营销关键节点
	 */
	@ApiModelProperty("营销关键节点")
	private String marketingKeyPoints;

	/**
	 * 营销关键词
	 */
	@ApiModelProperty("营销关键词")
	private String marketingKeyWords;

	/**
	 * 活动关键节点
	 */
	@ApiModelProperty("活动关键节点")
	private String activityKeyPoints;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;


	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;



	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

}
