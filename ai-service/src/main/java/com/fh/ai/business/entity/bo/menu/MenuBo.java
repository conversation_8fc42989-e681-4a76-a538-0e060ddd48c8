package com.fh.ai.business.entity.bo.menu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email
 * @date 2023-05-04 10:58:10
 */
@Data
public class MenuBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long menuId;

    /**
     * 菜单id
     */
    @ApiModelProperty("菜单id")
    private Long id;
    /**
     * 菜单父id (一级菜单为0)
     */
    @ApiModelProperty("菜单父id (一级菜单为0)")
    private Long parentId;
    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String menuName;
    /**
     *
     */
    @ApiModelProperty("")
    private String permission;
    /**
     * 菜单状态(2:不公开 1:公开)
     */
    @ApiModelProperty("菜单状态(2:不公开 1:公开)")
    private Long state;
    /**
     * 菜单地址
     */
    @ApiModelProperty("菜单地址")
    private String url;
    /**
     * 0：目录   1：菜单   2：按钮
     */
    @ApiModelProperty("0：目录   1：菜单   2：按钮")
    private Integer type;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Long sort;
    /**
     * 图标
     */
    @ApiModelProperty("图标")
    private String icon;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private Long updateUser;

}
