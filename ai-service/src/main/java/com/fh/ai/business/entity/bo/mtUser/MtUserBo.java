package com.fh.ai.business.entity.bo.mtUser;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 美图用户使用表
 * 
 * <AUTHOR>
 * @date 2024-08-16 09:45:28
 */
@Data
public class MtUserBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 美图用户使用表id
	 */
	@ApiModelProperty("美图用户使用表id")
	private Long mtUserId;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 用户唯一oid
	 */
	@ApiModelProperty("用户唯一oid")
	private String userOid;
	private String fileOid;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 类型：1文生图，2图生图
	 */
	@ApiModelProperty("类型：1文生图，2图生图")
	private Integer type;

	/**
	 * 结果
	 */
	@ApiModelProperty("结果")
	private String result;

	/**
	 * 是否收藏：1收藏，2不收藏
	 */
	@ApiModelProperty("是否收藏：1收藏，2不收藏")
	private Integer isFavorite;

	/**
	 * 渠道：1web端，2H5端
	 */
	@ApiModelProperty("渠道：1web端，2H5端")
	private Integer channel;

	/**
	 * 记录id
	 */
	@ApiModelProperty("记录id")
	private Long historyId;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	@ApiModelProperty("接口响应")
	private String responseData;

	/**
	 * 图片业务类型：1美图，2liblib
	 */
	@ApiModelProperty("图片业务类型：1美图，2liblib")
	private Integer sourceType;
}