package com.fh.ai.business.entity.bo.mtWord;

import java.util.Date;
import java.util.List;

import com.fh.ai.business.entity.bo.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 美图词库表
 * 
 * <AUTHOR>
 * @date 2024-08-19 14:52:50
 */
@Data
public class MtWordConditionBo extends PageLimitBo{

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 父id (一级为0)
	 */
	@ApiModelProperty("父id (一级为0)")
	private Long parentId;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String name;
	private String searchName;

	/**
	 * 英文名称
	 */
	@ApiModelProperty("英文名称")
	private String nameEnglish;

	/**
	 * 类型：1类别，2类别值
	 */
	@ApiModelProperty("类型：1类别，2类别值")
	private Integer type;

	/**
	 * 多类型查询
	 */
	private List<Integer> types;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	/**
	 * 图片业务类型：1美图，2liblib
	 */
	@ApiModelProperty("图片业务类型：1美图，2liblib")
	private Integer sourceType;
}