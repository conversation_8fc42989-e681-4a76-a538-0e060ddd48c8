package com.fh.ai.business.entity.bo.personalFile;

import com.fh.ai.business.entity.bo.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户上传的个人文件表查询条件
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-14 10:00:00
 */
@Data
public class PersonalFileConditionBo extends PageLimitBo {

	private static final long serialVersionUID = 1L;

	/**
	 * 个人文件id
	 */
	@ApiModelProperty("个人文件id")
	private Long id;

	/**
	 * 文件名称-展示用
	 */
	@ApiModelProperty("文件名称-展示用")
	private String fileName;

	/**
	 * 文件oid，attachment表的oid
	 */
	@ApiModelProperty("文件oid，attachment表的oid")
	private String fileOid;

	/**
	 * 文件查看地址
	 */
	@ApiModelProperty("文件查看地址")
	private String fileUrl;

	/**
	 * 业务类型：1用户上传图书海报的参考图片
	 */
	@ApiModelProperty("业务类型：1用户上传图书海报的参考图片")
	private Integer bizType;

	/**
	 * 顺序，默认0
	 */
	@ApiModelProperty("顺序，默认0")
	private Long sort;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	/**
	 * 用户唯一oid
	 */
	@ApiModelProperty("用户唯一oid")
	private String userOid;

}
