package com.fh.ai.business.entity.bo.preInstruction;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 预置指令
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-07 16:45:32
 */
@Data
public class PreInstructionBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 用户指令唯一uuid
	 */
	@ApiModelProperty("用户指令唯一uuid")
	private String uuid;

	/**
	 * 用户唯一oid，系统预置指令该字段为空
	 */
	@ApiModelProperty("用户唯一oid，系统预置指令该字段为空")
	private String userOid;

	/**
	 * 指令标题
	 */
	@ApiModelProperty("指令标题")
	private String title;

	/**
	 * 指令内容
	 */
	@ApiModelProperty("指令内容")
	private String content;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 更新时间
	 */
	private Date updateTime;



	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

}
