package com.fh.ai.business.entity.bo.publishNews;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.ai.business.entity.bo.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Classname QueryPublishListBo
 * @Description 出版信息列表查询请求体
 * @Date 2024/12/19 10:49
 * @Created by admin
 */
@Data
public class QueryPublishListBo extends PageLimitBo {

    @ApiModelProperty("主键")
    private Long id;

    /**
     * 资讯来源 1微信公众号 2榜眼数据
     */
    @ApiModelProperty("资讯来源 1微信公众号，2榜眼数据")
    private Integer sourceType;
    /**
     * 资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事
     */
    @ApiModelProperty("资讯文章类型 1出版 2文化 3教育 4科技 5人物 6时事")
    private Integer newsType;

    /**
     * 链接来源于哪个栏目，比如公众号的某个具体的公众号；或者榜眼的某个渠道（滂湃新闻等）
     */
    @ApiModelProperty("链接来源于哪个栏目，比如公众号的某个具体的公众号；或者榜眼的某个渠道（滂湃新闻等）")
    private String channel;

    /**
     * 资讯文章分类 出版 文化 教育 科技 人物 时事
     */
    @ApiModelProperty("资讯文章分类 出版 文化 教育 科技 人物 时事")
    private String newsCategory;

    /**
     * 录入方式 1程序爬取，2后台手动录入
     */
    @ApiModelProperty("录入方式 1程序爬取，2后台手动录入")
    private Integer inputYpe;

    /**
     * 文章作者（网名/公众号）
     */
    @ApiModelProperty("文章作者（网名/公众号）")
    private String author;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 文章摘要
     */
    @ApiModelProperty("文章摘要")
    private String summary;


    /**
     * 资讯链接
     */
    @ApiModelProperty("资讯链接")
    private String url;

    /**
     * 文章原发布时间-开始
     */
    @ApiModelProperty("文章原发布时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTimeStart;

    /**
     * 文章原发布时间-结束
     */
    @ApiModelProperty("文章原发布时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTimeEnd;


    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date searchTimeBegin;

    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date searchTimeEnd;
}
