package com.fh.ai.business.entity.bo.scene;

import com.fh.ai.business.entity.bo.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场景表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-11-20 15:01:12
 */
@Data
public class SceneConditionBo extends PageLimitBo {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 场景名称
	 */
	@ApiModelProperty("场景名称")
	private String name;

	/**
	 * 应用类型
	 */
	@ApiModelProperty("应用类型")
	private Integer type;





	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

}
