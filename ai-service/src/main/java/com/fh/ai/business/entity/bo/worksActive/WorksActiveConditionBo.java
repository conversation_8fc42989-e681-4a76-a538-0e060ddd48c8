package com.fh.ai.business.entity.bo.worksActive;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fh.ai.business.entity.bo.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 作品活动表
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-21 18:12:16
 */
@Data
public class WorksActiveConditionBo extends PageLimitBo {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 用户唯一oid，提交人id
	 */
	@ApiModelProperty("用户唯一oid，提交人id")
	private String userOid;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 活动状态：1开启，2禁用
	 */
	@ApiModelProperty("活动状态：1开启，2禁用")
	private Integer activeStatus;

	/**
	 * 活动名称
	 */
	@ApiModelProperty("活动名称")
	private String activeName;

	/**
	 * 活动简介
	 */
	@ApiModelProperty("活动简介")
	private String activeProfile;

	/**
	 * 活动开始时间
	 */
	@ApiModelProperty("活动开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date activeStartTime;

	/**
	 * 活动结束时间
	 */
	@ApiModelProperty("活动结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date activeEndTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

	/**
	 * 是否支持投票 1-支持 2-不支持
	 */
	@ApiModelProperty("是否支持投票 1-支持 2-不支持")
	private Integer isSupportVote;

	/**
	 * 投票开始时间
	 */
	@ApiModelProperty("投票开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date voteStartTime;

	/**
	 * 投票结束时间
	 */
	@ApiModelProperty("投票结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date voteEndTime;

	/**
	 * 每天投票次数限制
	 */
	@ApiModelProperty("每天投票次数限制")
	private Integer dailyVoteLimit;

}
