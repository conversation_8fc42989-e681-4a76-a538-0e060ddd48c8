package com.fh.ai.business.entity.bo.zegoUser;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 美图用户使用表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-09-06 15:18:02
 */
@Data
public class ZegoUserBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 美图用户使用表id
	 */
	@ApiModelProperty("美图用户使用表id")
	private Long zegoUserId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * 用户唯一oid
	 */
	@ApiModelProperty("用户唯一oid")
	private String userOid;
	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	private String name;

	private String cover;
	/**
	 * 类型：1文生图，2图生图
	 */
	@ApiModelProperty("类型：1文生图，2图生图")
	private Integer type;
	/**
	 * 结果
	 */
	@ApiModelProperty("结果")
	private String result;
	/**
	 * 是否收藏：1收藏，2不收藏
	 */
	@ApiModelProperty("是否收藏：1收藏，2不收藏")
	private Integer isFavorite;
	/**
	 * 渠道：1web端，2H5端
	 */
	@ApiModelProperty("渠道：1web端，2H5端")
	private Integer channel;
	/**
	 * 用户参数
	 */
	@ApiModelProperty("用户参数")
	private String params;
	/**
	 * 记录id
	 */
	@ApiModelProperty("记录id")
	private Long historyId;
	/**
	 * 状态：1处理中，2处理成功，3处理失败
	 */
	@ApiModelProperty("状态：1处理中，2处理成功，3处理失败")
	private Integer state = 1;
	/**
	 * 任务id
	 */
	@ApiModelProperty("任务id")
	private String taskId;
	/**
	 * 应用类型，多个逗号隔开
	 */
	@ApiModelProperty("应用类型，多个逗号隔开")
	private String appType;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 是否删除，1：正常，2：删除
	 */
	@ApiModelProperty("是否删除，1：正常，2：删除")
	private Integer isDelete;

}
