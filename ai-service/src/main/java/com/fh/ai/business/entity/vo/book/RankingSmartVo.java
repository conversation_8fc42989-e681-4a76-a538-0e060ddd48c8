package com.fh.ai.business.entity.vo.book;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 凤凰本版畅销书表-有销售数据（开卷）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-03 13:50:36
 */
@Data
public class RankingSmartVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 榜单类型：1月，2年
     */
    @ApiModelProperty("榜单类型：1月，2年")
    private Integer rankingType;

    /**
     * 榜单排序
     */
    @ApiModelProperty("榜单排序")
    private Long sortIndex;

    /**
     * 书籍id,p_book的id
     */
    @ApiModelProperty("书籍id,p_book的id")
    private Long bookId;

    /**
     * 榜单相比较于昨日上升xx位
     */
    @ApiModelProperty("榜单相比较于昨日上升xx位")
    private Long rankingUp;

    /**
     * 榜单相比较于昨日下降xx位
     */
    @ApiModelProperty("榜单相比较于昨日下降xx位")
    private Long rankingDown;

    /**
     * 榜单年分份
     */
    @ApiModelProperty("榜单年分份")
    private Long rankingYear;

    /**
     * 榜单季度，1-4
     */
    @ApiModelProperty("榜单季度，1-4")
    private Long rankingQuarter;

    /**
     * 榜单月份，1-12
     */
    @ApiModelProperty("榜单月份，1-12")
    private Long rankingMonth;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，1：正常，2：删除
     */
    @ApiModelProperty("是否删除，1：正常，2：删除")
    private Integer isDelete;

    /**
     * 一次任务的uuid
     */
    @ApiModelProperty("一次任务的uuid")
    private String uuid;

    /**
     * 榜单渠道：1 零售 2 实体店 4 网店
     */
    @ApiModelProperty("榜单渠道：1 零售 2 实体店 4 网店")
    private Integer rankingChannel;


    /**
     * 书籍名称
     */
    @ApiModelProperty("书籍名称")
    private String bookName;

    /**
     * 书籍介绍
     */
    @ApiModelProperty("书籍介绍")
    private String bookDescription;

    /**
     * 书籍作者
     */
    @ApiModelProperty("书籍作者")
    private String bookAuthor;

    /**
     * 书籍分类
     */
    @ApiModelProperty("书籍分类")
    private String bookCategory;

    /**
     * 书籍分类，按照层级
     */
    @ApiModelProperty("书籍分类，按照层级")
    private String bookCategoryAll;

    /**
     * 书籍标签，多个用英文逗号
     */
    @ApiModelProperty("书籍标签，多个用英文逗号")
    private String bookLabel;

    /**
     * 出版时间，格式为yyyy年MM月
     */
    @ApiModelProperty("出版时间，格式为yyyy年MM月")
    private String publishTimeStr;

    /**
     * 出版社名称
     */
    @ApiModelProperty("出版社名称")
    private String publishName;

    /**
     * 书籍isbn
     */
    @ApiModelProperty("书籍isbn")
    private String bookIsbn;

    /**
     * 书籍推荐语
     */
    @ApiModelProperty("书籍推荐语")
    private String bookRecommendation;

    /**
     * 书籍封面图地址
     */
    @ApiModelProperty("书籍封面图地址")
    private String bookCover;

    /**
     * 书籍在第三方系统的id，与source_type结合使用
     */
    @ApiModelProperty("书籍在第三方系统的id，与source_type结合使用")
    private String bookThirdId;

    /**
     * 书籍推荐值格式为：87.0，前端要拼上%
     */
    private String bookRecommendRating;

    /**
     * 排名变化情况，正数：上升名次；负数：下降名次
     */
    private Integer rankingChange;

    /**
     * 月销量
     */
    @ApiModelProperty("月销量")
    private Integer salesCount;

    /**
     * 收集数据日期
     */
    @ApiModelProperty("收集数据日期")
    private Date collectTime;

    /**
     * 图书价格
     */
    @ApiModelProperty("图书价格")
    private String bookPrice;

    /*
     * 方便steam流存入自身
     * */
    public RankingSmartVo returnOwn() {
        return this;
    }

}
