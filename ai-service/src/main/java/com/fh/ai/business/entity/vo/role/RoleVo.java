package com.fh.ai.business.entity.vo.role;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2023-05-04 15:14:34
 */
@Data
public class RoleVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 角色id
     */
    private Integer id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否锁定：0：否，1：是
     */
    private Integer isLocked;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Integer createUser;


    /**
     * 更新人
     */
    private Integer updateUser;


    /**
     * 是否删除（1：正常 2：删除）
     */
    private Integer isDelete;

    /**
     * 所属平台：1局管，2学校
     */
    private Integer platform;

    /**
     * 菜单ids
     */
    List<Long> menuIds;
}
