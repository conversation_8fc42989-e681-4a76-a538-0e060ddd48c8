package com.fh.ai.business.util;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * @Classname BookCommonUtil
 * @Description 书籍服务通用方法
 * @Date 2024/12/12 16:40
 * @Created by admin
 */
public class BookCommonUtil {

    /**
     * 计算排名变化情况
     *
     * @param yesterDayMap
     * @param todayList
     */
    public static <T> List<T> calculateRankChange(Map<Long, T> yesterDayMap, List<T> todayList) {
        // 如何前一天数据为空或者当天数据为空，则不进行排名处理。
        if (MapUtils.isEmpty(yesterDayMap) || CollectionUtils.isEmpty(todayList)) {
            return todayList;
        }
        try {
            Class<?> tClass = todayList.get(0).getClass();
            Method getBookId = tClass.getMethod("getBookId");
            Method setRankingChange = tClass.getMethod("setRankingChange", Integer.class);
            Method getSortIndex = tClass.getMethod("getSortIndex");
            for (T todayVo : todayList) {
                Long bookId = (Long) getBookId.invoke(todayVo);
                if (yesterDayMap.containsKey(bookId)) {
                    T yesterdayVo = yesterDayMap.get(bookId);
                    Long yesterdaySortIndex = (Long) getSortIndex.invoke(yesterdayVo);
                    Long todaySortIndex = (Long) getSortIndex.invoke(todayVo);
                    long rankingChange = (yesterdaySortIndex == null || todaySortIndex == null) ? null : (yesterdaySortIndex - todaySortIndex);
                    setRankingChange.invoke(todayVo,(int)rankingChange);
//                    setRankingChange.invoke(todayVo, getSortIndex.invoke(yesterdayVo) == null ? null : (Long)(getSortIndex.invoke(yesterdayVo)) - (Long)(getSortIndex.invoke(todayVo)));
                }

            }
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return todayList;
    }


}
