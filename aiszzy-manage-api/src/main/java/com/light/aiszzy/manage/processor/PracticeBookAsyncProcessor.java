package com.light.aiszzy.manage.processor;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.aiszzy.hwlOpen.service.HwlOpenApiService;
import com.light.aiszzy.manage.config.AiszzyThreadExecutor;
import com.light.aiszzy.practiceBook.entity.bean.PracticeBookPositionQuestion;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookPageBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.service.PracticeBookCatalogApiService;
import com.light.aiszzy.practiceBook.service.PracticeBookPageApiService;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.beans.HwlOpenAutomaticBoxVO;
import com.light.beans.ImageRequestBo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import com.light.core.exception.util.ThrowableUtil;
import com.light.redis.component.RedisComponent;
import com.light.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2025/07/10
 */
@Slf4j
@Service
public class PracticeBookAsyncProcessor {


    @Resource
    private AttachmentApiService attachmentApiService;

    @Resource
    private HwlOpenApiService hwlOpenApiService;

    @Resource
    private PracticeBookPageApiService practiceBookPageApiService;

    @Resource
    private PracticeBookCatalogApiService practiceBookCatalogApiService;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private ProgressService progressService;


    @Async(AiszzyThreadExecutor.EXECUTOR_PRACTICE_BOOK_ANALYSIS)
    public void analysisByPracticeBookOid(String url, String practiceBookOid) {


        String originalFilename = FileUtil.getName(url);
        String extName = FileUtil.extName(originalFilename);
        // 文件转换为 map
        Map<String, String> imgMap;
        try(InputStream inputStream = new URL(url).openStream()) {
            imgMap = this.parseInput2ImgMap(inputStream, extName);
        } catch (IOException e) {
            log.error("【教辅解析】 教辅 OID：{}, url地址: {} ", practiceBookOid, url);
            this.progressService.configStatus(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS, ProgressService.Status.FAILURE.getVal(), "文件解析失败");
            return;
        }
        if(CollUtil.isEmpty(imgMap)) {
            log.error("【教辅解析】 文件解析失败，地址：{}, 教辅 OID:{}, 空数据", url, practiceBookOid);
            this.progressService.configStatus(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS, ProgressService.Status.FAILURE.getVal(), "文件解析失败，未获得数据，请检查文件内容");
        }

        try {
            // 删除所有页码数据
            this.practiceBookPageApiService.deleteByPraticeBookOid(practiceBookOid);
            this.progressService.configStatus(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS,  ProgressService.Status.IN_PROGRESS.getVal(), "进行中");
            // 设置总页数
            this.progressService.configTotalTaskNum(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS, imgMap.size());

            //每 5 页进行一个线程处理
            int diff = 20;
            List<List<String>> keyList = Lists.partition(new ArrayList<>(imgMap.keySet()), diff);
            int size = keyList.size();
            CountDownLatch countDownLatch = new CountDownLatch(size);
            PracticeBookAsyncProcessor practiceBookAsyncProcessor = SpringUtil.getBean(PracticeBookAsyncProcessor.class);
            keyList.forEach(x-> {
                Map<String, String> splitMap = Maps.newHashMap();
                x.forEach(y-> splitMap.put(y,imgMap.get(y)));
                practiceBookAsyncProcessor.practiceBookAnalysisImgMap(practiceBookOid, splitMap, pageList-> {
                    countDownLatch.countDown();
                    this.progressService.addFinishTaskNum(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS, splitMap.size());
                });
            });

            // 等待所有线程执行结束
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            log.error("【教辅解析】 解析异常 , 教辅 OID:{}, 异常内容:{}",  practiceBookOid, ThrowableUtil.getStackTrace(e));
            throw new RuntimeException(e);
        }finally {
            log.info("【教辅解析】 解析完成 ，教辅 OID: {}", practiceBookOid);
            this.progressService.configStatus(practiceBookOid, ProgressService.Business.PRACTICE_BOOK_ANALYSIS, ProgressService.Status.SUCCESS.getVal(), "成功");
        }
    }


    /**
     *  解析文件为 map
     * @param inputStream the inputstream 文件流
     * @param extName the extname 文件后缀名
     * @return {@link Map }<{@link Integer }, {@link String }>
     */
    public Map<String, String> parseInput2ImgMap(InputStream inputStream, String extName) {
        if(extName.equalsIgnoreCase("pdf")) {
            return PdfUtil.partitionPdf2ImgBase64Map(inputStream);
        }
        if((extName.equalsIgnoreCase("zip"))) {
            return ZipUtils.unImgZip2ImgMap(inputStream);
        }
        throw new WarningException("不支持文件类型【"+ extName +"】解析");
    }


    /**
     *  解析教辅图片信息 （调用好未来进行数据解析)
     * @param practiceBookOid the practice book oid 教辅 OID
     * @param pageImgMap the page img map 图片 bas64数据 , key 为 页码
     */
    @Async(AiszzyThreadExecutor.EXECUTOR_PRACTICE_BOOK_ANALYSIS)
    public void practiceBookAnalysisImgMap(String practiceBookOid, Map<String, String> pageImgMap, Consumer<List<PracticeBookPageBo>> consumer) {
        log.info("【教辅异步解析】 线程名称：{}, 教辅 OID: {}", Thread.currentThread().getName(), practiceBookOid);
        Map<Long, PracticeBookCatalogVo> pageNoCatalogMap = queryCatalogMapByPageNoList(practiceBookOid, pageImgMap.keySet());
        List<PracticeBookPageBo> list = null;
        try {

            list = pageImgMap.entrySet().stream().map((v)-> {
                String name = v.getKey();
                PracticeBookPageBo practiceBookPageBo = new PracticeBookPageBo();
                practiceBookPageBo.setPracticeBookOid(practiceBookOid);
                Long pageNo = getPageNo(name);
                practiceBookPageBo.setPageNo(pageNo);

                // 文件保存
                MultipartFile multipartFile = new MockMultipartFile("file", name, MediaType.MULTIPART_FORM_DATA_VALUE, Base64.getDecoder().decode(v.getValue()));
                AjaxResult<AttachmentVo> attachmentResult = this.attachmentApiService.uploadFile(multipartFile);
                AttachmentVo data = attachmentResult.getData();
                practiceBookPageBo.setImageUrl(data.getNginxUrl());

                // 获取图片宽度
                ImgUtil.MetaInfo imgMeta = Optional.ofNullable(ImgUtil.getMetaInfo(Base64.getDecoder().decode(v.getValue()))).orElseThrow(() -> new WarningException("图片信息获取失败"));
                int width = imgMeta.getWidth();
                int height = imgMeta.getHeight();

                // 好未来内容解析
                HwlOpenAutomaticBoxVO hwlOpenAutomaticBoxVO = this.automaticBox(data.getNginxUrl());
                if(hwlOpenAutomaticBoxVO == null) {
                    log.error("【教辅异步解析】 内容解析失败，好未来调用失败，教辅 OID: {}, 页码:{}, 文件地址：{}", practiceBookOid, name, data.getNginxUrl());
                    throw new WarningException("内容解析错误");
                }
                List<HwlOpenAutomaticBoxVO.ItemData> itemDataList = hwlOpenAutomaticBoxVO.getData();
                int size = itemDataList.size();
                practiceBookPageBo.setAnalysisQuestionNum((long) size);
                practiceBookPageBo.setQuestionNum((long) size);
                practiceBookPageBo.setAnalysisJson(JSON.toJSONString(hwlOpenAutomaticBoxVO));

                // 处理 坐标点高度
                IntStream.range(0, size).forEach(i-> {
                    HwlOpenAutomaticBoxVO.ItemData item = itemDataList.get(i);
                    List<List<Integer>> nextPosition = null;
                    if(i < size - 1) {
                        nextPosition = itemDataList.get(i + 1).getItemPosition();
                    }
                    // 处理最大高度
                    item.setItemPosition(PositionUtil.adjustHeightPoints(height, item.getItemPosition(), nextPosition));
                });
                // 处理宽度
                IntStream.range(0, size).forEach(i-> {
                    HwlOpenAutomaticBoxVO.ItemData item = itemDataList.get(i);
                    this.widenWidth(itemDataList, i,width );
                });

                // 获取题目坐标信息进行组装
                List<PracticeBookQuestionBo> questionList = IntStream.range(0, size).mapToObj(i-> {
                    PracticeBookQuestionBo questionBo = new PracticeBookQuestionBo();
                    questionBo.setOid(IdUtil.fastSimpleUUID());
                    questionBo.setPracticeBookOid(practiceBookOid);

                    HwlOpenAutomaticBoxVO.ItemData item = itemDataList.get(i);
                    String position = PositionUtil.covert2Wh(item.getItemPosition());
                    questionBo.setPosition(position);
                    questionBo.setPageNoOrderNum(i+1L);
                    // 设置目录OID
                    Optional.ofNullable(pageNoCatalogMap.get(pageNo)).ifPresent(catalogVo-> {
                        questionBo.setPracticeBookCatalogOid(catalogVo.getOid());
                    });
                    return questionBo;
                }).collect(Collectors.toList());

                // 设置每页数据
                List<PracticeBookPositionQuestion> positionQuestions = questionList.stream().map(x -> {
                    PracticeBookPositionQuestion positionQuestion = new PracticeBookPositionQuestion();
                    positionQuestion.setPosition(x.getPosition());
                    positionQuestion.setPracticeBookQuestionOid(x.getOid());
                    positionQuestion.setOrderNum(x.getPageNoOrderNum());
                    return positionQuestion;
                }).collect(Collectors.toList());

                practiceBookPageBo.setQuestionJson(JSON.toJSONString(positionQuestions));
                // 设置教辅题目合集信息
                practiceBookPageBo.setPracticeBookQuestionList(questionList);

                return practiceBookPageBo;
            }).collect(Collectors.toList());


            if(CollUtil.isEmpty(list)) {
                log.warn("【教辅异步解析】 解析内容为空，线程名称：{}, 教辅 OID: {}",Thread.currentThread().getName(), practiceBookOid);
                return;
            }

            // 存储数据
            this.practiceBookPageApiService.saveBatchByPracticeBookOid(practiceBookOid, list);
        } finally {
            consumer.accept(list);
        }
    }




    private void widenWidth(List<HwlOpenAutomaticBoxVO.ItemData> itemDataList,int index, Integer width) {
        int size = itemDataList.size();
        HwlOpenAutomaticBoxVO.ItemData itemData = itemDataList.get(index);
        List<List<Integer>> itemPosition = itemData.getItemPosition();

        int maxWidth = IntStream.range(0, size).filter(x -> x != index).mapToObj(itemDataList::get)
                // 第一个坐标点的 x  大于 当前第二个坐标点的 x 轴
                .filter(x -> x.getItemPosition().get(0).get(0) >= itemPosition.get(1).get(0))
                //
                .filter(x -> {
                    // 第一个坐标点的 y 在 当前 第一个坐标点 y  到第四个坐标点的 y 之间
                    // 或者
                    // 第四个坐标点 y 在当前 第一个坐标点 y  到第四个坐标点的 y 之间
                    List<Integer> firstPoints = x.getItemPosition().get(0);
                    List<Integer> fourPoints = x.getItemPosition().get(3);
                    return firstPoints.get(1) >= itemPosition.get(0).get(1) && firstPoints.get(1) <= itemPosition.get(3).get(1)
                            ||
                            fourPoints.get(1) >= itemPosition.get(0).get(1) && fourPoints.get(1) <= itemPosition.get(3).get(1)
                            ||
                            itemPosition.get(0).get(1) >= firstPoints.get(1) && itemPosition.get(0).get(1) <= fourPoints.get(1)
                            ||
                            itemPosition.get(3).get(1) >= firstPoints.get(1) && itemPosition.get(3).get(1) <= fourPoints.get(1)
                            ;
                }).mapToInt(x-> x.getItemPosition().get(0).get(0)).min().orElse(width);


        itemData.setItemPosition(PositionUtil.adjustWidthPoints(maxWidth, itemPosition));

    }


    /**
     *  根据页号 获取对应的章节目录
     * @param practiceBookOid the practice book oid 教辅 OID
     * @param pageNoSets the page no set 页号set集合
     * @return {@link Map }<{@link Long }, {@link PracticeBookCatalogVo }>
     */
    private Map<Long, PracticeBookCatalogVo> queryCatalogMapByPageNoList(String practiceBookOid, Set<String>pageNoSets) {
        PracticeBookCatalogConditionBo bo = new PracticeBookCatalogConditionBo();
        bo.setPracticeBookOid(practiceBookOid);
        List<Long> pageNoList = pageNoSets.stream().map(this::getPageNo).collect(Collectors.toList());
        bo.setPageNoList(pageNoList);
        bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        AjaxResult<List<PracticeBookCatalogVo>> result = this.practiceBookCatalogApiService.getPracticeBookCatalogListByCondition(bo);
        if(result.isFail()) {
            log.error("【教辅解析】 根据页号获取目录接口异常，线程名称：{}, 教辅 OID:{}", Thread.currentThread().getName(), practiceBookOid);
            return Maps.newHashMap();
        }
        List<PracticeBookCatalogVo> data = result.getData();
        if(CollUtil.isEmpty(data)) {
            log.warn("【教辅解析】 根据页号获取目录接口没有数据返回，线程名称：{}, 教辅 OID:{}", Thread.currentThread().getName(), practiceBookOid);
            return Maps.newHashMap();
        }


        Map<Long, PracticeBookCatalogVo> pageNoCatalogMap = Maps.newHashMap();
        // 整理获取每个页号 对应的 目录信息, 获取逻辑：页号 在目录 范围内 最大 level catalog数据
        pageNoList.forEach(pageNo -> {
             data.stream()
                    .filter(catalogVo -> pageNo >= catalogVo.getPageStart() && pageNo <= catalogVo.getPageEnd())
                    .max(Comparator.comparing(PracticeBookCatalogVo::getLevel)).ifPresent(catalogVO-> {
                        pageNoCatalogMap.put(pageNo, catalogVO);
                    });
        });

        return pageNoCatalogMap;
    }


    /**
     *  获取页号
     * @param name the name
     * @return {@link Long }
     */
    private Long getPageNo(String name) {
        String pageNoStr = name.substring(0, name.lastIndexOf("."));
        return Long.valueOf(pageNoStr);
    }

    /**
     * 调用好未来数据
     * @param imgUrl the img url
     * @return {@link HwlOpenAutomaticBoxVO }
     */
    private HwlOpenAutomaticBoxVO automaticBox(String imgUrl) {
        // 好未来内容解析
        ImageRequestBo bo = new ImageRequestBo();
        bo.setImageUrl(imgUrl);
        AjaxResult ajaxResult = this.hwlOpenApiService.automaticBox(bo);
        if(ajaxResult.isSuccess()) {
            // 解析内容
            Object automaticObj = ajaxResult.getData();
            String jsonString = JSON.toJSONString(automaticObj);

            // 设置题目数量
            return  JSON.parseObject(jsonString, HwlOpenAutomaticBoxVO.class);
        }
        return null;
    }
}
