package com.light.aiszzy.web.controller.basicInfo;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.light.aiszzy.baseInfo.entity.dto.OrganizationBoExt;
import com.light.aiszzy.baseInfo.entity.vo.OrganizationVoExt;
import com.light.base.config.api.ConfigApi;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.StringUtils;
import com.light.security.service.CurrentUserService;
import com.light.user.organization.api.OrganizationApi;
import com.light.user.organization.api.OrganizationSetupApi;
import com.light.user.organization.entity.bo.OrganizationConfig;
import com.light.user.organization.entity.bo.OrganizationSetupBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.user.user.api.UserApi;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApiOperation("学校管理")
@RestController
@RequestMapping("school")
public class SchoolApiController {

    @Resource
    private OrganizationApi organizationApi;

    @Resource
    private OrganizationSetupApi organizationSetupApi;

    @Resource
    private UserApi userApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    private CurrentUserService currentUserService;


    /**
     * 获取组织机构详情
     *
     * @return org detail
     * @throws Exception the exception
     */
    @ApiOperation(value = "获取组织机构详情", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult getOrgDetail()  {

        Long organizationId = currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        AjaxResult detail = organizationApi.getDetail(organizationId);
        if (detail.isFail() || detail.getData() == null) {
            return detail;
        }
        Map<String, Object> map = (Map<String, Object>) detail.getData();
        OrganizationVoExt organizationVo = JSONObject.parseObject(JSONObject.toJSONString(map.get("organizationVo")), OrganizationVoExt.class);
        if (null != organizationVo) {
            // 副标题，logo,建校日期存org_setup
            AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationId);
            if (byOrgId.isSuccess() && byOrgId.getData() != null) {
                OrganizationSetupVo organizationSetupVo = byOrgId.getData();
                organizationVo.setLogo(organizationSetupVo.getLogo());
                organizationVo.setWebName(organizationSetupVo.getWebName());
                organizationVo.setOtherConfig(organizationSetupVo.getOtherConfig());
            }

            // 获取超级管理员
            Optional.ofNullable(configApi.getConfigValue(SystemConstants.ORG_ADD_WITH_USER).getData())
                    .map(param -> JSONUtil.toBean(param, OrganizationConfig.class)).map(OrganizationConfig::getSchoolRoleKey)
                    .map(schoolRoleKey -> this.userApi.queryByRoleCodeAndOrgId(schoolRoleKey, organizationId).getData())
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(x -> x.stream().min(Comparator.comparing(UserVo::getCreateTime)))
                    .ifPresent(userVo -> {
                        organizationVo.setSuperAdminAccount(userVo.getAccountName());
                    });

            map.put("organizationVo", organizationVo);
        }

        return detail;
    }

    /**
     * 编辑组织机构
     *
     * @param organizationBo 组织机构信息
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-31 15:37:40
     */
    @ApiOperation(value = "修改组织机构信息", httpMethod = "POST")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateOrg(@RequestBody OrganizationBoExt organizationBo)  {
        organizationBo.setId(this.currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId());
        AjaxResult ajaxResult = organizationApi.updateOrganization(organizationBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationBo.getId());
        if (byOrgId.isFail()) {
            return byOrgId;
        }

        OrganizationSetupVo data = byOrgId.getData();
        OrganizationSetupBo organizationSetupBo = new OrganizationSetupBo();
        organizationSetupBo.setOrganizationId(organizationBo.getId());
        organizationSetupBo.setLogo(organizationBo.getLogo());
        organizationSetupBo.setWebName(organizationBo.getWebName());
        organizationSetupBo.setOtherConfig(organizationBo.getOtherConfig());
        if (data == null) {
            organizationSetupApi.saveOrganizationSetup(organizationSetupBo);
        } else {
            organizationSetupBo.setId(data.getId());
            organizationSetupApi.updateOrganizationSetup(organizationSetupBo);
        }

        return ajaxResult;
    }


}
