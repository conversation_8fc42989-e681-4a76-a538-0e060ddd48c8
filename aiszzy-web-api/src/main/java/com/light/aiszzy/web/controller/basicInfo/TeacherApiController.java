package com.light.aiszzy.web.controller.basicInfo;

import com.light.core.entity.AjaxResult;
import com.light.security.service.CurrentUserService;
import com.light.user.clazz.api.ClazzHeadmasterApi;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.teacher.api.TeacherClassesSubjectApi;
import com.light.user.teacher.entity.vo.TeacherClassesSubjectVo;
import com.light.user.user.api.UserApi;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


@Api(tags = "老师")
@RestController
@RequestMapping("teacher")
public class TeacherApiController {


    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private TeacherClassesSubjectApi teacherClassesSubjectApi;

    @Resource
    private ClazzHeadmasterApi clazzHeadmasterApi;

    @Resource
    private UserApi userApi;


    @ApiOperation("当前老师信息")
    @GetMapping("current")
    public AjaxResult current() {
        String currentOid = this.currentUserService.getCurrentOid();
        AjaxResult<UserVo> userVoAjaxResult = this.userApi.getVoByUserOid(currentOid);
        if(userVoAjaxResult.isFail() || userVoAjaxResult.getData() == null) {
            return userVoAjaxResult;
        }
        UserVo userVo = userVoAjaxResult.getData();
        Map<String, Object> map = new HashMap<>();
        map.put("user", userVo);
        // 任教信息
        Optional.ofNullable(this.teacherClassesSubjectApi.getByUserOid(currentOid).getData())
                .ifPresent(x->   map.put("teacherClassesSubjectList", x));

        // 班主任信息
        Optional.ofNullable(this.clazzHeadmasterApi.getByUserOid(currentOid).getData())
                .ifPresent(x->   map.put("classHeadmasterList", x));
        return AjaxResult.success(map);
    }
}
