package com.light.aiszzy.web.controller.user;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.Maps;
import com.light.aiszzy.web.model.StudentImportThread;
import com.light.aiszzy.web.model.TeacherExcelModel;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.base.dictionary.service.DictionaryDataApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.entity.Progress;
import com.light.core.exception.UnifiedException;
import com.light.core.exception.WarningException;

import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import com.light.redis.utils.ExcelUtils;
import com.light.security.service.CurrentAdminService;
import com.light.user.admin.entity.vo.LoginAdminVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.clazz.service.ClazzApiService;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.organization.service.OrganizationApiService;
import com.light.user.student.entity.bo.StudentBo;
import com.light.user.student.service.StudentApiService;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.service.TeacherApiService;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.service.UserApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Validated
@Api(tags = "导入模板")
public class UploadController {

    @Resource
    private CurrentAdminService currentAdminService;

    @Resource
    private StudentApiService studentApiService;

    @Resource
    private TeacherApiService teacherApiService;

    @Value("${filepath.windows}")
    private String windowsPath;

    @Value("${filepath.linux}")
    private String linuxPath;

    @Autowired
    private RedisComponent redisComponent;

    @Autowired
    private ClazzApiService clazzApiService;

    @Autowired
    private UserApiService userApiService;

    @Resource
    private DictionaryDataApiService  dictionaryDataApiService;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Value("${email.default.suffix:''}")
    private String emailDefaultSuffix;

    //表格header
    private static final String SHCOOL = "学校（必填）";
    private static final String GRADE = "年级（必填，手动选择）";
    private static final String CLASS = "班级（必填：1班~99班）";
    private static final String STUDENT_NO = "学号（必填：01~99）";
    private static final String STUDENT_NAME = "学生姓名（必填）";
    private static final String IDENTITY_CARD_NO = "证件号码（身份证号/学籍号）（非必填）";

    private static final String CLASS_NAME_SUFFIX = "班";

    private static Map<String, String> map = new HashMap() {{
        put("小学", "01");
        put("初中", "02");
        put("高中", "03");
    }};

    private static Map<String, Integer> sexMap = new HashMap() {{
        put("男", 1);
        put("女", 2);
        put("未知性别", 3);
    }};

    //@PostMapping("/student/import")
    @ApiOperation(value = "导入学生", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult importStudentUser(MultipartFile file) throws Exception {
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(1);
        importParams.setTitleRows(1);
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, importParams);
        System.out.println(list);
        final Map<String, String> sectionMap = this.getDictValLabelMapByDictType("section");
        List<StudentBo> stuList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (Map<String, Object> obj : list) {
                StudentBo stu = new StudentBo();
                stu.setGeneratorAccount(true);
                stu.setSetDefaultEmail(true);
                stu.setIdentityType("1");
                stu.setIdentityCardNumber(obj.get("身份证号").toString().trim());
                UserBo user = new UserBo();
                user.setIdentityType("1");
                user.setIdentityCardNumber(obj.get("身份证号").toString().trim());
                user.setSection(sectionMap.get(obj.get("学段").toString().trim()));
                user.setRealName(obj.get("姓名").toString());
                user.setSex(sexMap.get(obj.get("性别").toString().trim()));
                stu.setUser(user);
                stuList.add(stu);
            }
        } else {
            return AjaxResult.fail("表格数据为空");
        }
        return studentApiService.addStudentBatch(stuList);
    }

    @PostMapping("/teacher/admin/import")
    @ApiOperation(value = "导入教师(用户管理导入)", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult importTeacherUser(MultipartFile file)  {

        List<TeacherBo> teaList = readExcelToTeacher(file, null);

        return teacherApiService.addBatchTeacher(teaList);
    }


    /**
     *  读取 老师 excel
     * @param file the excel file
     * @param organizationId the organization id  组织机构ID
     * @return {@link List}<{@link TeacherBo}>
     */
    private List<TeacherBo> readExcelToTeacher(MultipartFile file, Long organizationId){
        final List<TeacherExcelModel> list;
        try (InputStream inputStream = file.getInputStream()){
            list = ExcelUtils.importExcelByDict(inputStream, 1,1, TeacherExcelModel.class);
        } catch (IOException e) {
            e.printStackTrace();
            throw new UnifiedException("数据读取失败");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new WarningException("表格数据为空");
        }

        return list.stream().map(x-> {
            TeacherBo tea = new TeacherBo();
            tea.setGeneratorAccount(true);
            tea.setSetDefaultEmail(true);
            UserBo user = new UserBo();

            user.setSection(x.getSection());
            user.setRealName(x.getRealName());
            user.setSex(Optional.ofNullable(x.getSex()).map(Integer::parseInt).orElse(null));
            user.setPhone(x.getPhone());
            user.setOrganizationId(organizationId);
            user.setAccountSource(SystemConstants.ACCOUNT_SOURCE_IMPORT);
            tea.setUser(user);
            return tea;
        }).collect(Collectors.toList());
    }


    private Map<String, String> getDictValLabelMapByDictType(String dictType){
        DictionaryDataListConditionBo bo = new DictionaryDataListConditionBo();
        bo.setDictType(dictType);
        final List<DictionaryDataVo> data = this.dictionaryDataApiService.getAvailableList(bo).getData();
        if(CollUtil.isEmpty(data)){
            return Maps.newHashMap();
        }

        return data.stream().collect(Collectors.toMap(DictionaryDataVo::getDictValue, DictionaryDataVo::getDictLabel, (k1,k2)-> k2));
    }

    @PostMapping("/teacher/import")
    @ApiOperation(value = "导入教师", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult importTeacherUser(@RequestParam("file") MultipartFile file,
                                        @RequestParam("organizationId") Long organizationId)  {
        List<TeacherBo> teaList = readExcelToTeacher(file, organizationId);
        return teacherApiService.addBatchTeacher(teaList);
    }

    @PostMapping("/teacher/school/import")
    @ApiOperation(value = "学校管理员导入教师", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult importSchoolTeacherUser(MultipartFile file) {
        //获取当前用户的组织机构
        LoginAdminVo admin = currentAdminService.getCurrentAdmin();
        if(null == admin ) {
            return AjaxResult.fail("当前用户不存在");
        }
        if(null == admin.getOrganizationId()) {
            return AjaxResult.fail("当前用户没有所属组织机构");
        }
        List<TeacherBo> teaList = readExcelToTeacher(file, admin.getOrganizationId());
        return teacherApiService.addBatchTeacher(teaList);
    }

    @GetMapping("/user/downloadTemplate")
    @ApiOperation(value = "下载用户模板", httpMethod = SystemConstants.GET_REQUEST)
    public void importStudentUser(@RequestParam("fileName") String fileName,@RequestParam("type") Integer type, HttpServletResponse response) throws Exception {
        if (StringUtils.isNotEmpty(fileName)) {
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        }
        org.springframework.core.io.Resource resource = new ClassPathResource(type.equals(1)?"template/teacher.xlsx":"template/student.xlsx");

        IoUtil.write(response.getOutputStream(), false, FileUtil.readBytes(resource.getFile()));

    }

    @GetMapping("/teacher/downloadTemplate")
    @ApiOperation(value = "下载用户模板", httpMethod = SystemConstants.GET_REQUEST)
    public void downloadTeachTemplate(@RequestParam("fileName") String fileName, HttpServletResponse response) throws Exception {
        if (StringUtils.isNotEmpty(fileName)) {
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        }
        ExcelUtils.exportExcel(Collections.EMPTY_LIST,
                "教师模板",
                fileName,
                TeacherExcelModel.class,
                fileName,
                response);
    }

    @GetMapping("/school/student/import/template")
    @ApiOperation(value = "学生导入模板（学校管理员）")
    public void getSchoolStudentImportTemplate(HttpServletResponse response) throws Exception {
        ExcelWriter writer = ExcelUtil.getWriter(true);
        this.writeCell(writer, 0, 4, GRADE);
        this.writeCell(writer, 1, 4, CLASS);
        this.writeCell(writer, 2, 4, STUDENT_NO);
        this.writeCell(writer, 3, 4, STUDENT_NAME);
        this.writeCell(writer, 4, 4, IDENTITY_CARD_NO);
        this.writeCell(writer, 5, 4, "请注意：证件号码粘贴进表格时，需使用“只粘贴文本”，直接“粘贴”容易造成数值错误。");
        //获取年级字典
        Map<Object, Object> dicts = redisComponent.hmget(RedisKeyEnum.DICTIONARY_AVAILABLE.getValue() + "grade");
        if(!MapUtils.isEmpty(dicts)) {
            List<DictionaryDataVo> datas = new ArrayList<DictionaryDataVo>();
            dicts.forEach((key, value) -> {
                datas.add(JSON.parseObject(JSON.toJSONString(value), DictionaryDataVo.class));
            });
            List<String> keys = datas.stream().sorted(Comparator.comparing(DictionaryDataVo::getDictSort))
                    .map(DictionaryDataVo::getDictLabel).collect(Collectors.toList());
            CellRangeAddressList grades = new CellRangeAddressList(1, 10000, 0, 0 );
            writer.addSelect(grades, keys.toArray(new String[keys.size()]));
        }
//        CellRangeAddressList classes = new CellRangeAddressList(1, 10000, 1, 1 );
//        writer.addSelect(classes, this.getClasses(50).toArray(new String[50]));
        //设置内容字体
        for(int i = 0; i < 3; i++) {
            writer.setColumnWidth(i, 20);
        }
        writer.setColumnWidth(3, 40);
        writer.setColumnWidth(4, 80);
        //设置content—type
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");
        //设置标题
        String fileName = URLEncoder.encode("学生导入模板", "UTF-8");
        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream = response.getOutputStream();

        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }

    @GetMapping("/student/import/template")
    @ApiOperation(value = "学生导入模板")
    public void getStudentImportTemplate(HttpServletResponse response) throws Exception {
        ExcelWriter writer = ExcelUtil.getWriter(true);
        this.writeCell(writer, 0, 5, "学校（必填）");
        this.writeCell(writer, 1, 5, GRADE);
        this.writeCell(writer, 2, 5, CLASS);
        this.writeCell(writer, 3, 5, STUDENT_NO);
        this.writeCell(writer, 4, 5, STUDENT_NAME);
        this.writeCell(writer, 5, 5, IDENTITY_CARD_NO);
        this.writeCell(writer, 6, 5, "请注意：证件号码粘贴进表格时，需使用“只粘贴文本”，直接“粘贴”容易造成数值错误。");
        //获取年级字典
        Map<Object, Object> dicts = redisComponent.hmget(RedisKeyEnum.DICTIONARY_AVAILABLE.getValue() + "grade");
        if(!MapUtils.isEmpty(dicts)) {
            List<DictionaryDataVo> datas = new ArrayList<DictionaryDataVo>();
            dicts.forEach((key, value) -> {
                datas.add(JSON.parseObject(JSON.toJSONString(value), DictionaryDataVo.class));
            });
            List<String> keys = datas.stream().sorted(Comparator.comparing(DictionaryDataVo::getDictSort))
                    .map(DictionaryDataVo::getDictLabel).collect(Collectors.toList());
            CellRangeAddressList grades = new CellRangeAddressList(1, 10000, 1, 1 );
            writer.addSelect(grades, keys.toArray(new String[keys.size()]));
        }
//        CellRangeAddressList classes = new CellRangeAddressList(1, 10000, 2, 2 );
//        writer.addSelect(classes, this.getClasses(50).toArray(new String[50]));
        //设置内容字体
        for(int i = 0; i < 4; i++) {
            writer.setColumnWidth(i, 20);
        }
        writer.setColumnWidth(4, 20);
        writer.setColumnWidth(5, 40);
        writer.setColumnWidth(6, 80);
        //设置content—type
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");
        //设置标题
        String fileName = URLEncoder.encode("学生导入模板", "UTF-8");
        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream = response.getOutputStream();

        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }

    @PostMapping("/student/import")
    @ApiOperation(value = "导入学生")
    public AjaxResult importStudentAdmin(@RequestParam("file") MultipartFile file) throws IOException {
        Map<String, Long> school = new HashMap<>();
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map<String, Object>> list = reader.readAll();
        Map<String, Integer> existIdNo = new HashMap<>();
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.fail("表格数据为空");
        }else{//数据处理
            //年级字典
            Map<Object, Object> gradeMap = redisComponent.hmget(RedisKeyEnum.DICTIONARY_REVERSE.getValue() + "grade");
            Map<String, String> grade = new HashMap<String, String>();
            if(null != gradeMap) {
                gradeMap.forEach((key, value) -> {
                    DictionaryDataVo data = JSONObject.parseObject(value.toString(), DictionaryDataVo.class);
                    if(null != data) {
                        grade.put(UnicodeUtil.toString(key.toString()), data.getDictValue());
                    }
                });
            }
            Map<String, Integer> existStuNo = new HashMap<String, Integer>();
            List<UserBo> users = new ArrayList<UserBo>();
            for(int i = 0; i < list.size(); i++) {
                Long organizationId = null;
                Map<String, Object> map = list.get(i);
                UserBo user = new UserBo();
                //查询找学校组织机构id
                if(null == map.get(SHCOOL) || StringUtils.isEmpty(map.get(SHCOOL).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据学校不能为空");
                }else{
                    if(school.containsKey(map.get(SHCOOL).toString())) {//之前数据有没有存在过组织机构
                        organizationId = school.get(map.get(SHCOOL).toString());//使用之前查询过得
                    }else{
                        //从接口中查询组织机构
                        OrganizationVo org = organizationApiService.getOrganizationByName(map.get(SHCOOL).toString()).getData();
                        if(null != org) {
                            organizationId = org.getId();
                            school.put(map.get(SHCOOL).toString(), organizationId);
                        }else{
                            return AjaxResult.fail("第" + (i+1) +"条数据学校不存在");
                        }
                    }
                }
                if(null == map.get(GRADE)
                        || StringUtils.isEmpty(map.get(GRADE).toString())
                        || !grade.containsKey(map.get(GRADE).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据年级不能为空或者年级不存在");
                }
                if(null == map.get(CLASS) || StringUtils.isEmpty(map.get(CLASS).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据班级不能为空");
                }else{
                    //校验班级的格式
                    String className = map.get(CLASS).toString();
                    if(!className.endsWith(CLASS_NAME_SUFFIX)) {
                        return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（正确格式：“X班”");
                    }
                    //判断数值
                    String numberic = className.replace(CLASS_NAME_SUFFIX, "");
                    if(numberic.length() > 0) {
                        if(!isNumeric(numberic)) {
                            return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（“班”前面应为1-99之间的数值）");
                        }else{
                            if(numberic.startsWith("0")) {//班之前的数值不能首位补0
                                return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（“班”前面应为1-99之间的数值，班级序号不得以“0”开头）");
                            }
                            if(Integer.valueOf(numberic) > 99 || Integer.valueOf(numberic) < 1) {
                                return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（“班”前面应为1-99之间的数值）");
                            }
                        }
                    }else{
                        return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（正确格式：“X班”）");
                    }
                    user.setClassesName(className);
                }
                if(null == map.get(STUDENT_NO) || StringUtils.isEmpty(map.get(STUDENT_NO).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据学号不能为空");
                }else{
                    if(!isNumeric(map.get(STUDENT_NO).toString())) {
                        return AjaxResult.fail("第" + (i+1) +"条数据学号只能包含数字不能为空");
                    }else{
                        Integer stuNo = Integer.parseInt(map.get(STUDENT_NO).toString());
                        if(stuNo > 99 || stuNo < 1) {
                            return AjaxResult.fail("第" + (i+1) +"条数据学号只能在01到99之间不能为空");
                        }else{
                            if(existStuNo.containsKey(map.get(SHCOOL).toString() + SystemConstants.UNDERLINE
                                            +  map.get(GRADE).toString() + SystemConstants.UNDERLINE
                                            +  map.get(CLASS).toString() + SystemConstants.UNDERLINE
                                            +  map.get(STUDENT_NO).toString())) {
                                return AjaxResult.fail("第" + (i+1) +"条数据学号与第" +
                                        existStuNo.get(map.get(SHCOOL).toString() + SystemConstants.UNDERLINE
                                                +  map.get(GRADE).toString() + SystemConstants.UNDERLINE
                                                +  map.get(CLASS).toString() + SystemConstants.UNDERLINE
                                                +  map.get(STUDENT_NO).toString()) +
                                        "条数据的学号重复");
                            }else{
                                existStuNo.put(map.get(SHCOOL).toString() + SystemConstants.UNDERLINE
                                            +  map.get(GRADE).toString() + SystemConstants.UNDERLINE
                                            +  map.get(CLASS).toString() + SystemConstants.UNDERLINE
                                            +  map.get(STUDENT_NO).toString(), i + 1);
                            }
                        }
                    }
                }
                if(null == map.get(STUDENT_NAME) || StringUtils.isEmpty(map.get(STUDENT_NAME).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据学生姓名不能为空");
                }
                if(null != map.get(IDENTITY_CARD_NO) && !StringUtils.isEmpty(map.get(IDENTITY_CARD_NO).toString())) {
                    //首先判断文档中是否重复
                    if(existIdNo.containsKey(map.get(IDENTITY_CARD_NO).toString())) {
                        return AjaxResult.fail("第" + (i+1) +"条数据证件号码与第"
                                + (existIdNo.get(map.get(IDENTITY_CARD_NO).toString())) + "条数据的证件号码重复");
                    }else{
                        //查询证件号是否已使用
                        Integer count =  userApiService.getCountByIdentityTypeAndCard("1",
                                map.get(IDENTITY_CARD_NO).toString()).getData();
                        if(null != count && count > 0) {
                            return AjaxResult.fail("第" + (i+1) +"条数据证件号码已存在");
                        }else{
                            existIdNo.put(map.get(IDENTITY_CARD_NO).toString(), i+1);
                        }
                    }
                }
                //年级转换，根据班级名称查询classid
                ClazzVo clazz = clazzApiService.getOrSaveByCondition(map.get(CLASS).toString(),
                        grade.get(map.get(GRADE).toString()).toString(), organizationId ).getData();
                if(null != clazz) {
                    user.setClazzId(clazz.getId());
                    user.setEnrollmentYear(clazz.getEnrollmentYear());
                    user.setSection(clazz.getSection());
                }else{
                    return AjaxResult.fail("第" + (i+1) +"条数据班级不存在或创建班级失败");
                }
                user.setUserIdentityType(SystemConstants.STUDENT_IDENTITY);
                user.setIdentityCardNumber(null != map.get(IDENTITY_CARD_NO)?map.get(IDENTITY_CARD_NO).toString():"");
                user.setRealName(map.get(STUDENT_NAME).toString());
                user.setOrganizationId(organizationId);
                user.setAccountSource(SystemConstants.ACCOUNT_SOURCE_IMPORT);
                user.setIdentityType("1");
                user.setRemark(map.get(STUDENT_NO).toString());//remark字段作为学号暂存数据
                users.add(user);
            }
            String redisKey = "school:student:import:" + IdUtil.simpleUUID();
            Progress progress = new Progress();
            progress.setTotal(users.size());
            progress.setSuccess(0);
            progress.setFail(0);
            if(!CollectionUtils.isEmpty(users)) {
                progress.setProgress(0d);
                redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
                StudentImportThread thread = new StudentImportThread(redisKey, users);
                thread.start();
            }else{
                progress.setProgress(100d);
                redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
            }
            return AjaxResult.success(HttpStatus.HTTP_OK, "正在导入，请等待...", redisKey);
        }
    }

    @PostMapping("/school/student/import")
    @ApiOperation(value = "导入学生(学校管理员)")
    public AjaxResult importStudentSchool(@RequestParam("file") MultipartFile file) throws IOException {
        Long organizationId = currentAdminService.getCurrentAdmin().getOrganizationId();
        Map<String, String> classMap = new HashMap<String, String>();
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<Map<String, Object>> list = reader.readAll();
        Map<String, Integer> existIdNo = new HashMap<>();
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.fail("表格数据为空");
        }else{//数据处理
            //年级字典
            Map<Object, Object> gradeMap = redisComponent.hmget(RedisKeyEnum.DICTIONARY_REVERSE.getValue() + "grade");
            Map<String, String> grade = new HashMap<String, String>();
            if(null != gradeMap) {
                gradeMap.forEach((key, value) -> {
                    DictionaryDataVo data = JSONObject.parseObject(value.toString(), DictionaryDataVo.class);
                    if(null != data) {
                        grade.put(UnicodeUtil.toString(key.toString()), data.getDictValue());
                    }
                });
            }
            Map<String, Integer> existStuNo = new HashMap<String, Integer>();
            List<UserBo> users = new ArrayList<UserBo>();
            for(int i = 0; i < list.size(); i++) {
                Map<String, Object> map = list.get(i);
                UserBo user = new UserBo();
                if(null == map.get(GRADE)
                        || StringUtils.isEmpty(map.get(GRADE).toString())
                        || !grade.containsKey(map.get(GRADE).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据年级不能为空或者年级不存在");
                }
                if(null == map.get(CLASS) || StringUtils.isEmpty(map.get(CLASS).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据班级不能为空");
                }else{
                    //校验班级的格式
                    String className = map.get(CLASS).toString();
                    if(!className.endsWith(CLASS_NAME_SUFFIX)) {
                        return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（正确格式：“X班”）");
                    }
                    //判断数值
                    String numberic = className.replace(CLASS_NAME_SUFFIX, "");
                    if(numberic.length() > 0) {
                        if(!isNumeric(numberic)) {
                            return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（“班”前面应为1-99之间的数值）");
                        }else{
                            if(numberic.startsWith("0")) {//班之前的数值不能首位补0
                                return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（“班”前面应为1-99之间的数值，班级序号不得以“0”开头）");
                            }
                            if(Integer.valueOf(numberic) > 99 || Integer.valueOf(numberic) < 1) {
                                return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（“班”前面应为1-99之间的数值）");
                            }
                        }
                    }else{
                        return AjaxResult.fail("第" + (i+1) + "条数据的班级格式不正确（正确格式：“X班”）");
                    }
                    user.setClassesName(className);
                }
                if(null == map.get(STUDENT_NO) || StringUtils.isEmpty(map.get(STUDENT_NO).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据学号不能为空");
                }else{
                    if(!isNumeric(map.get(STUDENT_NO).toString())) {
                        return AjaxResult.fail("第" + (i+1) +"条数据学号只能包含数字不能为空");
                    }else{
                        Integer stuNo = Integer.parseInt(map.get(STUDENT_NO).toString());
                        if(stuNo > 99 || stuNo < 1) {
                            return AjaxResult.fail("第" + (i+1) +"条数据学号只能在01到99之间不能为空");
                        }else{
                            if(existStuNo.containsKey(map.get(GRADE).toString() + SystemConstants.UNDERLINE
                                                    +  map.get(CLASS).toString() + SystemConstants.UNDERLINE
                                                    +  map.get(STUDENT_NO).toString())) {
                                return AjaxResult.fail("第" + (i+1) +"条数据学号与第" +
                                            existStuNo.get(map.get(GRADE).toString() + SystemConstants.UNDERLINE
                                                    +  map.get(CLASS).toString() + SystemConstants.UNDERLINE
                                                    +  map.get(STUDENT_NO).toString()) +
                                            "条数据的学号重复");
                            }else{
                                existStuNo.put(map.get(GRADE).toString() + SystemConstants.UNDERLINE
                                            +  map.get(CLASS).toString() + SystemConstants.UNDERLINE
                                            +  map.get(STUDENT_NO).toString(), i + 1);
                            }
                        }
                    }
                }
                if(null == map.get(STUDENT_NAME) || StringUtils.isEmpty(map.get(STUDENT_NAME).toString())) {
                    return AjaxResult.fail("第" + (i+1) +"条数据学生姓名不能为空");
                }
                if(null != map.get(IDENTITY_CARD_NO) && !StringUtils.isEmpty(map.get(IDENTITY_CARD_NO).toString())) {
                    //首先判断文档中是否重复
                    if(existIdNo.containsKey(map.get(IDENTITY_CARD_NO).toString())) {
                        return AjaxResult.fail("第" + (i+1) +"条数据证件号码与第"
                                + (existIdNo.get(map.get(IDENTITY_CARD_NO).toString())) + "条数据的证件号码重复");
                    }else{
                        //查询证件号是否已使用
                        Integer count =  userApiService.getCountByIdentityTypeAndCard("1",
                                map.get(IDENTITY_CARD_NO).toString()).getData();
                        if(null != count && count > 0) {
                            return AjaxResult.fail("第" + (i+1) +"条数据证件号码已存在");
                        }else{
                            existIdNo.put(map.get(IDENTITY_CARD_NO).toString(), i+1);
                        }
                    }
                }
                //年级转换，根据班级名称查询classid
                if(classMap.containsKey(map.get(GRADE).toString() + map.get(CLASS).toString())) {//如果map中存在，则从map中取值
                    String[] gc = classMap.get(map.get(GRADE).toString() + map.get(CLASS).toString()).split(SystemConstants.SEPERATOR_COMMA);
                    user.setClazzId(Long.valueOf(gc[0]));
                    user.setEnrollmentYear(Long.valueOf(gc[1]));
                    user.setSection(gc[2]);
                }else{
                    ClazzVo clazz = clazzApiService.getOrSaveByCondition(map.get(CLASS).toString(),
                            grade.get(map.get(GRADE).toString()).toString(), organizationId ).getData();
                    if(null != clazz) {
                        user.setClazzId(clazz.getId());
                        user.setEnrollmentYear(clazz.getEnrollmentYear());
                        user.setSection(clazz.getSection());
                        //放入 map
                        classMap.put(map.get(GRADE).toString() + map.get(CLASS).toString(),
                                clazz.getId() + SystemConstants.SEPERATOR_COMMA + clazz.getEnrollmentYear()
                                        + SystemConstants.SEPERATOR_COMMA + clazz.getSection());
                    }else{
                        return AjaxResult.fail("第" + (i+1) +"条数据班级不存在或创建班级失败");
                    }
                }
                user.setUserIdentityType(SystemConstants.STUDENT_IDENTITY);
                user.setIdentityCardNumber(null != map.get(IDENTITY_CARD_NO)?map.get(IDENTITY_CARD_NO).toString():"");
                user.setRealName(map.get(STUDENT_NAME).toString());
                user.setOrganizationId(organizationId);
                user.setAccountSource(SystemConstants.ACCOUNT_SOURCE_IMPORT);
                user.setIdentityType("1");
                user.setRemark(map.get(STUDENT_NO).toString());//remark字段作为学号暂存数据
                users.add(user);
            }
            String redisKey = "school:student:import:" + IdUtil.simpleUUID();
            Progress progress = new Progress();
            progress.setTotal(users.size());
            progress.setSuccess(0);
            progress.setFail(0);
            if(!CollectionUtils.isEmpty(users)) {
                progress.setProgress(0d);
                redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
                StudentImportThread thread = new StudentImportThread(redisKey, users);
                thread.start();
            }else{
                progress.setProgress(100d);
                redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
            }
            return AjaxResult.success(HttpStatus.HTTP_OK, "正在导入，请等待...", redisKey);
        }
    }

    private List<String> getClasses(int count) {
        List<String> classes = new ArrayList<String>();
        for(int i = 0; i < count; i++) {
            classes.add((i + 1) + "班");
        }
        return classes;
    }

    private List<String> getStudentNos(int count) {
        List<String> classes = new ArrayList<String>();
        for(int i = 0; i < count; i++) {
            classes.add((i + 1) < 10 ? ("0" + (i+1)) : (i + 1) + "");
        }
        return classes;
    }

    /**
     * 输入标题到excel
     * @param writer excel对象
     * @param column 当前列位置
     * @param cellValue 标题内容
     */
    private void writeCell(ExcelWriter writer, int column, int limit, String cellValue){
        // 根据x,y轴设置单元格内容
        writer.writeCellValue(column , 0, cellValue);
        if(column < limit) {
            // 字体颜色标红
            Font font = writer.createFont();
            font.setColor(Font.COLOR_RED);
            // 根据x,y轴获取当前单元格样式
            CellStyle cellStyle = writer.createCellStyle(column, 0);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setFont(font);
        }
    }

    private boolean isNumeric(String str) {
        // 使用正则表达式匹配字符串是否只包含数字
        return str.matches("\\d+");
    }

}
