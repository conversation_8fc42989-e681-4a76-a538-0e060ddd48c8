package com.light.aiszzy.homework.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 作业本
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-11 15:21:34
 */
@Data
public class HomeworkBookBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 作业本id
	 */
	@ApiModelProperty("作业本id")
	private Long homeworkBookId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	private String code;
	/**
	 * 作业本名称
	 */
	@ApiModelProperty("作业本名称")
	private String name;
	/**
	 * 学校CODE
	 */
	@ApiModelProperty("学校CODE")
	private String orgCode;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 启用年份
	 */
	@ApiModelProperty("启用年份")
	private String year;
	/**
	 * 学期，1上学期，2下学期
	 */
	@ApiModelProperty("学期，1上学期，2下学期")
	private Integer term;
	/**
	 * 教材版本
	 */
	@ApiModelProperty("教材版本")
	private Long textBookVersionId;
	/**
	 * 教材
	 */
	@ApiModelProperty("教材")
	private Long textBookId;
	/**
	 * 1基础2
巩固3
提高
	 */
	@ApiModelProperty("1基础2巩固3 提高")
	private Integer exerciseType;
	/**
	 * 封面
	 */
	@ApiModelProperty("封面")
	private String coverUrl;
	/**
	 * 作业数量
	 */
	@ApiModelProperty("作业数量")
	private Integer num;
	/**
	 * 状态0未发布，1已经发布
	 */
	@ApiModelProperty("状态0未发布，1已经发布")
	private Integer status;
	/**
	 * 是否有未审核作业1是，0否
	 */
	@ApiModelProperty("是否有未审核作业1是，0否")
	private Integer hasNoConfirm;
	/**
	 * 是否完结1是，0否
	 */
	@ApiModelProperty("是否完结1是，0否")
	private Integer isCompleted;
	/**
	 * 来源（1、自建；2、引用教辅；3引用作业本）
	 */
	@ApiModelProperty("来源（1、自建；2、引用教辅；3引用作业本）")
	private Integer sourceType;
	/**
	 * 教辅的oid或作业本oid
	 */
	@ApiModelProperty("教辅的oid或作业本oid")
	private String sourceOid;
	/**
	 * 运营创建和老师创建
	 */
	@ApiModelProperty("运营创建和老师创建")
	private String createSource;
	/**
	 * 所有作业pdf合并生成pdf下载地址
	 */
	@ApiModelProperty("所有作业pdf合并生成pdf下载地址")
	private String homeworkPdfUrl;
	/**
	 * 所有作业答案解析合并生成word下载地址
	 */
	@ApiModelProperty("所有作业答案解析合并生成word下载地址")
	private String homeworkAnswerPdfUrl;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
