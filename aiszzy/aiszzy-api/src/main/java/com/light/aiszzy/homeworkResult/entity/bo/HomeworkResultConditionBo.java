package com.light.aiszzy.homeworkResult.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 校本作业结果表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 11:09:04
 */
@Data
public class HomeworkResultConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@ApiModelProperty("作业id")
	private String homeworkOid;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;

	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private String classId;

	/**
	 * 学生oid
	 */
	@ApiModelProperty("学生oid")
	private String stuOid;

	/**
	 * 学生姓名
	 */
	@ApiModelProperty("学生姓名")
	private String stuName;

	/**
	 * 页码个数合集
	 */
	@ApiModelProperty("页码个数合集")
	private String pageNos;

	/**
	 * ocr识别名字结果
	 */
	@ApiModelProperty("ocr识别名字结果")
	private String orcStuNames;

	/**
	 * ocr识别学号结果
	 */
	@ApiModelProperty("ocr识别学号结果")
	private String ocrStuNos;

	/**
	 * 学生作答存储地址
	 */
	@ApiModelProperty("学生作答存储地址")
	private String stuAnswerUrls;


	/**
	 * 智批结果打印合并学生作答和智批结果的地址
	 */
	@ApiModelProperty("智批结果打印合并学生作答和智批结果的地址")
	private String stuAnswerCorrectResult;

	/**
	 * 智批结果（白底红色对错图片地址）
	 */
	@ApiModelProperty("智批结果（白底红色对错图片地址）")
	private String stuAnswerCorrectResultPrint;

	/**
	 * 学生作答图片信息json存储
	 */
	@ApiModelProperty("学生作答图片信息json存储")
	private String stuAnswerPageInfoJson;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
