package  com.light.aiszzy.resourcesUserAddToCart.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface ResourcesUserAddToCartApi  {

	/**
	 * 查询用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesUserAddToCart/pageList")
	@ApiOperation(value = "分页查询用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	AjaxResult<PageInfo<ResourcesUserAddToCartVo>> getResourcesUserAddToCartPageListByCondition(@RequestBody ResourcesUserAddToCartConditionBo condition);

	/**
	 * 查询所有用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesUserAddToCart/list")
	@ApiOperation(value = "查询所有用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	AjaxResult<List<ResourcesUserAddToCartVo>> getResourcesUserAddToCartListByCondition(@RequestBody ResourcesUserAddToCartConditionBo condition);


	/**
	 * 新增用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesUserAddToCart/add")
	@ApiOperation(value = "新增用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	AjaxResult addResourcesUserAddToCart(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo);

	/**
	 * 修改用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
	 * @param resourcesUserAddToCartBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/resourcesUserAddToCart/update")
	@ApiOperation(value = "修改用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "POST")
	AjaxResult updateResourcesUserAddToCart(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo);

	/**
	 * 查询用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/resourcesUserAddToCart/detail")
	@ApiOperation(value = "查询用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)详情",httpMethod = "GET")
	AjaxResult<ResourcesUserAddToCartVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/resourcesUserAddToCart/delete")
	@ApiOperation(value = "删除用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	@PostMapping("/resourcesUserAddToCart/deleteQuestion")
	@ApiOperation(value = "根据参数删除",httpMethod = "POST")
	AjaxResult deleteQuestion(@Validated @RequestBody ResourcesUserAddToCartBo resourcesUserAddToCartBo);


}

