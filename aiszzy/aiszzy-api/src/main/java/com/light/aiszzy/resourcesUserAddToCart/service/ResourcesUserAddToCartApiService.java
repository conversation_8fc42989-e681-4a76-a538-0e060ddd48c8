package com.light.aiszzy.resourcesUserAddToCart.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.resourcesUserAddToCart.api.ResourcesUserAddToCartApi;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "resourcesUserAddToCartApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = ResourcesUserAddToCartApiService.ResourcesUserAddToCartApiFallbackFactory.class)
@Component
public interface ResourcesUserAddToCartApiService  extends ResourcesUserAddToCartApi {

	@Component
	class ResourcesUserAddToCartApiFallbackFactory implements FallbackFactory<ResourcesUserAddToCartApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(ResourcesUserAddToCartApiService.ResourcesUserAddToCartApiFallbackFactory.class);
		@Override
		public ResourcesUserAddToCartApiService create(Throwable cause) {
			ResourcesUserAddToCartApiService.ResourcesUserAddToCartApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new ResourcesUserAddToCartApiService() {
				public AjaxResult getResourcesUserAddToCartPageListByCondition(ResourcesUserAddToCartConditionBo condition){
					return AjaxResult.fail("用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)查询失败");
				}
				public AjaxResult getResourcesUserAddToCartListByCondition(ResourcesUserAddToCartConditionBo condition){
					return AjaxResult.fail("用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)查询失败");
				}

				public AjaxResult addResourcesUserAddToCart(ResourcesUserAddToCartBo resourcesUserAddToCartBo){
					return AjaxResult.fail("用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)新增失败");
				}

				public AjaxResult updateResourcesUserAddToCart(ResourcesUserAddToCartBo resourcesUserAddToCartBo){
					return AjaxResult.fail("用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)删除失败");
				}

				@Override
				public AjaxResult deleteQuestion(ResourcesUserAddToCartBo resourcesUserAddToCartBo) {
					return AjaxResult.fail("根据题目类型删除失败");

				}
			};
		}
	}
}