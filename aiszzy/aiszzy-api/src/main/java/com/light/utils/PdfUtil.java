package com.light.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.light.core.exception.WarningException;
import com.light.core.utils.StringUtils;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * PDF 工具类
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
public class PdfUtil {

    /**
     * 解析 pdf 文件 2 图片 map {key: 页码，value ： 图片 base64}
     *
     * @param inputStream the input stream pdf 流
     * @return {@link Map }<{@link String }, {@link String }>
     */
    public static Map<String, String> partitionPdf2ImgBase64Map(InputStream inputStream) {
        Map<String, String> result = Maps.newHashMap();
        try (PDDocument document = PDDocument.load(inputStream)) {

            // 创建 PDF 渲染器
            PDFRenderer pdfRenderer = new PDFRenderer(document);

            // 遍历每一页，将其转换为图片
            int numberOfPages = document.getNumberOfPages();
            if (numberOfPages == 0) {
                throw new WarningException("PDF文件解析失败，未解析到数据");
            }
            for (int page = 0; page < numberOfPages; page++) {
                // 渲染 PDF 页面为 BufferedImage
                BufferedImage image = pdfRenderer.renderImageWithDPI(page, 300, ImageType.RGB);
                // 保存图片到本地
                try (ByteArrayOutputStream output = new ByteArrayOutputStream();) {
                    ImageIO.write(image, "JPEG", output);
                    result.put((page + 1) + ".jpg", Base64.getEncoder().encodeToString(output.toByteArray()));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    public static void mergePDFs(List<String> pdfUrls, ByteArrayOutputStream outputFile) {
        try {
            // 使用PDFMergerUtility来合并PDF文件
            PDFMergerUtility merger = new PDFMergerUtility();
            merger.setDestinationStream(outputFile);
            for (String pdfUrl : pdfUrls) {
                if (StringUtils.isNotEmpty(pdfUrl)) {
                    merger.addSource(new ByteArrayInputStream(HttpUtil.downloadBytes(pdfUrl)));
                }
            }
            merger.mergeDocuments(null);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static ByteArrayOutputStream mergeBytePDFs(List<byte[]> bytes) {
        try {
            ByteArrayOutputStream outputFile = new ByteArrayOutputStream();
            // 使用PDFMergerUtility来合并PDF文件
            PDFMergerUtility merger = new PDFMergerUtility();
            merger.setDestinationStream(outputFile);
            for (byte[] by : bytes) {
                if (by != null) {
                    merger.addSource(new ByteArrayInputStream(by));
                }
            }
            merger.mergeDocuments(null);
            return outputFile;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }



}
