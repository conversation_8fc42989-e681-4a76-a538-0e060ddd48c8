package com.light.aiszzy.homeworkResult.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本作业疑问表（影子表，处理作业整张卷子的疑问项）
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_result_doubt")
public class HomeworkResultDoubtDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 学校id
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 班级id
	 */
	@TableField("class_id")
	private String classId;

	/**
	 * 学生oid
	 */
	@TableField("stu_oid")
	private String stuOid;

	/**
	 * 学生作答存储地址
	 */
	@TableField("stu_answer_url")
	private String stuAnswerUrl;

	/**
	 * 是否有疑问 0：无，1：存在
	 */
	@TableField("is_doubt")
	private Long isDoubt;

	/**
	 * 1学号没有，2重复学号，3作业页数不全
	 */
	@TableField("doubt_type")
	private Integer doubtType;

	/**
	 * 学生作答时间
	 */
	@TableField("student_answer_time")
	private Long studentAnswerTime;

	/**
	 * 重复id（记录与哪张卷子重复）
	 */
	@TableField("repeat_id")
	private String repeatId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
