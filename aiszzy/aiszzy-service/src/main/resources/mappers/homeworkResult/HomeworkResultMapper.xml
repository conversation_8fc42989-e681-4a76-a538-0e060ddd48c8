<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkResultMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="classId" column="class_id"/>
        <result property="stuOid" column="stu_oid"/>
        <result property="stuName" column="stu_name"/>
        <result property="pageNos" column="page_nos"/>
        <result property="orcStuNames" column="orc_stu_names"/>
        <result property="ocrStuNos" column="ocr_stu_nos"/>
        <result property="stuAnswerUrls" column="stu_answer_urls"/>
        <result property="stuAnswerCorrectResult" column="stu_answer_correct_result"/>
        <result property="stuAnswerCorrectResultPrint" column="stu_answer_correct_result_print"/>
        <result property="stuAnswerPageInfoJson" column="stu_answer_page_info_json"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkResultListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo">
		select t.* from (
			select a.* from homework_result as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="classId != null and classId != ''">and class_id = #{classId}</if>
				<if test="stuOid != null and stuOid != ''">and stu_oid = #{stuOid}</if>
				<if test="stuName != null and stuName != ''">and stu_name = #{stuName}</if>
				<if test="pageNos != null and pageNos != ''">and page_nos = #{pageNos}</if>
				<if test="orcStuNames != null and orcStuNames != ''">and orc_stu_names = #{orcStuNames}</if>
				<if test="ocrStuNos != null and ocrStuNos != ''">and ocr_stu_nos = #{ocrStuNos}</if>
				<if test="stuAnswerUrls != null and stuAnswerUrls != ''">and stu_answer_urls = #{stuAnswerUrls}</if>
				<if test="stuAnswerCorrectResult != null and stuAnswerCorrectResult != ''">and stu_answer_correct_result = #{stuAnswerCorrectResult}</if>
				<if test="stuAnswerCorrectResultPrint != null and stuAnswerCorrectResultPrint != ''">and stu_answer_correct_result_print = #{stuAnswerCorrectResultPrint}</if>
				<if test="stuAnswerPageInfoJson != null and stuAnswerPageInfoJson != ''">and stu_answer_page_info_json = #{stuAnswerPageInfoJson}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>