<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.practiceBook.mapper.PracticeBookQuestionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.practiceBook.entity.dto.PracticeBookQuestionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="practiceBookOid" column="practice_book_oid"/>
        <result property="practiceBookCatalogOid" column="practice_book_catalog_oid"/>
        <result property="practiceBookPageOid" column="practice_book_page_oid"/>
        <result property="questionOid" column="question_oid"/>
        <result property="thirdSourceType" column="third_source_type"/>
        <result property="thirdOutId" column="third_out_id"/>
        <result property="imageUrl" column="image_url"/>
        <result property="pageNo" column="page_no"/>
        <result property="pageNoOrderNum" column="page_no_order_num"/>
        <result property="markStatus" column="mark_status"/>
        <result property="questionContentJson" column="question_content_json"/>
        <result property="outQuestionRelationType" column="out_question_relation_type"/>
        <result property="outQuestionRelationTypeStatus" column="out_question_relation_type_status"/>
        <result property="ocrContent" column="ocr_content"/>
        <result property="similarity" column="similarity"/>
        <result property="isQuestionModify" column="is_question_modify"/>
        <result property="isCrossPage" column="is_cross_page"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getPracticeBookQuestionListByCondition" resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo">
		select t.* from (
		select
		`id` ,
		`oid` ,
		`practice_book_oid`,
		`practice_book_catalog_oid` ,
		`practice_book_page_oid` ,
		`question_oid` ,
		`third_source_type` ,
		`third_out_id` ,
		`image_url` ,
		`page_no` ,
		`mark_status` ,
		`out_question_relation_type` ,
		`out_question_relation_type_status` ,
		`ocr_content` ,
		`similarity` ,
		`is_question_modify` ,
		`is_cross_page` ,
		`create_time` ,
		`update_time`,
		`create_by` ,
		`update_by`,
		is_delete
		from practice_book_question as a
		) t
		<where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="practiceBookOid != null and practiceBookOid != ''">and practice_book_oid = #{practiceBookOid}</if>
				<if test="practiceBookCatalogOid != null and practiceBookCatalogOid != ''">and practice_book_catalog_oid = #{practiceBookCatalogOid}</if>
				<if test="practiceBookPageOid != null and practiceBookPageOid != ''">and practice_book_page_oid = #{practiceBookPageOid}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="thirdSourceType != null and thirdSourceType != ''">and third_source_type = #{thirdSourceType}</if>
				<if test="thirdOutId != null and thirdOutId != ''">and third_out_id = #{thirdOutId}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="pageNo != null">and page_no = #{pageNo}</if>
				<if test="pageNoOrderNum != null">and page_no_order_num = #{pageNoOrderNum}</if>
				<if test="markStatus != null">and mark_status = #{markStatus}</if>
				<if test="questionContentJson != null and questionContentJson != ''">and question_content_json = #{questionContentJson}</if>
				<if test="outQuestionRelationType != null and outQuestionRelationType != ''">and out_question_relation_type = #{outQuestionRelationType}</if>
				<if test="outQuestionRelationTypeStatus != null">and out_question_relation_type_status = #{outQuestionRelationTypeStatus}</if>
				<if test="ocrContent != null and ocrContent != ''">and ocr_content = #{ocrContent}</if>
				<if test="similarity != null and similarity != ''">and similarity = #{similarity}</if>
				<if test="isQuestionModify != null">and is_question_modify = #{isQuestionModify}</if>
				<if test="isCrossPage != null">and is_cross_page = #{isCrossPage}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="getPracticeBookQuestionWithBodyListByCondition" resultType="com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo">
		select t.* from (
		select
		a.`id` ,
		a.`oid` ,
		a.`practice_book_oid`,
		a.`practice_book_catalog_oid` ,
		a.`practice_book_page_oid` ,
		a.`question_oid` ,
		a.`third_source_type` ,
		a.`third_out_id` ,
		a.`image_url` ,
		a.`page_no` ,
		a.`mark_status` ,
		a.`out_question_relation_type` ,
		a.`out_question_relation_type_status` ,
		a.`ocr_content` ,
		a.`similarity` ,
		a.`is_question_modify` ,
		a.`is_cross_page` ,
		a.`create_time` ,
		a.`update_time`,
		a.`create_by` ,
		a.`update_by`,
		a.is_delete,

		b.oid as `questionVo.oid`,
		b.question_type_id as `questionVo.questionTypeId`,
		b.question_type_name as `questionVo.questionTypeName`,
		b.subject as `questionVo.subject`,
		b.grade as `questionVo.grade`,
		b.difficult_id as `questionVo.difficultId`,
		b.ques_body as `questionVo.quesBody`,
		b.public_ques as `questionVo.publicQues`,
-- 		b.ques_answer as `questionVo.quesAnswer`,
-- 		b.analysis_answer as `questionVo.analysisAnswer`,
		b.ques_body_type as `questionVo.quesBodyType`,
-- 		b.public_ques_type as `questionVo.publicQuesType`,
-- 		b.ques_answer_type as `questionVo.quesAnswerType`,
-- 		b.analysis_answer_type as `questionVo.analysisAnswerType`,
		b.third_source_type as `questionVo.thirdSourceType`,
		b.third_out_id as `questionVo.thirdOutId`,
		b.knowledge_points_id as `questionVo.knowledgePointsId`,
		b.chapter_id as `questionVo.chapterId`,
		b.section_id as `questionVo.sectionId`,
		b.inside_source_type as `questionVo.insideSourceType`,
		b.inside_link_oid as `questionVo.insideLinkOid`
-- 		b.similar_recommend_result as `questionVo.similarRecommendResult`

		from practice_book_question as a
		left join question b on a.question_oid = b.oid
		) t
		<where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="practiceBookOid != null and practiceBookOid != ''">and practice_book_oid = #{practiceBookOid}</if>
				<if test="practiceBookCatalogOid != null and practiceBookCatalogOid != ''">and practice_book_catalog_oid = #{practiceBookCatalogOid}</if>
				<if test="practiceBookPageOid != null and practiceBookPageOid != ''">and practice_book_page_oid = #{practiceBookPageOid}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="thirdSourceType != null and thirdSourceType != ''">and third_source_type = #{thirdSourceType}</if>
				<if test="thirdOutId != null and thirdOutId != ''">and third_out_id = #{thirdOutId}</if>
				<if test="imageUrl != null and imageUrl != ''">and image_url = #{imageUrl}</if>
				<if test="pageNo != null">and page_no = #{pageNo}</if>
				<if test="pageNoOrderNum != null">and page_no_order_num = #{pageNoOrderNum}</if>
				<if test="markStatus != null">and mark_status = #{markStatus}</if>
				<if test="questionContentJson != null and questionContentJson != ''">and question_content_json = #{questionContentJson}</if>
				<if test="outQuestionRelationType != null and outQuestionRelationType != ''">and out_question_relation_type = #{outQuestionRelationType}</if>
				<if test="outQuestionRelationTypeStatus != null">and out_question_relation_type_status = #{outQuestionRelationTypeStatus}</if>
				<if test="ocrContent != null and ocrContent != ''">and ocr_content = #{ocrContent}</if>
				<if test="similarity != null and similarity != ''">and similarity = #{similarity}</if>
				<if test="isQuestionModify != null">and is_question_modify = #{isQuestionModify}</if>
				<if test="isCrossPage != null">and is_cross_page = #{isCrossPage}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

    <select id="selectMaxOrderByPracticeBookPageOid" resultType="java.lang.Integer">
		select ifnull(max(page_no_order_num), 0) from practice_book_question where is_delete = 0 and practice_book_page_oid = #{practiceBookPageOid}
	</select>

    <delete id="batchUpdateByOid">
		<foreach collection="questionList" item="item" separator=";">
			UPDATE practice_book_question
			SET  page_no_order_num = #{item.pageNoOrderNum}
			WHERE oid = #{item.oid}
		</foreach>

    </delete>
</mapper>