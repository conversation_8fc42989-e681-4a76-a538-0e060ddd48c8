<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.schoolBook.mapper.SchoolBookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.schoolBook.entity.dto.SchoolBookDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="orgAreaName" column="org_area_name"/>
        <result property="orgAreaCode" column="org_area_code"/>
        <result property="orgCityName" column="org_city_name"/>
        <result property="orgCityCode" column="org_city_code"/>
        <result property="orgProvinceName" column="org_province_name"/>
        <result property="orgProvinceCode" column="org_province_code"/>
        <result property="bookOid" column="book_oid"/>
        <result property="status" column="status"/>
        <result property="startDate" column="start_date"/>
        <result property="bookType" column="book_type"/>
        <result property="endDate" column="end_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getSchoolBookListByCondition" resultType="com.light.aiszzy.schoolBook.entity.vo.SchoolBookVo">
		select t.*, 
			CASE 
				WHEN t.status = 1 THEN 1  -- 启用
				WHEN t.status = 2 THEN 2  -- 停用
				WHEN t.status = 1 AND CURDATE() BETWEEN t.start_date AND t.end_date THEN 3  -- 使用中
				WHEN t.status = 1 AND CURDATE() > t.end_date THEN 5  -- 已到期
				ELSE t.status
			END as computedStatus
		from (
			select a.* from school_book as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
			<if test="oid != null and oid != ''">and oid = #{oid}</if>
			<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
			<if test="orgName != null and orgName != ''">and org_name = #{orgName}</if>
			<if test="orgAreaName != null and orgAreaName != ''">and org_area_name = #{orgAreaName}</if>
			<if test="orgAreaCode != null and orgAreaCode != ''">and org_area_code = #{orgAreaCode}</if>
			<if test="orgCityName != null and orgCityName != ''">and org_city_name = #{orgCityName}</if>
			<if test="orgCityCode != null and orgCityCode != ''">and org_city_code = #{orgCityCode}</if>
			<if test="orgProvinceName != null and orgProvinceName != ''">and org_province_name = #{orgProvinceName}</if>
			<if test="orgProvinceCode != null and orgProvinceCode != ''">and org_province_code = #{orgProvinceCode}</if>
			<if test="bookOid != null and bookOid != ''">and book_oid = #{bookOid}</if>
			<if test="status != null">and status = #{status}</if>
			<if test="startDate != null">and start_date = #{startDate}</if>
			<if test="endDate != null">and end_date = #{endDate}</if>
			<if test="bookType != null and bookType != ''">and book_type = #{bookType}</if>
			<if test="createTime != null">and create_time = #{createTime}</if>
			<if test="updateTime != null">and update_time = #{updateTime}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
			<if test="isDelete != null">and is_delete = #{isDelete}</if>
				<!-- 状态字段查询逻辑 -->
				<if test="queryStatus != null">
					<choose>
						<when test="queryStatus == 1">
							and t.status = 1
						</when>
						<when test="queryStatus == 2">
							and t.status = 2
						</when>
						<when test="queryStatus == 3">
							and t.status = 1 AND CURDATE() BETWEEN t.start_date AND t.end_date
						</when>
						<when test="queryStatus == 5">
							and t.status = 1 AND CURDATE() > t.end_date
						</when>
					</choose>
				</if>
		    </where>
		ORDER BY t.create_time DESC
	</select>

	<!-- 批量查询教辅的总开通学校数量 -->
	<select id="getTotalSchoolCountByPracticeBookOids" resultType="java.util.Map">
		SELECT 
			book_oid as practiceBookOid,
			COUNT(DISTINCT org_code) as schoolCount
		FROM school_book
		WHERE is_delete = 0
		AND status = 1
		AND book_oid IN
		<foreach item="oid" collection="practiceBookOids" open="(" separator="," close=")">
			#{oid}
		</foreach>
		GROUP BY book_oid
	</select>

	<!-- 批量查询教辅的活跃学校数量（有效期内） -->
	<select id="getActiveSchoolCountByPracticeBookOids" resultType="java.util.Map">
		SELECT
			book_oid as practiceBookOid,
			COUNT(DISTINCT org_code) as schoolCount
		FROM school_book
		WHERE is_delete = 0
		AND status = 1
		AND start_date &lt;= CURDATE()
		AND end_date &gt;= CURDATE()
		AND book_oid IN
		<foreach item="oid" collection="practiceBookOids" open="(" separator="," close=")">
			#{oid}
		</foreach>
		GROUP BY book_oid
	</select>

		<!-- 查询教辅列表与学校开通信息关联 -->
		<select id="getPracticeBookWithSchoolListByCondition" resultType="com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo">
			SELECT
				-- 教辅信息字段
				pb.id,
				pb.oid,
				pb.name,
				pb.cover_image as coverImage,
				pb.description,
				pb.publisher,
				pb.subject,
				pb.grade,
				pb.isbn,
				pb.text_book_version_id as textBookVersionId,
				pb.year,
				pb.term,
				pb.series,
				pb.category,
				pb.visibility,
				pb.file_url as fileUrl,
				pb.file_type as fileType,
				pb.is_marketization as isMarketization,
				pb.is_high_shots as isHighShots,
				pb.catalog_file_path as catalogFilePath,
				pb.catalog_status as catalogStatus,
				pb.review_status as reviewStatus,
				pb.review_comment as reviewComment,
				pb.status,
				pb.remark,
				pb.finish_question_num as finishQuestionNum,
				pb.total_question_num as totalQuestionNum,
				pb.xkw_zs_id as xkwZsId,
				pb.create_time as createTime,
				pb.update_time as updateTime,
				pb.create_by as createBy,
				pb.update_by as updateBy,
				pb.is_delete as isDelete,
				-- 学校开通信息字段
				sb.id as schoolBookId,
				sb.oid as schoolBookOid,
				sb.org_code as orgCode,
				sb.org_name as orgName,
				sb.org_area_name as orgAreaName,
				sb.org_area_code as orgAreaCode,
				sb.org_city_name as orgCityName,
				sb.org_city_code as orgCityCode,
				sb.org_province_name as orgProvinceName,
				sb.org_province_code as orgProvinceCode,
				sb.status as schoolBookStatus,
				sb.start_date as startDate,
				sb.end_date as endDate,
				sb.book_type as bookType,
				sb.create_time as schoolBookCreateTime,
				sb.update_time as schoolBookUpdateTime,
				sb.create_by as schoolBookCreateBy,
				sb.update_by as schoolBookUpdateBy,
				-- 计算状态
				CASE
					WHEN sb.status = 1 THEN 1  -- 启用
					WHEN sb.status = 2 THEN 2  -- 停用
					WHEN sb.status = 1 AND CURDATE() BETWEEN sb.start_date AND sb.end_date THEN 3  -- 使用中
					WHEN sb.status = 1 AND CURDATE() > sb.end_date THEN 5  -- 已到期
					ELSE sb.status
				END as computedStatus
			FROM practice_book pb
			INNER JOIN school_book sb ON pb.oid = sb.book_oid
			<where>
				pb.is_delete = 0
				AND sb.is_delete = 0
				<if test="orgCode != null and orgCode != ''">AND sb.org_code = #{orgCode}</if>
				<if test="bookOid != null and bookOid != ''">AND sb.book_oid = #{bookOid}</if>
				<if test="orgName != null and orgName != ''">AND sb.org_name LIKE CONCAT('%', #{orgName}, '%')</if>
				<if test="practiceBookName != null and practiceBookName != ''">AND pb.name LIKE CONCAT('%', #{practiceBookName}, '%')</if>
				<if test="practiceBookSubject != null">AND pb.subject = #{practiceBookSubject}</if>
				<if test="practiceBookGrade != null">AND pb.grade = #{practiceBookGrade}</if>
				<if test="status != null">AND sb.status = #{status}</if>
				<if test="startDate != null">AND sb.start_date = #{startDate}</if>
				<if test="endDate != null">AND sb.end_date = #{endDate}</if>
				<if test="bookType != null and bookType != ''">AND sb.book_type = #{bookType}</if>
				<!-- 状态字段查询逻辑 -->
				<if test="queryStatus != null">
					<choose>
						<when test="queryStatus == 1">
							AND sb.status = 1
						</when>
						<when test="queryStatus == 2">
							AND sb.status = 2
						</when>
						<when test="queryStatus == 3">
							AND sb.status = 1 AND CURDATE() BETWEEN sb.start_date AND sb.end_date
						</when>
						<when test="queryStatus == 5">
							AND sb.status = 1 AND CURDATE() > sb.end_date
						</when>
					</choose>
				</if>
				<if test="practiceBookStatus != null">
					AND pb.status=#{practiceBookStatus}
				</if>
			    <if test="keywords != null and keywords != ''">
					AND pb.name LIKE CONCAT('%', #{keywords}, '%')
				</if>
			</where>
			ORDER BY sb.create_time DESC
		</select>
</mapper>