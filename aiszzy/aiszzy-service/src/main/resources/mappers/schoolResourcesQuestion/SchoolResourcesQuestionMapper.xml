<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.schoolResourcesQuestion.mapper.SchoolResourcesQuestionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.schoolResourcesQuestion.entity.dto.SchoolResourcesQuestionDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="questionTypeId" column="question_type_id"/>
        <result property="questionTypeName" column="question_type_name"/>
        <result property="subject" column="subject"/>
        <result property="year" column="year"/>
        <result property="grade" column="grade"/>
        <result property="difficultId" column="difficult_id"/>
        <result property="quesBody" column="ques_body"/>
        <result property="publicQues" column="public_ques"/>
        <result property="quesAnswer" column="ques_answer"/>
        <result property="analysisAnswer" column="analysis_answer"/>
        <result property="quesBodyType" column="ques_body_type"/>
        <result property="publicQuesType" column="public_ques_type"/>
        <result property="quesAnswerType" column="ques_answer_type"/>
        <result property="analysisAnswerType" column="analysis_answer_type"/>
        <result property="knowledgePointsId" column="knowledge_points_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="sectionId" column="section_id"/>
        <result property="thirdSourceType" column="third_source_type"/>
        <result property="thirdOutId" column="third_out_id"/>
        <result property="questionOid" column="question_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getSchoolResourcesQuestionListByCondition" resultType="com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo">
		select t.* from (
			select a.*,up.is_publish,up.publish_time, up.user_oid,up.grade user_paper_grade,up.`subject` user_paper_subject from school_resources_question as a
			left join user_paper_question upq on a.oid = upq.school_resource_question_oid
			left join user_paper up on upq.user_paper_oid = up.oid
		) t
	    <where>
			and is_delete = 0
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="questionTypeId != null and questionTypeId != ''">and question_type_id = #{questionTypeId}</if>
				<if test="questionTypeName != null and questionTypeName != ''">and question_type_name = #{questionTypeName}</if>
<!--				<if test="subject != null">and subject = #{subject}</if>-->
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="difficultId != null">and difficult_id = #{difficultId}</if>
				<if test="quesBody != null and quesBody != ''">and ques_body = #{quesBody}</if>
				<if test="publicQues != null and publicQues != ''">and public_ques = #{publicQues}</if>
				<if test="quesAnswer != null and quesAnswer != ''">and ques_answer = #{quesAnswer}</if>
				<if test="analysisAnswer != null and analysisAnswer != ''">and analysis_answer = #{analysisAnswer}</if>
				<if test="quesBodyType != null">and ques_body_type = #{quesBodyType}</if>
				<if test="publicQuesType != null">and public_ques_type = #{publicQuesType}</if>
				<if test="quesAnswerType != null">and ques_answer_type = #{quesAnswerType}</if>
				<if test="analysisAnswerType != null">and analysis_answer_type = #{analysisAnswerType}</if>
				<if test="knowledgePointsId != null and knowledgePointsId != ''">and knowledge_points_id = #{knowledgePointsId}</if>
				<if test="chapterId != null and chapterId != ''">and chapter_id = #{chapterId}</if>
				<if test="sectionId != null and sectionId != ''">and section_id = #{sectionId}</if>
				<if test="thirdSourceType != null and thirdSourceType != ''">and third_source_type = #{thirdSourceType}</if>
				<if test="thirdOutId != null and thirdOutId != ''">and third_out_id = #{thirdOutId}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="userPaperIsPublish != null">
					and is_publish = #{userPaperIsPublish}
				</if>
				<if test="userPaperUserOid != null and userPaperUserOid != ''">
					and user_oid = #{userPaperUserOid}
				</if>
				<if test="subject != null and subject != ''">
					and user_paper_subject = #{subject}
				</if>
				<if test="gradeList != null">
					and user_paper_grade in
					<foreach collection="gradeList" open="(" close=")" item="item" separator=",">
						#{item}
					</foreach>
				</if>
				<if test="publishStartTime != null and publishStartTime != ''">
					and  publish_time &gt;= #{publishStartTime}
				</if>
				<if test="publishEndTime != null and publishEndTime != ''">
					and  publish_time &lt;= #{publishEndTime}
				</if>
		    </where>
			order by publish_time desc
	</select>

	<select id="selectByOid" resultType="com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo">
		select * from school_resources_question where is_delete = 0 and oid = #{oid}
	</select>
</mapper>