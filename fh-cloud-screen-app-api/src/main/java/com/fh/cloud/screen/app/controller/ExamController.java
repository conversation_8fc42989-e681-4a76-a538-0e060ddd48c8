package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.er.api.ExamInfoApi;
import com.fh.cloud.screen.service.er.api.ExamInfoStudentApi;
import com.fh.cloud.screen.service.er.api.ExamInfoSubjectApi;
import com.fh.cloud.screen.service.er.entity.bo.*;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/29
 */
@Api(value = "", tags = "考试接口")
@RestController
@RequestMapping("/exam")
public class ExamController {
    @Resource
    private ExamInfoStudentApi examInfoStudentApi;
    @Resource
    private ExamInfoSubjectApi examInfoSubjectApi;

    /**
     * 考试打卡，需要参数：考试时间，考试科目关系表id，考试学生oid
     * 
     * @param examInfoStudentBo
     * @return
     */
    @PostMapping("/student/sign")
    @ApiOperation(value = "考试打卡", httpMethod = "POST")
    public AjaxResult examSign(@RequestBody ExamInfoStudentBo examInfoStudentBo) {
        return examInfoStudentApi.examSign(examInfoStudentBo);
    }

    /**
     * 获取当前考试
     * 
     * @param conditionBo
     * @return
     */
    @PostMapping("/now")
    @AccessTokenAuth
    @ApiOperation(value = "获取当前考试", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getNowExamInfoByCondition(@RequestBody ExamInfoSubjectConditionBo conditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        conditionBo.setSpaceGroupUseType(jwtModel.getSpaceGroupUseType());
        conditionBo.setSpaceInfoId(jwtModel.getSpaceInfoId());
        if (null == conditionBo.getExamStartTime()) {
            conditionBo.setExamStartTime(new Date());
        }
        return examInfoSubjectApi.getNowExamInfoByCondition(conditionBo);
    }

    /**
     * 一次考试、考试地点、科目的签到信息-缓存
     *
     * @param examInfoSubjectId the exam info subject id
     * @return the detail cache by meeting id
     * <AUTHOR>
     * @date 2023 -10-12 17:35:33
     */
    @GetMapping("/detail-cache")
    @ApiOperation(value = "考试签到详情", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getDetailCacheByExamInfoSubjectId(@RequestParam("examInfoSubjectId") Long examInfoSubjectId) {
        if (null == examInfoSubjectId) {
            return AjaxResult.fail("科目关系id不可以为空");
        }
        // sunqbtodo 需要根据考试科目关系id获取考试id，考试地点id，科目id查询缓存的信息，具体要结合前端已有数据结构
        return null;
    }
}
