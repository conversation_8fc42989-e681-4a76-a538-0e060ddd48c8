package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.app.constant.ConstantsInteger;
import com.fh.cloud.screen.app.enums.SpaceGroupUseTypeEnums;
import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.service.SpaceInfoApiService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 区域信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Api(tags = "区域信息")
@RequestMapping("space/info")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class SpaceInfoApiController {

    private final SpaceInfoApiService spaceInfoApiService;

    /**
     * 查询非区域信息表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     * @param condition { spaceGroupId , campusId , organizationId }
     */
    @PostMapping("list")
    @ApiOperation(value = "查询非区域信息表列表", httpMethod = "POST")
    public AjaxResult getSpaceInfoListByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setSpaceGroupUseType(SpaceGroupUseTypeEnums.NOT_EXECUTIVE.getVal());
        return this.spaceInfoApiService.getSpaceInfoListByCondition(condition);
    }

    /**
     * 查询行政区域信息表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     * @param condition { spaceGroupId , campusId , organizationId}
     */
    @PostMapping("class/list")
    @ApiOperation(value = "查询行政区域信息表列表", httpMethod = "POST")
    public AjaxResult getClassInfoList(@RequestBody SpaceInfoListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setSpaceGroupUseType(SpaceGroupUseTypeEnums.EXECUTIVE.getVal());
        condition.setQueryType(ConstantsInteger.APP_QUERY);
        return this.spaceInfoApiService.queryList(condition);
    }

    /**
     * 获取地点详细信息
     *
     * @return space info name
     */
    @AccessTokenAuth
    @PostMapping("/detail")
    @ApiOperation(value = "查询地点详情", httpMethod = "POST")
    public AjaxResult getSpaceInfoDetail() {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        return this.spaceInfoApiService.getSpaceInfo(screenBusinessBo);
    }

}
