package com.fh.cloud.screen.app.jwt.interceptor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.enums.AuthExceptionEnums;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.app.jwt.util.JwtUtil;
import com.fh.cloud.screen.app.jwt.util.ResponseUtils;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/12 3:47 下午 @description：
 */
@Component
public class JwtHandlerInterceptor implements HandlerInterceptor {

    private final static String tokenKey = "X-token-key";

    @Autowired
    private RedisComponent redisComponent;

    private final static String CLASS_NAME_KEY = "cache:class:name:";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        // 验证匹配:没加@AccessTokenAuth 直接跳过，不验证token
        if (match((HandlerMethod)handler)) {
            return true;
        }
        // 解析token
        final String token = request.getHeader(tokenKey);
        final AuthExceptionEnums unAuth = AuthExceptionEnums.UN_AUTH;
        if (StrUtil.isEmpty(token)) {
            ResponseUtils.responseJson(response, AjaxResult.fail(unAuth.getCode(), unAuth.getMsg()));
            return false;
        }

        Boolean tokenExpired = true;
        try {
            tokenExpired = JwtUtil.isTokenExpired(token);
        } catch (Exception e) {
        }

        if (tokenExpired) {
            ResponseUtils.responseJson(response, AjaxResult.fail(unAuth.getCode(), unAuth.getMsg()));
            return false;
        }

        Map<String, Object> map = new HashMap<>();
        final Claims claims = JwtUtil.extractAllClaims(token);
        final List<String> fieldNames = getFieldNames();
        fieldNames.stream().forEach(val -> map.put(val, claims.get(val)));

        JwtModel jwtModel = BeanUtil.mapToBean(map, JwtModel.class, true);
        JwtContextHandler.set(jwtModel);

        // 业务方使用：JwtContextHandler.get()拿到数据

        return true;
    }

    /**
     * 匹配认证
     * 
     * @param handlerMethod
     * @return
     */
    private boolean match(HandlerMethod handlerMethod) {
        AccessTokenAuth annotation = handlerMethod.getMethodAnnotation(AccessTokenAuth.class);
        return annotation == null;
    }

    /**
     * 获取属性名称
     * 
     * @return
     */
    private List<String> getFieldNames() {

        // 缓存处理
        final Class<JwtModel> jwtModelClass = JwtModel.class;
        final String name = jwtModelClass.getName();
        final Set<Object> objects = this.redisComponent.sGet(CLASS_NAME_KEY + name);
        if (CollectionUtil.isNotEmpty(objects)) {
            return objects.stream().map(Object::toString).collect(Collectors.toList());
        }

        // 反射获取 并存储缓存
        final Field[] declaredFields = ReflectUtil.getFields(jwtModelClass);
        final List<String> fieldNames = Arrays.stream(declaredFields).map(Field::getName).collect(Collectors.toList());
        final String[] strings = fieldNames.toArray(new String[fieldNames.size()]);
        this.redisComponent.sSet(CLASS_NAME_KEY + name, strings);

        return fieldNames;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
        @Nullable Exception ex) throws Exception {
        JwtContextHandler.remove();
    }
}
