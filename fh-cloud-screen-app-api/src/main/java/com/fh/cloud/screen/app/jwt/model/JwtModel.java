package com.fh.cloud.screen.app.jwt.model;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/12 3:56 下午
 * @description：针对具体业务生成的JWT MODEL 不通用
 */
@Data
public class JwtModel {

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 空间或者classesId
     */
    private Long spaceInfoId;

    /**
     * 区域分组使用类型：行政或者非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 对象转换
     *
     * @param jwtModel the jwt model
     * @return screen business bo
     * <AUTHOR>
     * @date 2022 -06-08 09:30:38
     */
    public static ScreenBusinessBo convert2ScreenBusinessBo(JwtModel jwtModel) {
        if (jwtModel == null) {
            return null;
        }
        ScreenBusinessBo screenBusinessBo = new ScreenBusinessBo();
        screenBusinessBo.setCampusId(jwtModel.getCampusId());
        screenBusinessBo.setDeviceNumber(jwtModel.getDeviceNumber());
        screenBusinessBo.setOrganizationId(jwtModel.getOrganizationId());
        screenBusinessBo.setSpaceGroupUseType(jwtModel.getSpaceGroupUseType());
        screenBusinessBo.setSpaceInfoId(jwtModel.getSpaceInfoId());
        return screenBusinessBo;
    }

}
