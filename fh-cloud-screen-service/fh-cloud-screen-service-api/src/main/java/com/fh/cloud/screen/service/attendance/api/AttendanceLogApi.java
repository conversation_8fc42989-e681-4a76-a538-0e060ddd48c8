package com.fh.cloud.screen.service.attendance.api;

import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface AttendanceLogApi {
    /**
     * 查询考勤流水表，不用于业务查询列表
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/attendance-log/list")
    @ApiOperation(value = "查询考勤流水表，不用于业务查询列表", httpMethod = "POST")
    AjaxResult getAttendanceLogListByCondition(@RequestBody AttendanceLogListConditionBo condition);

    /**
     * 新增考勤流水表，不用于业务查询
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/attendance-log/add")
    @ApiOperation(value = "新增考勤流水表，不用于业务查询", httpMethod = "POST")
    AjaxResult addAttendanceLog(@RequestBody AttendanceLogBo attendanceLogBo);

    /**
     * 修改考勤流水表，不用于业务查询
     *
     * @param attendanceLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/attendance-log/update")
    @ApiOperation(value = "修改考勤流水表，不用于业务查询", httpMethod = "POST")
    AjaxResult updateAttendanceLog(@RequestBody AttendanceLogBo attendanceLogBo);

    /**
     * 查询考勤流水表，不用于业务查询详情
     *
     * @param attendanceLogId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/attendance-log/detail")
    @ApiOperation(value = "查询考勤流水表，不用于业务查询详情", httpMethod = "GET")
    AjaxResult getDetail(@RequestParam("attendanceLogId") Long attendanceLogId);

    /**
     * 删除考勤流水表，不用于业务查询
     *
     * @param attendanceLogId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/attendance-log/delete")
    @ApiOperation(value = "删除考勤流水表，不用于业务查询", httpMethod = "GET")
    AjaxResult delete(@RequestParam("attendanceLogId") Long attendanceLogId);

    /**
     * 根据班级ID、日期获取学生历史考勤统计信息
     *
     * @param classesId the classesId 班级ID
     * @param dateTime the dateTime yyyy-MM-dd HH:mm:ss 日期
     * @return
     */
    @GetMapping("/attendance-log/student-census/class-date")
    AjaxResult getStudentCensusByClassesId(@RequestParam("classesId") Long classesId,
        @RequestParam("dateTime") String dateTime);

    /**
     * 根据学校ID、日期 获取老师考勤统计信息
     *
     * @param orgId the orgId 学校ID
     * @param cacheKey the student clock attendance cache key 老师打卡考勤信息key
     * @return
     */
    @GetMapping("/attendance-log/clock-teacher-census")
    AjaxResult<AttendanceLogCensusVo> getClockTeacherCensusByOrgId(@RequestParam("orgId") Long orgId,
        @RequestParam("cacheKey") String cacheKey);

    /**
     * 根据班级打卡Key获取班级当天考勤统计信息
     * 
     * @param classesId
     * @param cacheKey
     * @return
     */
    @GetMapping("/attendance-log/clock-student-census")
    AjaxResult<AttendanceDayCensusVo> getClockStudentCensusByClassesId(
        @RequestParam("organizationId") Long organizationId, @RequestParam("classesId") Long classesId);

    /**
     * 根据班级和日期预先插入班级用户当天考勤数据（全部正常签到）
     *
     * @param classesId, date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    @GetMapping("/attendance-log/pre-insert-student")
    AjaxResult preInsertAttendanceLogByClass(@RequestParam("classesId") Long classesId,
        @RequestParam("date") String date);

    /**
     * 根据组织id和日期预先插入全部教师当天考勤数据（全部正常签到）
     *
     * @param organizationId, date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    @GetMapping("/attendance-log/pre-insert-teacher")
    AjaxResult preInsertAttendanceLogByTeacher(@RequestParam("organizationId") Long organizationId,
        @RequestParam("date") String date);
}
