package com.fh.cloud.screen.service.attendance.api;

import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;

public interface AttendanceRuleApi {

    /**
     * 新增或修改考勤规则表
     *
     * @param attendanceRuleAddBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/6 16:07
     */
    @PostMapping("/attendance-rule/save-or-update")
    AjaxResult saveOrUpdateAttendanceRule(@Validated @RequestBody AttendanceRuleAddBo attendanceRuleAddBo);

    @PostMapping("/attendance-rule/detail")
    AjaxResult getDetail(@RequestBody AttendanceRuleBo attendanceRuleBo);

    /**
     * 获取靠近当天时间的考勤规则时间
     *
     * @param attendanceRuleBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/16 16:03
     */
    @PostMapping("/attendance-rule/getDate")
    AjaxResult getDate(@RequestBody AttendanceRuleBo attendanceRuleBo);

    /**
     * app查询缓存中的考勤规则
     *
     * @param attendanceRuleBo 根据组织id查询，必填
     * @return com.light.core.entity.AjaxResult 包括考勤时间
     * <AUTHOR>
     * @date 2022/6/8 15:04
     */
    @PostMapping("/attendance-rule/info")
    AjaxResult getInfo(@RequestBody AttendanceRuleBo attendanceRuleBo) ;
}
