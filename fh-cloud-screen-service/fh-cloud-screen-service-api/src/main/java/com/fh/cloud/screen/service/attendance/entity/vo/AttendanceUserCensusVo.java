package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceUserCensusVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总数量
     */
    private long count = 0;

    /**
     * 正常数量
     */
    private long normalCount = 0;

    /**
     * 异常数量
     */
    private long exceptionCount = 0;

    /**
     * 用户数据
     */
    private List<AttendanceUserVo> list = new ArrayList<>();

}
