package com.fh.cloud.screen.service.attendance.entity.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤用户信息展示vo-一次考勤的信息展示
 * 
 * <AUTHOR>
 * @date 2023/9/15 11:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AttendanceUserShowVo implements Serializable {
    /**
     * 考勤规则id
     */
    @ExcelIgnore
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @ExcelIgnore
    private Integer attendanceType;

    /**
     * 考勤 日
     */
    @Excel(name = "签到日期", width = 30.0, fixedIndex = 0)
    private String attendanceDay;

    /**
     * 用户oid
     */
    @ExcelIgnore
    private String userOid;

    /**
     * 用户姓名
     */
    @Excel(name = "姓名", width = 30.0, fixedIndex = 0)
    private String realName;

    /**
     * 年级
     */
    @ExcelIgnore
    private String grade;

    /**
     * 年级名称
     */
    @ExcelIgnore
    private String gradeName;

    /**
     * 班级
     */
    @Excel(name = "学生所在班级", width = 30.0, fixedIndex = 0)
    private String classesName;

    /**
     * 考勤一组顺序：1，2，3...
     */
    @ExcelIgnore
    private Integer attendanceRuleDayIndex;

    /**
     * 签到地点
     */
    @Excel(name = "签到地点", width = 30.0, fixedIndex = 0)
    private String signInAddress;

    /**
     * 签退地点
     */
    @ExcelIgnore
    private String signOutAddress;

    /**
     * 签到时间
     */
    @Excel(name = "签到时间", width = 30.0, fixedIndex = 0, exportFormat = "HH:mm:ss")
    private Date signInTime;

    /**
     * 签退时间
     */
    @ExcelIgnore
    private Date signOutTime;

    /**
     * 一组考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，6缺卡
     */
    @Excel(name = "签到状态", width = 30.0, fixedIndex = 0,
        replace = {"正常_1", "异常_2", "迟到_3", "早退_4", "缺卡_6", "其他_0", "缺卡_null"})
    private Integer signInRecordType;

    /**
     * 一组考勤记录状态：1正常，2异常（保留状态），3迟到，4早退,6缺卡
     */
    @ExcelIgnore
    private Integer signOutRecordType;

    /**
     * 签到考勤方式：1实体卡，2人脸识别
     */
    @Excel(name = "签到方式", width = 30.0, fixedIndex = 0, replace = {"实体卡_1", "人脸识别_2", "_null"})
    private Integer signInAttendanceMethod;

    /**
     * 签退考勤方式：1实体卡，2人脸识别
     */
    @ExcelIgnore
    private Integer signOutAttendanceMethod;

    /**
     * 该用户当天考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，5迟到早退，6缺卡
     */
    @ExcelIgnore
    private Integer attendanceRecordType;
}
