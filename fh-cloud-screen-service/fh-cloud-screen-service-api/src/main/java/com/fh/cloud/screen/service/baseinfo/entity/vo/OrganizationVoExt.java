package com.fh.cloud.screen.service.baseinfo.entity.vo;

import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Data
public class OrganizationVoExt extends OrganizationVo {

    /**
     *平台名称  (副标题)
     */
    @ApiModelProperty("平台名称 (副标题)")
    private String webName;
    /**
     * logo
     */
    @ApiModelProperty("logo")
    private String logo;

    /**
     * 其他配置（建校日期）
     */
    @ApiModelProperty("其他配置（建校日期）")
    private String otherConfig;

    /**
     * 地点列表
     */
    @ApiModelProperty("地点列表")
    private List<SpaceInfoVo> spaceInfoList;
}
