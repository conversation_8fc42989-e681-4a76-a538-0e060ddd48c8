package com.fh.cloud.screen.service.calendar.entity.bo;

import java.io.Serializable;
import java.util.Date;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 校历上课日日期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Data
public class SchoolCalendarDayListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long schoolCalendarDayId;

    /**
     * FK校历主表主键id
     */
    private Long schoolCalendarId;

    /**
     * 上课类型：1上课、2不上课
     */
    private Integer type;

    /**
     * 日期
     */
    private Date day;

    /**
     * 上课上周几的课
     */
    private Integer week;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     *
     * "2022-05" 根据月份检索天
     */
    private String month;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

}
