package com.fh.cloud.screen.service.calendar.entity.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 校历上课日星期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
@Data
@ApiModel
public class SchoolCalendarWeekVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long schoolCalendarWeekId;

    /**
     * FK校历主表主键id
     */
    @ApiModelProperty(name = "FK校历主表主键id", required = true)
    private Long schoolCalendarId;

    /**
     * 上课类型：1上课、2不上课
     */
    private Integer type;

    /**
     * 星期几：1-7，分别为星期一到星期日
     */
    private Integer week;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
