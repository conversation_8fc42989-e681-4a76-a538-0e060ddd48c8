package com.fh.cloud.screen.service.calendar.service;

import com.fh.cloud.screen.service.calendar.api.SchoolCalendarApi;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarDayApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "schoolCalendarDayApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = SchoolCalendarDayApiService.SchoolCalendarDayApiFallbackFactory.class)
@Component
public interface SchoolCalendarDayApiService extends SchoolCalendarDayApi {
    @Component
    class SchoolCalendarDayApiFallbackFactory implements FallbackFactory<SchoolCalendarDayApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(SchoolCalendarDayApiFallbackFactory.class);

        @Override
        public SchoolCalendarDayApiService create(Throwable cause) {
            SchoolCalendarDayApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new SchoolCalendarDayApiService() {

                @Override
                public AjaxResult getSchoolCalendarDayListByCondition(SchoolCalendarDayListConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                @Override
                public AjaxResult saveOrUpdateSchoolCalendarDay(SchoolCalendarDayBo schoolCalendarDayBo) {
                    return AjaxResult.fail("新增或修改失败");
                }
            };
        }
    }
}
