package com.fh.cloud.screen.service.crm.api;


import com.fh.cloud.screen.service.crm.entity.bo.CrmContactConditionBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactBo;
import com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * CRM商讯联系人表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
public interface CrmContactApi {

    /**
     * 查询CRM商讯联系人表分页列表
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/contact/page/list")
    public AjaxResult<PageInfo<CrmContactVo>> getCrmContactPageListByCondition(@RequestBody CrmContactConditionBo condition);

    /**
     * 查询CRM商讯联系人表列表
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/contact/list")
    public AjaxResult<List<CrmContactVo>> getCrmContactListByCondition(@RequestBody CrmContactConditionBo condition);


    /**
     * 新增CRM商讯联系人表
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/contact/add")
    public AjaxResult addCrmContact(@Validated @RequestBody CrmContactBo crmContactBo);

    /**
     * 修改CRM商讯联系人表
     * @param crmContactBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/contact/update")
    public AjaxResult updateCrmContact(@Validated @RequestBody CrmContactBo crmContactBo);

    /**
     * 查询CRM商讯联系人表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @GetMapping("/crm/contact/detail")
    public AjaxResult<CrmContactVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除CRM商讯联系人表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @GetMapping("/crm/contact/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
