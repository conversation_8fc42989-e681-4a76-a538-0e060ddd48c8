package com.fh.cloud.screen.service.crm.api;

import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoConditionBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoBo;
import com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * CRM商讯管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
public interface CrmInfoApi {

    /**
     * 查询CRM商讯管理表分页列表
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/page/list")
    public AjaxResult<PageInfo<CrmInfoVo>> getCrmInfoPageListByCondition(@RequestBody CrmInfoConditionBo condition);

    /**
     * 查询CRM商讯管理表列表
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/list")
    public AjaxResult<List<CrmInfoVo>> getCrmInfoListByCondition(@RequestBody CrmInfoConditionBo condition);

    /**
     * 新增CRM商讯管理表
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/add")
    public AjaxResult addCrmInfo(@Validated @RequestBody CrmInfoBo crmInfoBo);

    /**
     * 修改CRM商讯管理表
     * 
     * @param crmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/update")
    public AjaxResult updateCrmInfo(@Validated @RequestBody CrmInfoBo crmInfoBo);

    /**
     * 查询CRM商讯管理表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @GetMapping("/crm/info/detail")
    public AjaxResult<CrmInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除CRM商讯管理表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @GetMapping("/crm/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 新增CRM商讯管理表-顺带添加人
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/add-with-contact")
    public AjaxResult addCrmInfoWithContact(@RequestBody CrmInfoBo crmInfoBo);

    /**
     * 查询CRM商讯管理表详情-顺带查询出联系人
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @GetMapping("/crm/info/detail-with-contact")
    public AjaxResult<CrmInfoVo> getDetailWithContact(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询CRM商讯管理表分页列表-带联系人信息查询
     *
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/page/list-with-contact")
    public AjaxResult<PageInfo<CrmInfoVo>>
        getCrmInfoPageListByConditionWithContact(@RequestBody CrmInfoConditionBo condition);

    /**
     * 查询CRM商讯管理表列表-带联系人信息查询
     *
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @PostMapping("/crm/info/list-with-contact")
    public AjaxResult<List<CrmInfoVo>> getCrmInfoListByConditionWithContact(@RequestBody CrmInfoConditionBo condition);
}
