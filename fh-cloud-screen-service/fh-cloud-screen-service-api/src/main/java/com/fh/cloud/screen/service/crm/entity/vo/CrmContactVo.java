package com.fh.cloud.screen.service.crm.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * CRM商讯联系人表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@Data
public class CrmContactVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long crmContactId;

    /**
     * CRM商讯表id
     */
    @ApiModelProperty("CRM商讯表id")
    private Long crmInfoId;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    private String contactName;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contactWay;

    /**
     * 联系人职务类型：1校长，2副校长，3书记，4副书记，5教研室主任，6总务主任，7教导主任，8信息主任，9信息老师
     */
    @ApiModelProperty("联系人职务类型：1校长，2副校长，3书记，4副书记，5教研室主任，6总务主任，7教导主任，8信息主任，9信息老师")
    private Integer contactDutiesType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public CrmContactVo returnOwn() {
        return this;
    }

}
