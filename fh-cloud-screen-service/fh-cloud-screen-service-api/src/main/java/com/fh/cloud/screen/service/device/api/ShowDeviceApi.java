package com.fh.cloud.screen.service.device.api;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceOperateListBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceListConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceOperateBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * The interface Show device api.
 */
public interface ShowDeviceApi {

    /**
     * 根据设备号 激活设备
     *
     * @param bo 设备相关信息
     * @return ajax result
     */
    @PostMapping("/device/activate")
    public AjaxResult<ShowDeviceVo> activate(@RequestBody ShowDeviceBo bo);

    /**
     * 根据设备号获取设备信息
     *
     * @param deviceNubmer the device nubmer
     * @return by device number
     */
    @GetMapping("/device/getByDeviceNum/{deviceNubmer}")
    public AjaxResult<ShowDeviceVo> getByDeviceNumber(@PathVariable("deviceNubmer") String deviceNubmer);

    /**
     * 查询绑定了地点的设备列表
     *
     * @param condition the condition
     * @return ajax result
     */
    @PostMapping("/device/list-data")
    AjaxResult listShowDeviceDataByCondition(@RequestBody ShowDeviceListConditionBo condition);

    /**
     * 根据设备号更新 设备状态
     *
     * @param deviceNumber the device number
     * @param status the status
     * @return ajax result
     */
    @PostMapping("/device/status/{deviceNumber}/{status}")
    AjaxResult updateStatusByDeviceNum(@PathVariable("deviceNumber") String deviceNumber,
        @PathVariable("status") Integer status);

    /**
     * 设备号
     *
     * @param showDeviceOperateBo the show device operate bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -05-12 15:19:01
     */
    @PostMapping("/device/operate")
    AjaxResult switchOperate(@RequestBody ShowDeviceOperateBo showDeviceOperateBo);

    /**
     * 更改设备 模式
     *
     * @param deviceNumber 设备号
     * @param pattern 模式
     * @return ajax result
     */
    @PostMapping("/device/pattern/{deviceNumber}/{pattern}")
    AjaxResult changePattern(@PathVariable("deviceNumber") String deviceNumber,
        @PathVariable("pattern") Integer pattern);

    /**
     * 根据设备序列号修改横竖屏版式
     *
     * @param bo 设备相关信息
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -06-30 10:38:54
     */
    @PostMapping("/device/full")
    AjaxResult<ShowDeviceVo> full(@RequestBody ShowDeviceBo bo);

    /**
     * 根据设备序列号修改虹软人脸激活码
     *
     * @param bo 设备相关信息
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -06-06 10:38:54
     */
    @PostMapping("/device/arc-code")
    AjaxResult arcCode(@RequestBody ShowDeviceBo bo);

    /**
     * 根据设备号获取设备班牌名称及详情
     *
     * @param deviceNumber the device number
     * @return ajax result
     */
    @GetMapping("/device/aboutByDeviceNum/{deviceNumber}")
    public AjaxResult aboutByDeviceNum(@PathVariable("deviceNumber") String deviceNumber);

    /**
     * 获取设备二维码的内容(注意：包含拼接了跳转地址链接后的加密内容)
     *
     * @param bo the bo
     * @return ajax result
     * <AUTHOR>
     */
    @PostMapping("/device/qrcode/content")
    public AjaxResult qrcodeContent(@RequestBody ScreenBusinessBo bo);

    /**
     * 解密二维码加密的内容体，返回对象
     * 
     * @param qrcodeContent 仅仅是二维码信息里面的加密的对象内容
     * @return
     */
    @GetMapping("/device/qrcode/decode")
    public AjaxResult qrDecode(@RequestParam("qrcodeContent") String qrcodeContent);

    /**
     * 设备列表
     * 
     * @param condition
     * @return
     */
    @PostMapping("/device/list")
    public AjaxResult listByCondition(@RequestBody ShowDeviceListConditionBo condition);

    /**
     * 新增设备
     * 
     * @param showDeviceBo
     * @return
     */
    @PostMapping("/device/add")
    public AjaxResult addShowDevice(@RequestBody ShowDeviceBo showDeviceBo);

    /**
     * 修改设备
     * 
     * @param showDeviceBo
     * @return
     */
    @PostMapping("/device/update")
    public AjaxResult updateShowDevice(@RequestBody ShowDeviceBo showDeviceBo);

    /**
     * 删除设备
     * 
     * @param showDeviceId
     * @return
     */
    @GetMapping("/device/del")
    public AjaxResult delete(@RequestParam("showDeviceId") Long showDeviceId);

    /**
     * 导入设备
     * 
     * @param file
     * @return
     */
    @PostMapping(value = "/device/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult upload(@RequestPart(value = "file") MultipartFile file,
        @RequestParam("organizationId") Long organizationId);

    /**
     * 查询设备统计
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/15 14:47
     */
    @PostMapping("/device/count")
    public AjaxResult countDeviceByCondition(@RequestBody ShowDeviceListConditionBo conditionBo);

    /**
     * 根据设备号，更新设备版本
     *
     * @param deviceNumBer, version
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/20 11:38
     */
    @GetMapping("/device/update/version")
    public AjaxResult updateVersionByDeviceNumber(@RequestParam("deviceNumber") String deviceNumBer,
        @RequestParam("version") String version);

    /**
     * 根据设备id 设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @RequestMapping(value = "/device/set/poster", method = RequestMethod.POST)
    public AjaxResult setPosterRule(@RequestBody ShowDeviceBo showDeviceBo);

    /**
     * 根据空间列表 批量设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @RequestMapping(value = "/device/set/poster-batch", method = RequestMethod.POST)
    public AjaxResult setPosterRuleBatch(@RequestBody ShowDeviceBo showDeviceBo);

    /**
     * 根据设备id 获取设备主动推送及设备标签列表
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @RequestMapping(value = "/device/get/poster", method = RequestMethod.GET)
    public AjaxResult getPosterRule(@RequestParam("showDeviceId") Long showDeviceId);

    /**
     * 手动推送初始化云屏的接口
     *
     * @param showDeviceOperateBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/18 14:55
     */
    @ApiOperation(value = "手动推送初始化云屏的接口")
    @RequestMapping(value = "/device/init", method = RequestMethod.POST)
    public AjaxResult initDeviceByOrganizationIdAndDeviceNumbers(@RequestBody ShowDeviceOperateBo showDeviceOperateBo);

    /**
     * 更新设备横竖版、是否全屏、海报播放间隔时长(通知app设备变更)
     *
     * @param showDeviceBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/18 15:12
     */
    @ApiOperation(value = "更新设备横竖版、是否全屏、海报播放间隔时长")
    @RequestMapping(value = "/device/update/format", method = RequestMethod.POST)
    public AjaxResult updateDeviceByDeviceNumberWithSendApp(@RequestBody ShowDeviceBo showDeviceBo);

    /**
     * 设备总览信息
     *
     * @param showDeviceBo the show device bo
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -05-31 18:08:43
     */
    @PostMapping("/device/overview")
    public AjaxResult deviceOverview();

    /**
     * 当云屏在线的时候，重启云屏app。需要参数：organizationId,deviceNumber
     * @param showDeviceBo
     * @return
     */
    @RequestMapping(value = "/device/app/restart", method = RequestMethod.POST)
    public AjaxResult restartApp(@RequestBody ShowDeviceBo showDeviceBo);

    /**
     * 根据监管教育局id查询绑定了地点的设备列表
     *
     * @param condition the condition
     * @return ajax result
     */
    @PostMapping("/device/list-by-parentId")
    AjaxResult listShowDeviceDataByParentOrganizationId(@RequestBody ShowDeviceListConditionBo condition);

    /**
     * 批量开关机
     *
     * @param operateListBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/6 16:09
     **/
    @PostMapping("/device/operate-batch")
    AjaxResult switchOperateBatch(@RequestBody ShowDeviceOperateListBo operateListBo);

    /**
     * 设备号
     *
     * @param showDeviceOperateBo the show device operate bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -09-23 11:19:01
     */
    @PostMapping("/device/facemod-change")
    AjaxResult changeFaceMod(@RequestBody ShowDeviceOperateBo showDeviceOperateBo);

    /**
     * 修改监管状态
     *
     * @param showDeviceOperateBo the show device operate bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -09-23 11:19:01
     */
    @PostMapping("/device/supervisestate-change")
    AjaxResult changeSuperviseState(@RequestBody ShowDeviceOperateBo showDeviceOperateBo);
}
