package com.fh.cloud.screen.service.device.api;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备抓图表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
public interface ShowDeviceCaptureApi {

    /**
     * 查询设备抓图表分页列表
     * 
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @PostMapping("show/device/capture/page/list")
    public AjaxResult<PageInfo<ShowDeviceCaptureVo>>
        getShowDeviceCapturePageListByCondition(@RequestBody ShowDeviceCaptureConditionBo condition);

    /**
     * 查询设备抓图表列表
     * 
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @PostMapping("show/device/capture/list")
    public AjaxResult<List<ShowDeviceCaptureVo>>
        getShowDeviceCaptureListByCondition(@RequestBody ShowDeviceCaptureConditionBo condition);

    /**
     * 新增设备抓图表
     * 
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @PostMapping("show/device/capture/add")
    public AjaxResult addShowDeviceCapture(@Validated @RequestBody ShowDeviceCaptureBo showDeviceCaptureBo);

    /**
     * 修改设备抓图表
     * 
     * @param showDeviceCaptureBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @PostMapping("show/device/capture/update")
    public AjaxResult updateShowDeviceCapture(@Validated @RequestBody ShowDeviceCaptureBo showDeviceCaptureBo);

    /**
     * 查询设备抓图表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @GetMapping("show/device/capture/detail")
    public AjaxResult<ShowDeviceCaptureVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除设备抓图表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @GetMapping("show/device/capture/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询设备抓图表详情-根据deviceNumber。如果数据库有多条则选取最后一条
     *
     * @param deviceNumber the device number
     * @return detail by number
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022 -12-20 15:05:05
     */
    @GetMapping("show/device/capture/detail-number")
    public AjaxResult<ShowDeviceCaptureVo> getDetailByNumber(@RequestParam("deviceNumber") String deviceNumber);

    /**
     * 云屏提交截图数据，会更新截图数据
     *
     * @param showDeviceCaptureBo the show device capture bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -12-20 15:05:05
     */
    @PostMapping("show/device/capture/upload")
    public AjaxResult uploadShowDeviceCapture(@Validated @RequestBody ShowDeviceCaptureBo showDeviceCaptureBo);

    /**
     * web发起截图请求，会新增一条截图数据
     *
     * @param showDeviceCaptureBo the show device capture bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -12-21 10:51:11
     */
    @PostMapping("show/device/capture/launch")
    public AjaxResult launchShowDeviceCapture(@RequestBody ShowDeviceCaptureBo showDeviceCaptureBo);
}
