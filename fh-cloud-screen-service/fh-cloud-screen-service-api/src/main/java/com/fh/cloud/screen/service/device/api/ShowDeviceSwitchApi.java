package com.fh.cloud.screen.service.device.api;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBatchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

public interface ShowDeviceSwitchApi {

    /**
     * 查询开关机设置列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/device/switch/list")
    @ApiOperation(value = "查询开关机设置列表", httpMethod = "POST")
    AjaxResult getShowDeviceSwitchListByCondition(@RequestBody ShowDeviceSwitchListConditionBo condition);

    /**
     * 新增开关机设置
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/device/switch/add")
    @ApiOperation(value = "新增开关机设置", httpMethod = "POST")
    AjaxResult addShowDeviceSwitch(@RequestBody ShowDeviceSwitchBo showDeviceSwitchBo);

    /**
     * 修改开关机设置
     *
     * @param showDeviceSwitchBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/device/switch/update")
    @ApiOperation(value = "修改开关机设置", httpMethod = "POST")
    AjaxResult updateShowDeviceSwitch(@RequestBody ShowDeviceSwitchBo showDeviceSwitchBo);

    /**
     * 保存开关机设置（新增或修改）
     *
     * @param showDeviceSwitchBo the show device switch bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "保存开关机设置（新增或修改）", httpMethod = "POST")
    @RequestMapping(value = "/device/switch/save", method = RequestMethod.POST)
    AjaxResult saveShowDeviceSwitch(@RequestBody ShowDeviceSwitchBatchBo showDeviceSwitchBatchBo);
}
