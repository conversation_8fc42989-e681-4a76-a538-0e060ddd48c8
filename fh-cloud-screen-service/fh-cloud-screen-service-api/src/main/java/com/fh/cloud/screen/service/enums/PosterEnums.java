package com.fh.cloud.screen.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 海报的枚举
 */
@AllArgsConstructor
@Getter
public enum PosterEnums {

    /**
     * dictionary_data枚举 type:1非海报，2海报展示分组，3海报不展示分组
     */
    TYPE_NOT(1, "非海报"), TYPE_SHOW(2, "海报展示分组"), TYPE_SHOW_NOT(3, "海报不展示分组"),

    /**
     * screen_module_library枚举
     */
    POST_IS(1, "是海报"), POST_NOT(2, "不是海报"),

    /**
     * 校本海报角标
     */
    SCHOOL_POST_IS(1, "是校本海报"), SCHOOL_POST_NOT(2, "不是校本海报"),

    // SOURCE_SYSTEM(1,"系统上传"),
    // SOURCE_USER(2,"用户上传"),
    // PATTERN_HORIZONTAL(1,"横屏"),
    // PATTERN_VERTICAL(2,"竖屏"),

    // 查询是否配置标签查询 1:未配置标签列表，2配置标签列表
    QUERY_NOT_LABEL(1, "未配置标签列表"), QUERY_LABEL(2, "配置标签列表"),;

    private Integer code;
    private String value;
}
