package com.fh.cloud.screen.service.er.api;


import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 考场_考试计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamPlanApi {

    /**
     * 查询考场_考试计划分页列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/page/list")
    public AjaxResult<PageInfo<ExamPlanVo>> getExamPlanPageListByCondition(@RequestBody ExamPlanConditionBo condition);

    /**
     * 查询考场_考试计划列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/list")
    public AjaxResult<List<ExamPlanVo>> getExamPlanListByCondition(@RequestBody ExamPlanConditionBo condition);


    /**
     * 新增考场_考试计划
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/add")
    public AjaxResult addExamPlan(@Validated @RequestBody ExamPlanBo examPlanBo);

    /**
     * 修改考场_考试计划
     * @param examPlanBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/update")
    public AjaxResult updateExamPlan(@Validated @RequestBody ExamPlanBo examPlanBo);

    /**
     * 查询考场_考试计划详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/plan/detail")
    public AjaxResult<ExamPlanVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除考场_考试计划
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/plan/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 发布或者取消发布_考试计划
     * @param examPlanBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/pubsub")
    public AjaxResult pubsubExamPlan(@RequestBody ExamPlanBo examPlanBo);
}
