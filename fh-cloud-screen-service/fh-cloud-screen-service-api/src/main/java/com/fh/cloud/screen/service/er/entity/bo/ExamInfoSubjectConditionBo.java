package com.fh.cloud.screen.service.er.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 考场_考试计划里面一次考试科目信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@Data
public class ExamInfoSubjectConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoSubjectId;

    /**
     * 考试id
     */
    @ApiModelProperty("考试id")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 学科code
     */
    @ApiModelProperty("学科code")
    private String subjectCode;

    /**
     * 学科名称
     */
    @ApiModelProperty("学科名称")
    private String subjectName;

    /**
     * 准考证开始
     */
    @ApiModelProperty("准考证开始")
    private String atNoStart;

    /**
     * 准考证结束
     */
    @ApiModelProperty("准考证结束")
    private String atNoEnd;

    /**
     * 该场考试开始时间
     */
    @ApiModelProperty("该场考试开始时间")
    private Date examStartTime;

    /**
     * 该场考试结束时间
     */
    @ApiModelProperty("该场考试结束时间")
    private Date examEndTime;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 考试ids集合，用于本考场考试查询科目数据
     */
    private List<Long> examInfoIds;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 地点id
     */
    @ApiModelProperty("地点id")
    private Long spaceInfoId;

    /**
     * 科目的typeId：1全，2局，3校
     */
    private Long typeId;
}
