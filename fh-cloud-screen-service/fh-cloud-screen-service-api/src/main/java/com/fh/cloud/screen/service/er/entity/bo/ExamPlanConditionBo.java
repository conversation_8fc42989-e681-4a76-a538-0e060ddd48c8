package com.fh.cloud.screen.service.er.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 考场_考试计划
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamPlanConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long examPlanId;

	/**
	 * 考场计划名称
	 */
	@ApiModelProperty("考场计划名称")
	private String examPlanName;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 考试计划开始日期
	 */
	@ApiModelProperty("考试计划开始日期")
	private Date examPlanStartTime;

	/**
	 * 考试计划结束日期
	 */
	@ApiModelProperty("考试计划结束日期")
	private Date examPlanEndTime;

	/**
	 * 考场计划说明
	 */
	@ApiModelProperty("考场计划说明")
	private String examPlanRemark;

	/**
	 * 考场计划类型：1未发布，2已发布
	 */
	@ApiModelProperty("考场计划类型：1未发布，2已发布")
	private Integer examPlanType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 是否签到，0：否，1：是
	 */
	@ApiModelProperty("是否签到，0：否，1：是")
	private Integer isSignIn;
}
