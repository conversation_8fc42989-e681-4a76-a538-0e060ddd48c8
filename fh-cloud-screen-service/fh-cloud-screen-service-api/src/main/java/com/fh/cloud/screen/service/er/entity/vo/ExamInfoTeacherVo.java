package com.fh.cloud.screen.service.er.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 考场_考试计划里面一次考试的老师
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamInfoTeacherVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoTeacherId;

    /**
     * 考试科目表id
     */
    @ApiModelProperty("考试科目表id")
    private Long examInfoSubjectId;

    /**
     * 考试id
     */
    @ApiModelProperty("考试id")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 教师的user_oid
     */
    @ApiModelProperty("教师的user_oid")
    private String userOid;

    /**
     * 教师的姓名
     */
    @ApiModelProperty("教师的姓名")
    private String realName;

    /**
     * 用户来源类型：1校内，2校外
     */
    @ApiModelProperty("用户来源类型：1校内，2校外")
    private Integer userFromType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
