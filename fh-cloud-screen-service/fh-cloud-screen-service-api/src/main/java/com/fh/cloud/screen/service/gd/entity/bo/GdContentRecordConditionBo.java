package com.fh.cloud.screen.service.gd.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 稿定内容记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
@Data
public class GdContentRecordConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 稿定系统用户oid
	 */
	@ApiModelProperty("稿定系统用户oid")
	private String gdOid;

	/**
	 * 文件oid
	 */
	@ApiModelProperty("文件oid")
	private String gdId;

	/**
	 * 文件信息json
	 */
	@ApiModelProperty("文件信息json")
	private String fileInfoJson;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
