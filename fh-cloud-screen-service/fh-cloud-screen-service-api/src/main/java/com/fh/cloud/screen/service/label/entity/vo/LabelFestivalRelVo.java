package com.fh.cloud.screen.service.label.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 标签节日关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@Data
public class LabelFestivalRelVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 节日表关联key
     */
    @ApiModelProperty("节日表关联key")
    private String festivalCode;

    /**
     * 标签表主键
     */
    @ApiModelProperty("标签表主键")
    private Long labelId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 标签名称
     */
    private String labelName;

}
