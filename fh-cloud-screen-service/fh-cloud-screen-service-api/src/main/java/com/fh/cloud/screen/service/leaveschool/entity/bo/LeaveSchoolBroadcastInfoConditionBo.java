package com.fh.cloud.screen.service.leaveschool.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 放学播报信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
@Data
public class LeaveSchoolBroadcastInfoConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 放学播报信息表id
	 */
	@ApiModelProperty("放学播报信息表id")
	private Long broadcastInfoId;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@ApiModelProperty("校区id")
	private Long campusId;

	/**
	 * 播报文字内容
	 */
	@ApiModelProperty("播报文字内容")
	private String broadcastContent;

	/**
	 * 播报文件oid
	 */
	@ApiModelProperty("播报文件oid")
	private String broadcastId;

	/**
	 * 播报文件url
	 */
	@ApiModelProperty("播报文件url")
	private String broadcastUrl;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 播放次数
	 */
	@ApiModelProperty("播放次数")
	private Integer playTimes;
}
