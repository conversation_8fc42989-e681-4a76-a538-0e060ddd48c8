package com.fh.cloud.screen.service.leaveschool.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 放学配置设备表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
@Data
public class LeaveSchoolConfigDeviceVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 放学配置设备表id
     */
    @ApiModelProperty("放学配置设备表id")
    private Long leaveSchoolConfigDeviceId;

    /**
     * 放学配置表id
     */
    @ApiModelProperty("放学配置表id")
    private Long leaveSchoolConfigId;

    /**
     * 展示设备id
     */
    @ApiModelProperty("展示设备id")
    private Long showDeviceId;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 设备号
     */
    @ApiModelProperty("设备号")
    private String deviceNumber;

    /**
     * 设备类型：1云屏。设备绑定的时候更新
     */
    @ApiModelProperty("设备类型：1云屏。设备绑定的时候更新")
    private Integer deviceType;

    /**
     * 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    @ApiModelProperty("设备模式：1横屏，2竖屏。设备绑定的时候更新")
    private Integer devicePattern;

    /**
     * 是否全屏类型：1全屏，2不是全屏
     */
    @ApiModelProperty("是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 开关机状态：1开机，2关机，3异常，4开机中，5关机中
     */
    @ApiModelProperty("开关机状态：1开机，2关机，3异常，4开机中，5关机中")
    private Integer deviceStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;



    /*
     * 方便steam流存入自身
     * */
    public LeaveSchoolConfigDeviceVo returnOwn() {
        return this;
    }

}
