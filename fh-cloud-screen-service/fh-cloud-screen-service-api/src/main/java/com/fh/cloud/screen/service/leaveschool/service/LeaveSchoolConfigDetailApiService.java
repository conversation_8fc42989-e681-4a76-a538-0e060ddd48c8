package com.fh.cloud.screen.service.leaveschool.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigDetailApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 放学配置详情表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
@FeignClient(contextId = "leaveSchoolConfigDetailApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LeaveSchoolConfigDetailApiService.LeaveSchoolConfigDetailApiFallbackFactory.class)
@Component
public interface LeaveSchoolConfigDetailApiService extends LeaveSchoolConfigDetailApi {

    @Component
    class LeaveSchoolConfigDetailApiFallbackFactory implements FallbackFactory<LeaveSchoolConfigDetailApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LeaveSchoolConfigDetailApiFallbackFactory.class);
        @Override
        public LeaveSchoolConfigDetailApiService create(Throwable cause) {
            LeaveSchoolConfigDetailApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LeaveSchoolConfigDetailApiService() {
                public AjaxResult getLeaveSchoolConfigDetailPageListByCondition(LeaveSchoolConfigDetailConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLeaveSchoolConfigDetailListByCondition(LeaveSchoolConfigDetailConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLeaveSchoolConfigDetail(LeaveSchoolConfigDetailBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLeaveSchoolConfigDetail(LeaveSchoolConfigDetailBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}