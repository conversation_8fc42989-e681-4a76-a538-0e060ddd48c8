package com.fh.cloud.screen.service.leaveschool.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolRecordApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 放学记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
@FeignClient(contextId = "leaveSchoolRecordApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LeaveSchoolRecordApiService.LeaveSchoolRecordApiFallbackFactory.class)
@Component
public interface LeaveSchoolRecordApiService extends LeaveSchoolRecordApi {

    @Component
    class LeaveSchoolRecordApiFallbackFactory implements FallbackFactory<LeaveSchoolRecordApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LeaveSchoolRecordApiFallbackFactory.class);
        @Override
        public LeaveSchoolRecordApiService create(Throwable cause) {
            LeaveSchoolRecordApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LeaveSchoolRecordApiService() {
                public AjaxResult getLeaveSchoolRecordPageListByCondition(LeaveSchoolRecordConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLeaveSchoolRecordListByCondition(LeaveSchoolRecordConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLeaveSchoolRecord(LeaveSchoolRecordBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLeaveSchoolRecord(LeaveSchoolRecordBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo) {
                    return AjaxResult.fail("获取放学记录失败");
                }

            };
        }
    }
}