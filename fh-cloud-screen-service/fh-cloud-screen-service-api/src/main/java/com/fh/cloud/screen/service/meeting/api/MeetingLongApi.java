package com.fh.cloud.screen.service.meeting.api;


import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 长期预约表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
public interface MeetingLongApi {

    /**
     * 查询长期预约表分页列表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/page/list")
    public AjaxResult<PageInfo<MeetingLongVo>> getMeetingLongPageListByCondition(@RequestBody MeetingLongConditionBo condition);

    /**
     * 查询长期预约表列表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/list")
    public AjaxResult<List<MeetingLongVo>> getMeetingLongListByCondition(@RequestBody MeetingLongConditionBo condition);


    /**
     * 新增长期预约表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/add")
    public AjaxResult addMeetingLong(@Validated @RequestBody MeetingLongBo meetingLongBo);

    /**
     * 修改长期预约表
     * @param meetingLongBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/update")
    public AjaxResult updateMeetingLong(@Validated @RequestBody MeetingLongBo meetingLongBo);

    /**
     * 查询长期预约表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @GetMapping("/meeting/long/detail")
    public AjaxResult<MeetingLongVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除长期预约表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @GetMapping("/meeting/long/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
