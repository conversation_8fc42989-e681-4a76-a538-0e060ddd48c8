package com.fh.cloud.screen.service.meeting.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 场地预约学生导入数据封装
 * <AUTHOR>
 * @date 2024/5/29 14:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MeetingStudentImportBo implements Serializable {

    /**
     * 姓名
     */
    @Excel(name = "*姓名")
    private String realName;
    /**
     * 年级：例如：一年级
     */
    @Excel(name = "*年级")
    private String gradeName;
    /**
     * 班级：例如：（1）班
     */
    @Excel(name = "*班级",suffix = "班")
    private String clazzName;
}
