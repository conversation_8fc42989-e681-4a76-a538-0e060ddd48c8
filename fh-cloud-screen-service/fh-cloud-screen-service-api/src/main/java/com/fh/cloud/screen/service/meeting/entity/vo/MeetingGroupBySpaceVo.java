package com.fh.cloud.screen.service.meeting.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/22
 */
@Data
public class MeetingGroupBySpaceVo {

    /**
     * 会议室名称
     */
    @ApiModelProperty("会议室名称")
    private String meetingName;

    /**
     * 会议室名称
     */
    @ApiModelProperty("会议室地点id")
    private Long spaceInfoId;

    /**
     * 会议地点相关属性拼接
     */
    private String spaceProperty;

    private List<MeetingVo> meetingVos;

    /**
     * 地点组使用类型：1行政教室，2非行政教室
     */
    private Integer spaceGroupUseType;
}
