package com.fh.cloud.screen.service.meeting.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 场地预约教师和学生导入后的结果反馈
 * <AUTHOR>
 * @date 2024/5/29 15:21
 */
@Data
public class MeetingImportVo implements Serializable {
    /**
     * 成功数量
     */
    private Long successNum = 0L;
    /**
     * 失败数量，不存在数据库的信息+姓名重复的信息的并集数量
     */
    private Long failNum = 0L;
    /**
     * 不存在数据库的信息
     */
    private List<String> notExistInfos;
    /**
     * 姓名重复的信息（包含excel文件重复和与db重复的数据）
     */
    private List<String> repeatInfos;
    /**
     * 导入存在的人的信息，数量和successNum一致，只需要关注：userOid、realName。
     */
    private List<MeetingUserVo> meetingUserVos;
}
