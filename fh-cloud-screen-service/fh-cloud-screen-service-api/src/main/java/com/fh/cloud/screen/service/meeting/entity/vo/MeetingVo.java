package com.fh.cloud.screen.service.meeting.entity.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会议表
 * 
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Data
public class MeetingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long meetingId;

    /**
     * FK所属组织ID
     */
    @ApiModelProperty("FK所属组织ID")
    private Long organizationId;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 会议室地点id
     */
    @ApiModelProperty("会议室地点id")
    private Long spaceInfoId;

    /**
     * 申请人user_oid
     */
    @ApiModelProperty("申请人user_oid")
    private String userOid;

    /**
     * 会议主题
     */
    @ApiModelProperty("会议主题")
    private String title;

    /**
     * 会议日期:yyyy-MM-dd
     */
    @ApiModelProperty("会议日期:yyyy-MM-dd ")
    private Date meetingDate;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Time meetingStartTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Time meetingEndTime;

    /**
     * 是否签到（1：是，2：否）
     */
    @ApiModelProperty("是否签到（1：是，2：否）")
    private Integer isSignIn;

    /**
     * 会议内容
     */
    @ApiModelProperty("会议内容")
    private String content;

    /**
     * 会议备注
     */
    @ApiModelProperty("会议备注")
    private String note;

    /**
     * 会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）
     */
    @ApiModelProperty("会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 会议室名称
     */
    @ApiModelProperty("会议室名称")
    private String meetingName;

    /**
     * 是否有电脑，0：否，1：是
     */
    private Integer computerUse;

    /**
     * 是否有网络，0：否，1：是
     */
    private Integer networkUse;

    /**
     * 是否有投影，0：否，1：是
     */
    private Integer shadowUse;

    /**
     * 容纳人数
     */
    private Integer userCapacity;

    /**
     * 会议地点相关属性拼接
     */
    private String spaceProperty;

    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String userName;

    /**
     * 与会人数
     */
    @ApiModelProperty("与会人数")
    private Integer userCount;

    /**
     * 与会人列表
     */
    @ApiModelProperty("与会人列表")
    private List<MeetingUserVo> meetingUserVos;

    /**
     * 打卡百分比
     */
    private BigDecimal percent = new BigDecimal("0.00");

    /*
    *是否当前用户申请的会议
    */
    @ApiModelProperty("是否当前用户申请的会议")
    private boolean currentUserFlag = false;

    /**
     * 会议人员类型：1：教师，2：学生
     */
    private Integer meetingUserType;

    /**
     * 正常签到时间
     */
    private Time normalSignInTime;

    /**
     * 会议uuid
     */
    @ApiModelProperty("会议uuid")
    private String meetingUuid;
}
