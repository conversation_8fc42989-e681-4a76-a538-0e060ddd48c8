package com.fh.cloud.screen.service.meeting.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.meeting.api.MeetingUserApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.light.core.entity.AjaxResult;

/**
 * 会议人员表
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@FeignClient(contextId = "meetingUserApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = MeetingUserApiService.MeetingUserApiFallbackFactory.class)
@Component
public interface MeetingUserApiService extends MeetingUserApi {

    @Component
    class MeetingUserApiFallbackFactory implements FallbackFactory<MeetingUserApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(MeetingUserApiFallbackFactory.class);

        @Override
        public MeetingUserApiService create(Throwable cause) {
            MeetingUserApiFallbackFactory.LOGGER.error("云屏会议服务调用失败:{}", cause.getMessage());
            return new MeetingUserApiService() {
                public AjaxResult getMeetingUserPageListByCondition(MeetingUserConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getMeetingUserListByCondition(MeetingUserConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addMeetingUser(MeetingUserBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateMeetingUser(MeetingUserBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}