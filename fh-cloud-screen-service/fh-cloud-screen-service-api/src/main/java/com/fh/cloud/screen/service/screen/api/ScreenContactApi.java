package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContactVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 云屏产品咨询收集联系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
public interface ScreenContactApi {

    /**
     * 查询云屏产品咨询收集联系表分页列表
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @PostMapping("/screen/contact/page/list")
    public AjaxResult<PageInfo<ScreenContactVo>> getScreenContactPageListByCondition(@RequestBody ScreenContactConditionBo condition);

    /**
     * 查询云屏产品咨询收集联系表列表
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @PostMapping("/screen/contact/list")
    public AjaxResult<List<ScreenContactVo>> getScreenContactListByCondition(@RequestBody ScreenContactConditionBo condition);


    /**
     * 新增云屏产品咨询收集联系表
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @PostMapping("/screen/contact/add")
    public AjaxResult addScreenContact(@Validated @RequestBody ScreenContactBo screenContactBo);

    /**
     * 修改云屏产品咨询收集联系表
     * @param screenContactBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @PostMapping("/screen/contact/update")
    public AjaxResult updateScreenContact(@Validated @RequestBody ScreenContactBo screenContactBo);

    /**
     * 查询云屏产品咨询收集联系表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @GetMapping("/screen/contact/detail")
    public AjaxResult<ScreenContactVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除云屏产品咨询收集联系表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @GetMapping("/screen/contact/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
