package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ScreenModuleApi {

    /**
     * 查询云屏模块表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/module-data/list")
    @ApiOperation(value = "查询云屏模块表列表", httpMethod = "POST")
    AjaxResult getScreenModuleDataListByCondition(@RequestBody ScreenModuleDataListConditionBo condition);

    /**
     * 新增云屏模块表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/module-data/add")
    @ApiOperation(value = "新增云屏模块表", httpMethod = "POST")
    AjaxResult addScreenModuleData(@RequestBody ScreenModuleDataBo screenModuleDataBo);

    /**
     * 修改云屏模块表
     *
     * @param screenModuleDataBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/module-data/update")
    @ApiOperation(value = "修改云屏模块表", httpMethod = "POST")
    AjaxResult updateScreenModuleData(@RequestBody ScreenModuleDataBo screenModuleDataBo);

    /**
     * 查询云屏模块表详情
     *
     * @param screenModuleDataId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/screen/module-data/detail")
    @ApiOperation(value = "查询云屏模块表详情", httpMethod = "GET")
    AjaxResult getDetail(@RequestParam("screenModuleDataId") Long screenModuleDataId);

    /**
     * 删除云屏模块表
     *
     * @param screenModuleDataId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/screen/module-data/delete")
    @ApiOperation(value = "删除云屏模块表", httpMethod = "GET")
    AjaxResult delete(@RequestParam("screenModuleDataId") Long screenModuleDataId);

    /**
     * 查询云屏模块表列表-学校（如果学校没有模块会初始化预置的模块）
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/module-data/group-school")
    @ApiOperation(value = "查询云屏模块表分组显示-学校", httpMethod = "POST")
    AjaxResult getScreenModuleDataGroupMapByConditionOfSchool(@RequestBody ScreenModuleDataListConditionBo condition);

    /**
     * 学校模块新增或移除预置模块
     *
     * @param screenModuleDataBo the screen module data bo
     * @return ajax result
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     */
    @PostMapping("/screen/module-data/update-preset")
    @ApiOperation(value = "学校模块新增或移除预置模块", httpMethod = "POST")
    AjaxResult updatePresetModule(@RequestBody ScreenModuleDataBo screenModuleDataBo);

}
