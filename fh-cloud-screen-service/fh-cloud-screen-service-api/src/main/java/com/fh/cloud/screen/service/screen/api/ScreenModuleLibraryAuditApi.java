package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 模块库审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
public interface ScreenModuleLibraryAuditApi {

    /**
     * 查询模块库审核表分页列表
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @PostMapping("/screen/module/library/audit/page/list")
    public AjaxResult<PageInfo<ScreenModuleLibraryAuditVo>> getScreenModuleLibraryAuditPageListByCondition(@RequestBody ScreenModuleLibraryAuditConditionBo condition);

    /**
     * 查询模块库审核表列表
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @PostMapping("/screen/module/library/audit/list")
    public AjaxResult<List<ScreenModuleLibraryAuditVo>> getScreenModuleLibraryAuditListByCondition(@RequestBody ScreenModuleLibraryAuditConditionBo condition);


    /**
     * 新增模块库审核表
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @PostMapping("/screen/module/library/audit/add")
    public AjaxResult addScreenModuleLibraryAudit(@Validated @RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 修改模块库审核表
     * @param screenModuleLibraryAuditBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @PostMapping("/screen/module/library/audit/update")
    public AjaxResult updateScreenModuleLibraryAudit(@Validated @RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 查询模块库审核表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @GetMapping("/screen/module/library/audit/detail")
    public AjaxResult<ScreenModuleLibraryAuditVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除模块库审核表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @GetMapping("/screen/module/library/audit/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 获取模块库审核表列表（包含分类标签和资源信息）
     *
     * @param condition 
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/6 14:35
     **/
    @PostMapping("/screen/module/library/audit/poster-audit-page")
    public AjaxResult getPosterAuditPage(@RequestBody ScreenModuleLibraryAuditConditionBo condition);

    /**
     * 新增海报审核数据
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/7 10:48
     **/
    @PostMapping("/screen/module/library/audit/add-poster")
    public AjaxResult addPosterAudit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 编辑海报审核数据
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:20
     **/
    @PostMapping("/screen/module/library/audit/update-poster")
    public AjaxResult updatePosterAudit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 海报资源审核
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:32
     **/
    @PostMapping("/screen/module/library/audit/audit-poster")
    public AjaxResult audit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 海报资源批量审核
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:35
     **/
    @PostMapping("/screen/module/library/audit/audit-batch-poster")
    public AjaxResult auditBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 海报资源审核详情
     *
     * @param screenModuleLibraryAuditId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:43
     **/
    @GetMapping("/screen/module/library/audit/poster-detail")
    public AjaxResult getPosterAuditDetail(@RequestParam("screenModuleLibraryAuditId") Long screenModuleLibraryAuditId);

    /**
     * 海报资源发布、取消发布
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/12 9:45
     **/
    @PostMapping("/screen/module/library/audit/release")
    public AjaxResult releasePoster(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 批量更新海报审核标签
     *
     * @param screenModuleLibraryAuditBo the screen module library audit bo
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2023 /12/12 9:45
     */
    @PostMapping("/screen/module/library/audit/update-poster-audit-label")
    public AjaxResult updatePosterAuditLabel(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 删除模块库审核表-批量
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @PostMapping("/screen/module/library/audit/delete-batch")
    public AjaxResult deleteBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 海报资源发布、取消发布
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/12 9:45
     **/
    @PostMapping("/screen/module/library/audit/release-batch")
    public AjaxResult releasePosterBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

}
