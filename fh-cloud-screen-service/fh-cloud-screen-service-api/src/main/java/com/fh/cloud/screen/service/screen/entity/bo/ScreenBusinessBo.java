package com.fh.cloud.screen.service.screen.entity.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 云屏业务bo
 * 
 * <AUTHOR>
 * @date 2022/6/7 10:43
 */
@Data
public class ScreenBusinessBo implements Serializable {

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 空间或者classesId
     */
    private Long spaceInfoId;

    /**
     * 区域分组使用类型：行政或者非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 是否计算出当前场景：默认false
     */
    private boolean calCurrentScene = false;

    /**
     * 二维码参数
     */
    private String f;

    /**
     * 客户端版本
     */
    private String version;

    /**
     * 教育局id
     */
    private Long parentOrganizationId;

    /**
     * 校区
     */
    private String campusName;

    /**
     * 地点
     */
    private String spaceInfoName;

    /**
     * 是否获取地点信息 默认否
     */
    private boolean getSpaceInfo = false;

}
