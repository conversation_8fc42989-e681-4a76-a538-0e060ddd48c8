package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏场景模块关系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
@Data
public class ScreenSceneModuleRelListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * FK场景ID
     */
    private Long screenSceneId;

    /**
     * FK模块ID
     */
    private Long screenModuleDataId;

    /**
     * FK模块库ID
     */
    private Long screenModuleLibraryId;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 海报多选id，使用逗号分割
     */
    private String screenModuleLibrarySelIds;

}
