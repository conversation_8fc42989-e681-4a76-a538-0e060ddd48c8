package com.fh.cloud.screen.service.screen.entity.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 云屏紧急发布内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentSpecialVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long screenContentSpecialId;

    /**
     * FK所属组织ID
     */
    private Long organizationId;

    /**
     * 所属校区ID
     */
    private Long campusId;

    /**
     * 使用模板类型：1通知公告、2图片、3标语、4倒计时、5视频、6网页URL、7富文本
     */
    private Integer screenTemplateType;

    /**
     * 云屏内容-标题
     */
    private String screenContentTitle;

    /**
     * 云屏内容-文本
     */
    private String screenContentTxt;

    /**
     * 云屏内容-url
     */
    private String screenContentUrl;

    /**
     * 云屏图片或者视频媒体地址
     */
    private String screenContentMediaUrl;

    /**
     * 云屏图片或者视频媒体地址-压缩后
     */
    private String screenContentMediaUrlCompress;

    /**
     * 云屏图片或者视频媒体地址-封面
     */
    private String screenContentMediaUrlCover;

    /**
     * 云屏图片或者视频媒体名称（不包含后缀）
     */
    private String screenContentMediaName;

    /**
     * 云屏图片或者视频原始媒体名称（包含后缀）
     */
    private String screenContentMediaNameOri;

    /**
     * 有效时间-开始时间
     */
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    private Date endTime;

    /**
     * 发布状态：1未发布，2已发布
     */
    private Integer screenContentStatus;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 云屏图片或者视频媒体fileoid
     */
    private String screenContentMediaId;

    /**
     * 云屏图片或者视频媒体fileoid-压缩后
     */
    private String screenContentMediaIdCompress;

    /**
     * 称呼内容
     */
    private String callContent;

    /**
     * 落款内容
     */
    private String signContent;

    /**
     * 发布对象名称拼接
     */
    private String spaceGroupNameConcat;

    /**
     * 发布对象名称集合
     */
    private List<String> spaceGroupNames;

    /**
     * 发布对象id集合
     */
    private List<Long> spaceGroupIds;

    /**
     * 内容数据来源
     */
    private Integer screenContentSource;
}
