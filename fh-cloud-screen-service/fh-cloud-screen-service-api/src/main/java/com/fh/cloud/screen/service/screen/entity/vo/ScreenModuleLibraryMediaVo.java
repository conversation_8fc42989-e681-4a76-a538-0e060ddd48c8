package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
@Data
public class ScreenModuleLibraryMediaVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long screenModuleLibraryMediaId;

    @ApiModelProperty(value = "FK模块库表")
    private Long screenModuleLibraryId;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址")
    private String screenModuleLibraryMediaUrl;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址-压缩后")
    private String screenModuleLibraryMediaUrlCompress;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址-封面")
    private String screenModuleLibraryMediaUrlCover;

    @ApiModelProperty(value = "云屏图片或者视频媒体名称（不包含后缀）")
    private String screenModuleLibraryMediaName;

    @ApiModelProperty(value = "云屏图片或者视频原始媒体名称（包含后缀）")
    private String screenModuleLibraryMediaNameOri;

    @ApiModelProperty(value = "云屏图片或者视频媒体fileoid")
    private String screenContentMediaId;

    @ApiModelProperty(value = "云屏图片或者视频媒体fileoid-压缩后")
    private String screenContentMediaIdCompress;

    @ApiModelProperty(value = "更新时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否删除，0：否，1：是")
    private Integer isDelete;

    @ApiModelProperty(value = "设备模式：1横屏，2竖屏。设备绑定的时候更新")
    private Integer devicePattern;

    @ApiModelProperty(value = "文件md5")
    private String screenContentMediaMd5;

    /**
     * 媒体图片排序
     */
    @ApiModelProperty(value = "媒体图片排序")
    private Long mediaSort;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private Long moduleGroupType;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private String moduleGroupTypeName;

    /**
     * 模块分组的组织id
     */
    private Long organizationId;

    /**
     * 海报与第三方对接类型（1，不对接，2：H5页面对接，3：app对接）
     */
    private Integer pluginType;

    /**
     * 海报与h5对接的地址
     */
    private String pluginH5Url;

    /**
     * 海报与app对接包名称
     */
    private String pluginAppPackageName;

    /**
     * 海报主题关联标签的parentId,用于获取父标签名称作为分类名称
     */
    private Long parentLabelId;

    /**
     * 一个主题对应的多个标签，使用逗号分割
     */
    private String labelIdConcat;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    private Integer mediaSource;

    /**
     * 模块来源id
     */
    private String thirdId;
}
