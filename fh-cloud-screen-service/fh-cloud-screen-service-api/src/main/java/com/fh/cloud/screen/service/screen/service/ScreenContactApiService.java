package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenContactApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 云屏产品咨询收集联系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
@FeignClient(contextId = "screenContactApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenContactApiService.ScreenContactApiFallbackFactory.class)
@Component
public interface ScreenContactApiService extends ScreenContactApi {

    @Component
    class ScreenContactApiFallbackFactory implements FallbackFactory<ScreenContactApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenContactApiFallbackFactory.class);
        @Override
        public ScreenContactApiService create(Throwable cause) {
            ScreenContactApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ScreenContactApiService() {
                public AjaxResult getScreenContactPageListByCondition(ScreenContactConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenContactListByCondition(ScreenContactConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenContact(ScreenContactBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenContact(ScreenContactBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}