package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenContentApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenContentApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenContentApiService.ScreenContentApiFallbackFactory.class)
@Component
public interface ScreenContentApiService extends ScreenContentApi {
    @Component
    class ScreenContentApiFallbackFactory implements FallbackFactory<ScreenContentApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenContentApiService.ScreenContentApiFallbackFactory.class);

        @Override
        public ScreenContentApiService create(Throwable cause) {
            ScreenContentApiService.ScreenContentApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ScreenContentApiService() {
                public AjaxResult getScreenContentListByCondition(ScreenContentListConditionBo condition) {
                    return AjaxResult.fail("查询云屏内容列表失败");
                }

                public AjaxResult addScreenContent(ScreenContentBo screenContentBo) {
                    return AjaxResult.fail("新增云屏内容失败");
                }

                public AjaxResult updateScreenContent(ScreenContentBo screenContentBo) {
                    return AjaxResult.fail("更新云屏内容失败");
                }

                public AjaxResult getDetail(Long screenContentId) {
                    return AjaxResult.fail("查询云屏内容详情失败");
                }

                public AjaxResult delete(Long attendanceLogId) {
                    return AjaxResult.fail("删除云屏内容失败");
                }

                @Override
                public AjaxResult saveScreenContent(ScreenContentBo screenContentBo) {
                    return AjaxResult.fail("保存云屏内容失败");
                }

                @Override
                public AjaxResult cancelSubmit(Long screenContentId) {
                    return AjaxResult.fail("撤回云屏内容失败");
                }

                @Override
                public AjaxResult getMenuByCondition(ScreenContentListConditionBo condition) {
                    return AjaxResult.fail("获取菜单失败");
                }

                @Override
                public AjaxResult getMenuByConditionWithCache(ScreenContentListConditionBo condition) {
                    return AjaxResult.fail("获取菜单缓存失败");
                }
            };
        }
    }
}
