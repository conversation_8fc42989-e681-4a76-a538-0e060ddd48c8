package com.fh.cloud.screen.service.screenConfig.api;


import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 云屏配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
public interface ScreenConfigApi {

    /**
     * 查询云屏配置表分页列表
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @PostMapping("/screen/config/page/list")
    public AjaxResult<PageInfo<ScreenConfigVo>> getScreenConfigPageListByCondition(@RequestBody ScreenConfigConditionBo condition);

    /**
     * 查询云屏配置表列表
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @PostMapping("/screen/config/list")
    public AjaxResult<List<ScreenConfigVo>> getScreenConfigListByCondition(@RequestBody ScreenConfigConditionBo condition);


    /**
     * 新增云屏配置表
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @PostMapping("/screen/config/add")
    public AjaxResult addScreenConfig(@Validated @RequestBody ScreenConfigBo screenConfigBo);

    /**
     * 修改云屏配置表
     * @param screenConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @PostMapping("/screen/config/update")
    public AjaxResult updateScreenConfig(@Validated @RequestBody ScreenConfigBo screenConfigBo);

    /**
     * 查询云屏配置表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @GetMapping("/screen/config/detail")
    public AjaxResult<ScreenConfigVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除云屏配置表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @GetMapping("/screen/config/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 保存云屏配置
     *
     * @param configBo
     * @return
     */
    @PostMapping("/screen/config/save")
    public AjaxResult saveScreenConfig(@RequestBody ScreenConfigBo configBo);

    /**
     * 根据组织id和配置类型查询云屏配置信息
     *
     * @param condition
     * @return
     */
    @PostMapping("/screen/config/get-by-org")
    public AjaxResult<ScreenConfigVo> getByOrganizationIdAndType(@RequestBody ScreenConfigConditionBo condition);

}
