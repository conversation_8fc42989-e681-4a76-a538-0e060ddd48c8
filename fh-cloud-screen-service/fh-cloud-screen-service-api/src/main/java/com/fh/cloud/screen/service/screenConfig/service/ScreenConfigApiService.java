package com.fh.cloud.screen.service.screenConfig.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screenConfig.api.ScreenConfigApi;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigBo;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 云屏配置表
 *
 * <AUTHOR>
 * @email
 * @date 2024-07-29 09:10:13
 */
@FeignClient(contextId = "screenConfigApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
        configuration = FeignClientInterceptor.class, fallbackFactory = ScreenConfigApiService.ScreenConfigApiFallbackFactory.class)
@Component
public interface ScreenConfigApiService extends ScreenConfigApi {
    @Component
    class ScreenConfigApiFallbackFactory implements FallbackFactory<ScreenConfigApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenConfigApiFallbackFactory.class);

        @Override
        public ScreenConfigApiService create(Throwable cause) {
            ScreenConfigApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new ScreenConfigApiService() {
                public AjaxResult getScreenConfigPageListByCondition(ScreenConfigConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenConfigListByCondition(ScreenConfigConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenConfig(ScreenConfigBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenConfig(ScreenConfigBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult saveScreenConfig(ScreenConfigBo configBo) {
                    return AjaxResult.fail("保存云屏配置失败");
                }

                @Override
                public AjaxResult<ScreenConfigVo> getByOrganizationIdAndType(ScreenConfigConditionBo condition) {
                    return AjaxResult.fail("查询云屏配置失败");
                }

            };
        }
    }
}