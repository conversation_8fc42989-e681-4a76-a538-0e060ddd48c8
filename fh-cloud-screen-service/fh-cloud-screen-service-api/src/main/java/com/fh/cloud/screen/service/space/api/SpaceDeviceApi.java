package com.fh.cloud.screen.service.space.api;

import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 设备管理相关的api
 */
public interface SpaceDeviceApi {

    @PostMapping("space/device-rel/save")
    AjaxResult saveSpaceDeviceRel(@RequestBody SpaceDeviceRelBo spaceDeviceRelBo);

    @GetMapping("space/device-rel/getByDeviceId/{deviceId}")
    AjaxResult<SpaceDeviceRelVo> getByDeviceId(@PathVariable("deviceId") Long deviceId);

    @PostMapping("space/device-rel/update")
    AjaxResult updateSpaceDeviceRel(@RequestBody SpaceDeviceRelBo spaceDeviceRelBo);
}
