package com.fh.cloud.screen.service.space.api;

import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

public interface SpaceGroupApi {

    /**
     * 获取 空间区域组列表
     * 
     * @return
     */
    @GetMapping("space/group/findAll")
    AjaxResult<List<SpaceGroupVo>> findAll();
}
