package com.fh.cloud.screen.service.thirdinfo.api;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoWithRestVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

/**
 * 调用basic-info-third-party-api服务的接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:16:07
 */
public interface ThirdInfoApi {

    /**
     * 查询课表信息分页列表
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @PostMapping("/third/syllabus/info/sync")
    public AjaxResult<String> syncThirdSyllabusInfo(@RequestBody SyllabusInfoBo syllabusInfoBo);

}
