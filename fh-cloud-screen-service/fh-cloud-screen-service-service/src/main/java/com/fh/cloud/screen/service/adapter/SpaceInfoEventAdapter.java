package com.fh.cloud.screen.service.adapter;

import com.fh.cloud.screen.service.rabbitmq.constant.SpaceInfoRabbitConstant;
import lombok.AllArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/22 3:38 下午
 * @description：
 */
@Component
@AllArgsConstructor
public class SpaceInfoEventAdapter {

    private final RabbitTemplate rabbitTemplate;


    /**
     *  地点删除
     *
     * @param spaceInfoId the space info id 地点ID
     */
    public void spaceInfoDelEvent(Long spaceInfoId){
        this.rabbitTemplate.convertAndSend(SpaceInfoRabbitConstant.SPACE_INFO_DELETE_QUEUE,"");
    }


}
