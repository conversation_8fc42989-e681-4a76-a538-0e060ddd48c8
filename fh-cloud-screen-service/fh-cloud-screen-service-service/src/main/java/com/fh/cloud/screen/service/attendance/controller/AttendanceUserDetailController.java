package com.fh.cloud.screen.service.attendance.controller;

import com.fh.cloud.screen.service.attendance.api.AttendanceUserDetailApi;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRule;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserDetailService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 考勤用户详情表，需要日终计算
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@RestController
@Validated
@Api(value = "", tags = "考勤用户详情接口")
public class AttendanceUserDetailController implements AttendanceUserDetailApi {

    @Autowired
    private IAttendanceUserDetailService attendanceUserDetailService;

    @Autowired
    private IAttendanceUserService attendanceUserService;

    @Autowired
    private IAttendanceRuleService iAttendanceRuleService;

    /**
     * 根据考勤记录查询考勤信息详情
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     * @return
     */
    @ApiOperation(value = "根据考勤记录查询考勤信息详情", httpMethod = "POST")
    public AjaxResult<List<AttendanceUserDetailVo>>
        getListByAttendanceUserId(@PathVariable("attendanceUserId") Long attendanceUserId) {

        AttendanceUser attendanceUser = this.attendanceUserService.getByAttendanceUserId(attendanceUserId);
        if(attendanceUser == null){
            return AjaxResult.fail("考勤记录不存在");
        }


        List<AttendanceUserDetailVo> list =
            this.attendanceUserDetailService.getListByAttendanceUserId(attendanceUserId);

        final Long attendanceRuleId = attendanceUser.getAttendanceRuleId();
        AttendanceRule attendanceRule = this.iAttendanceRuleService.getAttendanceRuleId(attendanceRuleId);
        if(attendanceRule == null){
            return AjaxResult.success(list);
        }
        // 获取一天几次打卡
        final Integer attendanceModeNum = attendanceRule.getAttendanceModeNum();

        //转换map key index ： value
        final Map<Integer, AttendanceUserDetailVo> indexAttendanceUserMap = list.stream().collect(
                Collectors.toMap(AttendanceUserDetailVo::getAttendanceRuleDayIndex, x -> x, (k1, k2) -> k2)
        );

        // 处理未打卡记录
//        for (int i = 1 ; i <= attendanceModeNum ; i++){
//            AttendanceUserDetailVo attendanceUserDetailVo = indexAttendanceUserMap.get(i);
//            if(attendanceUserDetailVo == null){
//                attendanceUserDetailVo = new AttendanceUserDetailVo();
//                attendanceUserDetailVo.setAttendanceUserId(attendanceUserId);
//                attendanceUserDetailVo.setAttendanceRuleDayIndex(i);
//                list.add(attendanceUserDetailVo);
//            }
//        }
        list = list.stream().sorted(Comparator.comparing(AttendanceUserDetailVo::getAttendanceRuleDayIndex)).collect(Collectors.toList());

        return AjaxResult.success(list);
    }


}
