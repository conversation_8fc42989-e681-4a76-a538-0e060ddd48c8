package com.fh.cloud.screen.service.attendance.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤流水表，不用于业务查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("attendance_log")
public class AttendanceLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "attendance_log_id", type = IdType.AUTO)
    private Long attendanceLogId;

    /**
     * 考勤规则id，当天考勤使用的考勤规则
     */
    @TableField("attendance_rule_id")
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @TableField("attendance_type")
    private Integer attendanceType;

    /**
     * 考勤设备号，例如云屏设备的设备号
     */
    @TableField("device_number")
    private String deviceNumber;

    /**
     * 打卡地点
     */
    @TableField("address")
    private String address;

    /**
     * 考勤卡号，例如学生卡卡号
     */
    @TableField("card_number")
    private String cardNumber;

    /**
     * 用户oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 学生年级
     */
    @TableField("grade")
    private String grade;

    /**
     * 学生所属班级id
     */
    @TableField("classes_id")
    private Long classesId;

    /**
     * 考勤时间
     */
    @TableField("attendance_time")
    private Date attendanceTime;

    /**
     * 签到签退类型：1签到，2签退
     */
    @TableField("sign_type")
    private Integer signType;

    /**
     * 考勤方式：1实体卡，2人脸识别
     */
    @TableField("attendance_method")
    private Integer attendanceMethod;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 人脸图片fileOid
     */
    @TableField("face_media_id")
    private String faceMediaId;
}
