package com.fh.cloud.screen.service.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRule;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleAddVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo;

import java.util.List;

/**
 * 考勤规则表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface IAttendanceRuleService extends IService<AttendanceRule> {

    List<AttendanceRuleVo> getAttendanceRuleListByCondition(AttendanceRuleListConditionBo condition);

    boolean saveOrUpdateAttendanceRule(AttendanceRuleAddBo attendanceRuleAddBo);

    boolean updateAttendanceRule(AttendanceRuleBo attendanceRuleBo);

    AttendanceRuleAddVo getDetail(AttendanceRuleBo attendanceRuleBo);

    AttendanceRuleVo getInfoByOrganizationId(Long organizationId, Integer attendanceType);

    /**
     * 获取靠近当天时间的考勤规则时间
     *
     * @param attendanceRuleBo
     * @return java.sql.Date
     * <AUTHOR>
     * @date 2022/6/16 15:24
     */
    String getRuleDataTimeByCondition(AttendanceRuleBo attendanceRuleBo);

    /**
     * 通过考勤id获取缓存中的考勤规则（不更新缓存）-getAttendanceRuleByCache和getCacheAttendanceRuleIdByOrganizationIdAndType合并
     *
     * @param organizationId the organization id
     * @param attendanceType the attendance type
     * @return com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo attendance rule by organization id
     *         and type
     * <AUTHOR>
     * @date 2022 /6/17 11:37
     */
    AttendanceRuleAddBo getAttendanceRuleByOrganizationIdAndType(Long organizationId, Integer attendanceType);

    /**
     * 根据ID获取规则信息
     *
     * @param attendanceRuleId the attendanace rule id 规则ID
     * @return
     */
    AttendanceRule getAttendanceRuleId(Long attendanceRuleId);
}
