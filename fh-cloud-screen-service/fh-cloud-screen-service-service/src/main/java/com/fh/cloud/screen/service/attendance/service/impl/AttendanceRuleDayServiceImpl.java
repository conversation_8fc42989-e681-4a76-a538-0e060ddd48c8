package com.fh.cloud.screen.service.attendance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRuleDay;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo;
import com.fh.cloud.screen.service.attendance.mapper.AttendanceRuleDayMapper;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleDayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 考勤规则天表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Service
public class AttendanceRuleDayServiceImpl extends ServiceImpl<AttendanceRuleDayMapper, AttendanceRuleDay>
    implements IAttendanceRuleDayService {

    @Resource
    private AttendanceRuleDayMapper attendanceRuleDayMapper;

    @Override
    public List<AttendanceRuleDayVo> getAttendanceRuleDayListByCondition(AttendanceRuleDayListConditionBo condition) {
        return attendanceRuleDayMapper.getAttendanceRuleDayListByCondition(condition);
    }

    @Override
    public boolean addAttendanceRuleDay(AttendanceRuleDayBo attendanceRuleDayBo) {
        return false;
    }

    @Override
    public boolean updateAttendanceRuleDay(AttendanceRuleDayBo attendanceRuleDayBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long attendanceRuleDayId) {
        return null;
    }

}