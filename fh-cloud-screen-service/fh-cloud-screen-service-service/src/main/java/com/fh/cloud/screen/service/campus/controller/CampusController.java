package com.fh.cloud.screen.service.campus.controller;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.campus.api.CampusScreenApi;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/13 2:26 下午 @description：
 */
@RestController
@Api(value = "", tags = "校区")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class CampusController implements CampusScreenApi {

    private final BaseDataService baseDataService;

    /**
     * 根据条件获取 校区列表
     *
     * @param condition
     * @return
     */
    @Override
    public AjaxResult getCampusByCondition(CampusListConditionBo condition) {
        return AjaxResult.success(baseDataService.getCampusVoByCondition(condition));
    }
}
