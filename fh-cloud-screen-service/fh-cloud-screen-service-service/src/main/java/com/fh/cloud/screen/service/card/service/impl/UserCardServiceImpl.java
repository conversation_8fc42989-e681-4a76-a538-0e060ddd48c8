package com.fh.cloud.screen.service.card.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.dto.UserCard;
import com.fh.cloud.screen.service.card.entity.vo.UserCardVo;
import com.fh.cloud.screen.service.card.mapper.UserCardMapper;
import com.fh.cloud.screen.service.card.service.IUserCardService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户卡表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class UserCardServiceImpl extends ServiceImpl<UserCardMapper, UserCard> implements IUserCardService {

    @Resource
    private UserCardMapper userCardMapper;

    @Override
    public List<UserCardVo> getUserCardListByCondition(UserCardListConditionBo condition) {
        return userCardMapper.getUserCardListByCondition(condition);
    }

    @Override
    public boolean addUserCard(UserCardBo userCardBo) {
        UserCard userCard = new UserCard();
        BeanUtils.copyProperties(userCard, userCard);
        userCard.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(userCard);
    }

    @Override
    public boolean updateUserCard(UserCardBo userCardBo) {
        UserCard userCard = new UserCard();
        BeanUtils.copyProperties(userCard, userCard);
        return updateById(userCard);
    }

    @Override
    public UserCardVo getDetail(Long userCardId) {
        UserCardVo userCardVo = new UserCardVo();
        UserCard userCard = userCardMapper.selectById(userCardId);
        if (null != userCard) {
            BeanUtils.copyProperties(userCard, userCardVo);
        }
        return userCardVo;
    }

    @Override
    public AjaxResult updateCard(UserCardBo userCardBo) {
        UserCard userCard = userCardMapper.selectById(userCardBo.getUserCardId());
        if (null == userCard || StatusEnum.ISDELETE.getCode().equals(userCard.getIsDelete())) {
            return AjaxResult.fail("该用户未绑卡");
        }
        LambdaQueryWrapper<UserCard> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserCard::getCardNumber, userCardBo.getCardNumber());
        lqw.eq(UserCard::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.ne(UserCard::getUserCardId, userCard.getUserCardId());
        lqw.last("limit 1");
        UserCard one = userCardMapper.selectOne(lqw);
        if (null == one) {
            userCard.setCardNumber(userCardBo.getCardNumber());
            userCard.setUpdateTime(new Date());
            if (updateById(userCard)) {
                return AjaxResult.success("编辑卡成功");
            }
            return AjaxResult.fail("编辑卡失败");
        }
        return AjaxResult.fail("该卡号已被绑定，请联系管理员");
    }

    @Override
    public AjaxResult tiedCard(UserCardBo userCardBo) {
        LambdaQueryWrapper<UserCard> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserCard::getCardNumber, userCardBo.getCardNumber());
        lqw.eq(UserCard::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.last("limit 1");
        UserCard one = userCardMapper.selectOne(lqw);
        if (null == one) {
            UserCard addUserCard = new UserCard();
            BeanUtils.copyProperties(userCardBo, addUserCard);
            if (save(addUserCard)) {
                return AjaxResult.success("绑卡成功");
            }
            return AjaxResult.fail("绑卡失败");
        }
        return AjaxResult.fail("该卡号已被绑定，请联系管理员");
    }

    @Override
    public AjaxResult unbindCard(Long userCardId) {
        UserCard upUserCard = new UserCard();
        upUserCard.setUserCardId(userCardId);
        upUserCard.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (updateById(upUserCard)) {
            return AjaxResult.success("解绑卡成功");
        }
        return AjaxResult.fail("解绑卡失败");
    }

    @Override
    public AjaxResult batchUnbindCard(String userCardIds) {
        List<String> list = Arrays.asList(userCardIds.split(","));
        List<UserCard> upList = new ArrayList<>();
        for (String userCardStr : list) {
            UserCard userCard = new UserCard();
            userCard.setUserCardId(Long.parseLong(userCardStr));
            userCard.setIsDelete(StatusEnum.ISDELETE.getCode());
            upList.add(userCard);
        }
        if (updateBatchById(upList)) {
            return AjaxResult.success("批量解绑卡成功");
        }
        return AjaxResult.fail("批量解绑卡失败");
    }

    @Override
    public UserCard getDetailByUserOid(String userOid) {
        LambdaQueryWrapper<UserCard> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserCard::getUserOid, userOid);
        lqw.eq(UserCard::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.last("limit 1");
        return userCardMapper.selectOne(lqw);
    }

    @Override
    public List<UserCard> getUserCardList(UserCardBo userCardBo) {
        LambdaQueryWrapper<UserCard> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserCard::getCardType, userCardBo.getCardType());
        lqw.eq(UserCard::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return userCardMapper.selectList(lqw);
    }

    @Override
    public boolean userCardImport(UserCardBo userCardBo) {
        LambdaQueryWrapper<UserCard> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserCard::getCardNumber, userCardBo.getCardNumber());
        lqw.eq(UserCard::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.ne(UserCard::getUserOid, userCardBo.getUserOid());
        lqw.last("limit 1");
        UserCard one = userCardMapper.selectOne(lqw);
        if (null == one) {
            LambdaQueryWrapper<UserCard> lqwByUserOid = new LambdaQueryWrapper<>();
            lqwByUserOid.eq(UserCard::getUserOid, userCardBo.getUserOid());
            lqwByUserOid.eq(UserCard::getIsDelete, StatusEnum.NOTDELETE.getCode());
            lqwByUserOid.last("limit 1");
            UserCard oneByUserOid = userCardMapper.selectOne(lqwByUserOid);
            if (null == oneByUserOid) {
                UserCard addUserCard = new UserCard();
                addUserCard.setCardNumber(userCardBo.getCardNumber());
                addUserCard.setCardType(userCardBo.getCardType());
                addUserCard.setUserOid(userCardBo.getUserOid());
                userCardMapper.insert(addUserCard);
                return true;
            } else {
                oneByUserOid.setCardNumber(userCardBo.getCardNumber());
                oneByUserOid.setUpdateTime(new Date());
                userCardMapper.updateById(oneByUserOid);
                return true;
            }
        }
        return false;
    }
}