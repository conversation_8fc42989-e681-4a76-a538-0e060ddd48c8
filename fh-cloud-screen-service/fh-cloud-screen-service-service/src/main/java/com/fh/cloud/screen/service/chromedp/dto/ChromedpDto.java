package com.fh.cloud.screen.service.chromedp.dto;

import cn.hutool.core.lang.UUID;
import com.fh.cloud.screen.service.chromedp.service.ChromedpService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * url转截图dto
 * 
 * <AUTHOR>
 * @date 2022/12/28 17:22
 */
@Data
public class ChromedpDto implements Serializable {
    /**
     * 唯一id
     */
    private String uuid;

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 链接url
     */
    private String linkUrl;
    /**
     * 图片url
     */
    private String imgUrl;
    /**
     * 图片本地路径(截图服务更新)
     */
    private String imgPath;
    /**
     * 文件名称（截图服务更新）
     */
    private String fileName;
    /**
     * 状态（1：未转换为图片，2转换图片成功，3转换图片失败）
     */
    private Integer status;
    /**
     * 业务类型：1普通内容，2紧急发布的内容
     */
    private Integer businessType;

    /**
     * 生成一个ChromedpDto任务对象
     *
     * @param linkUrl the link url
     * @return chromedp dto
     * <AUTHOR>
     * @date 2022 -12-29 10:01:54
     */
    public static ChromedpDto generateChromedpDto(String taskId, Integer businessType, String linkUrl, String imgUrl,
        String imgPath, String fileName) {
        ChromedpDto chromedpDto = new ChromedpDto();
        if (StringUtils.isBlank(taskId)) {
            taskId = UUID.fastUUID().toString();
        }
        if (StringUtils.isBlank(fileName)) {
            fileName = UUID.fastUUID().toString();
        }

        chromedpDto.setUuid(UUID.fastUUID().toString());
        chromedpDto.setTaskId(taskId);
        chromedpDto.setBusinessType(businessType);
        chromedpDto.setLinkUrl(linkUrl);
        chromedpDto.setImgUrl(imgUrl);
        chromedpDto.setImgPath(imgPath);
        chromedpDto.setFileName(fileName);
        chromedpDto.setStatus(ChromedpService.UrlConvertType.NOT.getValue());
        return chromedpDto;
    }
}
