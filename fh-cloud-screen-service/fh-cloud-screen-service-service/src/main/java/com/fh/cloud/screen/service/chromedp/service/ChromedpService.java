package com.fh.cloud.screen.service.chromedp.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.nacos.common.utils.IoUtils;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentDetailService;
import com.fh.cloud.screen.service.screen.service.IScreenContentService;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.chromedp.dto.ChromedpDto;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * chromedp的url转截图service。注意url截图队列拆分为2个：1个是java生产未截图任务node消费，一个是node生产截图过的任务java消费
 * 
 * <AUTHOR>
 * @date 2022/12/28 17:18
 */
@Slf4j
@Service
public class ChromedpService {
    @Autowired
    private RedisComponent redisComponent;
    @Resource
    private AttachmentApi attachmentApi;
    @Autowired
    private IScreenContentDetailService screenContentDetailService;
    @Autowired
    private IScreenContentSpecialService screenContentSpecialService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IScreenContentService screenContentService;

    @Value("${screen.content.link.image.prefix:}")
    private String screenContentLinkImagePrefix;

    /**
     * 添加任务到url截图队列
     *
     * @param chromedpDto the chromedp dto
     * <AUTHOR>
     * @date 2022 -12-29 10:20:48
     */
    public void produceChromedpTask(ChromedpDto chromedpDto) {
        String value = JSONObject.toJSONString(chromedpDto);
        String key = ConstantsRedis.CHROMEDP_TASK_LIST;
        redisComponent.rPush(key, value);
    }

    /**
     * 消费url截图成功队列里面的任务
     *
     * <AUTHOR>
     * @date 2022 -12-29 10:23:27
     */
    public void consumeChromedpTask() {
        // 一次处理全部数据(成功或失败的数据)
        String key = ConstantsRedis.CHROMEDP_TASK_LIST_SUCCESS;
        List<Object> valueObjs = redisComponent.lGet(key, 0, -1);

        for (Object valueObj : valueObjs) {
            if (valueObj == null) {
                continue;
            }
            String value = (String)valueObj;
            ChromedpDto chromedpDto = JSONObject.parseObject(value, ChromedpDto.class);
            if (chromedpDto.getStatus() == null || !chromedpDto.getStatus().equals(UrlConvertType.SUCCESS.getValue())) {
                continue;
            }
            if (StringUtils.isBlank(chromedpDto.getImgPath())) {
                continue;
            }

            String imgPath = chromedpDto.getImgPath();
            InputStream contentStream = null;
            InputStream inputStream = null;
            ByteArrayOutputStream outStream = null;
            try {
                if (StringUtils.isBlank(screenContentLinkImagePrefix)) {
                    contentStream = new FileInputStream(imgPath);
                } else {
                    final String tailPath =
                        imgPath.replace(ConstantsConfig.SCREEN_SHOT_DISK_PATH_PREFIX, screenContentLinkImagePrefix);
                    URL url = new URL(tailPath);
                    inputStream = url.openStream();
                    outStream = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int len = 0;
                    while ((len = inputStream.read(buffer)) != -1) {
                        outStream.write(buffer, 0, len);
                    }
                    byte[] bytes = outStream.toByteArray();
                    contentStream = new ByteArrayInputStream(bytes);
                    IoUtils.closeQuietly(outStream);
                    IoUtils.closeQuietly(inputStream);
                }
                if (contentStream == null) {
                    log.error("contentStream is null, function stack return!");
                    return;
                }

                MultipartFile cMultiFile =
                    new MockMultipartFile("file", chromedpDto.getFileName(), null, contentStream);
                AjaxResult result = attachmentApi.upload(cMultiFile);
                if (null == result || result.isFail() || null == result.getData()) {
                    continue;
                }
                final Map<String, Object> map = (Map<String, Object>)result.getData();
                AttachmentVo attachmentVo = BeanUtil.mapToBean(map, AttachmentVo.class, true);
                if (chromedpDto.getBusinessType().equals(BusinessType.NORMAL.getValue())) {
                    screenContentDetailService.updateFirstImgUrlByScreenContentId(Long.valueOf(chromedpDto.getTaskId()),
                        attachmentVo.getWebUrl(), attachmentVo.getFileOid());
                    // 清除首页和菜单关联的缓存
                    // publish event
                    ScreenContentVo screenContentVo =
                        screenContentService.getDetail(Long.valueOf(chromedpDto.getTaskId()));
                    if (screenContentVo != null) {
                        Long organizationId = screenContentVo.getOrganizationId();
                        applicationContext.publishEvent(
                            PublishEvent.produceContentPublishEvent(MessageWsType.MODIFY_CONTENT.getValue(),
                                    organizationId, Long.valueOf(chromedpDto.getTaskId()), null));
                    }

                } else if (chromedpDto.getBusinessType().equals(BusinessType.SPECIAL.getValue())) {
                    screenContentSpecialService.updateImgUrlByScreenContentSpecialId(
                        Long.valueOf(chromedpDto.getTaskId()), attachmentVo.getWebUrl(), attachmentVo.getFileOid());
                    // 清除首页和菜单关联的缓存 sunqbtodo 特殊场景暂时不推送，特殊场景且发送链接的情况不多，后面有需求再开发。
                }
                // 成功后移除该元素
                redisComponent.lRemove(key, 1, value);
            } catch (Exception e) {
                log.error("consumeChromedpTask exception:", e);
            } finally {
                IoUtils.closeQuietly(outStream);
                IoUtils.closeQuietly(inputStream);
                IOUtils.closeQuietly(contentStream);
            }
        }
    }

    /**
     * 业务类型
     */
    public enum BusinessType {

        /**
         * 普通内容
         */
        NORMAL(1),
        /***
         * 特殊北荣
         */
        SPECIAL(2),;

        private int value;

        BusinessType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * url-convert类型
     */
    public enum UrlConvertType {

        /**
         * 未转换
         */
        NOT(1),
        /***
         * 成功
         */
        SUCCESS(2),
        /***
         * 失败
         */
        FAIL(3),;

        private int value;

        UrlConvertType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
}
