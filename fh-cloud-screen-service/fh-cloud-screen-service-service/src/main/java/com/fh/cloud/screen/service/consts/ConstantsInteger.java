package com.fh.cloud.screen.service.consts;

/**
 * <AUTHOR>
 * @date 2022/4/12 15:11
 */
public interface ConstantsInteger {

    /**
     * 合法
     */
    int STATUS_YES = 1;
    /**
     * 非法
     */
    int STATUS_NO = 0;

    int NUM_0 = 0;
    int NUM_1 = 1;
    int NUM_2 = 2;
    int NUM_3 = 3;
    int NUM_4 = 4;
    int NUM_5 = 5;
    int NUM_7 = 7;
    int NUM_10 = 10;
    int NUM_16 = 16;
    int NUM_17 = 17;
    int NUM_30 = 30;

    int NUM_100 = 100;
    int NUM_1000 = 1000;

    /**
     * 用户角色权限 1个人 2班级,3年级，4校级，5区级
     */
    Integer USER_DATA_AUTHORITY_PERSONAL = 1;
    /**
     * 用户角色权限 2班级,3年级，4校级，5区级
     */
    Integer USER_DATA_AUTHORITY_CLASS = 2;
    /**
     * 用户角色权限 2班级,3年级，4校级，5区级
     */
    Integer USER_DATA_AUTHORITY_GRADE = 3;
    /**
     * 用户角色权限 2班级,3年级，4校级，5区级
     */
    Integer USER_DATA_AUTHORITY_SCHOOL = 4;
    /**
     * 用户角色权限 2班级,3年级，4校级，5区级
     */
    Integer USER_DATA_AUTHORITY_AREA = 5;

    Integer APP_QUERY = 1;

    Integer MEETING_FAIL_CODE = 505;

    /**
     * 热门海报全部的数量
     */
    Integer HOT_LIMIT = 10;
    /**
     * 1：公共海报包含热门海报
     */
    Integer QUERY_HOT = 1;
    /**
     * 2：公共海报不包含热门海报；
     */
    Integer QUERY_POSTER = 2;
    /**
     * 3：校本海报
     */
    Integer QUERY_SCHOOL_POSTER = 3;

    /**
     * 4：班级海报
     */
    Integer QUERY_CLASS_POSTER = 4;

    /**
     * 会议和考试场景需要提前30分钟霸屏
     */
    Integer MEETING_SCENE_BEFORE_TIME = -30;

    /**
     * 扫码返回的业务状态：1成功（最大权限），2失败-无权限编辑
     */
    Integer SCAN_RESULT_TYPE_SUCCESS = 1;
    Integer SCAN_RESULT_TYPE_FAIL = 2;
}
