package com.fh.cloud.screen.service.cs.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.cs.api.CsContactApi;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactConditionBo;
import com.fh.cloud.screen.service.cs.entity.dto.CsContactDto;
import com.fh.cloud.screen.service.cs.entity.vo.CsContactVo;
import com.fh.cloud.screen.service.cs.service.ICsContactService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * Cultural-Station文化小站联系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
@RestController
@Validated
public class CsContactController implements CsContactApi{
	
    @Autowired
    private ICsContactService csContactService;

    /**
     * 查询Cultural-Station文化小站联系表分页列表
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
     */
    @Override
    public AjaxResult<PageInfo<CsContactVo>> getCsContactPageListByCondition(@RequestBody CsContactConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CsContactVo> pageInfo = new PageInfo<>(csContactService.getCsContactListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询Cultural-Station文化小站联系表列表
	 * <AUTHOR>
	 * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult<List<CsContactVo>> getCsContactListByCondition(@RequestBody CsContactConditionBo condition){
		List<CsContactVo> list = csContactService.getCsContactListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增Cultural-Station文化小站联系表
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
     */
	@Override
    public AjaxResult addCsContact(@Validated @RequestBody CsContactBo csContactBo){
		return csContactService.addCsContact(csContactBo);
    }

    /**
	 * 修改Cultural-Station文化小站联系表
	 * @param csContactBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult updateCsContact(@Validated @RequestBody CsContactBo csContactBo) {
		if(null == csContactBo.getId()) {
			return AjaxResult.fail("Cultural-Station文化小站联系表id不能为空");
		}
		return csContactService.updateCsContact(csContactBo);
	}

	/**
	 * 查询Cultural-Station文化小站联系表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult<CsContactVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("Cultural-Station文化小站联系表id不能为空");
		}
		CsContactConditionBo condition = new CsContactConditionBo();
		condition.setId(id);
		CsContactVo vo = csContactService.getCsContactByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除Cultural-Station文化小站联系表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CsContactDto csContactDto = new CsContactDto();
		csContactDto.setId(id);
		csContactDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(csContactService.updateById(csContactDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
