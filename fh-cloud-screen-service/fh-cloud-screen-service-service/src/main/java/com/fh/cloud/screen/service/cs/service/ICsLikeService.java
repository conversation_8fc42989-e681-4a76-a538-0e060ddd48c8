package com.fh.cloud.screen.service.cs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.cs.entity.dto.CsLikeDto;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeConditionBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeBo;
import com.fh.cloud.screen.service.cs.entity.vo.CsLikeVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * Cultural-Station文化小站喜欢记录表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
public interface ICsLikeService extends IService<CsLikeDto> {

    List<CsLikeVo> getCsLikeListByCondition(CsLikeConditionBo condition);

	AjaxResult addCsLike(CsLikeBo csLikeBo);

	AjaxResult updateCsLike(CsLikeBo csLikeBo);

	CsLikeVo getCsLikeByCondition(CsLikeConditionBo condition);

}

