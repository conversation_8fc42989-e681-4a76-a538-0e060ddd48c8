package com.fh.cloud.screen.service.cs.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactConditionBo;
import com.fh.cloud.screen.service.cs.entity.dto.CsContactDto;
import com.fh.cloud.screen.service.cs.entity.vo.CsContactVo;
import com.fh.cloud.screen.service.cs.mapper.CsContactMapper;
import com.fh.cloud.screen.service.cs.service.ICsContactService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * Cultural-Station文化小站联系表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
@Service
public class CsContactServiceImpl extends ServiceImpl<CsContactMapper, CsContactDto> implements ICsContactService {

	@Resource
	private CsContactMapper csContactMapper;
	
    @Override
	public List<CsContactVo> getCsContactListByCondition(CsContactConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return csContactMapper.getCsContactListByCondition(condition);
	}

	@Override
	public AjaxResult addCsContact(CsContactBo csContactBo) {
		CsContactDto csContact = new CsContactDto();
		BeanUtils.copyProperties(csContactBo, csContact);
		csContact.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(csContact)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCsContact(CsContactBo csContactBo) {
		CsContactDto csContact = new CsContactDto();
		BeanUtils.copyProperties(csContactBo, csContact);
		if(updateById(csContact)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CsContactVo getCsContactByCondition(CsContactConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		CsContactVo vo = csContactMapper.getCsContactByCondition(condition);
		return vo;
	}

}