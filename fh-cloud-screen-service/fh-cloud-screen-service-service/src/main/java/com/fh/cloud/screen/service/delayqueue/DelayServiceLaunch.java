package com.fh.cloud.screen.service.delayqueue;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 队列任务启动器，使用观察者模式
 * 
 * <AUTHOR>
 * @date 2022/12/21 15:22
 */
@Slf4j
@Component
public class DelayServiceLaunch {
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 所有的延迟任务服务
     */
    private List<? extends DelayService> delayServices;
    /**
     * 运行模式：0初始状态未运行，1自动运行，2手动运行（需要手动设置delayServices）
     */
    private int runMode = 0;

    /**
     * 手动设置delayServices
     *
     * @param delayServices the delay services
     * <AUTHOR>
     * @date 2022 -12-21 15:26:27
     */
    public DelayServiceLaunch setDelayServices(List<? extends DelayService> delayServices) {
        this.delayServices = delayServices;
        return this;
    }

    /**
     * 手动设置runMode
     *
     * @param runMode the run mode
     * @return run mode
     * <AUTHOR>
     * @date 2022 -12-21 15:41:19
     */
    public DelayServiceLaunch setRunMode(int runMode) {
        this.runMode = runMode;
        if (runMode == RunModeType.AUTO.getValue()) {
            // 自动的方式运行
            Map<String, DelayService> beansOfType = applicationContext.getBeansOfType(DelayService.class);
            this.delayServices = beansOfType.entrySet().stream()
                .map(stringDelayServiceEntry -> stringDelayServiceEntry.getValue()).collect(Collectors.toList());
        }
        return this;
    }

    /**
     * 运行并给出result，后续可以考虑增加取消运行的方法
     *
     * @return the string
     * <AUTHOR>
     * @date 2022 -12-21 15:41:13
     */
    public void run() {
        if (runMode != RunModeType.AUTO.getValue() && runMode != RunModeType.MANUAL.getValue()) {
            log.error("运行模式不合法");
            return;
        }
        delayServices.forEach(delayService -> delayService.init());
        log.info("延迟队列启动器正在以【" + runMode + "】模式运行");
    }

    /**
     * 运行模式枚举类
     */
    public enum RunModeType {

        /**
         * 导入
         */
        AUTO(1),
        /***
         * 单个添加
         */
        MANUAL(2),;

        private int value;

        RunModeType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
}
