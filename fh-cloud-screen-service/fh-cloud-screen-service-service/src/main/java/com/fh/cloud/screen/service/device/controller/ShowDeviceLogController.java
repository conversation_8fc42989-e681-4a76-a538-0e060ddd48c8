package com.fh.cloud.screen.service.device.controller;

import java.util.List;

import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.delayqueue.DelayService;
import com.fh.cloud.screen.service.delayqueue.entity.TaskDelayed;
import com.fh.cloud.screen.service.device.api.ShowDeviceLogApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLogDto;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceLogService;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.DeviceLogStatusType;
import com.fh.cloud.screen.service.enums.LogMethodType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 设备日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
@RestController
@Validated
public class ShowDeviceLogController implements ShowDeviceLogApi {

    @Autowired
    private IShowDeviceLogService showDeviceLogService;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Qualifier("DelayServiceLogImpl")
    @Autowired
    private DelayService<ShowDeviceLogVo> delayService;
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 查询设备日志表分页列表
     * 
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @Override
    public AjaxResult<PageInfo<ShowDeviceLogVo>>
        getShowDeviceLogPageListByCondition(@RequestBody ShowDeviceLogConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ShowDeviceLogVo> pageInfo =
            new PageInfo<>(showDeviceLogService.getShowDeviceLogListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询设备日志表列表
     * 
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @Override
    public AjaxResult<List<ShowDeviceLogVo>>
        getShowDeviceLogListByCondition(@RequestBody ShowDeviceLogConditionBo condition) {
        List<ShowDeviceLogVo> list = showDeviceLogService.getShowDeviceLogListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增设备日志表
     * 
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @Override
    public AjaxResult addShowDeviceLog(@Validated @RequestBody ShowDeviceLogBo showDeviceLogBo) {
        String deviceNumber = showDeviceLogBo.getDeviceNumber();
        if (StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("deviceNumber不可为空");
        }
        if (showDeviceLogBo.getShowDeviceId() == null) {
            Long showDeviceId = showDeviceService.getByDeviceNum(deviceNumber).getShowDeviceId();
            showDeviceLogBo.setShowDeviceId(showDeviceId);
        }
        showDeviceLogService.addShowDeviceLog(showDeviceLogBo);
        return AjaxResult.success();
    }

    /**
     * 修改设备日志表
     * 
     * @param showDeviceLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @Override
    public AjaxResult updateShowDeviceLog(@Validated @RequestBody ShowDeviceLogBo showDeviceLogBo) {
        if (null == showDeviceLogBo.getId()) {
            return AjaxResult.fail("设备日志表id不能为空");
        }
        return showDeviceLogService.updateShowDeviceLog(showDeviceLogBo);
    }

    /**
     * 查询设备日志表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @Override
    public AjaxResult<ShowDeviceLogVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("设备日志表id不能为空");
        }
        ShowDeviceLogConditionBo condition = new ShowDeviceLogConditionBo();
        condition.setId(id);
        ShowDeviceLogVo vo = showDeviceLogService.getShowDeviceLogByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除设备日志表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ShowDeviceLogDto showDeviceLogDto = new ShowDeviceLogDto();
        showDeviceLogDto.setId(id);
        showDeviceLogDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (showDeviceLogService.updateById(showDeviceLogDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 查询设备日志表详情-根据deviceNumber。如果数据库有多条则选取最后一条
     *
     * @param deviceNumber the device number
     * @return detail by number
     * <AUTHOR>
     * @date 2022 -12-20 15:05:05
     * @returnType AjaxResult
     */
    @Override
    public AjaxResult<ShowDeviceLogVo> getDetailByNumber(String deviceNumber) {
        if (StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("deviceNumber不可为空");
        }
        return AjaxResult.success(showDeviceLogService.getDetailByNumber(deviceNumber));
    }

    /**
     * 云屏设备提交日志数据，会更新日志数据
     *
     * @param showDeviceLogBo the show device log bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -12-20 15:05:05
     */
    @Override
    public AjaxResult uploadShowDeviceLog(ShowDeviceLogBo showDeviceLogBo) {
        // 主动提交一次日志
        if (StringUtils.isNotBlank(showDeviceLogBo.getLogMethod())
            && showDeviceLogBo.getLogMethod().equals(LogMethodType.CREATE.getValue())) {
            showDeviceLogBo.setDeviceLogStatus(DeviceLogStatusType.SUCCESS.getValue());
            if (showDeviceLogBo.getShowDeviceId() == null) {
                Long showDeviceId =
                    showDeviceService.getByDeviceNum(showDeviceLogBo.getDeviceNumber()).getShowDeviceId();
                showDeviceLogBo.setShowDeviceId(showDeviceId);
                return AjaxResult.success(showDeviceLogService.addShowDeviceLog(showDeviceLogBo));
            }
        }

        // 更新一次日志
        ShowDeviceLogVo showDeviceLogVo = showDeviceLogService.getDetailByNumber(showDeviceLogBo.getDeviceNumber());
        if (showDeviceLogVo == null) {
            return AjaxResult.fail("未查询到获取日志的请求");
        }
        if (showDeviceLogVo.getDeviceLogStatus().equals(DeviceLogStatusType.SUCCESS.getValue())) {
            return AjaxResult.fail("日志已经存在，需要重新发起日志请求");
        }
        // if (showDeviceLogVo.getDeviceLogStatus().equals(DeviceLogStatusType.FAIL.getValue())) {
        // return AjaxResult.fail("日志处理已经超时，需要重新发起日志请求");
        // }
        showDeviceLogBo.setId(showDeviceLogVo.getId());
        showDeviceLogBo.setDeviceNumber(showDeviceLogVo.getDeviceNumber());
        showDeviceLogBo.setIsDelete(showDeviceLogVo.getIsDelete());
        showDeviceLogBo.setOrganizationId(showDeviceLogVo.getOrganizationId());
        showDeviceLogBo.setShowDeviceId(showDeviceLogVo.getShowDeviceId());
        showDeviceLogBo.setDeviceLogStatus(DeviceLogStatusType.SUCCESS.getValue());
        return showDeviceLogService.updateShowDeviceLog(showDeviceLogBo);
    }

    /**
     * web发起日志拉取请求，会新增一条日志数据（由云屏提交）
     *
     * @param showDeviceLogBo the show device log bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -12-21 10:51:11
     */
    @Override
    public AjaxResult launchShowDeviceLog(ShowDeviceLogBo showDeviceLogBo) {
        String deviceNumber = showDeviceLogBo.getDeviceNumber();
        if (StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("deviceNumber不可为空");
        }
        if (showDeviceLogBo.getShowDeviceId() == null) {
            ShowDevice byDeviceNum = showDeviceService.getByDeviceNum(deviceNumber);
            Long showDeviceId = byDeviceNum.getShowDeviceId();
            Long organizationId = byDeviceNum.getOrganizationId();
            showDeviceLogBo.setShowDeviceId(showDeviceId);
            showDeviceLogBo.setOrganizationId(organizationId);
        }

        // 添加截图数据
        Long id = showDeviceLogService.addShowDeviceLog(showDeviceLogBo);
        if (id == null) {
            return AjaxResult.fail("添加日志数据失败");
        }

        // 添加任务到延迟队列
        TaskDelayed<ShowDeviceLogVo> taskDelayed =
            new TaskDelayed<>(String.valueOf(id), null, ConstantsLong.DELAY_SERVICE_LOG_EXPIRE_IN, null);
        delayService.addToDelayQueue(taskDelayed);

        // 通知云屏端ws消息
        applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(MessageWsType.SCREEN_LOG.getValue(),
            showDeviceLogBo.getOrganizationId(), Lists.newArrayList(deviceNumber), null));
        return AjaxResult.success();
    }
}
