package com.fh.cloud.screen.service.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 展示设备表，例如云屏Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ShowDeviceMapper extends BaseMapper<ShowDevice> {

    List<ShowDeviceVo> getShowDeviceListByCondition(ShowDeviceListConditionBo condition);

    /**
     * 获取地点组下的所有行政教室的设备
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param classesIds the classes ids
     * @param spaceGroupIds the space group ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:39:18
     */
    List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfXz(@Param("organizationId") Long organizationId,
        @Param("campusId") Long campusId, @Param("classesIds") List<Long> classesIds,
        @Param("spaceGroupIds") List<Long> spaceGroupIds);

    /**
     * 获取地点组下的所有非行政教室的设备
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param spaceInfoIds the space info ids
     * @param spaceGroupIds the space group ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:39:18
     */
    List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfNotXz(@Param("organizationId") Long organizationId,
        @Param("campusId") Long campusId, @Param("spaceInfoIds") List<Long> spaceInfoIds,
        @Param("spaceGroupIds") List<Long> spaceGroupIds);

    /**
     * 根据地点组id查询绑定了地点的设备列表-行政地点
     *
     * @param condition the condition
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-11 16:01:42
     */
    List<ShowDeviceVo> listShowDeviceDataByConditionOfXz(ShowDeviceListConditionBo condition);

    /**
     * 根据地点组id查询绑定了地点的设备列表-非行政地点
     *
     * @param condition the condition
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-11 16:01:42
     */
    List<ShowDeviceVo> listShowDeviceDataByConditionOfNotXz(ShowDeviceListConditionBo condition);

    /**
     * 根据设备ids查询设备列表
     *
     * @param showDeviceIds the show device ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-23 20:32:57
     */
    List<ShowDeviceVo> listShowDeviceDataByIds(@Param("showDeviceIds") List<Long> showDeviceIds);

    /**
     * 查询该学校绑定了地点的所有设备
     *
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-24 16:38:13
     */
    List<ShowDeviceVo> listShowDeviceBindByOrganizationId(@Param("organizationId") Long organizationId);

    /**
     * 查询专用教室的设备
     * 
     * @param spaceIfoIds
     * @return
     */
    List<ShowDeviceVo> listBySpaceInfoIdsOfNotXz(@Param("spaceIfoIds") List<Long> spaceIfoIds);

    /**
     * 查询行政班级设备
     *
     * @param spaceInfoIds
     * @return
     */
    List<ShowDeviceVo> listBySpaceInfoIdsOfXz(@Param("spaceInfoIds") List<Long> spaceInfoIds);

    /**
     * 查询设备列表
     * 
     * @param condition
     * @return
     */
    List<ShowDeviceVo> listShowDeviceBindByCondition(ShowDeviceListConditionBo condition);

    /**
     * 根据deviceIds查询绑定了地点的设备列表
     *
     * @param deviceIds the device ids
     * @return list list
     * <AUTHOR>
     * @date 2024 -04-08 14:13:53
     */
    List<ShowDeviceVo> listShowDeviceByDeviceIdsOfBind(@Param("deviceIds")List<Long> deviceIds);
}
