package com.fh.cloud.screen.service.device.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceFullCustomDto;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceFullCustomMapper;
import com.fh.cloud.screen.service.device.service.IShowDeviceFullCustomService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.redis.component.RedisComponent;

/**
 * 云屏全屏非全屏设置自定义接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
@Service
public class ShowDeviceFullCustomServiceImpl extends ServiceImpl<ShowDeviceFullCustomMapper, ShowDeviceFullCustomDto>
    implements IShowDeviceFullCustomService {

    @Resource
    private ShowDeviceFullCustomMapper showDeviceFullCustomMapper;
    @Resource
    private RedisComponent redisComponent;

    @Override
    public List<ShowDeviceFullCustomVo>
        getShowDeviceFullCustomListByCondition(ShowDeviceFullCustomConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return showDeviceFullCustomMapper.getShowDeviceFullCustomListByCondition(condition);
    }

    @Override
    public AjaxResult addShowDeviceFullCustom(ShowDeviceFullCustomBo showDeviceFullCustomBo) {
        ShowDeviceFullCustomDto showDeviceFullCustom = new ShowDeviceFullCustomDto();
        BeanUtils.copyProperties(showDeviceFullCustomBo, showDeviceFullCustom);
        showDeviceFullCustom.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(showDeviceFullCustom)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateShowDeviceFullCustom(ShowDeviceFullCustomBo showDeviceFullCustomBo) {
        ShowDeviceFullCustomDto showDeviceFullCustom = new ShowDeviceFullCustomDto();
        BeanUtils.copyProperties(showDeviceFullCustomBo, showDeviceFullCustom);
        if (updateById(showDeviceFullCustom)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ShowDeviceFullCustomVo getShowDeviceFullCustomByCondition(ShowDeviceFullCustomConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ShowDeviceFullCustomVo vo = showDeviceFullCustomMapper.getShowDeviceFullCustomByCondition(condition);
        if (null != vo) {
            return vo;
        }
        return null;
    }

    @Override
    public List<ShowDeviceFullCustomVo> getShowDeviceFullCustomListByOrganizationIdWithCache(Long organizationId) {
        if (organizationId == null) {
            return Lists.newArrayList();
        }

        // 缓存是否存在，存在则返回
        String cacheKey = ConstantsRedis.DEVICE_FULL_CUSTOM_BY_ORG_PREFIX + organizationId;
        if (redisComponent.hasKey(cacheKey)) {
            String value = (String)redisComponent.get(cacheKey);
            return JSONObject.parseArray(value, ShowDeviceFullCustomVo.class);
        }

        // 缓存不存在则查询db，同时更新缓存返回
        List<ShowDeviceFullCustomVo> showDeviceFullCustomVos =
            getShowDeviceFullCustomListByOrganizationId(organizationId);
        if (CollectionUtils.isEmpty(showDeviceFullCustomVos)) {
            return showDeviceFullCustomVos;
        }
        String res = JSONObject.toJSONString(showDeviceFullCustomVos);
        redisComponent.set(cacheKey, res, ConstantsRedis.DEVICE_FULL_CUSTOM_BY_ORG_EXPIRE_IN);
        return showDeviceFullCustomVos;
    }

    /**
     * 根据学校id查询所有的自定义全屏非全屏的配置
     *
     * @param organizationId the organization id
     * @return show device full custom list by organization id with cache
     * <AUTHOR>
     * @date 2023 -06-12 16:59:06
     */
    private List<ShowDeviceFullCustomVo> getShowDeviceFullCustomListByOrganizationId(Long organizationId) {
        if (organizationId == null) {
            return Lists.newArrayList();
        }

        ShowDeviceFullCustomConditionBo condition = new ShowDeviceFullCustomConditionBo();
        condition.setOrganizationId(organizationId);
        return getShowDeviceFullCustomListByCondition(condition);
    }

    @Override
    public List<Long> getExistOrganizationIds() {
        return showDeviceFullCustomMapper.getExistOrganizationIds();
    }
}