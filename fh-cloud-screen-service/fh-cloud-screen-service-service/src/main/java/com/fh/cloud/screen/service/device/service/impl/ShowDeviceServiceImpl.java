package com.fh.cloud.screen.service.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.enums.DeviceActivationEnums;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceMapper;
import com.fh.cloud.screen.service.device.service.IShowDeviceLabelRelService;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.DeviceFullType;
import com.fh.cloud.screen.service.enums.DeviceStatusType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.role.servise.UserRoleService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenQrcodeContentVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentService;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialService;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialSpaceGroupRelService;
import com.fh.cloud.screen.service.screen.service.IScreenSceneModuleRelService;
import com.fh.cloud.screen.service.screen.service.IScreenSceneService;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.fh.cloud.screen.service.space.service.ISpaceDeviceRelService;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.EncryptUtil;
import com.fh.cloud.screen.service.utils.RandomUtil;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import com.light.user.campus.entity.vo.CampusVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 展示设备表，例如云屏接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class ShowDeviceServiceImpl extends ServiceImpl<ShowDeviceMapper, ShowDevice> implements IShowDeviceService {
    @Resource
    private ShowDeviceMapper showDeviceMapper;
    @Lazy
    @Autowired
    private IScreenContentService screenContentService;
    @Lazy
    @Autowired
    private IScreenSceneModuleRelService screenSceneModuleRelService;
    @Lazy
    @Autowired
    private IScreenSceneService screenSceneService;
    @Lazy
    @Autowired
    private ISpaceGroupService spaceGroupService;
    @Lazy
    @Autowired
    private IShowDeviceService showDeviceService;
    @Lazy
    @Autowired
    private IClassesInfoService classesInfoService;
    @Lazy
    @Autowired
    private IScreenContentSpecialService screenContentSpecialService;
    @Lazy
    @Autowired
    private IScreenContentSpecialSpaceGroupRelService screenContentSpecialSpaceGroupRelService;
    @Lazy
    @Autowired
    private MessageService messageService;
    @Lazy
    @Autowired
    private BaseDataService baseDataService;
    @Resource
    private ISpaceDeviceRelService spaceDeviceRelService;
    @Resource
    private ISpaceInfoService spaceInfoService;
    @Autowired
    private RedisComponent redisComponent;
    @Resource
    private IShowDeviceLabelRelService showDeviceLabelRelService;
    @Resource
    private UserRoleService userRoleService;

    @Override
    public List<ShowDeviceVo> getShowDeviceListByConditionSingle(ShowDeviceListConditionBo condition) {
        return showDeviceMapper.getShowDeviceListByCondition(condition);
    }

    @Override
    public List<ShowDeviceVo> getShowDeviceListByCondition(ShowDeviceListConditionBo condition) {
        // 检索状态时，查询全部，状态重新赋值并返回前端
        Integer statusQuery = null;
        if (null != condition.getDeviceStatus()) {
            statusQuery = condition.getDeviceStatus();
            condition.setDeviceStatus(null);
        }
        // 获取设备
        List<ShowDeviceVo> showDeviceVos = showDeviceMapper.getShowDeviceListByCondition(condition);
        if (CollectionUtils.isNotEmpty(showDeviceVos)) {
            // 更新设备异常状态
            showDevicesSetOnlineError(showDeviceVos, condition.getOrganizationId());
            // 有状态检索
            if (null != statusQuery) {
                Integer finalStatusQuery = statusQuery;
                showDeviceVos = showDeviceVos.stream().filter(x -> x.getDeviceStatus().equals(finalStatusQuery))
                    .collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return showDeviceVos;
        }
        // 获取设备关联地点
        List<Long> deviceIds =
            showDeviceVos.stream().map(ShowDeviceVo::getShowDeviceId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<SpaceDeviceRel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SpaceDeviceRel::getIsDelete, StatusEnum.NOTDELETE);
        lqw.in(SpaceDeviceRel::getShowDeviceId, deviceIds);
        List<SpaceDeviceRel> deviceRelList = spaceDeviceRelService.list(lqw);
        if (CollectionUtils.isEmpty(deviceRelList)) {
            return showDeviceVos;
        }
        // 根据关联地点封装空间名称
        Map<Long, List<SpaceDeviceRel>> relMap =
            deviceRelList.stream().collect(Collectors.groupingBy(SpaceDeviceRel::getShowDeviceId));
        for (ShowDeviceVo showDeviceVo : showDeviceVos) {
            List<SpaceDeviceRel> spaceDeviceRels = relMap.get(showDeviceVo.getShowDeviceId());
            if (CollectionUtils.isEmpty(spaceDeviceRels)) {
                continue;
            }
            SpaceDeviceRel spaceDeviceRel = spaceDeviceRels.get(0);
            showDeviceVo.setSpaceGroupUseType(spaceDeviceRel.getSpaceGroupUseType());
            showDeviceVo.setSpaceInfoId(spaceDeviceRel.getSpaceInfoId());
        }

        List<ShowDeviceVo> showDeviceVosXz = showDeviceVos.stream()
            .filter(
                x -> x.getSpaceGroupUseType() != null && SpaceGroupUseType.XZ.getValue() == x.getSpaceGroupUseType())
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(showDeviceVosXz)) {
            List<Long> classesIds =
                showDeviceVosXz.stream().map(ShowDeviceVo::getSpaceInfoId).distinct().collect(Collectors.toList());
            ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
            clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
            clazzConditionBoExt.setQueryType(ConstantsInteger.APP_QUERY);
            clazzConditionBoExt.setIds(classesIds);
            List<ClazzInfoVo> clazzInfoVoList =
                (List<ClazzInfoVo>)baseDataService.getClazzInfoVoList(clazzConditionBoExt).get("list");
            Map<Long, String> clazzInfoMap =
                clazzInfoVoList.stream().collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getClassesNameShow));
            showDeviceVos.stream().filter(
                x -> x.getSpaceGroupUseType() != null && SpaceGroupUseType.XZ.getValue() == x.getSpaceGroupUseType())
                .forEach(showDeviceVo -> {
                    showDeviceVo.setSpaceInfoName(clazzInfoMap.get(showDeviceVo.getSpaceInfoId()));
                });
        }

        List<ShowDeviceVo> showDeviceVosNXZ = showDeviceVos.stream().filter(
            x -> x.getSpaceGroupUseType() != null && SpaceGroupUseType.NOT_XZ.getValue() == x.getSpaceGroupUseType())
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(showDeviceVosNXZ)) {
            List<Long> spaceInfoIds =
                showDeviceVosNXZ.stream().map(ShowDeviceVo::getSpaceInfoId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<SpaceInfo> qw = new LambdaQueryWrapper<>();
            qw.eq(SpaceInfo::getIsDelete, StatusEnum.NOTDELETE);
            qw.in(SpaceInfo::getSpaceInfoId, spaceInfoIds);
            List<SpaceInfo> spaceInfoList = spaceInfoService.list(qw);
            Map<Long, String> spaceMap = spaceInfoList.stream()
                .collect(Collectors.toMap(SpaceInfo::getSpaceInfoId, SpaceInfo::getSpaceInfoName));
            showDeviceVos.stream().filter(x -> x.getSpaceGroupUseType() != null
                && SpaceGroupUseType.NOT_XZ.getValue() == x.getSpaceGroupUseType()).forEach(showDeviceVo -> {
                    showDeviceVo.setSpaceInfoName(spaceMap.get(showDeviceVo.getSpaceInfoId()));
                });
        }

        return showDeviceVos;
    }

    @Override
    public boolean addShowDevice(ShowDeviceBo showDeviceBo) {
        ShowDevice showDevice = new ShowDevice();
        BeanUtils.copyProperties(showDeviceBo, showDevice);
        showDevice.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // 设备号
        if (StringUtils.isBlank(showDevice.getDeviceNumber())) {
            showDevice.setDeviceNumber(getRandomDeviceNumber());
        }
        // 出货号
        Date date = new Date();
        ShowDeviceListConditionBo condition = new ShowDeviceListConditionBo();
        condition.setCreateTime(date);
        int count = showDeviceMapper.getShowDeviceListByCondition(condition).size();
        String s = DateKit.getStringDay(date).replaceAll("-", "");
        showDevice.setShipmentNo(s + getCode(count + 1));
        return save(showDevice);
    }

    /**
     * Add show device batch.
     *
     * @param showDeviceBos the show device bo
     * @return the boolean
     */
    @Override
    public boolean addShowDeviceBatch(List<ShowDeviceBo> showDeviceBos) {
        for (ShowDeviceBo showDeviceBo : showDeviceBos) {
            addShowDevice(showDeviceBo);
        }
        return true;
    }

    private String getRandomDeviceNumber() {
        // 随机生成
        String deviceNumber = RandomUtil.randomString(RandomUtil.BASE_UPPER_CHAR, 6);
        if (CollectionUtils.isEmpty(showDeviceMapper
            .selectList((new LambdaQueryWrapper<ShowDevice>()).eq(ShowDevice::getDeviceNumber, deviceNumber)))) {
            return deviceNumber;
        } else {
            return getRandomDeviceNumber();
        }
    }

    private String getCode(int count) {
        StringBuilder code = new StringBuilder();
        int length = 4 - String.valueOf(count).length();
        for (int i = length; i > 0; i--) {
            code.append("0");
        }
        return code + String.valueOf(count);
    }

    @Override
    public boolean updateShowDevice(ShowDeviceBo showDeviceBo) {
        ShowDevice showDevice = new ShowDevice();
        BeanUtils.copyProperties(showDeviceBo, showDevice);
        return updateById(showDevice);
    }

    @Override
    public ShowDeviceVo getDetail(Long showDeviceId) {
        if (showDeviceId == null) {
            return null;
        }
        LambdaQueryWrapper<ShowDevice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ShowDevice::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ShowDevice::getShowDeviceId, showDeviceId);
        ShowDevice showDevice = getOne(lqw);
        if (null == showDevice) {
            return null;
        }
        ShowDeviceVo showDeviceVo = new ShowDeviceVo();
        BeanUtils.copyProperties(showDevice, showDeviceVo);
        return showDeviceVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<ShowDeviceVo> activate(ShowDeviceBo bo) {
        // 设备号
        final String deviceNumber = bo.getDeviceNumber();
        final ShowDevice device = this.getByDeviceNum(deviceNumber);
        if (device == null) {
            return AjaxResult.fail("序列号错误,请重新输入!");
        }

        // 卡号已绑定，并且mac地址一致则更新设备信息
        if (StringUtils.isNotBlank(device.getDeviceMacAddress())
            && device.getDeviceMacAddress().equals(bo.getDeviceMacAddress())) {
            // 更新模式
            device.setDevicePattern(bo.getDevicePattern());
            if (bo.getDeviceFullType() == null) {
                device.setDeviceFullType(DeviceFullType.VERTICAL.getValue());
            } else {
                device.setDeviceFullType(bo.getDeviceFullType());
            }
            this.baseMapper.updateById(device);
            final ShowDeviceVo showDeviceVo = BeanUtil.toBean(device, ShowDeviceVo.class);
            return AjaxResult.success(showDeviceVo);
        }

        // 校验激活状态
        final String deviceActivation = device.getDeviceActivation();
        if (DeviceActivationEnums.ENABLED.getVal().equals(deviceActivation)) {
            return AjaxResult.fail("设备序列号重复，请重新输入");
        }

        // 主业务更新
        device.setDeviceActivation(DeviceActivationEnums.ENABLED.getVal());
        device.setDeviceMacAddress(bo.getDeviceMacAddress());
        device.setDevicePattern(bo.getDevicePattern());
        if (bo.getDeviceFullType() == null) {
            device.setDeviceFullType(DeviceFullType.VERTICAL.getValue());
        } else {
            device.setDeviceFullType(bo.getDeviceFullType());
        }
        this.baseMapper.updateById(device);
        final ShowDeviceVo showDeviceVo = BeanUtil.toBean(device, ShowDeviceVo.class);
        return AjaxResult.success(showDeviceVo);
    }

    /**
     * 根据设备号码 获取 设备信息
     *
     * @param deviceNum
     * @return
     */
    @Override
    public ShowDevice getByDeviceNum(String deviceNum) {
        QueryWrapper<ShowDevice> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ShowDevice::getDeviceNumber, deviceNum).eq(ShowDevice::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        return this.showDeviceMapper.selectOne(queryWrapper);
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceVoByClassesId(Long classesId) {
        if (classesId == null) {
            return Lists.newArrayList();
        }

        return showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfXz(null, null, Lists.newArrayList(classesId), null);
    }

    /**
     * 根据内容获取设备列表
     *
     * @return
     */
    @Override
    public List<ShowDeviceVo> listShowDeviceVoByContentId(Long contentId) {
        // 获取内容的模块
        ScreenContentVo screenContentVo = screenContentService.getDetail(contentId);
        if (screenContentVo == null) {
            return Lists.newArrayList();
        }
        // 域范围信息，用于精确推送到指定设备
        Long organizationId = screenContentVo.getOrganizationId();
        Long campusId = screenContentVo.getCampusId();
        if (campusId != null && campusId.equals(ConstantsLong.NUM_0)) {
            campusId = null;
        }
        Long classesId = screenContentVo.getClassesId();

        return showDeviceService.listShowDeviceVoByModuleId(organizationId, campusId, classesId,
            screenContentVo.getScreenModuleDataId());
    }

    /**
     * 根据特殊内容获取设备列表
     *
     * @return
     */
    @Override
    public List<ShowDeviceVo> listShowDeviceVoByContentSpecialId(Long contentSpecialId) {
        ScreenContentSpecialVo screenContentSpecialVo = screenContentSpecialService.getDetail(contentSpecialId);
        if (screenContentSpecialVo == null) {
            return Lists.newArrayList();
        }
        // 域范围信息，用于精确推送到指定设备
        Long organizationId = screenContentSpecialVo.getOrganizationId();
        Long campusId = screenContentSpecialVo.getCampusId();
        if (campusId != null && campusId.equals(ConstantsLong.NUM_0)) {
            campusId = null;
        }

        List<Long> spaceGroupIds =
            screenContentSpecialSpaceGroupRelService.listSpaceGroupIdsByScreenContentSpecialId(contentSpecialId);

        // 查询设备，因为spaceGroupIds不会重复，因此重复数据带入两边查询没有问题。
        List<ShowDeviceVo> showDeviceVosXz =
            showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, campusId, null, spaceGroupIds);
        List<ShowDeviceVo> showDeviceVosNotXz =
            showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfNotXz(organizationId, campusId, null, spaceGroupIds);
        List<ShowDeviceVo> resultList = Lists.newArrayList(Iterables.concat(showDeviceVosXz, showDeviceVosNotXz));
        return resultList;
    }

    /**
     * 根据ModuleDataId获取设备列表(如无特指，moduleId指screen_module_data_id)
     *
     * @return
     */
    @Override
    public List<ShowDeviceVo> listShowDeviceVoByModuleId(Long organizationId, Long campusId, Long classesId,
        Long moduleId) {
        ScreenSceneModuleRelListConditionBo condition = new ScreenSceneModuleRelListConditionBo();
        condition.setScreenModuleDataId(moduleId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenSceneModuleRelVo> screenSceneModuleRelVos =
            screenSceneModuleRelService.getScreenSceneModuleRelListByCondition(condition);
        if (CollectionUtils.isEmpty(screenSceneModuleRelVos)) {
            return Lists.newArrayList();
        }
        List<Long> sceneIds =
            screenSceneModuleRelVos.stream().map(screenSceneModuleRelVo -> screenSceneModuleRelVo.getScreenSceneId())
                .distinct().collect(Collectors.toList());

        return showDeviceService.listShowDeviceVoBySceneIds(organizationId, campusId, classesId, sceneIds,
            StatusEnum.NOTDELETE.getCode());
    }

    /**
     * 根据场景获取设备列表。这里会推送给所有的横竖屏设备。因为地点为空则不作为条件，因此可能会多查询设备（多推送不会产生bug）
     *
     * @return
     */
    @Override
    public List<ShowDeviceVo> listShowDeviceVoBySceneIds(Long organizationId, Long campusId, Long classesId,
        List<Long> sceneIds, Integer isDelete) {
        // 获取场景的地点组
        // 5.11变更：增加场景绑定的地点和设备的处理。
        List<ScreenSceneVo> screenSceneVos = screenSceneService.listScreenSceneVoByScreenSceneIds(sceneIds, isDelete);
        if (CollectionUtils.isEmpty(screenSceneVos)) {
            return Lists.newArrayList();
        }
        if (organizationId == null) {
            organizationId = screenSceneVos.get(0).getOrganizationId();
        }
        if (campusId != null && campusId.equals(ConstantsLong.NUM_0)) {
            campusId = null;
        }

        // 1、非按点位发布的所有地点组
        List<Long> spaceGroupIds = screenSceneVos.stream()
            .filter(screenSceneVo -> screenSceneVo.getSpaceInfoId().equals(ConstantsLong.NUM_0)
                && screenSceneVo.getShowDeviceId().equals(ConstantsLong.NUM_0))
            .map(screenSceneVo -> screenSceneVo.getSpaceGroupId()).distinct().collect(Collectors.toList());
        List<SpaceGroupVo> groupVos = spaceGroupService.listSpaceGroupVoBySpaceGroupIds(spaceGroupIds);
        // 行政地点组
        List<Long> xzSpaceGroupIds = Lists.newArrayList();
        // 非行政地点组
        List<Long> notXzSpaceGroupIds = Lists.newArrayList();
        for (SpaceGroupVo groupVo : groupVos) {
            if (groupVo.getSpaceGroupUseType() != null
                && groupVo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue())) {
                xzSpaceGroupIds.add(groupVo.getSpaceGroupId());
            } else if (groupVo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue())) {
                notXzSpaceGroupIds.add(groupVo.getSpaceGroupId());
            }
        }
        // 查询设备:行政班级查询时候需要带classId条件,指定为某个班级。
        List<Long> classesIds = Lists.newArrayList();
        if (classesId != null && !classesId.equals(ConstantsLong.NUM_0)) {
            classesIds.add(classesId);
        }
        List<ShowDeviceVo> showDeviceVosXz = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(xzSpaceGroupIds)) {
            showDeviceVosXz = showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, campusId, classesIds,
                xzSpaceGroupIds);
        }
        List<ShowDeviceVo> showDeviceVosNotXz = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(notXzSpaceGroupIds)) {
            showDeviceVosNotXz = showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfNotXz(organizationId, campusId, null,
                notXzSpaceGroupIds);
        }

        // 2、按照点位发布且非按照设备发布的
        List<ScreenSceneVo> screenSceneVoListBySpaceOfXz = screenSceneVos.stream()
            .filter(screenSceneVo -> screenSceneVo.getSpaceInfoId() != null
                && !screenSceneVo.getSpaceInfoId().equals(ConstantsLong.NUM_0)
                && screenSceneVo.getSpaceGroupUseType() != null
                && screenSceneVo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue())
                && screenSceneVo.getShowDeviceId().equals(ConstantsLong.NUM_0))
            .collect(Collectors.toList());
        List<ScreenSceneVo> screenSceneVoListBySpaceOfNotXz = screenSceneVos.stream()
            .filter(screenSceneVo -> screenSceneVo.getSpaceInfoId() != null
                && !screenSceneVo.getSpaceInfoId().equals(ConstantsLong.NUM_0)
                && screenSceneVo.getSpaceGroupUseType() != null
                && screenSceneVo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue())
                && screenSceneVo.getShowDeviceId().equals(ConstantsLong.NUM_0))
            .collect(Collectors.toList());
        // 行政地点组
        final List<Long> xzSpaceGroupIdsBySpace = Lists.newArrayList();
        // 非行政地点组
        final List<Long> notXzSpaceGroupIdsBySpace = Lists.newArrayList();
        // 行政地点
        final List<Long> xzClassesIdsBySpace = Lists.newArrayList();
        // 非行政地点
        final List<Long> notXzSpaceInfoIdsBySpace = Lists.newArrayList();
        screenSceneVoListBySpaceOfXz.forEach(screenSceneVo -> {
            xzSpaceGroupIdsBySpace.add(screenSceneVo.getSpaceGroupId());
            xzClassesIdsBySpace.add(screenSceneVo.getSpaceInfoId());
        });
        screenSceneVoListBySpaceOfNotXz.forEach(screenSceneVo -> {
            notXzSpaceGroupIdsBySpace.add(screenSceneVo.getSpaceGroupId());
            notXzSpaceInfoIdsBySpace.add(screenSceneVo.getSpaceInfoId());
        });
        List<ShowDeviceVo> showDeviceVosXzBySpace = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(xzClassesIdsBySpace)) {
            showDeviceVosXzBySpace = showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, campusId,
                xzClassesIdsBySpace, xzSpaceGroupIds);
        }
        List<ShowDeviceVo> showDeviceVosNotXzBySpace = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(notXzSpaceInfoIdsBySpace)) {
            showDeviceVosNotXzBySpace = showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfNotXz(organizationId,
                campusId, notXzSpaceInfoIdsBySpace, notXzSpaceGroupIds);
        }

        // 3、按照点位发布且按照设备发布的
        List<ScreenSceneVo> screenSceneVoListByDevice = screenSceneVos.stream()
            .filter(screenSceneVo -> screenSceneVo.getSpaceInfoId() != null
                && !screenSceneVo.getSpaceInfoId().equals(ConstantsLong.NUM_0)
                && screenSceneVo.getShowDeviceId() != null
                && !screenSceneVo.getShowDeviceId().equals(ConstantsLong.NUM_0))
            .collect(Collectors.toList());
        List<Long> showDeviceIdsByDevice =
            screenSceneVoListByDevice.stream().map(ScreenSceneVo::getShowDeviceId).collect(Collectors.toList());
        List<ShowDeviceVo> showDeviceVosByDevice = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(showDeviceIdsByDevice)) {
            showDeviceVosByDevice = showDeviceMapper.listShowDeviceDataByIds(showDeviceIdsByDevice);
        }

        // 4、监管教育局发布
        List<ScreenSceneVo> screenSceneVoListByParentOrganizationId = screenSceneVos.stream()
            .filter(screenSceneVo -> !screenSceneVo.getParentOrganizationId().equals(ConstantsLong.NUM_0))
            .collect(Collectors.toList());
        List<ShowDeviceVo> showDeviceVosByParentOrganizationId = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(screenSceneVoListByParentOrganizationId)) {
            showDeviceVosByParentOrganizationId = showDeviceMapper.listShowDeviceBindByOrganizationId(organizationId);
        }

        List<ShowDeviceVo> resultList =
            Lists.newArrayList(Iterables.concat(showDeviceVosXz, showDeviceVosNotXz, showDeviceVosXzBySpace,
                showDeviceVosNotXzBySpace, showDeviceVosByDevice, showDeviceVosByParentOrganizationId));
        return resultList;
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfXz(Long organizationId, Long campusId,
        List<Long> classesIds, List<Long> spaceGroupIds) {
        if (CollectionUtils.isEmpty(spaceGroupIds)) {
            return Lists.newArrayList();
        }
        return showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, campusId, classesIds,
            spaceGroupIds);
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfNotXz(Long organizationId, Long campusId,
        List<Long> spaceInfoIds, List<Long> spaceGroupIds) {
        if (CollectionUtils.isEmpty(spaceGroupIds)) {
            return Lists.newArrayList();
        }
        return showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfNotXz(organizationId, campusId, spaceInfoIds,
            spaceGroupIds);
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceDataByCondition(ShowDeviceListConditionBo condition) {
        Integer spaceGroupUseType = condition.getSpaceGroupUseType();
        List<ShowDeviceVo> showDeviceVos = Lists.newArrayList();
        // 不分页的时候才支持查询全部返回
        if ((spaceGroupUseType == null && SystemConstants.NO_PAGE.equals(condition.getPageNo()))
            || spaceGroupUseType.equals(SpaceGroupUseType.XZ.getValue())) {
            // 根据数据权限先查询出对应的数据范围的班级
            ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
            clazzConditionBo.setOrganizationId(condition.getOrganizationId());
            AjaxResult<List<Long>> classesIdsListByDataAuthorityResult =
                baseDataService.getClassesIdsListByDataAuthority(clazzConditionBo);
            List<Long> classIdsByDataAuthority = Lists.newArrayList();
            if (classesIdsListByDataAuthorityResult.isSuccess()) {
                classIdsByDataAuthority = classesIdsListByDataAuthorityResult.getData();
            }
            List<Long> classIdsByDataAuthorityFinal = classIdsByDataAuthority;
            List<ShowDeviceVo> showDeviceVosXz = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(classIdsByDataAuthorityFinal)) {
                condition.setSpaceInfoIds(classIdsByDataAuthorityFinal);
                showDeviceVosXz = showDeviceMapper.listShowDeviceDataByConditionOfXz(condition);
            }
            if (CollectionUtils.isNotEmpty(showDeviceVosXz)) {
                // 设备仅能够保留数据范围内的设备
                // for (int i = 0; i < showDeviceVosXz.size(); i++) {
                // ShowDeviceVo showDeviceVo = showDeviceVosXz.get(i);
                // if (showDeviceVo.getSpaceInfoId() != null
                // && !classIdsByDataAuthorityFinal.contains(showDeviceVo.getSpaceInfoId())) {
                // showDeviceVosXz.remove(i);
                // i--;
                // }
                // }
                List<Long> classesIds =
                    showDeviceVosXz.stream().map(ShowDeviceVo::getSpaceInfoId).distinct().collect(Collectors.toList());
                List<ClazzInfoVo> clazzInfoVoList = classesInfoService.listClazzInfoVosByClassIds(classesIds);
                Map<Long, String> clazzInfoMap =
                    CollectionUtils.isEmpty(clazzInfoVoList) ? Maps.newHashMap() : clazzInfoVoList.stream()
                        .collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getClassesNameShow));
                Map<Long, String> clazzCampusMap = CollectionUtils.isEmpty(clazzInfoVoList) ? Maps.newHashMap()
                    : clazzInfoVoList.stream()
                        .filter(clazzInfoVo -> clazzInfoVo.getCampusId() != null && clazzInfoVo.getCampusName() != null)
                        .collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getCampusName));
                showDeviceVosXz.stream().forEach(showDeviceVo -> {
                    showDeviceVo.setSpaceInfoName(clazzInfoMap.get(showDeviceVo.getSpaceInfoId()));
                    showDeviceVo.setCampusName(clazzCampusMap.get(showDeviceVo.getSpaceInfoId()));
                });
                if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
                    showDeviceVos.addAll(showDeviceVosXz);
                } else {
                    showDeviceVos = showDeviceVosXz;
                }
            }
        }
        if ((spaceGroupUseType == null && SystemConstants.NO_PAGE.equals(condition.getPageNo()))
            || spaceGroupUseType.equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            // 数据权限-年级数据权限以上才可以查询地点
            List<ShowDeviceVo> showDeviceVosNotXz = Lists.newArrayList();
            Integer userDataAuthority = userRoleService.getUserMaxDataAuthority(null);
            if (userDataAuthority != null && userDataAuthority > ConstantsInteger.USER_DATA_AUTHORITY_GRADE) {
                showDeviceVosNotXz = showDeviceMapper.listShowDeviceDataByConditionOfNotXz(condition);
            }
            if (CollectionUtils.isNotEmpty(showDeviceVosNotXz)) {
                CampusListConditionBo campusListConditionBo = new CampusListConditionBo();
                campusListConditionBo.setPageNo(SystemConstants.NO_PAGE);
                campusListConditionBo.setOrganizationId(condition.getOrganizationId());
                List<CampusVo> campusVos = baseDataService.getCampusVoByCondition(campusListConditionBo);
                if (CollectionUtils.isNotEmpty(campusVos)) {
                    Map<Long, String> campusMap =
                        campusVos.stream().collect(Collectors.toMap(CampusVo::getId, CampusVo::getName));
                    showDeviceVosNotXz.stream()
                        .forEach(showDeviceVo -> showDeviceVo.setCampusName(campusMap.get(showDeviceVo.getCampusId())));
                }
                if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
                    showDeviceVos.addAll(showDeviceVosNotXz);
                } else {
                    showDeviceVos = showDeviceVosNotXz;
                }
            }
        }

        // 设备异常状态检测
        showDevicesSetOnlineError(showDeviceVos, condition.getOrganizationId());
        return showDeviceVos;
    }

    /**
     * 设备异常状态检测
     *
     * @param showDeviceVos, organizationId
     * @return void
     * <AUTHOR>
     * @date 2023/3/20 9:42
     */
    private void showDevicesSetOnlineError(List<ShowDeviceVo> showDeviceVos, Long organizationId) {
        // 设备异常状态检测
        if (CollectionUtils.isNotEmpty(showDeviceVos)) {
            List<String> showDeviceNumbers = messageService.listOnLineDeviceNumber(organizationId);
            showDeviceVos.forEach(showDeviceVo -> {
                if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                    showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
                } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                    showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
                }
            });
        }
    }

    /**
     * 设备异常状态检测
     *
     * @param showDeviceVos, organizationId
     * @return void
     * <AUTHOR>
     * @date 2024/4/08 9:42
     */
    private void showDevicesSetOnlineError(List<ShowDeviceVo> showDeviceVos) {
        // 设备异常状态检测
        if (CollectionUtils.isNotEmpty(showDeviceVos)) {
            List<String> showDeviceNumbers = messageService.listAllOnLineDeviceNumber();
            showDeviceVos.forEach(showDeviceVo -> {
                if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                    showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
                } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                    showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
                }
            });
        }
    }

    @Override
    public boolean updateShowDeviceByDeviceNumbers(Integer deviceStatusType, Long organizationId,
        List<String> deviceNumbers) {
        LambdaUpdateWrapper<ShowDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShowDevice::getOrganizationId, organizationId);
        updateWrapper.in(ShowDevice::getDeviceNumber, deviceNumbers);
        ShowDevice showDevice = new ShowDevice();
        showDevice.setDeviceStatus(deviceStatusType);
        showDevice.setUpdateTime(new Date());
        return update(showDevice, updateWrapper);
    }

    @Override
    public boolean updateShowDeviceFaceModByDeviceNumbers(Integer faceModType, Long organizationId,
        List<String> deviceNumbers) {
        LambdaUpdateWrapper<ShowDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShowDevice::getOrganizationId, organizationId);
        updateWrapper.in(ShowDevice::getDeviceNumber, deviceNumbers);
        ShowDevice showDevice = new ShowDevice();
        showDevice.setFaceModType(faceModType);
        showDevice.setUpdateTime(new Date());
        return update(showDevice, updateWrapper);
    }

    @Override
    public boolean updateShowDeviceSuperviseStateByDeviceNumbers(Integer superviseState, Long organizationId, List<String> deviceNumbers) {
        LambdaUpdateWrapper<ShowDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShowDevice::getOrganizationId, organizationId);
        updateWrapper.in(ShowDevice::getDeviceNumber, deviceNumbers);
        ShowDevice showDevice = new ShowDevice();
        showDevice.setSuperviseState(superviseState);
        showDevice.setUpdateTime(new Date());
        return update(showDevice, updateWrapper);
    }

    @Override
    public boolean updatePatternByDeviceNumber(String deviceNumber, Integer pattern) {
        UpdateWrapper<ShowDevice> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(true, ShowDevice::getDeviceNumber, deviceNumber)
            .set(ShowDevice::getDevicePattern, pattern).set(ShowDevice::getUpdateTime, new Date());
        return this.update(null, updateWrapper);
    }

    @Override
    public boolean updatePosterDurationByDeviceNumber(String deviceNumber, Integer devicePosterDuration) {
        UpdateWrapper<ShowDevice> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(true, ShowDevice::getDeviceNumber, deviceNumber)
            .set(ShowDevice::getDevicePosterDuration, devicePosterDuration).set(ShowDevice::getUpdateTime, new Date());
        return this.update(null, updateWrapper);
    }

    @Override
    public boolean updateArcsoftFaceCodeByDeviceNumber(String deviceNumber, String arcsoftFaceCode) {
        UpdateWrapper<ShowDevice> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(true, ShowDevice::getDeviceNumber, deviceNumber)
            .set(ShowDevice::getArcsoftFaceCode, arcsoftFaceCode).set(ShowDevice::getUpdateTime, new Date());
        return this.update(null, updateWrapper);
    }

    @Override
    public boolean updateDeviceNameByDeviceNumber(String deviceNumber, String deviceName) {
        UpdateWrapper<ShowDevice> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(true, ShowDevice::getDeviceNumber, deviceNumber)
            .set(ShowDevice::getDeviceName, deviceName).set(ShowDevice::getUpdateTime, new Date());
        return this.update(null, updateWrapper);
    }

    @Override
    public AjaxResult updateStatusByDeviceNum(String deviceNumber, Integer status) {

        UpdateWrapper<ShowDevice> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(true, ShowDevice::getDeviceNumber, deviceNumber).set(ShowDevice::getDeviceStatus,
            status);

        return AjaxResult.success(this.baseMapper.update(null, updateWrapper));
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceBindByOrganizationId(Long organizationId) {
        return showDeviceMapper.listShowDeviceBindByOrganizationId(organizationId);
    }

    @Override
    public ShowDeviceVo full(ShowDeviceBo bo) {
        // 设备号
        final String deviceNumber = bo.getDeviceNumber();
        final ShowDevice device = this.getByDeviceNum(deviceNumber);
        if (device == null) {
            return null;
        }

        // 主业务更新
        device.setDeviceFullType(bo.getDeviceFullType());
        this.baseMapper.updateById(device);
        final ShowDeviceVo showDeviceVo = BeanUtil.toBean(device, ShowDeviceVo.class);
        return showDeviceVo;
    }

    @Override
    public boolean arcCode(ShowDeviceBo bo) {
        UpdateWrapper<ShowDevice> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(true, ShowDevice::getDeviceNumber, bo.getDeviceNumber())
            .set(ShowDevice::getArcsoftFaceCode, bo.getArcsoftFaceCode());

        return this.update(null, updateWrapper);
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfAll(Long organizationId, Long campusId,
        List<Long> spaceInfoIds, List<Long> spaceGroupIds) {
        List<ShowDeviceVo> showDeviceVosXz =
            showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, campusId, spaceInfoIds, spaceGroupIds);
        List<ShowDeviceVo> showDeviceVosNotXz = showDeviceMapper.listShowDeviceVoBySpaceGroupIdsOfNotXz(organizationId,
            campusId, spaceInfoIds, spaceGroupIds);
        List<ShowDeviceVo> resultList = Lists.newArrayList(Iterables.concat(showDeviceVosXz, showDeviceVosNotXz));
        return resultList;
    }

    /**
     * @param spaceIfoIds 非专用教室地点ids
     * @return java.util.List<com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo>
     * <AUTHOR>
     * @date 2022/8/26 9:26
     */
    @Override
    public List<ShowDeviceVo> listBySpaceInfoIdsOfNotXz(List<Long> spaceIfoIds) {
        return showDeviceMapper.listBySpaceInfoIdsOfNotXz(spaceIfoIds);
    }

    /**
     * 根据序列号，获取设备名称（班牌名称），出货号。
     *
     * @param deviceNumber
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/11/4 11:51
     */
    @Override
    public AjaxResult aboutByDeviceNum(String deviceNumber) {
        // 获取设备信息
        final ShowDevice showDevice = this.showDeviceService.getByDeviceNum(deviceNumber);
        if (showDevice == null) {
            return AjaxResult.fail("设备不存在");
        }
        final ShowDeviceVo showDeviceVo = BeanUtil.toBean(showDevice, ShowDeviceVo.class);

        // 获取设备空间关系数据
        final SpaceDeviceRel spaceDeviceRel = spaceDeviceRelService.getByDeviceId(showDeviceVo.getShowDeviceId());
        if (spaceDeviceRel == null) {
            return AjaxResult.fail();
        }

        // 地点类型
        final Integer spaceGroupUseType = spaceDeviceRel.getSpaceGroupUseType();
        // 地点ID
        final Long spaceInfoId = spaceDeviceRel.getSpaceInfoId();
        if (SpaceGroupUseType.XZ.getValue() == spaceGroupUseType) {
            ClazzVo clazzVo = baseDataService.getByClazzId(spaceInfoId);
            String grade = SchoolYearUtil.gradeMap.get(clazzVo.getGrade());
            String spaceName = grade.concat(clazzVo.getClassesName()).concat("班");
            showDeviceVo.setSpaceInfoName(spaceName);
        } else if (SpaceGroupUseType.NOT_XZ.getValue() == spaceGroupUseType) {
            SpaceInfoVo spaceInfoVo = spaceInfoService.getDetail(spaceInfoId);
            showDeviceVo.setSpaceInfoName(spaceInfoVo.getSpaceInfoName());
        }
        return AjaxResult.success(showDeviceVo);
    }

    @Override
    public void cacheQrcodeContent(ScreenQrcodeContentVo screenQrcodeContentVo) {
        // 查询缓存里面有没有如果有直接返回
        String key = ConstantsRedis.SCREEN_DEVICE_QRCODE_CONTENT_PREFIX + screenQrcodeContentVo.getDeviceNumber();

        if (screenQrcodeContentVo != null) {
            // 如果缓存没有则插入缓存，返回加密的数据
            String value = JSONObject.toJSONString(screenQrcodeContentVo);
            redisComponent.set(key, value, ConstantsRedis.SCREEN_DEVICE_QRCODE_CONTENT_EXPIRE_IN);
        }
    }

    @Override
    public ScreenQrcodeContentVo getQrcodeContent(String deviceNumber) {
        // 查询缓存里面有没有如果有直接返回
        String key = ConstantsRedis.SCREEN_DEVICE_QRCODE_CONTENT_PREFIX + deviceNumber;
        if (redisComponent.hasKey(deviceNumber)) {
            String value = (String)redisComponent.get(key);
            return JSONObject.parseObject(value, ScreenQrcodeContentVo.class);
        }
        // 兼容二维码缓存没有的情况，从数据库查询
        ShowDevice showDevice = showDeviceService.getByDeviceNum(deviceNumber);
        ScreenQrcodeContentVo screenQrcodeContentVo = new ScreenQrcodeContentVo();
        screenQrcodeContentVo.setDeviceNumber(deviceNumber);
        screenQrcodeContentVo.setShowDeviceId(showDevice.getShowDeviceId());
        screenQrcodeContentVo.setDevicePattern(showDevice.getDevicePattern());
        screenQrcodeContentVo.setOrganizationId(showDevice.getOrganizationId());
        SpaceDeviceRel spaceDeviceRel = spaceDeviceRelService.getByDeviceId(showDevice.getShowDeviceId());
        screenQrcodeContentVo.setSpaceInfoId(spaceDeviceRel.getSpaceInfoId());
        screenQrcodeContentVo.setSpaceGroupUseType(spaceDeviceRel.getSpaceGroupUseType());
        screenQrcodeContentVo.setCampusId(spaceDeviceRel.getCampusId());
        String res = JSONObject.toJSONString(screenQrcodeContentVo);
        redisComponent.set(key, res, ConstantsRedis.SCREEN_DEVICE_QRCODE_CONTENT_EXPIRE_IN);
        return screenQrcodeContentVo;
    }

    /**
     * 根据设备id 设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @Override
    @Transactional
    public AjaxResult setPosterRule(ShowDeviceBo showDeviceBo) {
        ShowDevice byId = getById(showDeviceBo.getShowDeviceId());
        if (null == byId) {
            return AjaxResult.fail("设备不存在");
        }
        // 更新推送规则
        ShowDevice showDevice = new ShowDevice();
        showDevice.setShowDeviceId(showDeviceBo.getShowDeviceId());
        showDevice.setPushType(showDeviceBo.getPushType());
        boolean b = showDeviceService.updateById(showDevice);
        // 设备标签
        // 删除已绑定
        LambdaUpdateWrapper<ShowDeviceLabelRelDto> luw = new LambdaUpdateWrapper<>();
        luw.eq(ShowDeviceLabelRelDto::getShowDeviceId, showDeviceBo.getShowDeviceId());
        luw.set(ShowDeviceLabelRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        showDeviceLabelRelService.update(luw);
        // 添加新绑定
        List<ShowDeviceLabelRelDto> addList = new ArrayList<>();
        for (LabelBo labelBo : showDeviceBo.getLabelBos()) {
            ShowDeviceLabelRelDto labelLibraryRel = new ShowDeviceLabelRelDto();
            labelLibraryRel.setShowDeviceId(showDeviceBo.getShowDeviceId());
            labelLibraryRel.setLabelId(labelBo.getLabelId());
            labelLibraryRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
            addList.add(labelLibraryRel);
        }
        if (showDeviceLabelRelService.saveBatch(addList)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult setPosterRuleBatch(ShowDeviceBo showDeviceBo) {
        // 查询空间设备列表
        List<SpaceDeviceRelBo> xzSpaceList = showDeviceBo.getSpaceDeviceRelList().stream()
            .filter(x -> SpaceGroupUseType.XZ.getValue() == x.getSpaceGroupUseType()).collect(Collectors.toList());
        List<SpaceDeviceRelBo> notXzSpaceList = showDeviceBo.getSpaceDeviceRelList().stream()
            .filter(x -> SpaceGroupUseType.NOT_XZ.getValue() == x.getSpaceGroupUseType()).collect(Collectors.toList());
        List<ShowDeviceVo> showDeviceList = new ArrayList<>();
        // 行政教室设备
        if (CollectionUtils.isNotEmpty(xzSpaceList)) {
            List<Long> xzSpaceGroupIds =
                xzSpaceList.stream().map(SpaceDeviceRelBo::getSpaceGroupId).collect(Collectors.toList());
            List<ShowDeviceVo> xzShowDeviceList = baseMapper.listShowDeviceVoBySpaceGroupIdsOfXz(
                showDeviceBo.getOrganizationId(), showDeviceBo.getCampusId(), null, xzSpaceGroupIds);
            if (CollectionUtils.isNotEmpty(xzShowDeviceList)) {
                showDeviceList.addAll(xzShowDeviceList);
            }
        }
        // 非行政教室设备
        if (CollectionUtils.isNotEmpty(notXzSpaceList)) {
            List<Long> notXzSpaceGroupIds =
                notXzSpaceList.stream().map(SpaceDeviceRelBo::getSpaceGroupId).collect(Collectors.toList());
            List<ShowDeviceVo> notXzShowDeviceList = baseMapper.listShowDeviceVoBySpaceGroupIdsOfNotXz(
                showDeviceBo.getOrganizationId(), showDeviceBo.getCampusId(), null, notXzSpaceGroupIds);
            if (CollectionUtils.isNotEmpty(notXzShowDeviceList)) {
                showDeviceList.addAll(notXzShowDeviceList);
            }
        }
        // 批量更新推送规则
        List<ShowDevice> showDevices = new ArrayList<>();
        for (ShowDeviceVo showDeviceVo : showDeviceList) {
            ShowDevice showDevice = new ShowDevice();
            showDevice.setShowDeviceId(showDeviceVo.getShowDeviceId());
            showDevice.setPushType(showDeviceBo.getPushType());
            showDevices.add(showDevice);
        }
        if (CollectionUtils.isNotEmpty(showDevices)) {
            updateBatchById(showDevices);
        }
        return AjaxResult.success("批量设置成功");
    }

    /**
     * 根据设备id 获取设备主动推送及设备标签列表
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @Override
    public AjaxResult getPosterRule(Long showDeviceId) {
        ShowDeviceVo detail = this.getDetail(showDeviceId);
        if (null == detail) {
            return AjaxResult.fail("设备不存在");
        }
        List<String> showDeviceNumbers = messageService.listOnLineDeviceNumber(detail.getOrganizationId());
        if (CollectionUtils.isEmpty(showDeviceNumbers)) {
            detail.setDeviceStatus(DeviceStatusType.ERROR.getValue());
        } else if (!showDeviceNumbers.contains(detail.getDeviceNumber())) {
            detail.setDeviceStatus(DeviceStatusType.ERROR.getValue());
        }

        ShowDeviceLabelRelConditionBo condition = new ShowDeviceLabelRelConditionBo();
        condition.setShowDeviceId(showDeviceId);
        List<ShowDeviceLabelRelVo> deviceLabelRelVos =
            showDeviceLabelRelService.getShowDeviceLabelRelListByCondition(condition);
        detail.setDeviceLabelRelVos(deviceLabelRelVos);
        return AjaxResult.success(detail);

    }

    @Override
    public List<ShowDeviceVo> listBySpaceInfoIdsOfXz(List<Long> spaceIfoIds) {
        return baseMapper.listBySpaceInfoIdsOfXz(spaceIfoIds);
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceBindByCondition(ShowDeviceListConditionBo condition) {
        return baseMapper.listShowDeviceBindByCondition(condition);
    }

    @Override
    public List<ShowDeviceVo> listShowDeviceByDeviceIdsOfBind(List<Long> deviceIds) {
        List<ShowDeviceVo> showDeviceVos = baseMapper.listShowDeviceByDeviceIdsOfBind(deviceIds);
        // 更新设备异常状态
        showDevicesSetOnlineError(showDeviceVos);
        return showDeviceVos;
    }
}