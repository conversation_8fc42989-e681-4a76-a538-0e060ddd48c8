package com.fh.cloud.screen.service.dto;

import com.fh.cloud.screen.service.message.vo.MessageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 事件发布对象
 *
 * <AUTHOR>
 * @date 2022/5/10 16:55
 */
@Data
public class EventPublishDto implements Serializable {

    /**
     * 事件类型（等同与消息推送类型）
     */
    private Integer MessageWsType;

    /**
     * 组织机构id
     */
    private Long organizationId;

    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 场景id
     */
    private Long sceneId;

    /**
     * 紧急发布内容id
     */
    private Long contentSpecialId;

    /**
     * 紧急发布当前正在发布的内容id(有可能有contentSpecialId一样)
     */
    private Long contentSpecialIdNow;

    /**
     * 班级id
     */
    private Long classesId;

    /**
     * 区域id或班级id
     */
    private Long spaceInfoId;

    /**
     * 区域类型 1-行政 2-非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 区域名称 （年级名称+班级名称）
     */
    private String spaceName;

    /**
     * 设备号列表
     */
    private List<String> deviceNumbers;
    /**
     * 考场id
     */
    private Long examPlanId;

    /**
     * 事件触发需要推送的消息
     */
    private MessageVo msg;
}
