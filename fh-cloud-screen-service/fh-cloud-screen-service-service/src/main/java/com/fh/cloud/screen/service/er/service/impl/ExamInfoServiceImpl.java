package com.fh.cloud.screen.service.er.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.er.entity.bo.*;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoDto;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;
import com.fh.cloud.screen.service.er.mapper.ExamInfoMapper;
import com.fh.cloud.screen.service.er.service.IExamInfoService;
import com.fh.cloud.screen.service.er.service.IExamInfoStudentService;
import com.fh.cloud.screen.service.er.service.IExamInfoSubjectService;
import com.fh.cloud.screen.service.er.service.IExamInfoTeacherService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 考场_考试计划里面一次考试信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Service
public class ExamInfoServiceImpl extends ServiceImpl<ExamInfoMapper, ExamInfoDto> implements IExamInfoService {

    @Resource
    private ExamInfoMapper examInfoMapper;
    @Autowired
    private IExamInfoSubjectService examInfoSubjectService;
    @Autowired
    private IExamInfoStudentService examInfoStudentService;
    @Autowired
    private IExamInfoTeacherService examInfoTeacherService;
    @Autowired
    private IExamInfoService examInfoService;
    @Autowired
    private ISpaceInfoService spaceInfoService;

    @Override
    public List<ExamInfoVo> getExamInfoListByCondition(ExamInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<ExamInfoVo> list = examInfoMapper.getExamInfoListByCondition(condition);
        handleListResult(list);
        return list;
    }

    @Override
    public AjaxResult addExamInfo(ExamInfoBo examInfoBo) {
        ExamInfoDto examInfo = new ExamInfoDto();
        BeanUtils.copyProperties(examInfoBo, examInfo);
        examInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(examInfo)) {
            examInfoBo.setExamInfoId(examInfo.getExamInfoId());
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamInfo(ExamInfoBo examInfoBo) {
        ExamInfoDto examInfo = new ExamInfoDto();
        BeanUtils.copyProperties(examInfoBo, examInfo);
        if (updateById(examInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ExamInfoVo getDetail(Long id) {
        ExamInfoConditionBo condition = new ExamInfoConditionBo();
        condition.setExamInfoId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoVo> list = examInfoMapper.getExamInfoListByCondition(condition);
        ExamInfoVo vo = new ExamInfoVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        // List<ExamInfoVo> examInfoVos = Lists.newArrayList();
        // examInfoVos.add(vo);
        // handleListResult(examInfoVos);
        handleDetailResult(vo);
        return vo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult addExamInfoWithDetail(ExamInfoBo examInfoBo) {
        // 添加考试信息
        examInfoService.addExamInfo(examInfoBo);

        // 添加考试科目信息
        if (CollectionUtils.isNotEmpty(examInfoBo.getExamInfoSubjectBos())) {
            examInfoBo.getExamInfoSubjectBos().stream().forEach(examInfoSubjectBo -> {
                examInfoSubjectBo.setExamInfoId(examInfoBo.getExamInfoId());
                examInfoSubjectBo.setExamPlanId(examInfoBo.getExamPlanId());
            });
            examInfoSubjectService.addExamInfoSubjectBatchWithDetail(examInfoBo.getExamInfoSubjectBos());
        }

        return AjaxResult.success();
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult updateExamInfoWithDetail(ExamInfoBo examInfoBo) {
        // 更新考试信息
        examInfoService.updateExamInfo(examInfoBo);

        // 更新考试科目信息
        if (CollectionUtils.isNotEmpty(examInfoBo.getExamInfoSubjectBos())) {
            examInfoBo.getExamInfoSubjectBos().stream().forEach(examInfoSubjectBo -> {
                examInfoSubjectBo.setExamPlanId(examInfoBo.getExamPlanId());
                examInfoSubjectBo.setExamInfoId(examInfoBo.getExamInfoId());
            });
            examInfoSubjectService.updateExamInfoSubjectBatchWithDetail(examInfoBo.getExamInfoSubjectBos());
        }
        return AjaxResult.success();
    }

    /**
     * 导入批量添加考试信息
     *
     * @param examInfoBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/12 15:53
     */
    @Override
    @Transactional
    public AjaxResult addExamInfoBatchWithDetail(List<ExamInfoBo> examInfoBos) {
        List<ExamInfoSubjectBo> allSubjectList = new ArrayList<>();
        for (ExamInfoBo examInfoBo : examInfoBos) {
            // 添加考试信息(判断是否并入已有考场)
            if (examInfoBo.getExamInfoId() == null) {
                examInfoService.addExamInfo(examInfoBo);
            }
            // 添加考试科目信息
            if (CollectionUtils.isNotEmpty(examInfoBo.getExamInfoSubjectBos())) {
                examInfoBo.getExamInfoSubjectBos().forEach(examInfoSubjectBo -> {
                    examInfoSubjectBo.setExamInfoId(examInfoBo.getExamInfoId());
                    examInfoSubjectBo.setExamPlanId(examInfoBo.getExamPlanId());
                });
                allSubjectList.addAll(examInfoBo.getExamInfoSubjectBos());
            }
        }
        examInfoSubjectService.addExamInfoSubjectBatchWithDetail(allSubjectList);
        return AjaxResult.success();
    }

    /**
     * 处理列表返回的结果（添加科目名称）
     *
     * @param list the list
     * @autor sunqingbiao
     * @date 2022-09-30 14:35:17
     */
    private void handleListResult(List<ExamInfoVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> examInfoIds = list.stream().map(ExamInfoVo::getExamInfoId).collect(Collectors.toList());
        // 科目
        ExamInfoSubjectConditionBo examInfoSubjectConditionBo = new ExamInfoSubjectConditionBo();
        examInfoSubjectConditionBo.setExamInfoIds(examInfoIds);
        examInfoSubjectConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoSubjectVo> examInfoSubjectList =
            examInfoSubjectService.getExamInfoSubjectListByCondition(examInfoSubjectConditionBo);
        Map<Long, List<ExamInfoSubjectVo>> examInfoSubjectMap = examInfoSubjectList.stream()
            .collect(Collectors.groupingBy(ExamInfoSubjectVo::getExamInfoId, Collectors.toList()));
        // 行政地点
        List<Long> classesIds = list.stream()
            .filter(examInfoVo -> examInfoVo.getSpaceGroupUseType() != null
                && examInfoVo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue()))
            .map(ExamInfoVo::getSpaceInfoId).collect(Collectors.toList());
        List<ClazzInfoVo> classesInfoVos = spaceInfoService.getSpaceInfoVosByIdsXz(classesIds);
        Map<Long, String> classesNameMap = classesInfoVos.stream()
            .collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getClassesNameShow, (k1, k2) -> k1));
        // 非行政地点
        List<Long> spaceInfoIds = list.stream()
            .filter(examInfoVo -> examInfoVo.getSpaceGroupUseType() != null
                && examInfoVo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue()))
            .map(ExamInfoVo::getSpaceInfoId).collect(Collectors.toList());
        List<SpaceInfoVo> spaceInfoVos = spaceInfoService.getSpaceInfoVosByIdsNotXz(spaceInfoIds);
        Map<Long, String> spaceInfoNameMap = spaceInfoVos.stream()
            .collect(Collectors.toMap(SpaceInfoVo::getSpaceInfoId, SpaceInfoVo::getSpaceInfoName, (k1, k2) -> k1));

        list.forEach(examInfoVo -> {
            List<ExamInfoSubjectVo> examInfoSubjectVos = examInfoSubjectMap.get(examInfoVo.getExamInfoId());
            if (CollectionUtils.isNotEmpty(examInfoSubjectVos)) {
                examInfoVo.setSubjectNames(
                    examInfoSubjectVos.stream().map(ExamInfoSubjectVo::getSubjectName).collect(Collectors.toList()));
            }
            if (examInfoVo.getSpaceGroupUseType() != null
                && examInfoVo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue())) {
                examInfoVo.setSpaceInfoName(classesNameMap.get(examInfoVo.getSpaceInfoId()));
            }
            if (examInfoVo.getSpaceGroupUseType() != null
                && examInfoVo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue())) {
                examInfoVo.setSpaceInfoName(spaceInfoNameMap.get(examInfoVo.getSpaceInfoId()));
            }
        });
    }

    /**
     * 处理详情返回的结果（添加科目、教师、学生信息）
     *
     * @param examInfoVo the ExamInfoVo
     * @autor sunqingbiao
     * @date 2022-09-30 14:35:17
     */
    private void handleDetailResult(ExamInfoVo examInfoVo) {
        if (examInfoVo == null) {
            return;
        }

        ExamInfoSubjectConditionBo examInfoSubjectConditionBo = new ExamInfoSubjectConditionBo();
        examInfoSubjectConditionBo.setExamInfoId(examInfoVo.getExamInfoId());
        examInfoSubjectConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoSubjectVo> examInfoSubjectList =
            examInfoSubjectService.getExamInfoSubjectListByCondition(examInfoSubjectConditionBo);
        if (CollectionUtils.isEmpty(examInfoSubjectList)) {
            return;
        }
        examInfoVo.setExamInfoSubjectVos(examInfoSubjectList);

        List<Long> examInfoSubjectIds =
            examInfoSubjectList.stream().map(ExamInfoSubjectVo::getExamInfoSubjectId).collect(Collectors.toList());
        ExamInfoStudentConditionBo examInfoStudentConditionBo = new ExamInfoStudentConditionBo();
        examInfoStudentConditionBo.setExamInfoSubjectIds(examInfoSubjectIds);
        examInfoStudentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoStudentVo> examInfoStudentList =
            examInfoStudentService.getExamInfoStudentListByCondition(examInfoStudentConditionBo);
        Map<Long, List<ExamInfoStudentVo>> examInfoStudentMap = examInfoStudentList.stream()
            .collect(Collectors.groupingBy(ExamInfoStudentVo::getExamInfoSubjectId, Collectors.toList()));

        ExamInfoTeacherConditionBo examInfoTeacherConditionBo = new ExamInfoTeacherConditionBo();
        examInfoTeacherConditionBo.setExamInfoSubjectIds(examInfoSubjectIds);
        examInfoTeacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoTeacherVo> examInfoTeacherList =
            examInfoTeacherService.getExamInfoTeacherListByCondition(examInfoTeacherConditionBo);
        Map<Long, List<ExamInfoTeacherVo>> examInfoTeacherMap = examInfoTeacherList.stream()
            .collect(Collectors.groupingBy(ExamInfoTeacherVo::getExamInfoSubjectId, Collectors.toList()));

        examInfoSubjectList.forEach(examInfoSubjectVo -> {
            examInfoSubjectVo.setExamInfoStudentVos(examInfoStudentMap.get(examInfoSubjectVo.getExamInfoSubjectId()));
            examInfoSubjectVo.setExamInfoTeacherVos(examInfoTeacherMap.get(examInfoSubjectVo.getExamInfoSubjectId()));
        });
    }
}