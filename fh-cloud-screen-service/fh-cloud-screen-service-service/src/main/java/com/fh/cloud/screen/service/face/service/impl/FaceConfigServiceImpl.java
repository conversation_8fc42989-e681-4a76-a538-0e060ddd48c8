package com.fh.cloud.screen.service.face.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.cloud.screen.service.space.entity.dto.ClassesInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.face.entity.dto.FaceConfigDto;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;
import com.fh.cloud.screen.service.face.mapper.FaceConfigMapper;
import com.light.core.entity.AjaxResult;

/**
 * 人脸对比参数接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:35
 */
@Service
public class FaceConfigServiceImpl extends ServiceImpl<FaceConfigMapper, FaceConfigDto> implements IFaceConfigService {

    @Resource
    private FaceConfigMapper faceConfigMapper;

    @Override
    public List<FaceConfigVo> getFaceConfigListByCondition(FaceConfigConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return faceConfigMapper.getFaceConfigListByCondition(condition);
    }

    @Override
    public AjaxResult addFaceConfig(FaceConfigBo faceConfigBo) {
        FaceConfigDto faceConfig = new FaceConfigDto();
        BeanUtils.copyProperties(faceConfigBo, faceConfig);
        faceConfig.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(faceConfig)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateFaceConfig(FaceConfigBo faceConfigBo) {
        FaceConfigDto faceConfig = new FaceConfigDto();
        BeanUtils.copyProperties(faceConfigBo, faceConfig);
        if (updateById(faceConfig)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public FaceConfigVo getDetail(Long id) {
        FaceConfigConditionBo condition = new FaceConfigConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<FaceConfigVo> list = faceConfigMapper.getFaceConfigListByCondition(condition);
        FaceConfigVo vo = new FaceConfigVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public FaceConfigVo getDetailByOrganizationId(Long organizationId) {
        LambdaQueryWrapper<FaceConfigDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(FaceConfigDto::getOrganizationId, organizationId);
        lqw.eq(FaceConfigDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.orderByDesc(FaceConfigDto::getId);
        FaceConfigDto faceConfigDto = getOne(lqw,false);
        if (faceConfigDto == null) {
            return null;
        }
        FaceConfigVo faceConfigVo = new FaceConfigVo();
        BeanUtils.copyProperties(faceConfigDto, faceConfigVo);
        return faceConfigVo;
    }
}