package com.fh.cloud.screen.service.festival.controller;

import com.fh.cloud.screen.service.festival.api.FestivalApi;
import com.fh.cloud.screen.service.festival.entity.dto.FestivalDto;
import com.fh.cloud.screen.service.utils.DateKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalBo;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;
import com.fh.cloud.screen.service.festival.service.IFestivalService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.Date;
import java.util.List;

/**
 * 节日表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
@RestController
@Validated
public class FestivalController implements FestivalApi {

    @Autowired
    private IFestivalService festivalService;

    /**
     * 查询节日表分页列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @Override
    public AjaxResult<PageInfo<FestivalVo>> getFestivalPageListByCondition(@RequestBody FestivalConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<FestivalVo> pageInfo = new PageInfo<>(festivalService.getFestivalListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询节日表列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @Override
    public AjaxResult<List<FestivalVo>> getFestivalListByCondition(@RequestBody FestivalConditionBo condition) {
        List<FestivalVo> list = festivalService.getFestivalListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增节日表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @Override
    public AjaxResult addFestival(@Validated @RequestBody FestivalBo festivalBo) {
        return festivalService.addFestival(festivalBo);
    }

    /**
     * 修改节日表
     * 
     * @param festivalBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @Override
    public AjaxResult updateFestival(@Validated @RequestBody FestivalBo festivalBo) {
        if (null == festivalBo.getFestivalId()) {
            return AjaxResult.fail("节日表id不能为空");
        }
        return festivalService.updateFestival(festivalBo);
    }

    /**
     * 查询节日表详情
     * 
     * @param festivalId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @Override
    public AjaxResult<FestivalVo> getDetail(@RequestParam("festivalId") Long festivalId) {
        if (null == festivalId) {
            return AjaxResult.fail("节日表id不能为空");
        }
        FestivalVo vo = festivalService.getDetail(festivalId);
        return AjaxResult.success(vo);
    }

    /**
     * 删除节日表
     * 
     * @param festivalId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @Override
    public AjaxResult delete(@RequestParam("festivalId") Long festivalId) {
        if (null == festivalId) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        return festivalService.delete(festivalId);
    }

    /**
     * 通过日期获取当天节假日列表
     *
     * @param date
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/3 11:39
     */
    @Override
    public AjaxResult getFestivalListByDate(@RequestParam("date") String date) {
        List<FestivalVo> festivalDurationTypeListByDate = festivalService.getFestivalDurationListByDate(date);
        return AjaxResult.success(festivalDurationTypeListByDate);
    }
}
