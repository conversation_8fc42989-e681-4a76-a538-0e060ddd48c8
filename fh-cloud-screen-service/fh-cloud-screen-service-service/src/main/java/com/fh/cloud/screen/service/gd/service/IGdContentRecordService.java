package com.fh.cloud.screen.service.gd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.gd.entity.dto.GdContentRecordDto;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordConditionBo;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordBo;
import com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 稿定内容记录接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
public interface IGdContentRecordService extends IService<GdContentRecordDto> {

    List<GdContentRecordVo> getGdContentRecordListByCondition(GdContentRecordConditionBo condition);

	AjaxResult addGdContentRecord(GdContentRecordBo gdContentRecordBo);

	AjaxResult updateGdContentRecord(GdContentRecordBo gdContentRecordBo);

	GdContentRecordVo getGdContentRecordByCondition(GdContentRecordConditionBo condition);

	/**
	 * 根据gdId保存稿定内容记录
	 * @param gdContentRecordBo
	 */
	void saveGdContentRecordBo(GdContentRecordBo gdContentRecordBo);
}

