package com.fh.cloud.screen.service.gd.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordBo;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordConditionBo;
import com.fh.cloud.screen.service.gd.entity.dto.GdContentRecordDto;
import com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo;
import com.fh.cloud.screen.service.gd.mapper.GdContentRecordMapper;
import com.fh.cloud.screen.service.gd.service.IGdContentRecordService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 稿定内容记录接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
@Service
public class GdContentRecordServiceImpl extends ServiceImpl<GdContentRecordMapper, GdContentRecordDto> implements IGdContentRecordService {

	@Resource
	private GdContentRecordMapper gdContentRecordMapper;
	@Resource
	IGdContentRecordService gdContentRecordService;
	
    @Override
	public List<GdContentRecordVo> getGdContentRecordListByCondition(GdContentRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return gdContentRecordMapper.getGdContentRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addGdContentRecord(GdContentRecordBo gdContentRecordBo) {
		GdContentRecordDto gdContentRecord = new GdContentRecordDto();
		BeanUtils.copyProperties(gdContentRecordBo, gdContentRecord);
		gdContentRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(gdContentRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateGdContentRecord(GdContentRecordBo gdContentRecordBo) {
		GdContentRecordDto gdContentRecord = new GdContentRecordDto();
		BeanUtils.copyProperties(gdContentRecordBo, gdContentRecord);
		if(updateById(gdContentRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public GdContentRecordVo getGdContentRecordByCondition(GdContentRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		GdContentRecordVo vo = gdContentRecordMapper.getGdContentRecordByCondition(condition);
		return vo;
	}

	@Transactional(rollbackFor = Throwable.class)
	@Override
	public void saveGdContentRecordBo(GdContentRecordBo gdContentRecordBo) {
		if(StringUtils.isBlank(gdContentRecordBo.getGdId())){
			return;
		}
		GdContentRecordConditionBo condition = new GdContentRecordConditionBo();
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		condition.setGdId(gdContentRecordBo.getGdId());
		GdContentRecordVo gdContentRecordByCondition = gdContentRecordService.getGdContentRecordByCondition(condition);
		if(gdContentRecordByCondition == null){
			// 新增
			addGdContentRecord(gdContentRecordBo);
		}else{
			// 更新
			gdContentRecordByCondition.setFileInfoJson(gdContentRecordBo.getFileInfoJson());
			GdContentRecordBo gdContentRecordBoToUpdate = new GdContentRecordBo();
			BeanUtils.copyProperties(gdContentRecordByCondition, gdContentRecordBoToUpdate);
			updateGdContentRecord(gdContentRecordBoToUpdate);
		}
	}
}