package com.fh.cloud.screen.service.leaveschool.controller;

import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolBroadcastInfoApi;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolBroadcastInfoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolBroadcastInfoService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 放学播报信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
@RestController
@Validated
public class LeaveSchoolBroadcastInfoController implements LeaveSchoolBroadcastInfoApi{
	
    @Autowired
    private ILeaveSchoolBroadcastInfoService leaveSchoolBroadcastInfoService;

    /**
     * 查询放学播报信息表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<LeaveSchoolBroadcastInfoVo>> getLeaveSchoolBroadcastInfoPageListByCondition(@RequestBody LeaveSchoolBroadcastInfoConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<LeaveSchoolBroadcastInfoVo> pageInfo = new PageInfo<>(leaveSchoolBroadcastInfoService.getLeaveSchoolBroadcastInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询放学播报信息表列表
	 * <AUTHOR>
	 * @date 2023-08-23 10:23:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<LeaveSchoolBroadcastInfoVo>> getLeaveSchoolBroadcastInfoListByCondition(@RequestBody LeaveSchoolBroadcastInfoConditionBo condition){
		List<LeaveSchoolBroadcastInfoVo> list = leaveSchoolBroadcastInfoService.getLeaveSchoolBroadcastInfoListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增放学播报信息表
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addLeaveSchoolBroadcastInfo(@Validated @RequestBody LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo){
		return leaveSchoolBroadcastInfoService.addLeaveSchoolBroadcastInfo(leaveSchoolBroadcastInfoBo);
    }

    /**
	 * 修改放学播报信息表
	 * @param leaveSchoolBroadcastInfoBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateLeaveSchoolBroadcastInfo(@Validated @RequestBody LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo) {
		if(null == leaveSchoolBroadcastInfoBo.getBroadcastInfoId()) {
			return AjaxResult.fail("放学播报信息表id不能为空");
		}
		return leaveSchoolBroadcastInfoService.updateLeaveSchoolBroadcastInfo(leaveSchoolBroadcastInfoBo);
	}

	/**
	 * 查询放学播报信息表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<LeaveSchoolBroadcastInfoVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("放学播报信息表id不能为空");
		}
		LeaveSchoolBroadcastInfoConditionBo condition = new LeaveSchoolBroadcastInfoConditionBo();
		condition.setBroadcastInfoId(id);
		LeaveSchoolBroadcastInfoVo vo = leaveSchoolBroadcastInfoService.getLeaveSchoolBroadcastInfoByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除放学播报信息表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		LeaveSchoolBroadcastInfoDto leaveSchoolBroadcastInfoDto = new LeaveSchoolBroadcastInfoDto();
		leaveSchoolBroadcastInfoDto.setBroadcastInfoId(id);
		leaveSchoolBroadcastInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(leaveSchoolBroadcastInfoService.updateById(leaveSchoolBroadcastInfoDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
