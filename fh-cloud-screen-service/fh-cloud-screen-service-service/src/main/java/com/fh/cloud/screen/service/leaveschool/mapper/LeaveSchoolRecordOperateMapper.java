package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordOperateDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordOperateConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordOperateVo;

/**
 * 放学记录操作表idMapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-09-13 10:26:18
 */
public interface LeaveSchoolRecordOperateMapper extends BaseMapper<LeaveSchoolRecordOperateDto> {

	List<LeaveSchoolRecordOperateVo> getLeaveSchoolRecordOperateListByCondition(LeaveSchoolRecordOperateConditionBo condition);

	LeaveSchoolRecordOperateVo getLeaveSchoolRecordOperateByCondition(LeaveSchoolRecordOperateConditionBo condition);

}
