package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordSaveBatchBo;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolStudentSignRecordDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 学生进出记录表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:58:52
 */
public interface ILeaveSchoolStudentSignRecordService extends IService<LeaveSchoolStudentSignRecordDto> {

    List<LeaveSchoolStudentSignRecordVo> getLeaveSchoolStudentSignRecordListByCondition(LeaveSchoolStudentSignRecordConditionBo condition);

	AjaxResult addLeaveSchoolStudentSignRecord(LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo);

	AjaxResult updateLeaveSchoolStudentSignRecord(LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo);

	LeaveSchoolStudentSignRecordVo getLeaveSchoolStudentSignRecordByCondition(LeaveSchoolStudentSignRecordConditionBo condition);

	AjaxResult addLeaveSchoolStudentSignRecordBatch(LeaveSchoolStudentSignRecordSaveBatchBo leaveSchoolStudentSignRecordSaveBatchBo);
}

