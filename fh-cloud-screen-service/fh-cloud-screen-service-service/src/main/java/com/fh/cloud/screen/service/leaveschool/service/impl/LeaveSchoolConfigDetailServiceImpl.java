package com.fh.cloud.screen.service.leaveschool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDetailDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDetailService;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolConfigDetailMapper;
import com.light.core.entity.AjaxResult;
/**
 * 放学配置详情表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
@Service
public class LeaveSchoolConfigDetailServiceImpl extends ServiceImpl<LeaveSchoolConfigDetailMapper, LeaveSchoolConfigDetailDto> implements ILeaveSchoolConfigDetailService {

	@Resource
	private LeaveSchoolConfigDetailMapper leaveSchoolConfigDetailMapper;
	
    @Override
	public List<LeaveSchoolConfigDetailVo> getLeaveSchoolConfigDetailListByCondition(LeaveSchoolConfigDetailConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolConfigDetailMapper.getLeaveSchoolConfigDetailListByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolConfigDetail(LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo) {
		LeaveSchoolConfigDetailDto leaveSchoolConfigDetail = new LeaveSchoolConfigDetailDto();
		BeanUtils.copyProperties(leaveSchoolConfigDetailBo, leaveSchoolConfigDetail);
		leaveSchoolConfigDetail.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(leaveSchoolConfigDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateLeaveSchoolConfigDetail(LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo) {
		LeaveSchoolConfigDetailDto leaveSchoolConfigDetail = new LeaveSchoolConfigDetailDto();
		BeanUtils.copyProperties(leaveSchoolConfigDetailBo, leaveSchoolConfigDetail);
		if(updateById(leaveSchoolConfigDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public LeaveSchoolConfigDetailVo getLeaveSchoolConfigDetailByCondition(LeaveSchoolConfigDetailConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return leaveSchoolConfigDetailMapper.getLeaveSchoolConfigDetailByCondition(condition);
	}

	@Override
	public boolean deleteAndAddLeaveSchoolConfigDetailList(Long leaveSchoolConfigId, List<LeaveSchoolConfigDetailBo> configDetailList) {
    	if (leaveSchoolConfigId == null) {
    		return false;
		}
		LambdaUpdateWrapper<LeaveSchoolConfigDetailDto> updateWrapper = new LambdaUpdateWrapper<>();
    	updateWrapper.eq(LeaveSchoolConfigDetailDto::getLeaveSchoolConfigId, leaveSchoolConfigId);
    	updateWrapper.set(LeaveSchoolConfigDetailDto::getIsDelete, StatusEnum.ISDELETE.getCode());
    	update(updateWrapper);

    	if (CollectionUtil.isEmpty(configDetailList)) {
    		return true;
		}
    	List<LeaveSchoolConfigDetailDto> entities = configDetailList.stream().map(c -> {
    		LeaveSchoolConfigDetailDto entity = new LeaveSchoolConfigDetailDto();
    		BeanUtils.copyProperties(c, entity);
    		entity.setLeaveSchoolConfigId(leaveSchoolConfigId);
    		entity.setIsDelete(StatusEnum.NOTDELETE.getCode());
    		return entity;
		}).collect(Collectors.toList());

		return saveBatch(entities);
	}

}