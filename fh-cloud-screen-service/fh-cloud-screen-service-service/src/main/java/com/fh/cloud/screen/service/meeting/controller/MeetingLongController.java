package com.fh.cloud.screen.service.meeting.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.meeting.api.MeetingLongApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo;
import com.fh.cloud.screen.service.meeting.service.IMeetingLongService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 长期预约表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@RestController
@Validated
public class MeetingLongController implements MeetingLongApi{
	
    @Autowired
    private IMeetingLongService meetingLongService;

    /**
     * 查询长期预约表分页列表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @Override
    public AjaxResult<PageInfo<MeetingLongVo>> getMeetingLongPageListByCondition(@RequestBody MeetingLongConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<MeetingLongVo> pageInfo = new PageInfo<>(meetingLongService.getMeetingLongListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询长期预约表列表
	 * <AUTHOR>
	 * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult<List<MeetingLongVo>> getMeetingLongListByCondition(@RequestBody MeetingLongConditionBo condition){
		List<MeetingLongVo> list = meetingLongService.getMeetingLongListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增长期预约表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
	@Override
    public AjaxResult addMeetingLong(@Validated @RequestBody MeetingLongBo meetingLongBo){
		return meetingLongService.addMeetingLong(meetingLongBo);
    }

    /**
	 * 修改长期预约表
	 * @param meetingLongBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult updateMeetingLong(@Validated @RequestBody MeetingLongBo meetingLongBo) {
		if(null == meetingLongBo.getMeetingLongId()) {
			return AjaxResult.fail("长期预约表id不能为空");
		}
		return meetingLongService.updateMeetingLong(meetingLongBo);
	}

	/**
	 * 查询长期预约表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult<MeetingLongVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("长期预约表id不能为空");
		}
		MeetingLongConditionBo condition = new MeetingLongConditionBo();
		condition.setMeetingLongId(id);
		MeetingLongVo vo = meetingLongService.getMeetingLongByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除长期预约表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		MeetingLongDto meetingLongDto = new MeetingLongDto();
		meetingLongDto.setMeetingLongId(id);
		meetingLongDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(meetingLongService.updateById(meetingLongDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
