package com.fh.cloud.screen.service.message.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;
import com.fh.cloud.screen.service.er.service.IExamInfoService;
import com.google.common.collect.Iterables;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.utils.MessageUtil;
import com.fh.cloud.screen.websocekt.api.message.api.MessageApi;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;

/**
 * 消息service
 *
 * <AUTHOR>
 * @date 2022 /5/9 17:03
 */
@Service
@DependsOn(value = {"showDeviceServiceImpl"})
public class MessageService {
    @Resource
    private MessageApi messageApi;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Autowired
    private IExamInfoService examInfoService;

    /**
     * 查看所有的设备序列号在线数据
     *
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public List<String> listAllOnLineDeviceNumber() {
        AjaxResult ajaxResult = messageApi.sessionLook();
        if (ajaxResult.isFail()) {
            return Lists.newArrayList();
        }

        Map<String, List<String>> resultMap =
            JSONObject.parseObject(JSONObject.toJSONString(ajaxResult.getData()), Map.class);
        List<String> resultList = Lists.newArrayList();
        if (resultMap.isEmpty()) {
            return resultList;
        }
        for (String orgId : resultMap.keySet()) {
            resultList.addAll(resultMap.get(orgId));
        }
        return resultList;
    }

    /**
     * 发送websocket消息给安卓端-根据内容id推送
     *
     * @param organizationId the organization id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public List<String> listOnLineDeviceNumber(Long organizationId) {
        if (organizationId == null) {
            return Lists.newArrayList();
        }

        AjaxResult ajaxResult = messageApi.sessionLookOne(String.valueOf(organizationId));
        if (ajaxResult.isFail()) {
            return Lists.newArrayList();
        }

        List<String> resultList = JSONObject.parseArray(JSONObject.toJSONString(ajaxResult.getData()), String.class);
        return resultList;
    }

    /**
     * 发送websocket消息给安卓端-根据内容id推送
     *
     * @param organizationIds the organization id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public List<String> listOnLineDeviceNumberBatch(List<Long> organizationIds) {
        if (CollectionUtils.isEmpty(organizationIds)) {
            return Lists.newArrayList();
        }
        List<String> organizationIdStrList = organizationIds.stream().map(Object::toString).collect(Collectors.toList());
        AjaxResult ajaxResult = messageApi.sessionLookBatch(organizationIdStrList);
        if (ajaxResult.isFail()) {
            return Lists.newArrayList();
        }

        List<String> resultList = JSONObject.parseArray(JSONObject.toJSONString(ajaxResult.getData()), String.class);
        return resultList;
    }

    /**
     * 发送websocket消息给安卓端-根据内容id推送
     *
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWsByContentId(Long organizationId, Long contentId, MessageVo msg) {
        List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceVoByContentId(contentId);
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.fail("设备为空");
        }
        if (organizationId == null) {
            organizationId = showDeviceVos.get(0).getOrganizationId();
        }

        List<String> deviceNumbers = showDeviceVos.stream().map(showDeviceVo -> showDeviceVo.getDeviceNumber())
            .distinct().collect(Collectors.toList());
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }

    /**
     * 发送websocket消息给安卓端-根据班级推送。后面可能会有按照课程学生推送。
     *
     * @param organizationId the organization id
     * @param classesId the classes id
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWsByClassesId(Long organizationId, Long classesId, MessageVo msg) {
        List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceVoByClassesId(classesId);
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.fail("设备为空");
        }
        if (organizationId == null) {
            organizationId = showDeviceVos.get(0).getOrganizationId();
        }

        List<String> deviceNumbers = showDeviceVos.stream().map(showDeviceVo -> showDeviceVo.getDeviceNumber())
            .distinct().collect(Collectors.toList());
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }

    /**
     * 发送websocket消息给安卓端-指定设备发送
     *
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWs(Long organizationId, List<String> deviceNumbers, MessageVo msg) {
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }

    /**
     * 发送websocket消息给安卓端-根据紧急场景内容id推送
     *
     * @param organizationId the organization id
     * @param contentSpecialId 本次发布的特殊内容id
     * @param contentSpecialIdNow 当前正在发布的特殊内容id
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWsByContentSpecialId(Long organizationId, Long contentSpecialId,
        Long contentSpecialIdNow, MessageVo msg) {
        List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceVoByContentSpecialId(contentSpecialId);
        if (contentSpecialIdNow != null && !contentSpecialId.equals(contentSpecialIdNow)) {
            List<ShowDeviceVo> showDeviceVosNow =
                showDeviceService.listShowDeviceVoByContentSpecialId(contentSpecialIdNow);
            showDeviceVos.addAll(showDeviceVosNow);
        }
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.fail("设备为空");
        }
        if (organizationId == null) {
            organizationId = showDeviceVos.get(0).getOrganizationId();
        }

        List<String> deviceNumbers = showDeviceVos.stream().map(showDeviceVo -> showDeviceVo.getDeviceNumber())
            .distinct().collect(Collectors.toList());
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }

    /**
     * 发送websocket消息给安卓端-根据场景id推送
     *
     * @param sceneId the scene id
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWsBySceneId(Long organizationId, Long sceneId, MessageVo msg) {
        ArrayList<Long> sceneIds = Lists.newArrayList(sceneId);
        List<ShowDeviceVo> showDeviceVos =
            showDeviceService.listShowDeviceVoBySceneIds(organizationId, null, null, sceneIds, null);
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.fail("设备为空");
        }

        if (organizationId == null) {
            organizationId = showDeviceVos.get(0).getOrganizationId();
        }
        List<String> deviceNumbers = showDeviceVos.stream().map(showDeviceVo -> showDeviceVo.getDeviceNumber())
            .distinct().collect(Collectors.toList());
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }

    /**
     * 发送websocket消息给安卓端-学校所有设备/指定设备发送
     *
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWsBySchool(Long organizationId, List<String> deviceNumbers, MessageVo msg) {
        if (organizationId == null) {
            return AjaxResult.fail("organizationId不允许为空");
        }

        // 根据organizationId查询该学校所有的设备
        if (CollectionUtils.isEmpty(deviceNumbers)) {
            deviceNumbers = Lists.newArrayList();
            List<ShowDeviceVo> showDeviceVosAll = showDeviceService.listShowDeviceBindByOrganizationId(organizationId);
            if (CollectionUtils.isNotEmpty(showDeviceVosAll)) {
                List<String> ShowDeviceNumbersAll =
                    showDeviceVosAll.stream().map(ShowDeviceVo::getDeviceNumber).collect(Collectors.toList());
                deviceNumbers.addAll(ShowDeviceNumbersAll);
            }
        }
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }

    /**
     * 发送websocket消息给安卓端-根据考场id推送
     *
     * @param organizationId the organization id
     * @param examPlanId the exam plan id
     * @param msg the msg
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -05-09 17:09:26
     */
    public AjaxResult sendMessageWsByExamPlanId(Long organizationId, Long examPlanId, MessageVo msg) {
        if (organizationId == null) {
            return AjaxResult.fail("organizationId不允许为空");
        }

        ExamInfoConditionBo examInfoConditionBo = new ExamInfoConditionBo();
        examInfoConditionBo.setExamPlanId(examPlanId);
        List<ExamInfoVo> examInfoListByCondition = examInfoService.getExamInfoListByCondition(examInfoConditionBo);
        List<Long> classesIds = examInfoListByCondition.stream()
            .filter(examInfoVo -> examInfoVo.getSpaceGroupUseType() != null
                && examInfoVo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue()))
            .map(ExamInfoVo::getSpaceInfoId).collect(Collectors.toList());
        List<Long> spaceInfoIds = examInfoListByCondition.stream()
            .filter(examInfoVo -> examInfoVo.getSpaceGroupUseType() != null
                && examInfoVo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue()))
            .map(ExamInfoVo::getSpaceInfoId).collect(Collectors.toList());
        List<ShowDeviceVo> showDeviceVosOfXz =
            showDeviceService.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, null, classesIds, null);
        List<ShowDeviceVo> showDeviceVosOfNotXz =
            showDeviceService.listShowDeviceVoBySpaceGroupIdsOfXz(organizationId, null, spaceInfoIds, null);
        List<ShowDeviceVo> showDeviceVos =
            Lists.newArrayList(Iterables.concat(showDeviceVosOfXz, showDeviceVosOfNotXz));
        List<String> deviceNumbers =
            showDeviceVos.stream().map(ShowDeviceVo::getDeviceNumber).collect(Collectors.toList());
        return messageApi.sendWs(MessageUtil.produceWsSendBo(organizationId, deviceNumbers, msg));
    }
}
