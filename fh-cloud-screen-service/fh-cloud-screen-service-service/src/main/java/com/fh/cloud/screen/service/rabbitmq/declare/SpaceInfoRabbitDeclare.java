package com.fh.cloud.screen.service.rabbitmq.declare;

import com.fh.cloud.screen.service.rabbitmq.constant.SpaceInfoRabbitConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 *  用户 相关rabbitmq 声明 （不声明在启动时，如mq重没有该信息 会报错 但不影响服务启动）
 *  注：可放到单独模块供全局使用
 */
@Configuration
public class SpaceInfoRabbitDeclare {


    @Bean
    public DirectExchange spaceInfoExchange(){
        return new DirectExchange(SpaceInfoRabbitConstant.SPACE_INFO_EXCHANGE);
    }


    /**
     * ######################## 地点 服务监听 ############################
     */

    @Bean
    public Queue spaceInfoDelQueue(){
        return new Queue(SpaceInfoRabbitConstant.SPACE_INFO_DELETE_QUEUE);
    }


    @Bean
    public Binding studentUpdateBinding(){
        return BindingBuilder.bind(spaceInfoDelQueue()).to(spaceInfoExchange()).withQueueName();
    }




}
