package com.fh.cloud.screen.service.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.face.service.IFaceRecordStudentService;
import com.fh.cloud.screen.service.face.service.IFaceRecordTeacherService;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.rabbitmq.constant.UserRabbitConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用户相关rabbit mq 监听
 */
@Slf4j
@Component
public class UserListener {

    @Resource
    private MessageService messageService;
    @Resource
    private IFaceRecordStudentService faceRecordStudentService;
    @Resource
    private IFaceRecordTeacherService faceRecordTeacherService;

    /**
     * 学生数据更新消息接收
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(value = @Queue(UserRabbitConstant.STUDENT_UPDATE_QUEUE),
        exchange = @Exchange(name = UserRabbitConstant.USER_EXCHANGE, declare = "false")))
    public void studentUpdateListener(String message) {

        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String userOid = jsonObject.get("userOid").toString();
            String realName = jsonObject.get("realName").toString();
            faceRecordStudentService.updateRealNameByUserOid(userOid, realName);
            log.info("======= student .update receive :{} =========", message);
        } catch (Exception e) {
            log.error("============= student.update error ==========", e);
        }

    }

    /**
     * 学生数据添加消息接收
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(value = @Queue(UserRabbitConstant.STUDENT_ADD_QUEUE), // queue 定义
        key = UserRabbitConstant.STUDENT_ADD_QUEUE, // 发送的key
        exchange = @Exchange(name = UserRabbitConstant.USER_EXCHANGE, declare = "false"))) // exchange binding
    public void studentAddListener(String message) {

        try {
            // 学生新增时，通知app变更
            log.info("======= student.add receive :{} =========", message);
            JSONObject jsonObject = JSON.parseObject(message);
            Long organizationId = Long.valueOf(jsonObject.get("organizationId").toString());
            Long classesId = Long.valueOf(jsonObject.get("classesId").toString());
            MessageVo messageVo = new MessageVo();
            messageVo.setMessageBody(null);
            messageVo.setMessageType(MessageWsType.MODIFY_STUDENT.getValue());
            messageService.sendMessageWsByClassesId(organizationId, classesId, messageVo);
        } catch (Exception e) {
            log.error("============= student.add error ==========", e);
            e.printStackTrace();
        }
    }

    /**
     * 老师数据新增消息接收
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(value = @Queue(UserRabbitConstant.TEACHER_ADD_QUEUE), // queue 定义
        key = UserRabbitConstant.TEACHER_ADD_QUEUE, // 发送的key
        exchange = @Exchange(name = UserRabbitConstant.USER_EXCHANGE, declare = "false"))) // exchange binding
    public void teacherAddListener(String message) {

        try {
            // 教师新增时，通知app变更
            log.info("======= teacher .add receive :{} =========", message);
            JSONObject jsonObject = JSON.parseObject(message);
            Long organizationId = Long.valueOf(jsonObject.get("organizationId").toString());
            MessageVo messageVo = new MessageVo();
            messageVo.setMessageBody(null);
            messageVo.setMessageType(MessageWsType.MODIFY_TEACHER.getValue());
            messageService.sendMessageWsBySchool(organizationId, null, messageVo);
        } catch (Exception e) {
            log.error("============= teacher.add error ==========", e);
        }
    }

    /**
     * 老师数据更新消息接收
     *
     * @param message the message is json
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(value = @Queue(UserRabbitConstant.TEACHER_UPDATE_QUEUE), // queue 定义
        key = UserRabbitConstant.TEACHER_UPDATE_QUEUE, // 发送的key
        exchange = @Exchange(name = UserRabbitConstant.USER_EXCHANGE, declare = "false"))) // exchange binding
    public void teacherUpdateListener(String message) {

        try {
            log.info("======= teacher.update receive :{} =========", message);
            JSONObject jsonObject = JSON.parseObject(message);
            String userOid = jsonObject.get("userOid").toString();
            String realName = jsonObject.get("realName").toString();
            faceRecordTeacherService.updateRealNameByUserOid(userOid, realName);
            log.info("======= teacher.update receive :{} =========", message);
        } catch (Exception e) {
            log.error("============= teacher.update error ==========", e);
        }
    }

}