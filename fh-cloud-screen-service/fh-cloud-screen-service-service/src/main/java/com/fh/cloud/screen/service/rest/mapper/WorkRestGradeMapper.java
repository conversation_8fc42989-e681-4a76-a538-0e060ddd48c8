package com.fh.cloud.screen.service.rest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGrade;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeVo;

import java.util.List;

/**
 * 作息时间年级表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface WorkRestGradeMapper extends BaseMapper<WorkRestGrade> {

    List<WorkRestGradeVo> getWorkRestGradeListByCondition(WorkRestGradeListConditionBo condition);

}
