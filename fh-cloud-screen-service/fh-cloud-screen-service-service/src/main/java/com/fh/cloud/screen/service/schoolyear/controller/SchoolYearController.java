package com.fh.cloud.screen.service.schoolyear.controller;

import java.text.ParseException;
import java.util.*;

import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.schoolyear.api.SchoolYearApi;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.bo.OrganizationTermBo;
import com.light.user.organization.entity.bo.OrganizationTermDelSaveBo;
import com.light.user.organization.entity.vo.OrganizationTermVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 学年信息查询
 *
 * <AUTHOR>
 * @date 2022 /4/11 21:22
 */
@RestController
@Api(value = "学年信息查询", tags = "学年信息查询")
public class SchoolYearController implements SchoolYearApi {
    @Autowired
    private BaseDataService baseDataService;

    /**
     * years当前学年和下一学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:44:04
     */
    @ApiOperation(value = "查询当前学年和下一学年", httpMethod = "GET")
    public AjaxResult listSchoolYears() {
        List<String> schoolYears = SchoolYearUtil.getCurrentSchoolYearAndNext();
        return AjaxResult.success(schoolYears);
    }

    /**
     * 当前学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查询当前学年", httpMethod = "GET")
    public AjaxResult currentYear() {
        String currentYear = SchoolYearUtil.getCurrentSchoolYear();
        return AjaxResult.success(currentYear);
    }

    /**
     * 根据年级和学年获取入学年份
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "根据年级和学年获取入学年份", httpMethod = "GET")
    public AjaxResult getYear(@RequestParam(value = "grade") String grade,
        @RequestParam(value = "schoolYear") String schoolYear) {
        Integer year = SchoolYearUtil.getYearByGradeAndSchoolYear(grade, schoolYear);
        return AjaxResult.success(year);
    }

    /**
     * 获取学年月日
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "学年开始月日", httpMethod = "GET")
    public AjaxResult getMonthDay() {
        Map<String, Integer> result = new HashMap<>();
        result.put("month", ConstantsConfig.SCHOOL_YEAR_MONTH);
        result.put("day", ConstantsConfig.SCHOOL_YEAR_DAY);
        return AjaxResult.success(result);
    }

    /**
     * 保存学期设置
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "保存学期设置", httpMethod = "POST")
    public AjaxResult saveTerm(@RequestBody OrganizationTermDelSaveBo organizationTermDelSaveBo) {
        AjaxResult ajaxResult = baseDataService.delAndSaveByOrgIdAndStudyYear(organizationTermDelSaveBo);
        return ajaxResult;
    }

    /**
     * 查看学期列表
     *
     * @param organizationTermBo the organization term bo
     * @return the ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查看学期列表", httpMethod = "POST")
    public AjaxResult listTerm(@RequestBody OrganizationTermBo organizationTermBo) {
        if (StringUtils.isBlank(organizationTermBo.getStudyYear())) {
            organizationTermBo.setStudyYear(SchoolYearUtil.getCurrentSchoolYear());
        }

        AjaxResult<List<OrganizationTermVo>> ajaxResult = baseDataService
            .getByOrganizationIdAndStudyYear(organizationTermBo.getOrganizationId(), organizationTermBo.getStudyYear());
        return ajaxResult;
    }

    /**
     * 查看当前月份的教学周
     *
     * @param organizationId 组织id
     * @param month 要查看的月份
     * @return
     * @throws Exception
     */
    @Override
    public AjaxResult getSchoolTeacherWeekByMonth(Long organizationId, String month) throws ParseException {
        List<String> schoolWeekBySchoolTerm = null;
        // 按照校历周返回
        OrganizationTermBo organizationTermBo = new OrganizationTermBo();
        organizationTermBo.setOrganizationId(organizationId);
        organizationTermBo.setStudyYear(currentYear().getData().toString());
        AjaxResult ajaxResult = listTerm(organizationTermBo);
        List<OrganizationTermVo> organizationTermBos = (List<OrganizationTermVo>)ajaxResult.getData();
        // 同一学年 两个学期
        List<List<String>> allWeeks = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(organizationTermBos)) {
            for (OrganizationTermVo termBo : organizationTermBos) {
                Date termStartTime = termBo.getStartTime();
                Date termEndTime = termBo.getEndTime();
                allWeeks.add(SchoolYearUtil.getSchoolWeekBySchoolTerm(termStartTime, termEndTime, month));
            }
        }
        if (CollectionUtils.isEmpty(allWeeks)) {
            return AjaxResult.success(new ArrayList<>(6));
        }
        // 返回6周
        List<String> returnWeek = new ArrayList<>(6);
        for (int i = 0; i < 6; i++) {
            boolean addFlag = false;
            // 每个学期
            for (List<String> allWeek : allWeeks) {
                // 每个同样的周
                String week = allWeek.get(i);
                // 一个不为空，则把周记录返回
                if (StringUtils.isNotBlank(week)) {
                    returnWeek.add(i, week);
                    addFlag = true;
                    break;
                }
            }
            // 每个学期对应周都为空，添加空
            if (!addFlag) {
                returnWeek.add(null);
            }
        }
        return AjaxResult.success(returnWeek);
    }
}
