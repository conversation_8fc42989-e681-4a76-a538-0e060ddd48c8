package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.LabelLibraryAuditRelApi;
import com.fh.cloud.screen.service.screen.entity.dto.LabelLibraryAuditRelDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;
import com.fh.cloud.screen.service.screen.service.ILabelLibraryAuditRelService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 标签海报关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
@RestController
@Validated
public class LabelLibraryAuditRelController implements LabelLibraryAuditRelApi{
	
    @Autowired
    private ILabelLibraryAuditRelService labelLibraryAuditRelService;

    /**
     * 查询标签海报关联表分页列表
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<LabelLibraryAuditRelVo>> getLabelLibraryAuditRelPageListByCondition(@RequestBody LabelLibraryAuditRelConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<LabelLibraryAuditRelVo> pageInfo = new PageInfo<>(labelLibraryAuditRelService.getLabelLibraryAuditRelListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询标签海报关联表列表
	 * <AUTHOR>
	 * @date 2023-12-06 10:26:05
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<LabelLibraryAuditRelVo>> getLabelLibraryAuditRelListByCondition(@RequestBody LabelLibraryAuditRelConditionBo condition){
		List<LabelLibraryAuditRelVo> list = labelLibraryAuditRelService.getLabelLibraryAuditRelListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增标签海报关联表
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addLabelLibraryAuditRel(@Validated @RequestBody LabelLibraryAuditRelBo labelLibraryAuditRelBo){
		return labelLibraryAuditRelService.addLabelLibraryAuditRel(labelLibraryAuditRelBo);
    }

    /**
	 * 修改标签海报关联表
	 * @param labelLibraryAuditRelBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateLabelLibraryAuditRel(@Validated @RequestBody LabelLibraryAuditRelBo labelLibraryAuditRelBo) {
		if(null == labelLibraryAuditRelBo.getId()) {
			return AjaxResult.fail("标签海报关联表id不能为空");
		}
		return labelLibraryAuditRelService.updateLabelLibraryAuditRel(labelLibraryAuditRelBo);
	}

	/**
	 * 查询标签海报关联表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<LabelLibraryAuditRelVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("标签海报关联表id不能为空");
		}
		LabelLibraryAuditRelConditionBo condition = new LabelLibraryAuditRelConditionBo();
		condition.setId(id);
		LabelLibraryAuditRelVo vo = labelLibraryAuditRelService.getLabelLibraryAuditRelByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除标签海报关联表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		LabelLibraryAuditRelDto labelLibraryAuditRelDto = new LabelLibraryAuditRelDto();
		labelLibraryAuditRelDto.setId(id);
		labelLibraryAuditRelDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(labelLibraryAuditRelService.updateById(labelLibraryAuditRelDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
