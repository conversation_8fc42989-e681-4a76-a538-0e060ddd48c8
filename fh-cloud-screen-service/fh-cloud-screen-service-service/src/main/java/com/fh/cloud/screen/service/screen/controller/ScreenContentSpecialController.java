package com.fh.cloud.screen.service.screen.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.chromedp.dto.ChromedpDto;
import com.fh.cloud.screen.service.chromedp.service.ChromedpService;
import com.fh.cloud.screen.service.consts.ConstantsMessage;
import com.fh.cloud.screen.service.verify.service.VerifyPermissionsService;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.ScreenContentStatusType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.screen.api.ScreenContentSpecialApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialService;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialSpaceGroupRelService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 云屏紧急发布内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@RestController
@Api(value = "", tags = "云屏紧急发布管理")
@Slf4j
public class ScreenContentSpecialController implements ScreenContentSpecialApi {

    @Autowired
    private IScreenContentSpecialService screenContentSpecialService;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private IScreenContentSpecialSpaceGroupRelService screenContentSpecialSpaceGroupRelService;
    @Autowired
    private ChromedpService chromedpService;
    @Resource
    private VerifyPermissionsService verifyPermissionsService;
    @Resource
    private BaseDataService baseDataService;

    /**
     * 查询云屏紧急发布内容表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @Override
    @ApiOperation(value = "查询云屏紧急发布内容表列表", httpMethod = "POST")
    public AjaxResult
        getScreenContentSpecialListByCondition(@RequestBody ScreenContentSpecialListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // web紧急内容列表查询的时候，空认为是全校分类
        if (condition.getCampusId() == null) {
            condition.setCampusId(ConstantsLong.NUM_0);
        }
        condition.setOrderBy("update_time desc");

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", screenContentSpecialService.getScreenContentSpecialListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ScreenContentSpecialVo> pageInfo =
                new PageInfo<>(screenContentSpecialService.getScreenContentSpecialListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 保存云屏紧急发布内容表（新增和修改）
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @Override
    @ApiOperation(value = "保存云屏紧急发布内容表", httpMethod = "POST")
    public AjaxResult saveScreenContentSpecial(@RequestBody ScreenContentSpecialBo screenContentSpecialBo) {
        // 越权校验
        boolean verifyOrganization =
            verifyPermissionsService.verifyOrganization(screenContentSpecialBo.getOrganizationId());
        if (!verifyOrganization) {
            return AjaxResult.fail("失败：当前登陆用户与该组织不同");
        }
        // 内容审核
        if (baseDataService.getCheckSwitch()) {
            boolean check = checkScreenContentSpecial(screenContentSpecialBo);
            if (!check) {
                return AjaxResult.fail(ConstantsMessage.WRONG_INSPECT);
            }
        }

        // 注释内容为：保存即发布的模式，现已改成保存发布分离。因此注释
        // screenContentSpecialVoNow目的：当前正在发布的内容，如果有则需要推送给对应区域设备，重新拉取场景信息。
        // ScreenContentSpecialVo screenContentSpecialVoNow = screenContentSpecialService
        // .getNowPublish(screenContentSpecialBo.getOrganizationId(), screenContentSpecialBo.getCampusId());
        // Long screenContentSpecialIdNow =
        // screenContentSpecialVoNow == null ? null : screenContentSpecialVoNow.getScreenContentSpecialId();
        boolean save = false;
        Long screenContentSpecialId = null;
        try {
            screenContentSpecialBo.setScreenContentStatus(ScreenContentStatusType.NOT_PUBLISH.getValue());
            screenContentSpecialId =
                screenContentSpecialService.saveOrUpdateScreenContentSpecialPublish(screenContentSpecialBo);
            if (screenContentSpecialId != null) {
                save = true;
            }
        } catch (Exception e) {
            log.error("ScreenContentSpecialController saveScreenContentSpecial error:", e);
            save = false;
        }
        // if (save) {
        // // publish event
        // applicationContext.publishEvent(PublishEvent.produceContentSpecialPublishEvent(
        // MessageWsType.MODIFY_SPECIAL.getValue(), screenContentSpecialBo.getOrganizationId(),
        // screenContentSpecialId, screenContentSpecialIdNow, null));
        // return AjaxResult.success("保存成功");
        // }
        if (save) {
            // creat screenshotTask
            if (StringUtils.isNotBlank(screenContentSpecialBo.getScreenContentUrl())) {
                chromedpService.produceChromedpTask(ChromedpDto.generateChromedpDto(
                    String.valueOf(screenContentSpecialId), ChromedpService.BusinessType.SPECIAL.getValue(),
                    screenContentSpecialBo.getScreenContentUrl(), null, null, null));
            }
            return AjaxResult.success(screenContentSpecialId);
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏紧急发布内容表详情
     *
     * @param screenContentSpecialId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @Override
    @ApiOperation(value = "查询云屏紧急发布内容表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenContentSpecialId") Long screenContentSpecialId) {
        ScreenContentSpecialVo screenContentSpecialVo = screenContentSpecialService.getDetail(screenContentSpecialId);
        List<Long> spaceGroupIds =
            screenContentSpecialSpaceGroupRelService.listSpaceGroupIdsByScreenContentSpecialId(screenContentSpecialId);
        screenContentSpecialVo.setSpaceGroupIds(spaceGroupIds);
        return AjaxResult.success(screenContentSpecialVo);
    }

    /**
     * 删除云屏紧急发布内容表(暂时不需要)
     *
     * @param screenContentSpecialId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "删除云屏紧急发布内容表", httpMethod = "GET")
    @Deprecated
    public AjaxResult delete(@RequestParam("screenContentSpecialId") Long screenContentSpecialId) {
        ScreenContentSpecialBo screenContentSpecialBo = new ScreenContentSpecialBo();
        screenContentSpecialBo.setScreenContentSpecialId(screenContentSpecialId);
        screenContentSpecialBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenContentSpecialService.updateScreenContentSpecial(screenContentSpecialBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 插销发布
     *
     * @param screenContentSpecialId
     * @return
     */
    @Override
    public AjaxResult cancelSubmit(@RequestParam("screenContentSpecialId") Long screenContentSpecialId) {
        if (null == screenContentSpecialId) {
            return AjaxResult.fail("云屏紧急发布内容表id不能为空");
        }

        ScreenContentSpecialBo screenContentSpecialBo = new ScreenContentSpecialBo();
        screenContentSpecialBo.setScreenContentSpecialId(screenContentSpecialId);
        screenContentSpecialBo.setScreenContentStatus(ScreenContentStatusType.NOT_PUBLISH.getValue());
        boolean update = screenContentSpecialService.updateScreenContentSpecial(screenContentSpecialBo);
        if (update) {
            // publish event
            applicationContext
                .publishEvent(PublishEvent.produceContentSpecialPublishEvent(MessageWsType.MODIFY_SPECIAL.getValue(),
                    screenContentSpecialBo.getOrganizationId(), screenContentSpecialId, null, null));
            return AjaxResult.success("撤销发布成功");
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult submit(ScreenContentSpecialBo screenContentSpecialBo) {
        if (null == screenContentSpecialBo.getScreenContentSpecialId()) {
            return AjaxResult.fail("云屏紧急发布内容表id不能为空");
        }
        ScreenContentSpecialVo screenContentSpecialVoNow = screenContentSpecialService
            .getNowPublish(screenContentSpecialBo.getOrganizationId(), screenContentSpecialBo.getCampusId());
        if (screenContentSpecialVoNow != null && !screenContentSpecialVoNow.getScreenContentSpecialId()
            .equals(screenContentSpecialBo.getScreenContentSpecialId())) {
            return AjaxResult.fail("当前存在正在发布的紧急内容，请撤回后再发布");
        }

        screenContentSpecialBo.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
        screenContentSpecialBo.setOrganizationId(null);
        screenContentSpecialBo.setScreenContentTitle(null);
        screenContentSpecialBo.setScreenContentTxt(null);
        boolean update = screenContentSpecialService.updateScreenContentSpecial(screenContentSpecialBo);
        if (update) {
            // publish event
            applicationContext.publishEvent(PublishEvent.produceContentSpecialPublishEvent(
                MessageWsType.MODIFY_SPECIAL.getValue(), screenContentSpecialBo.getOrganizationId(),
                screenContentSpecialBo.getScreenContentSpecialId(), null, null));
            return AjaxResult.success("紧急发布成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 检查紧急发布文本或图片是否合规
     *
     * @param screenContentSpecialBo 紧急发布内容
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 14:31
     */
    private boolean checkScreenContentSpecial(ScreenContentSpecialBo screenContentSpecialBo) {
        boolean check = true;
        // 标题
        if (StringUtils.isNotBlank(screenContentSpecialBo.getScreenContentTitle())) {
            check = baseDataService.checkSingleText(screenContentSpecialBo.getScreenContentTitle());
        }
        if (!check) {
            return false;
        }
        // 内容
        if (StringUtils.isNotBlank(screenContentSpecialBo.getScreenContentTxt())) {
            check = baseDataService.checkRichText(screenContentSpecialBo.getScreenContentTxt());
        }
        if (!check) {
            return false;
        }
        // 称呼
        if (StringUtils.isNotBlank(screenContentSpecialBo.getCallContent())) {
            check = baseDataService.checkSingleText(screenContentSpecialBo.getCallContent());
        }
        if (!check) {
            return false;
        }
        // 落款
        if (StringUtils.isNotBlank(screenContentSpecialBo.getSignContent())) {
            check = baseDataService.checkSingleText(screenContentSpecialBo.getSignContent());
        }
        if (!check) {
            return false;
        }

        // 图片
        if (StringUtils.isNotBlank(screenContentSpecialBo.getScreenContentMediaUrl())) {
            check = baseDataService.checkSingleImage(screenContentSpecialBo.getScreenContentMediaUrl());
        }
        return check;
    }
}
