package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryCollectApi;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryCollectService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 海报收藏表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@RestController
@Validated
public class ScreenModuleLibraryCollectController implements ScreenModuleLibraryCollectApi {

    @Autowired
    private IScreenModuleLibraryCollectService screenModuleLibraryCollectService;

    /**
     * 查询海报收藏表分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @Override
    public AjaxResult<PageInfo<ScreenModuleLibraryCollectVo>>
        getScreenModuleLibraryCollectPageListByCondition(@RequestBody ScreenModuleLibraryCollectConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenModuleLibraryCollectVo> pageInfo =
            new PageInfo<>(screenModuleLibraryCollectService.getScreenModuleLibraryCollectListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询海报收藏表列表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @Override
    public AjaxResult<List<ScreenModuleLibraryCollectVo>>
        getScreenModuleLibraryCollectListByCondition(@RequestBody ScreenModuleLibraryCollectConditionBo condition) {
        List<ScreenModuleLibraryCollectVo> list =
            screenModuleLibraryCollectService.getScreenModuleLibraryCollectListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增海报收藏表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @Override
    public AjaxResult addScreenModuleLibraryCollect(
        @Validated @RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        return screenModuleLibraryCollectService.addScreenModuleLibraryCollect(screenModuleLibraryCollectBo);
    }

    /**
     * 修改海报收藏表
     * 
     * @param screenModuleLibraryCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @Override
    public AjaxResult updateScreenModuleLibraryCollect(
        @Validated @RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        if (null == screenModuleLibraryCollectBo.getId()) {
            return AjaxResult.fail("海报收藏表id不能为空");
        }
        return screenModuleLibraryCollectService.updateScreenModuleLibraryCollect(screenModuleLibraryCollectBo);
    }

    /**
     * 查询海报收藏表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @Override
    public AjaxResult<ScreenModuleLibraryCollectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("海报收藏表id不能为空");
        }
        ScreenModuleLibraryCollectVo vo = screenModuleLibraryCollectService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除海报收藏表
     * 
     * @param screenModuleLibraryCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @Override
    public AjaxResult delete(@RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        if (null == screenModuleLibraryCollectBo || null == screenModuleLibraryCollectBo.getOrganizationId()
            || null == screenModuleLibraryCollectBo.getScreenModuleLibraryId()) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        return screenModuleLibraryCollectService.delScreenModuleLibraryCollect(screenModuleLibraryCollectBo);
    }
}
