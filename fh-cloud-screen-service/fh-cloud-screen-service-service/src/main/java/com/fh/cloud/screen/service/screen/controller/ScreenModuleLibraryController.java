package com.fh.cloud.screen.service.screen.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.common.service.IScreenDictionaryDataService;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.consts.ScreenModuleConstants;
import com.fh.cloud.screen.service.enums.DictionaryType;
import com.fh.cloud.screen.service.enums.PosterEnums;
import com.fh.cloud.screen.service.enums.ScreenModuleLibrarySource;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryAuditDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryAuditService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 模块库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@RestController
@Api(value = "", tags = "模块库管理")
public class ScreenModuleLibraryController implements ScreenModuleLibraryApi {

    @Autowired
    private IScreenModuleLibraryService screenModuleLibraryService;
    @Autowired
    private IScreenDictionaryDataService dictionaryDataService;
    @Autowired
    private IScreenModuleLibraryAuditService screenModuleLibraryAuditService;

    @Autowired
    BaseDataService baseDataService;

    /**
     * 查询模块库表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询模块库表列表", httpMethod = "POST")
    public AjaxResult getScreenModuleLibraryListByCondition(@RequestBody ScreenModuleLibraryListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("library_sort,screen_module_library_id");
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", screenModuleLibraryService.getScreenModuleLibraryListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ScreenModuleLibraryVo> pageInfo =
                new PageInfo<>(screenModuleLibraryService.getScreenModuleLibraryListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }

    }

    /**
     * 查询模块库表分组
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询模块库表分组", httpMethod = "POST")
    public AjaxResult
        getScreenModuleLibraryGroupMapByCondition(@RequestBody ScreenModuleLibraryListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setParentScreenModuleLibraryId(0L);
        Map<String, Object> map = new HashMap<String, Object>();
        List<ScreenModuleLibraryVo> screenModuleLibraryVos =
            screenModuleLibraryService.getScreenModuleLibraryListByCondition(condition);
        if (CollectionUtils.isEmpty(screenModuleLibraryVos)) {
            map.put("list", Lists.newArrayList());
            return AjaxResult.success(map);
        }
        LinkedHashMap<Long, List<ScreenModuleLibraryVo>> moduleLibraryGroupVoMap =
            screenModuleLibraryVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleGroupType,
                LinkedHashMap::new, Collectors.toList()));
        LinkedHashMap<String, List<ScreenModuleLibraryVo>> resultMap = Maps.newLinkedHashMap();
        if (!moduleLibraryGroupVoMap.isEmpty()) {
            Map<String, String> dictionaryDataMap =
                dictionaryDataService.getDictionaryMapByType(DictionaryType.MODULE_GROUP_TYPE.getValue());
            moduleLibraryGroupVoMap.forEach((moduleGroupType, screenModuleLibraryVosTemp) -> {
                String labelName = dictionaryDataMap.get(String.valueOf(moduleGroupType));
                if (StringUtils.isNotBlank(labelName)) {
                    resultMap.put(labelName, screenModuleLibraryVosTemp);
                }
            });
        }
        return AjaxResult.success(resultMap);
    }

    /**
     * 新增模块库表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "新增模块库表", httpMethod = "POST")
    public AjaxResult addScreenModuleLibrary(@RequestBody ScreenModuleLibraryBo screenModuleLibraryBo) {
        screenModuleLibraryBo.setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
        boolean save = screenModuleLibraryService.addScreenModuleLibrary(screenModuleLibraryBo);
        if (save) {
            return getDetail(screenModuleLibraryBo.getScreenModuleLibraryId());
        }
        return AjaxResult.fail();
    }

    /**
     * 修改模块库表
     *
     * @param screenModuleLibraryBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "修改模块库表", httpMethod = "POST")
    public AjaxResult updateScreenModuleLibrary(@RequestBody ScreenModuleLibraryBo screenModuleLibraryBo) {
        if (null == screenModuleLibraryBo.getScreenModuleLibraryId()) {
            return AjaxResult.fail("模块库表id不能为空");
        }
        boolean update = screenModuleLibraryService.updateScreenModuleLibrary(screenModuleLibraryBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询模块库表详情
     *
     * @param screenModuleLibraryId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询模块库表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenModuleLibraryId") Long screenModuleLibraryId) {
        ScreenModuleLibraryVo screenModuleLibraryVo = screenModuleLibraryService.getDetail(screenModuleLibraryId);
        return AjaxResult.success(screenModuleLibraryVo);
    }

    /**
     * 删除模块库表
     *
     * @param screenModuleLibraryId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "删除模块库表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenModuleLibraryId") Long screenModuleLibraryId) {
        ScreenModuleLibraryVo vo = screenModuleLibraryService.getDetail(screenModuleLibraryId);

        ScreenModuleLibraryBo screenModuleLibraryBo = new ScreenModuleLibraryBo();
        screenModuleLibraryBo.setScreenModuleLibraryId(screenModuleLibraryId);
        screenModuleLibraryBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenModuleLibraryService.updateScreenModuleLibrary(screenModuleLibraryBo);
        if (delete) {
            // 判断是否有审核记录，若有，同步删除审核记录
            if (vo.getLibrarySource() == ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue()
                && StringUtils.isNotBlank(vo.getThirdId())) {
                ScreenModuleLibraryAuditDto dto = new ScreenModuleLibraryAuditDto();
                dto.setScreenModuleLibraryAuditId(Long.parseLong(vo.getThirdId()));
                dto.setIsDelete(StatusEnum.ISDELETE.getCode());
                screenModuleLibraryAuditService.updateById(dto);
            }
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 交换顺序
     *
     * @param firstId
     * @param secondId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/13 14:07
     */
    @Override
    public AjaxResult exchange(Long firstId, Long secondId) {
        ScreenModuleLibrary firstLibrary = screenModuleLibraryService.getById(firstId);
        ScreenModuleLibrary secondLibrary = screenModuleLibraryService.getById(secondId);
        if (null == firstLibrary || null == secondLibrary) {
            return AjaxResult.fail("参数错误");
        }
        Integer sort = firstLibrary.getLibrarySort();
        firstLibrary.setLibrarySort(secondLibrary.getLibrarySort());
        secondLibrary.setLibrarySort(sort);
        List<ScreenModuleLibrary> libraryList = new ArrayList<>();
        libraryList.add(firstLibrary);
        libraryList.add(secondLibrary);
        screenModuleLibraryService.updateBatchById(libraryList);
        return AjaxResult.success();
    }

    /**
     * 按照id顺序批量更新顺序
     *
     * @param idList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 14:27
     */
    @Override
    public AjaxResult updateLibrarySortByIdList(List<Long> idList) {
        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(idList)) {
            return AjaxResult.fail("参数不能为空");
        }
        List<ScreenModuleLibrary> libraryList = new ArrayList<>();
        int i = 1;
        for (Long id : idList) {
            ScreenModuleLibrary dictionaryData = new ScreenModuleLibrary();
            dictionaryData.setScreenModuleLibraryId(id);
            dictionaryData.setLibrarySort(i++);
            libraryList.add(dictionaryData);
        }
        screenModuleLibraryService.updateBatchById(libraryList);
        return AjaxResult.success();
    }

    /**
     * 海报条件查询主题列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 16:43
     */
    @Override
    public AjaxResult getPosterList(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryService.getPosterList(conditionBo);
    }

    @Override
    public AjaxResult getPosterListSel(ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryService.getPosterListSel(conditionBo);
    }

    /**
     * 设备关联标签关联海报主题列表(订阅海报列表)
     *
     * @param conditionBo the deviceNumber
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 9:38
     */
    @Override
    public AjaxResult getDevicePosterListByDeviceNumber(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryService.getDevicePosterListByDeviceNumber(conditionBo);
    }

    /**
     * 运营海报库列表（未配置标签、已配置标签）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 13:53
     */
    @Override
    public AjaxResult getLabelPosterListByCondition(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryService.getLabelPosterListByCondition(conditionBo);
    }

    /**
     * 获取标签海报统计信息（未配置标签、已配置标签）
     * 
     * @param conditionBo
     * @return
     */
    @Override
    public AjaxResult getLabelPosterStatistics(ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryService.getLabelPosterStatistics(conditionBo);
    }

    /**
     * 新增或编辑主题海报
     *
     * @param screenModuleLibraryBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/9 14:04
     */
    @Override
    public AjaxResult updateLibrary(@RequestBody ScreenModuleLibraryBo screenModuleLibraryBo) {
        return screenModuleLibraryService.updateLibrary(screenModuleLibraryBo);
    }

    /**
     * 我创建的海报列表
     *
     * @param conditionBo
     * @return AjaxResult
     * <AUTHOR>
     * @date 2023/3/31 11:24
     */
    @GetMapping("/person-posters")
    @Override
    public AjaxResult getPersonPosters(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryService.getPersonPosters(conditionBo);
    }

    /**
     * 查询校本海报数量
     *
     * @param conditionBo the condition bo
     * @return com.light.core.entity.AjaxResult poster school num
     * <AUTHOR>
     * @date 2024 -07-01 15:15:09
     */
    @Override
    public AjaxResult<List<ScreenModuleLibraryNumVo>> getPosterSchoolNum(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return AjaxResult.success(screenModuleLibraryService.getPosterSchoolNum(conditionBo));
    }

    /**
     * 查询校本海报海报数
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult<java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo>>
     * <AUTHOR>
     * @date 2024/8/2 14:28
     **/
    @Override
    public AjaxResult<List<ScreenModuleLibraryNumVo>> getPosterMediaSchoolNum(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        // 查询海报
        conditionBo.setIsPoster(PosterEnums.POST_IS.getCode());
        // 过滤班级海报
        conditionBo.setDictionaryDataClassesId(ConstantsLong.NUM_0);
        return AjaxResult.success(screenModuleLibraryService.getPosterMediaSchoolNum(conditionBo));
    }
}
