package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenPoetryContentApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryContentDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo;
import com.fh.cloud.screen.service.screen.service.IScreenPoetryContentService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 共话诗词表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
@RestController
@Validated
public class ScreenPoetryContentController implements ScreenPoetryContentApi{
	
    @Autowired
    private IScreenPoetryContentService screenPoetryContentService;

    /**
     * 查询共话诗词表分页列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ScreenPoetryContentVo>> getScreenPoetryContentPageListByCondition(@RequestBody ScreenPoetryContentConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ScreenPoetryContentVo> pageInfo = new PageInfo<>(screenPoetryContentService.getScreenPoetryContentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询共话诗词表列表
	 * <AUTHOR>
	 * @date 2023-06-26 16:32:47
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ScreenPoetryContentVo>> getScreenPoetryContentListByCondition(@RequestBody ScreenPoetryContentConditionBo condition){
		List<ScreenPoetryContentVo> list = screenPoetryContentService.getScreenPoetryContentListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增共话诗词表
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addScreenPoetryContent(@Validated @RequestBody ScreenPoetryContentBo screenPoetryContentBo){
		return screenPoetryContentService.addScreenPoetryContent(screenPoetryContentBo);
    }

    /**
	 * 修改共话诗词表
	 * @param screenPoetryContentBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateScreenPoetryContent(@Validated @RequestBody ScreenPoetryContentBo screenPoetryContentBo) {
		if(null == screenPoetryContentBo.getScreenPoetryContentId()) {
			return AjaxResult.fail("共话诗词表id不能为空");
		}
		return screenPoetryContentService.updateScreenPoetryContent(screenPoetryContentBo);
	}

	/**
	 * 查询共话诗词表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ScreenPoetryContentVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("共话诗词表id不能为空");
		}
		ScreenPoetryContentConditionBo condition = new ScreenPoetryContentConditionBo();
		condition.setScreenPoetryContentId(id);
		ScreenPoetryContentVo vo = screenPoetryContentService.getScreenPoetryContentByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除共话诗词表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ScreenPoetryContentDto screenPoetryContentDto = new ScreenPoetryContentDto();
		screenPoetryContentDto.setScreenPoetryContentId(id);
		screenPoetryContentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(screenPoetryContentService.updateById(screenPoetryContentDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
