package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenPoetryLikesApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryLikesDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo;
import com.fh.cloud.screen.service.screen.service.IScreenPoetryLikesService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 共话诗词点赞记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
@RestController
@Validated
public class ScreenPoetryLikesController implements ScreenPoetryLikesApi {
	
    @Autowired
    private IScreenPoetryLikesService screenPoetryLikesService;

    /**
     * 查询共话诗词点赞记录表分页列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ScreenPoetryLikesVo>> getScreenPoetryLikesPageListByCondition(@RequestBody ScreenPoetryLikesConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ScreenPoetryLikesVo> pageInfo = new PageInfo<>(screenPoetryLikesService.getScreenPoetryLikesListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询共话诗词点赞记录表列表
	 * <AUTHOR>
	 * @date 2023-06-26 16:32:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ScreenPoetryLikesVo>> getScreenPoetryLikesListByCondition(@RequestBody ScreenPoetryLikesConditionBo condition){
		List<ScreenPoetryLikesVo> list = screenPoetryLikesService.getScreenPoetryLikesListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增共话诗词点赞记录表
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addScreenPoetryLikes(@Validated @RequestBody ScreenPoetryLikesBo screenPoetryLikesBo){
		return screenPoetryLikesService.addScreenPoetryLikes(screenPoetryLikesBo);
    }

    /**
	 * 修改共话诗词点赞记录表
	 * @param screenPoetryLikesBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateScreenPoetryLikes(@Validated @RequestBody ScreenPoetryLikesBo screenPoetryLikesBo) {
		if(null == screenPoetryLikesBo.getScreenPoetryLikesId()) {
			return AjaxResult.fail("共话诗词点赞记录表id不能为空");
		}
		return screenPoetryLikesService.updateScreenPoetryLikes(screenPoetryLikesBo);
	}

	/**
	 * 查询共话诗词点赞记录表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ScreenPoetryLikesVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("共话诗词点赞记录表id不能为空");
		}
		ScreenPoetryLikesConditionBo condition = new ScreenPoetryLikesConditionBo();
		condition.setScreenPoetryLikesId(id);
		ScreenPoetryLikesVo vo = screenPoetryLikesService.getScreenPoetryLikesByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除共话诗词点赞记录表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ScreenPoetryLikesDto screenPoetryLikesDto = new ScreenPoetryLikesDto();
		screenPoetryLikesDto.setScreenPoetryLikesId(id);
		screenPoetryLikesDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(screenPoetryLikesService.updateById(screenPoetryLikesDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	/**
	 * 点赞
	 *
	 * @param screenPoetryContentId
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/6/27 9:25
	 **/
	@Override
	public AjaxResult addScreenPoetryLikesNum(@RequestParam("screenPoetryContentId") Long screenPoetryContentId) {
		return screenPoetryLikesService.addScreenPoetryLikesNum(screenPoetryContentId);
	}

}
