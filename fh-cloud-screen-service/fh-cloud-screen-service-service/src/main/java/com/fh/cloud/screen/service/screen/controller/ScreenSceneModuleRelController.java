package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneModuleRelService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 云屏场景模块关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
@RestController
@RequestMapping("/screen/scene-module")
@Validated
public class ScreenSceneModuleRelController {

    @Autowired
    private IScreenSceneModuleRelService screenSceneModuleRelService;

    /**
     * 查询云屏场景模块关系表列表
     * 
     * <AUTHOR>
     * @date 2022-05-10 09:31:56
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询云屏场景模块关系表列表", httpMethod = "POST")
    public AjaxResult
        getScreenSceneModuleRelListByCondition(@RequestBody ScreenSceneModuleRelListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenSceneModuleRelVo> pageInfo =
            new PageInfo<>(screenSceneModuleRelService.getScreenSceneModuleRelListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("screenSceneModuleRelList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增云屏场景模块关系表
     * 
     * <AUTHOR>
     * @date 2022-05-10 09:31:56
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增云屏场景模块关系表", httpMethod = "POST")
    public AjaxResult addScreenSceneModuleRel(@RequestBody ScreenSceneModuleRelBo screenSceneModuleRelBo) {
        boolean save = screenSceneModuleRelService.addScreenSceneModuleRel(screenSceneModuleRelBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏场景模块关系表
     * 
     * @param screenSceneModuleRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 09:31:56
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改云屏场景模块关系表", httpMethod = "POST")
    public AjaxResult updateScreenSceneModuleRel(@RequestBody ScreenSceneModuleRelBo screenSceneModuleRelBo) {
        if (null == screenSceneModuleRelBo.getId()) {
            return AjaxResult.fail("云屏场景模块关系表id不能为空");
        }
        boolean update = screenSceneModuleRelService.updateScreenSceneModuleRel(screenSceneModuleRelBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏场景模块关系表详情
     * 
     * @param screenSceneModuleRelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 09:31:56
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询云屏场景模块关系表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenSceneModuleRelId") Long screenSceneModuleRelId) {
        ScreenSceneModuleRelVo screenSceneModuleRelVo = screenSceneModuleRelService.getDetail(screenSceneModuleRelId);
        return AjaxResult.success(screenSceneModuleRelVo);
    }

    /**
     * 删除云屏场景模块关系表
     * 
     * @param screenSceneModuleRelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 09:31:56
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除云屏场景模块关系表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenSceneModuleRelId") Long screenSceneModuleRelId) {
        ScreenSceneModuleRelBo screenSceneModuleRelBo = new ScreenSceneModuleRelBo();
        screenSceneModuleRelBo.setId(screenSceneModuleRelId);
        screenSceneModuleRelBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenSceneModuleRelService.updateScreenSceneModuleRel(screenSceneModuleRelBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
