package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模块库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_module_library")
public class ScreenModuleLibrary implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "screen_module_library_id", type = IdType.AUTO)
    private Long screenModuleLibraryId;

    /**
     * 模块名称
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    @TableField("module_group_type")
    private Long moduleGroupType;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 预置类型：1预置，2不预置
     */
    @TableField("preset_type")
    private Integer presetType;

    /**
     * 父模块库id
     */
    @TableField("parent_screen_module_library_id")
    private Long parentScreenModuleLibraryId;

    /**
     * 排序
     */
    @TableField("library_sort")
    private Integer librarySort;

    /**
     * 是否海报：1是，2否
     */
    @TableField("is_poster")
    private Integer isPoster;

    // /**
    // * 海报模块版式：1横屏，2竖屏
    // */
    // @TableField("library_pattern")
    // private Integer libraryPattern;
    //
    // /**
    // * 海报模块类型：1系统上传，2用户上传
    // */
    // @TableField("poster_source")
    // private Integer posterSource;

    @TableField(exist = false)
    private List<ScreenModuleLibraryMedia> screenModuleLibraryMediaList;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    @TableField("library_source")
    private Integer librarySource;

    /**
     * 模块来源id
     */
    @TableField("third_id")
    private String thirdId;
}
