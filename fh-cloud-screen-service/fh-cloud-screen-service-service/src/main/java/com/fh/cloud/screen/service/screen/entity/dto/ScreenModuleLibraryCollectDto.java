package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 海报收藏表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_module_library_collect")
public class ScreenModuleLibraryCollectDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * FK模块库表id
	 */
	@TableField("screen_module_library_id")
	private Long screenModuleLibraryId;

	/**
	 * 收藏人id
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 收藏人组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 是否是校本海报：1 是，2否
	 */
	@TableField("is_school_poster")
	private Integer isSchoolPoster;

}
