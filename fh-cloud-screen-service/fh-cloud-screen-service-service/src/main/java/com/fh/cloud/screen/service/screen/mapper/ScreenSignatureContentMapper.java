package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureContentDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo;

/**
 * 电子签名表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
public interface ScreenSignatureContentMapper extends BaseMapper<ScreenSignatureContentDto> {

	List<ScreenSignatureContentVo> getScreenSignatureContentListByCondition(ScreenSignatureContentConditionBo condition);

	ScreenSignatureContentVo getScreenSignatureContentByCondition(ScreenSignatureContentConditionBo condition);

}
