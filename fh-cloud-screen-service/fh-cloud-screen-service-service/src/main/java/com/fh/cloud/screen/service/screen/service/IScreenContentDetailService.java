package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentDetail;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo;

import java.util.List;

/**
 * 云屏内容详情表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface IScreenContentDetailService extends IService<ScreenContentDetail> {

    List<ScreenContentDetailVo> getScreenContentDetailListByCondition(ScreenContentDetailListConditionBo condition);

    boolean addScreenContentDetail(ScreenContentDetailBo screenContentDetailBo);

    boolean updateScreenContentDetail(ScreenContentDetailBo screenContentDetailBo);

    ScreenContentDetailVo getDetail(Long screenContentDetailId);

    /**
     * 通过内容id集合查询内容详情集合
     *
     * @param screenContentIds the screen content ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-07 16:31:10
     */
    List<ScreenContentDetailVo> listScreenContentDetailByScreenContentIds(List<Long> screenContentIds);

    /**
     * 批量新增
     *
     * @param screenContentDetailBos the screen content detail bos
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -05-09 14:47:44
     */
    boolean saveOrUpdateScreenContentDetailBatch(List<ScreenContentDetailBo> screenContentDetailBos);

    /**
     * 删除后批量新增
     *
     * @param screenContentId the screen content id
     * @param screenContentDetailBos the screen content detail bos
     * @return boolean
     * <AUTHOR>
     * @date 2022 -05-09 16:02:01
     */
    boolean deleteAndAddScreenContentDetailBatch(Long screenContentId,
        List<ScreenContentDetailBo> screenContentDetailBos);

    /**
     * 根据screenContentId删除信息
     *
     * @param screenContentId the screen content id
     * <AUTHOR>
     * @date 2022 -05-09 16:12:48
     */
    void deleteByScreenContentId(Long screenContentId);

    /**
     * 根据内容id更新第一条详情数据ImgUrl
     * 
     * @param screenContentId
     * @param screenContentMediaUrl
     * @param screenContentMediaId
     */
    void updateFirstImgUrlByScreenContentId(Long screenContentId, String screenContentMediaUrl,
        String screenContentMediaId);
}
