package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecialSpaceGroupRel;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialSpaceGroupRelVo;

import java.util.List;

/**
 * 云屏紧急发布内容-地点组关系表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface IScreenContentSpecialSpaceGroupRelService extends IService<ScreenContentSpecialSpaceGroupRel> {

    List<ScreenContentSpecialSpaceGroupRelVo>
        getScreenContentSpecialSpaceGroupRelListByCondition(ScreenContentSpecialSpaceGroupRelListConditionBo condition);

    boolean
        addScreenContentSpecialSpaceGroupRel(ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo);

    boolean updateScreenContentSpecialSpaceGroupRel(
        ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo);

    ScreenContentSpecialSpaceGroupRelVo getDetail(Long screenContentSpecialSpaceGroupRelId);

    /**
     * 批量新增
     *
     * @param screenContentSpecialId
     * @param spaceGroupIds
     * @return
     */
    boolean addBatch(Long screenContentSpecialId, List<Long> spaceGroupIds);

    /**
     * 批量软删除
     *
     * @param screenContentSpecialId
     * @return
     */
    boolean delBatch(Long screenContentSpecialId);

    /**
     * 根据screenContentSpecialId查询发布的地点组Id
     *
     * @param screenContentSpecialId
     * @return
     */
    List<Long> listSpaceGroupIdsByScreenContentSpecialId(Long screenContentSpecialId);
}
