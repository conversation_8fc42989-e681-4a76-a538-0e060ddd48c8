package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryLikesDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 共话诗词点赞记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
public interface IScreenPoetryLikesService extends IService<ScreenPoetryLikesDto> {

    List<ScreenPoetryLikesVo> getScreenPoetryLikesListByCondition(ScreenPoetryLikesConditionBo condition);

	AjaxResult addScreenPoetryLikes(ScreenPoetryLikesBo screenPoetryLikesBo);

	AjaxResult updateScreenPoetryLikes(ScreenPoetryLikesBo screenPoetryLikesBo);

	ScreenPoetryLikesVo getScreenPoetryLikesByCondition(ScreenPoetryLikesConditionBo condition);

	AjaxResult addScreenPoetryLikesNum(Long screenPoetryContentId);
}

