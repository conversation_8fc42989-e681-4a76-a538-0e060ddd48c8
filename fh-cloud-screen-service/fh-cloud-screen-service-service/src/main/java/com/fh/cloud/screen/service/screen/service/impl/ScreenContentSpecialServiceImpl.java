package com.fh.cloud.screen.service.screen.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.ScreenContentStatusType;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecial;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenContentSpecialMapper;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialService;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialSpaceGroupRelService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.StringKit;
import com.google.common.collect.Lists;
import com.light.core.enums.StatusEnum;

/**
 * 云屏紧急发布内容表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Service
public class ScreenContentSpecialServiceImpl extends ServiceImpl<ScreenContentSpecialMapper, ScreenContentSpecial>
    implements IScreenContentSpecialService {

    @Resource
    private ScreenContentSpecialMapper screenContentSpecialMapper;

    @Lazy
    @Autowired
    private IScreenContentSpecialService screenContentSpecialService;

    @Lazy
    @Autowired
    private IScreenContentSpecialSpaceGroupRelService screenContentSpecialSpaceGroupRelService;

    @Override
    public List<ScreenContentSpecialVo>
        getScreenContentSpecialListByCondition(ScreenContentSpecialListConditionBo condition) {
        List<ScreenContentSpecialVo> screenContentSpecialVos =
            screenContentSpecialMapper.getScreenContentSpecialListByCondition(condition);
        if (CollectionUtils.isNotEmpty(screenContentSpecialVos)) {
            screenContentSpecialVos.stream()
                .filter(
                    screenContentSpecialVo -> StringUtils.isNotBlank(screenContentSpecialVo.getSpaceGroupNameConcat()))
                .forEach(screenContentSpecialVo -> {
                    screenContentSpecialVo.setSpaceGroupNames(
                        StringKit.splitString2List(screenContentSpecialVo.getSpaceGroupNameConcat(), null));
                    resetScreenContentSpecialStatus(screenContentSpecialVo);
                });
        }
        return screenContentSpecialVos;
    }

    @Override
    public Long addScreenContentSpecial(ScreenContentSpecialBo screenContentSpecialBo) {
        ScreenContentSpecial screenContentSpecial = new ScreenContentSpecial();
        BeanUtils.copyProperties(screenContentSpecialBo, screenContentSpecial);
        screenContentSpecial.setIsDelete(StatusEnum.NOTDELETE.getCode());
        save(screenContentSpecial);
        return screenContentSpecial.getScreenContentSpecialId();
    }

    @Override
    public boolean updateScreenContentSpecial(ScreenContentSpecialBo screenContentSpecialBo) {
        ScreenContentSpecial screenContentSpecial = new ScreenContentSpecial();
        BeanUtils.copyProperties(screenContentSpecialBo, screenContentSpecial);
        return updateById(screenContentSpecial);
    }

    @Override
    public ScreenContentSpecialVo getDetail(Long screenContentSpecialId) {
        if (screenContentSpecialId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenContentSpecial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenContentSpecial::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenContentSpecial::getScreenContentSpecialId, screenContentSpecialId);
        ScreenContentSpecial screenContentSpecial = getOne(lqw);
        ScreenContentSpecialVo screenContentSpecialVo = new ScreenContentSpecialVo();
        BeanUtils.copyProperties(screenContentSpecial, screenContentSpecialVo);
        resetScreenContentSpecialStatus(screenContentSpecialVo);
        return screenContentSpecialVo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Long addScreenContentSpecialPublish(ScreenContentSpecialBo screenContentSpecialBo) {
        Long screenContentSpecialId = screenContentSpecialService.addScreenContentSpecial(screenContentSpecialBo);
        if (screenContentSpecialId == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(screenContentSpecialBo.getSpaceGroupIds())) {
            return null;
        }
        screenContentSpecialSpaceGroupRelService.addBatch(screenContentSpecialId,
            screenContentSpecialBo.getSpaceGroupIds());
        return screenContentSpecialId;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Long saveOrUpdateScreenContentSpecialPublish(ScreenContentSpecialBo screenContentSpecialBo) {
        // 修改其他紧急发布为未发布。保存和发布拆开，这个处理去除
        // screenContentSpecialService.cancelPublish(screenContentSpecialBo.getOrganizationId(),
        // screenContentSpecialBo.getCampusId());

        // 发布新的数据
        Long screenContentSpecialId = screenContentSpecialBo.getScreenContentSpecialId();
        if (screenContentSpecialId == null) {
            screenContentSpecialId = screenContentSpecialService.addScreenContentSpecialPublish(screenContentSpecialBo);
        } else {
            screenContentSpecialService.updateScreenContentSpecialPublish(screenContentSpecialBo);
        }
        return screenContentSpecialId;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean updateScreenContentSpecialPublish(ScreenContentSpecialBo screenContentSpecialBo) {
        boolean result = screenContentSpecialService.updateScreenContentSpecial(screenContentSpecialBo);
        if (!result) {
            return false;
        }
        if (CollectionUtils.isEmpty(screenContentSpecialBo.getSpaceGroupIds())) {
            return false;
        }
        // 删除地点组关系
        Long screenContentSpecialId = screenContentSpecialBo.getScreenContentSpecialId();
        screenContentSpecialSpaceGroupRelService.delBatch(screenContentSpecialId);
        // 新增地点组关系
        screenContentSpecialSpaceGroupRelService.addBatch(screenContentSpecialId,
            screenContentSpecialBo.getSpaceGroupIds());
        return true;
    }

    @Override
    public List<ScreenContentSpecialVo> listScreenContentSpecialVosBySpaceGroupId(Long organizationId, Long campusId,
        Long spaceGroupId, Date nowDate) {
        if (organizationId == null || spaceGroupId == null) {
            return Lists.newArrayList();
        }

        return screenContentSpecialMapper.listScreenContentSpecialVosBySpaceGroupId(organizationId, campusId,
            spaceGroupId, nowDate);
    }

    @Override
    public void cancelPublish(Long organizationId, Long campusId) {
        if (organizationId == null) {
            return;
        }

        LambdaUpdateWrapper<ScreenContentSpecial> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ScreenContentSpecial::getOrganizationId, organizationId);
        updateWrapper.eq(ScreenContentSpecial::getScreenContentStatus, ScreenContentStatusType.PUBLISH.getValue());
        if (campusId != null) {
            // 取消本校区已经发布的，和发布到全校的紧急发布。
            updateWrapper.in(ScreenContentSpecial::getCampusId, Lists.newArrayList(ConstantsLong.NUM_0, campusId));
        }
        ScreenContentSpecial screenContentSpecial = new ScreenContentSpecial();
        screenContentSpecial.setScreenContentStatus(ScreenContentStatusType.NOT_PUBLISH.getValue());
        update(screenContentSpecial, updateWrapper);
    }

    @Override
    public ScreenContentSpecialVo getNowPublish(Long organizationId, Long campusId) {
        if (organizationId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenContentSpecial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenContentSpecial::getOrganizationId, organizationId);
        queryWrapper.eq(ScreenContentSpecial::getScreenContentStatus, ScreenContentStatusType.PUBLISH.getValue());
        queryWrapper.eq(ScreenContentSpecial::getIsDelete, StatusEnum.NOTDELETE.getCode());
        queryWrapper.ge(ScreenContentSpecial::getEndTime, new Date());
        if (campusId != null) {
            queryWrapper.in(ScreenContentSpecial::getCampusId, campusId);
        }
        queryWrapper.last("limit 1");
        ScreenContentSpecial screenContentSpecial = getOne(queryWrapper);
        if (null == screenContentSpecial) {
            return null;
        }
        ScreenContentSpecialVo screenContentSpecialVo = new ScreenContentSpecialVo();
        BeanUtils.copyProperties(screenContentSpecial, screenContentSpecialVo);
        return screenContentSpecialVo;
    }

    @Override
    public void updateImgUrlByScreenContentSpecialId(Long screenContentSpecialId, String screenContentMediaUrl,
        String screenContentMediaId) {
        LambdaQueryWrapper<ScreenContentSpecial> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenContentSpecial::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenContentSpecial::getScreenContentSpecialId, screenContentSpecialId);
        lqw.last("limit 1");
        ScreenContentSpecial screenContentSpecial = getOne(lqw);
        if (screenContentSpecial == null) {
            return;
        }
        screenContentSpecial.setScreenContentMediaUrl(screenContentMediaUrl);
        screenContentSpecial.setScreenContentMediaId(screenContentMediaId);
        updateById(screenContentSpecial);
    }

    /**
     * 重设screenContentSpecialVo的重设screenContentStatus
     * 
     * @param screenContentSpecialVo
     */
    private void resetScreenContentSpecialStatus(ScreenContentSpecialVo screenContentSpecialVo) {
        if (screenContentSpecialVo.getEndTime() != null) {
            if (!DateKit.ifMoreThenDate(screenContentSpecialVo.getEndTime())
                && screenContentSpecialVo.getScreenContentStatus().equals(ScreenContentStatusType.PUBLISH.getValue())) {
                screenContentSpecialVo.setScreenContentStatus(ScreenContentStatusType.ON_SHOW.getValue());
            }
            if (DateKit.ifMoreThenDate(screenContentSpecialVo.getEndTime())) {
                screenContentSpecialVo.setScreenContentStatus(ScreenContentStatusType.OUT_DATE.getValue());
            }
        }
    }

}