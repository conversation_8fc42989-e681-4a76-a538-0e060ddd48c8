package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecialSpaceGroupRel;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialSpaceGroupRelVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenContentSpecialSpaceGroupRelMapper;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialSpaceGroupRelService;
import com.fh.cloud.screen.service.utils.ListKit;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云屏紧急发布内容-地点组关系表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Service
public class ScreenContentSpecialSpaceGroupRelServiceImpl
    extends ServiceImpl<ScreenContentSpecialSpaceGroupRelMapper, ScreenContentSpecialSpaceGroupRel>
    implements IScreenContentSpecialSpaceGroupRelService {

    @Resource
    private ScreenContentSpecialSpaceGroupRelMapper screenContentSpecialSpaceGroupRelMapper;

    @Lazy
    @Autowired
    private IScreenContentSpecialSpaceGroupRelService screenContentSpecialSpaceGroupRelService;

    @Override
    public List<ScreenContentSpecialSpaceGroupRelVo> getScreenContentSpecialSpaceGroupRelListByCondition(
        ScreenContentSpecialSpaceGroupRelListConditionBo condition) {
        return screenContentSpecialSpaceGroupRelMapper.getScreenContentSpecialSpaceGroupRelListByCondition(condition);
    }

    @Override
    public boolean
        addScreenContentSpecialSpaceGroupRel(ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo) {
        ScreenContentSpecialSpaceGroupRel screenContentSpecialSpaceGroupRel = new ScreenContentSpecialSpaceGroupRel();
        BeanUtils.copyProperties(screenContentSpecialSpaceGroupRelBo, screenContentSpecialSpaceGroupRel);
        screenContentSpecialSpaceGroupRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(screenContentSpecialSpaceGroupRel);
    }

    @Override
    public boolean updateScreenContentSpecialSpaceGroupRel(
        ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo) {
        ScreenContentSpecialSpaceGroupRel screenContentSpecialSpaceGroupRel = new ScreenContentSpecialSpaceGroupRel();
        BeanUtils.copyProperties(screenContentSpecialSpaceGroupRelBo, screenContentSpecialSpaceGroupRel);
        return updateById(screenContentSpecialSpaceGroupRel);
    }

    @Override
    public ScreenContentSpecialSpaceGroupRelVo getDetail(Long screenContentSpecialSpaceGroupRelId) {
        if (screenContentSpecialSpaceGroupRelId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenContentSpecialSpaceGroupRel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenContentSpecialSpaceGroupRel::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenContentSpecialSpaceGroupRel::getId, screenContentSpecialSpaceGroupRelId);
        ScreenContentSpecialSpaceGroupRel screenContentSpecialSpaceGroupRel = getOne(lqw);
        ScreenContentSpecialSpaceGroupRelVo screenContentSpecialSpaceGroupRelVo =
            new ScreenContentSpecialSpaceGroupRelVo();
        BeanUtils.copyProperties(screenContentSpecialSpaceGroupRel, screenContentSpecialSpaceGroupRelVo);
        return screenContentSpecialSpaceGroupRelVo;
    }

    @Override
    public boolean addBatch(Long screenContentSpecialId, List<Long> spaceGroupIds) {
        if (CollectionUtils.isEmpty(spaceGroupIds) || screenContentSpecialId == null) {
            return true;
        }
        List<ScreenContentSpecialSpaceGroupRel> screenContentSpecialSpaceGroupRels =
            spaceGroupIds.stream().map(spaceGroupId -> {
                ScreenContentSpecialSpaceGroupRel screenContentSpecialSpaceGroupRel =
                    new ScreenContentSpecialSpaceGroupRel();
                screenContentSpecialSpaceGroupRel.setScreenContentSpecialId(screenContentSpecialId);
                screenContentSpecialSpaceGroupRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
                screenContentSpecialSpaceGroupRel.setSpaceGroupId(spaceGroupId);
                return screenContentSpecialSpaceGroupRel;
            }).collect(Collectors.toList());
        return saveBatch(screenContentSpecialSpaceGroupRels);
    }

    @Override
    public boolean delBatch(Long screenContentSpecialId) {
        LambdaUpdateWrapper<ScreenContentSpecialSpaceGroupRel> luw = new LambdaUpdateWrapper();
        luw.eq(ScreenContentSpecialSpaceGroupRel::getScreenContentSpecialId, screenContentSpecialId);
        luw.set(ScreenContentSpecialSpaceGroupRel::getIsDelete, StatusEnum.ISDELETE.getCode());
        return update(luw);
    }

    @Override
    public List<Long> listSpaceGroupIdsByScreenContentSpecialId(Long screenContentSpecialId) {
        if (screenContentSpecialId == null) {
            return Lists.newArrayList();
        }
        ScreenContentSpecialSpaceGroupRelListConditionBo condition =
            new ScreenContentSpecialSpaceGroupRelListConditionBo();
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setScreenContentSpecialId(screenContentSpecialId);
        List<ScreenContentSpecialSpaceGroupRelVo> screenContentSpecialSpaceGroupRelVos =
            screenContentSpecialSpaceGroupRelService.getScreenContentSpecialSpaceGroupRelListByCondition(condition);
        if (CollectionUtils.isEmpty(screenContentSpecialSpaceGroupRelVos)) {
            return Lists.newArrayList();
        }
        List<Long> spaceGroupIds = screenContentSpecialSpaceGroupRelVos.stream()
            .map(ScreenContentSpecialSpaceGroupRelVo::getSpaceGroupId).collect(Collectors.toList());
        ListKit.removeDuplicate(spaceGroupIds);
        return spaceGroupIds;
    }
}