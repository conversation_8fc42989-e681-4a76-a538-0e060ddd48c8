package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ScreenModuleConstants;
import com.fh.cloud.screen.service.enums.AuditType;
import com.fh.cloud.screen.service.enums.ReleaseType;
import com.fh.cloud.screen.service.enums.ScreenModuleLibrarySource;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.fh.cloud.screen.service.label.mapper.LabelLibraryRelMapper;
import com.fh.cloud.screen.service.label.mapper.LabelMapper;
import com.fh.cloud.screen.service.label.service.ILabelLibraryRelService;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.*;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo;
import com.fh.cloud.screen.service.screen.mapper.LabelLibraryAuditRelMapper;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMediaMapper;
import com.fh.cloud.screen.service.screen.service.*;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.utils.ListKit;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryAuditMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 模块库审核表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
@Service
public class ScreenModuleLibraryAuditServiceImpl
    extends ServiceImpl<ScreenModuleLibraryAuditMapper, ScreenModuleLibraryAuditDto>
    implements IScreenModuleLibraryAuditService {

    @Resource
    private ScreenModuleLibraryAuditMapper screenModuleLibraryAuditMapper;
    @Lazy
    @Resource
    private BaseDataService baseDataService;
    @Lazy
    @Resource
    private IScreenModuleLibraryMediaAuditService screenModuleLibraryMediaAuditService;
    @Lazy
    @Resource
    private ILabelLibraryAuditRelService labelLibraryAuditRelService;
    @Resource
    private LabelMapper labelMapper;
    @Lazy
    @Resource
    private IScreenModuleLibraryService screenModuleLibraryService;
    @Lazy
    @Resource
    private ILabelLibraryRelService labelLibraryRelService;
    @Lazy
    @Resource
    private IScreenModuleLibraryMediaService screenModuleLibraryMediaService;
    @Lazy
    @Resource
    private IScreenModuleLibraryAuditService screenModuleLibraryAuditService;

    @Override
    public List<ScreenModuleLibraryAuditVo>
        getScreenModuleLibraryAuditListByCondition(ScreenModuleLibraryAuditConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenModuleLibraryAuditMapper.getScreenModuleLibraryAuditListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenModuleLibraryAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        ScreenModuleLibraryAuditDto screenModuleLibraryAudit = new ScreenModuleLibraryAuditDto();
        BeanUtils.copyProperties(screenModuleLibraryAuditBo, screenModuleLibraryAudit);
        screenModuleLibraryAudit.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenModuleLibraryAudit)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenModuleLibraryAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        ScreenModuleLibraryAuditDto screenModuleLibraryAudit = new ScreenModuleLibraryAuditDto();
        BeanUtils.copyProperties(screenModuleLibraryAuditBo, screenModuleLibraryAudit);
        if (updateById(screenModuleLibraryAudit)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenModuleLibraryAuditVo
        getScreenModuleLibraryAuditByCondition(ScreenModuleLibraryAuditConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return screenModuleLibraryAuditMapper.getScreenModuleLibraryAuditByCondition(condition);
    }

    @Override
    public List<ScreenModuleLibraryAuditVo> getPosterAuditList(ScreenModuleLibraryAuditConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<ScreenModuleLibraryAuditVo> list =
            screenModuleLibraryAuditMapper.getScreenModuleLibraryAuditWithLabelNameListByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        // 班级信息封装返回
        List<Long> classesIds = list.stream().filter(x -> x.getClassesId() != null)
            .map(ScreenModuleLibraryAuditVo::getClassesId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(classesIds)) {
            ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
            clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
            clazzConditionBoExt.setQueryType(ConstantsInteger.APP_QUERY);
            clazzConditionBoExt.setIds(classesIds);
            List<ClazzInfoVo> clazzInfoVoList =
                (List<ClazzInfoVo>)baseDataService.getClazzInfoVoList(clazzConditionBoExt).get("list");
            Map<Long, String> clazzInfoMap =
                clazzInfoVoList.stream().collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getClassesNameShow));
            list.stream().filter(x -> x.getClassesId() != null).forEach(x -> {
                x.setClassesName(clazzInfoMap.get(x.getClassesId()));
            });
        }
        // 创建人姓名信息封装返回
        List<String> createUserOids =
            list.stream().map(ScreenModuleLibraryAuditVo::getCreateBy).collect(Collectors.toList());
        Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(createUserOids);
        list.forEach(s -> {
            if (userNameMap.containsKey(s.getCreateBy())) {
                s.setCreateUserName(userNameMap.get(s.getCreateBy()));
            }
        });
        // 主题的图封装返回
        List<Long> screenModuleLibraryAuditIds =
            list.stream().map(ScreenModuleLibraryAuditVo::getScreenModuleLibraryAuditId).collect(Collectors.toList());
        ScreenModuleLibraryMediaAuditConditionBo mediaAuditConditionBo = new ScreenModuleLibraryMediaAuditConditionBo();
        mediaAuditConditionBo.setScreenModuleLibraryAuditIds(screenModuleLibraryAuditIds);
        mediaAuditConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleLibraryMediaAuditVo> mediaAuditVos =
            screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditListByCondition(mediaAuditConditionBo);
        if (CollectionUtils.isNotEmpty(mediaAuditVos)) {
            Map<Long, List<ScreenModuleLibraryMediaAuditVo>> mediaAuditMap = mediaAuditVos.stream()
                .collect(Collectors.groupingBy(ScreenModuleLibraryMediaAuditVo::getScreenModuleLibraryAuditId));
            list.forEach(s -> {
                if (mediaAuditMap.containsKey(s.getScreenModuleLibraryAuditId())) {
                    List<ScreenModuleLibraryMediaAuditVo> mediaAuditList = mediaAuditMap.get(s.getScreenModuleLibraryAuditId());
                    s.setMediaAuditList(mediaAuditList);
                }
            });
        }
        return list;
    }

    @Override
    public AjaxResult addPosterAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        ScreenModuleLibraryAuditDto screenModuleLibraryAudit = new ScreenModuleLibraryAuditDto();
        BeanUtils.copyProperties(screenModuleLibraryAuditBo, screenModuleLibraryAudit);
        // 海报审核 设置parentModuleLibraryId为海报父模块id
        screenModuleLibraryAudit.setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
        screenModuleLibraryAudit.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenModuleLibraryAudit)) {
            // 保存分类
            if (CollectionUtils.isNotEmpty(screenModuleLibraryAuditBo.getLabelBos())) {
                List<LabelLibraryAuditRelDto> labelLibraryAuditRelDtos =
                    screenModuleLibraryAuditBo.getLabelBos().stream().map(l -> {
                        LabelLibraryAuditRelDto labelLibraryAuditRelDto = new LabelLibraryAuditRelDto();
                        labelLibraryAuditRelDto.setLabelId(l.getLabelId());
                        labelLibraryAuditRelDto
                            .setScreenModuleLibraryAuditId(screenModuleLibraryAudit.getScreenModuleLibraryAuditId());
                        return labelLibraryAuditRelDto;
                    }).collect(Collectors.toList());
                labelLibraryAuditRelService.saveBatch(labelLibraryAuditRelDtos);
            }
            // 保存资源
            if (CollectionUtils.isNotEmpty(screenModuleLibraryAuditBo.getMediaAuditList())) {
                List<ScreenModuleLibraryMediaAuditDto> mediaAuditDtos = new ArrayList<>();
                for (ScreenModuleLibraryMediaAuditBo mediaAuditBo : screenModuleLibraryAuditBo.getMediaAuditList()) {
                    ScreenModuleLibraryMediaAuditDto mediaAuditDto = new ScreenModuleLibraryMediaAuditDto();
                    BeanUtils.copyProperties(mediaAuditBo, mediaAuditDto);
                    mediaAuditDto
                        .setScreenModuleLibraryAuditId(screenModuleLibraryAudit.getScreenModuleLibraryAuditId());
                    mediaAuditDto.setDevicePattern(screenModuleLibraryAudit.getDevicePattern());
                    mediaAuditDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    mediaAuditDtos.add(mediaAuditDto);
                }
                screenModuleLibraryMediaAuditService.saveBatch(mediaAuditDtos);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updatePosterAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        ScreenModuleLibraryAuditConditionBo condition = new ScreenModuleLibraryAuditConditionBo();
        condition.setScreenModuleLibraryAuditId(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ScreenModuleLibraryAuditVo vo = baseMapper.getScreenModuleLibraryAuditByCondition(condition);
        if (vo == null) {
            return AjaxResult.fail("数据不存在");
        }
        ScreenModuleLibraryAuditDto dto = new ScreenModuleLibraryAuditDto();
        BeanUtils.copyProperties(screenModuleLibraryAuditBo, dto);
        dto.setReleaseType(ReleaseType.TO_RELEASE.getValue());
        if (updateById(dto)) {
            // 修改分类
            labelLibraryAuditRelService.updateLabelList(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId(),
                screenModuleLibraryAuditBo.getLabelBos());

            // 修改资源
            screenModuleLibraryMediaAuditService.updateMediaAuditList(
                screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId(),
                screenModuleLibraryAuditBo.getMediaAuditList());
            // 编辑审核通过的数据成功，移除已发布的数据。目前前端已加了限制，不允许编辑已发布的数据，因此下面逻辑走不到了
            if (AuditType.AUDIT_PASS.getValue() == vo.getAuditType()
                && ReleaseType.ALREADY_RELEASE.getValue() == vo.getReleaseType()
                && vo.getScreenModuleLibraryId() != null) {
                List<Long> libraryIds = new ArrayList<>();
                libraryIds.add(vo.getScreenModuleLibraryId());
                screenModuleLibraryService.deleteScreenModuleLibraryAndLibraryMediaByLibraryIds(libraryIds);
            }
            return AjaxResult.success("编辑成功");
        }
        return AjaxResult.fail("编辑失败");
    }

    @Override
    public AjaxResult<ScreenModuleLibraryAuditVo> getPosterAuditDetail(Long screenModuleLibraryAuditId) {
        ScreenModuleLibraryAuditConditionBo condition = new ScreenModuleLibraryAuditConditionBo();
        condition.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ScreenModuleLibraryAuditVo vo = baseMapper.getScreenModuleLibraryAuditWithLabelNameByCondition(condition);
        if (vo == null) {
            return AjaxResult.fail("数据不存在");
        }
        // 查询分类
        List<LabelVo> labelVos = labelLibraryAuditRelService.getLabelListByLibraryAuditId(screenModuleLibraryAuditId);
        if (CollectionUtils.isNotEmpty(labelVos)) {
            vo.setLabelList(labelVos);
        }
        // 查询资源
        ScreenModuleLibraryMediaAuditConditionBo mediaAuditConditionBo = new ScreenModuleLibraryMediaAuditConditionBo();
        mediaAuditConditionBo.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
        mediaAuditConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleLibraryMediaAuditVo> mediaAuditVos =
            screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditListByCondition(mediaAuditConditionBo);
        if (CollectionUtils.isNotEmpty(mediaAuditVos)) {
            vo.setMediaAuditList(mediaAuditVos);
        }
        List<String> createUserOids = Lists.newArrayList(vo.getCreateBy());
        Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(createUserOids);
        if (userNameMap != null && userNameMap.containsKey(vo.getCreateBy())) {
            vo.setCreateUserName(userNameMap.get(vo.getCreateBy()));
        }
        return AjaxResult.success(vo);
    }

    @Override
    public AjaxResult audit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        ScreenModuleLibraryAuditDto dto = getById(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId());
        if (dto == null) {
            return AjaxResult.fail("数据不存在");
        }
        ScreenModuleLibraryAuditDto entity = new ScreenModuleLibraryAuditDto();
        entity.setScreenModuleLibraryAuditId(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId());
        entity.setAuditType(screenModuleLibraryAuditBo.getAuditType());
        entity.setReason(screenModuleLibraryAuditBo.getReason());
        entity.setAuditUser(screenModuleLibraryAuditBo.getAuditUser());
        entity.setAuditTime(new Date());
        if (updateById(entity)) {
            return AjaxResult.success("审核成功");
        }
        return AjaxResult.fail("审核失败");
    }

    @Override
    public AjaxResult releasePoster(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        AjaxResult<ScreenModuleLibraryAuditVo> voAjaxResult =
            getPosterAuditDetail(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId());
        if (voAjaxResult.isFail() || voAjaxResult.getData() == null) {
            return AjaxResult.fail("数据不存在");
        }
        ScreenModuleLibraryAuditVo vo = voAjaxResult.getData();
        if (AuditType.AUDIT_PASS.getValue() != vo.getAuditType()
            && ReleaseType.ALREADY_RELEASE.getValue() == screenModuleLibraryAuditBo.getReleaseType()) {
            return AjaxResult.fail("当前数据未审核通过，无法发布");
        }

        ScreenModuleLibraryAuditDto dto = new ScreenModuleLibraryAuditDto();
        dto.setScreenModuleLibraryAuditId(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId());
        dto.setReleaseType(screenModuleLibraryAuditBo.getReleaseType());
        if (updateById(dto)) {
            // 发布，将数据保存至screen_module_library
            if (ReleaseType.ALREADY_RELEASE.getValue() == screenModuleLibraryAuditBo.getReleaseType()) {
                // 查询资源
                ScreenModuleLibraryMediaAuditConditionBo condition = new ScreenModuleLibraryMediaAuditConditionBo();
                condition.setScreenModuleLibraryAuditId(dto.getScreenModuleLibraryAuditId());
                condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
                List<ScreenModuleLibraryMediaAuditVo> mediaAuditVos =
                    screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditListByCondition(condition);

                // 保存screen_module_library
                ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                BeanUtils.copyProperties(vo, screenModuleLibrary);
                // 发布海报模块，设置parentModuleLibraryId为海报模块id
                screenModuleLibrary
                    .setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
                screenModuleLibrary.setLibrarySource(ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue());
                screenModuleLibrary.setThirdId(vo.getScreenModuleLibraryAuditId().toString());
                screenModuleLibraryService.save(screenModuleLibrary);
                // 保存label_library_rel
                if (CollectionUtils.isNotEmpty(vo.getLabelList())) {
                    List<LabelLibraryRelDto> labelLibraryRelDtos = vo.getLabelList().stream().map(l -> {
                        LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
                        labelLibraryRelDto.setLabelId(l.getLabelId());
                        labelLibraryRelDto.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                        return labelLibraryRelDto;
                    }).collect(Collectors.toList());
                    labelLibraryRelService.saveBatch(labelLibraryRelDtos);
                }
                // 保存screen_module_library_media
                if (CollectionUtils.isNotEmpty(mediaAuditVos)) {
                    List<ScreenModuleLibraryMedia> mediaList = mediaAuditVos.stream().map(mediaAuditVo -> {
                        ScreenModuleLibraryMedia media = new ScreenModuleLibraryMedia();
                        BeanUtils.copyProperties(mediaAuditVo, media);
                        media.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                        media.setMediaSource(ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue());
                        media.setThirdId(mediaAuditVo.getScreenModuleLibraryMediaAuditId().toString());
                        return media;
                    }).collect(Collectors.toList());
                    screenModuleLibraryMediaService.saveBatch(mediaList);
                }
                // 审核记录保存screenModuleLibraryId
                ScreenModuleLibraryAuditDto screenModuleLibraryAuditDto = new ScreenModuleLibraryAuditDto();
                screenModuleLibraryAuditDto
                    .setScreenModuleLibraryAuditId(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId());
                screenModuleLibraryAuditDto.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                updateById(screenModuleLibraryAuditDto);
            }
            // 取消发布，将screen_module_library数据移除
            if (ReleaseType.TO_RELEASE.getValue() == screenModuleLibraryAuditBo.getReleaseType()) {
                List<ScreenModuleLibrary> list =
                    screenModuleLibraryService.list(new LambdaQueryWrapper<ScreenModuleLibrary>()
                        .eq(ScreenModuleLibrary::getLibrarySource,
                            ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue())
                        .eq(ScreenModuleLibrary::getThirdId, vo.getScreenModuleLibraryAuditId())
                        .eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode()));
                if (CollectionUtils.isNotEmpty(list)) {
                    List<Long> libraryIds =
                        list.stream().map(ScreenModuleLibrary::getScreenModuleLibraryId).collect(Collectors.toList());
                    screenModuleLibraryService.deleteScreenModuleLibraryAndLibraryMediaByLibraryIds(libraryIds);
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult updatePosterAuditLabel(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        // 修改分类，将一批海报审核数据的分类更新一下
        boolean result = labelLibraryAuditRelService.updateLabelList(
            screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds(), screenModuleLibraryAuditBo.getLabelBos());
        if (result) {
            return AjaxResult.success("编辑成功");
        }
        return AjaxResult.success("编辑失败");
    }

    @Override
    public void deleteBatch(List<Long> screenModuleLibraryAuditIds) {
        if (CollectionUtils.isEmpty(screenModuleLibraryAuditIds)) {
            return;
        }
        LambdaUpdateWrapper<ScreenModuleLibraryAuditDto> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ScreenModuleLibraryAuditDto::getScreenModuleLibraryAuditId, screenModuleLibraryAuditIds);
        wrapper.set(ScreenModuleLibraryAuditDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        wrapper.set(ScreenModuleLibraryAuditDto::getUpdateTime, new Date());
        update(wrapper);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult releasePosterBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        // 待发布的数据合法性校验
        ScreenModuleLibraryAuditConditionBo screenModuleLibraryAuditConditionBo =
            new ScreenModuleLibraryAuditConditionBo();
        screenModuleLibraryAuditConditionBo
            .setScreenModuleLibraryAuditIds(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds());
        List<ScreenModuleLibraryAuditVo> screenModuleLibraryAuditVos = screenModuleLibraryAuditService
            .getScreenModuleLibraryAuditListByCondition(screenModuleLibraryAuditConditionBo);
        if (CollectionUtils.isEmpty(screenModuleLibraryAuditVos)) {
            return AjaxResult.fail("数据不存在");
        }
        List<Long> screenModuleLibraryAuditIds = screenModuleLibraryAuditVos.stream()
            .map(ScreenModuleLibraryAuditVo::getScreenModuleLibraryAuditId).collect(Collectors.toList());
        boolean hasNotAuditPassToRelease = screenModuleLibraryAuditVos.stream().anyMatch(screenModuleLibraryAuditVo -> {
            if (AuditType.AUDIT_PASS.getValue() != screenModuleLibraryAuditVo.getAuditType()
                && ReleaseType.ALREADY_RELEASE.getValue() == screenModuleLibraryAuditBo.getReleaseType()) {
                return true;
            }
            return false;
        });
        if (hasNotAuditPassToRelease) {
            return AjaxResult.fail("存在未审核通过的数据，无法发布");
        }

        // 更新审核表的发布状态
        List<ScreenModuleLibraryAuditDto> screenModuleLibraryAuditDtos = Lists.newArrayList();
        screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds().forEach(screenModuleLibraryAuditId -> {
            ScreenModuleLibraryAuditDto screenModuleLibraryAuditDto = new ScreenModuleLibraryAuditDto();
            screenModuleLibraryAuditDto.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
            screenModuleLibraryAuditDto.setReleaseType(screenModuleLibraryAuditBo.getReleaseType());
            screenModuleLibraryAuditDtos.add(screenModuleLibraryAuditDto);
        });
        boolean updateResult = updateBatchById(screenModuleLibraryAuditDtos);

        if (updateResult && screenModuleLibraryAuditBo.getReleaseType() != null) {
            // 发布到海报库，将数据保存至screen_module_library
            if (screenModuleLibraryAuditBo.getReleaseType().equals(ReleaseType.ALREADY_RELEASE.getValue())) {
                // 审核表id-海报id的映射
                Map<Long, Long> screenModuleLibraryAuditIdMap = Maps.newHashMap();
                // 批量保存screen_module_library
                List<ScreenModuleLibrary> screenModuleLibraries = Lists.newArrayList();
                screenModuleLibraryAuditVos.stream().map(screenModuleLibraryAuditVo -> {
                    ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                    BeanUtils.copyProperties(screenModuleLibraryAuditVo, screenModuleLibrary);
                    // 发布海报模块，设置parentModuleLibraryId为海报模块id
                    screenModuleLibrary
                        .setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
                    screenModuleLibrary.setLibrarySource(ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue());
                    screenModuleLibrary
                        .setThirdId(screenModuleLibraryAuditVo.getScreenModuleLibraryAuditId().toString());
                    screenModuleLibraries.add(screenModuleLibrary);
                    return screenModuleLibrary;
                }).collect(Collectors.toList());
                screenModuleLibraryService.saveBatch(screenModuleLibraries);
                screenModuleLibraries.forEach(screenModuleLibrary -> {
                    if (StringUtils.isBlank(screenModuleLibrary.getThirdId())
                        || screenModuleLibrary.getScreenModuleLibraryId() == null) {
                        return;
                    }
                    screenModuleLibraryAuditIdMap.put(Long.valueOf(screenModuleLibrary.getThirdId()),
                        screenModuleLibrary.getScreenModuleLibraryId());
                });

                // 查询出海报审核这边的海报和label的关系并保存label-library关系
                LabelLibraryAuditRelConditionBo labelLibraryAuditRelConditionBo = new LabelLibraryAuditRelConditionBo();
                labelLibraryAuditRelConditionBo.setScreenModuleLibraryAuditIds(screenModuleLibraryAuditIds);
                List<LabelLibraryAuditRelVo> labelLibraryAuditRelVos =
                    labelLibraryAuditRelService.getLabelLibraryAuditRelListByCondition(labelLibraryAuditRelConditionBo);
                if (CollectionUtils.isNotEmpty(labelLibraryAuditRelVos)) {
                    List<LabelLibraryRelDto> labelLibraryRelDtos = labelLibraryAuditRelVos.stream()
                        .filter(labelLibraryAuditRelVo -> screenModuleLibraryAuditIdMap
                            .containsKey(labelLibraryAuditRelVo.getScreenModuleLibraryAuditId())
                            && screenModuleLibraryAuditIdMap
                                .get(labelLibraryAuditRelVo.getScreenModuleLibraryAuditId()) != null)
                        .map(labelLibraryAuditRelVo -> {
                            LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
                            labelLibraryRelDto.setLabelId(labelLibraryAuditRelVo.getLabelId());
                            labelLibraryRelDto.setScreenModuleLibraryId(screenModuleLibraryAuditIdMap
                                .get(labelLibraryAuditRelVo.getScreenModuleLibraryAuditId()));
                            return labelLibraryRelDto;
                        }).collect(Collectors.toList());
                    labelLibraryRelService.saveBatch(labelLibraryRelDtos);
                }

                // 查询资源,保存screen_module_library_media
                ScreenModuleLibraryMediaAuditConditionBo condition = new ScreenModuleLibraryMediaAuditConditionBo();
                condition.setScreenModuleLibraryAuditIds(screenModuleLibraryAuditIds);
                List<ScreenModuleLibraryMediaAuditVo> screenModuleLibraryMediaAuditVos =
                    screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditListByCondition(condition);
                if (CollectionUtils.isNotEmpty(screenModuleLibraryMediaAuditVos)) {
                    List<ScreenModuleLibraryMedia> mediaList = screenModuleLibraryMediaAuditVos.stream()
                        .filter(screenModuleLibraryMediaAuditVo -> screenModuleLibraryAuditIdMap
                            .containsKey(screenModuleLibraryMediaAuditVo.getScreenModuleLibraryAuditId())
                            && screenModuleLibraryAuditIdMap
                                .get(screenModuleLibraryMediaAuditVo.getScreenModuleLibraryAuditId()) != null)
                        .map(screenModuleLibraryMediaAuditVo -> {
                            ScreenModuleLibraryMedia media = new ScreenModuleLibraryMedia();
                            BeanUtils.copyProperties(screenModuleLibraryMediaAuditVo, media);
                            media.setScreenModuleLibraryId(screenModuleLibraryAuditIdMap
                                .get(screenModuleLibraryMediaAuditVo.getScreenModuleLibraryAuditId()));
                            media.setMediaSource(ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue());
                            media.setThirdId(
                                screenModuleLibraryMediaAuditVo.getScreenModuleLibraryMediaAuditId().toString());
                            return media;
                        }).collect(Collectors.toList());
                    screenModuleLibraryMediaService.saveBatch(mediaList);
                }

                // 审核记录更新screenModuleLibraryId（双向记录）
                List<ScreenModuleLibraryAuditDto> screenModuleLibraryAuditDtosToUpdateLibraryId =
                    screenModuleLibraryAuditIdMap.entrySet().stream().map(entry -> {
                        ScreenModuleLibraryAuditDto screenModuleLibraryAuditDto = new ScreenModuleLibraryAuditDto();
                        screenModuleLibraryAuditDto.setScreenModuleLibraryAuditId(entry.getKey());
                        screenModuleLibraryAuditDto.setScreenModuleLibraryId(entry.getValue());
                        return screenModuleLibraryAuditDto;
                    }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(screenModuleLibraryAuditDtosToUpdateLibraryId)) {
                    updateBatchById(screenModuleLibraryAuditDtosToUpdateLibraryId);
                }
            }

            // 取消发布，将screen_module_library数据移除
            if (screenModuleLibraryAuditBo.getReleaseType().equals(ReleaseType.TO_RELEASE.getValue())) {
                List<ScreenModuleLibrary> screenModuleLibraries =
                    screenModuleLibraryService.list(new LambdaQueryWrapper<ScreenModuleLibrary>()
                        .eq(ScreenModuleLibrary::getLibrarySource,
                            ScreenModuleLibrarySource.RESOURCE_RELEASE.getValue())
                        .in(ScreenModuleLibrary::getThirdId,
                            ListKit.convertLongList2String(screenModuleLibraryAuditIds))
                        .eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode()));
                if (CollectionUtils.isNotEmpty(screenModuleLibraries)) {
                    List<Long> libraryIds = screenModuleLibraries.stream()
                        .map(ScreenModuleLibrary::getScreenModuleLibraryId).collect(Collectors.toList());
                    screenModuleLibraryService.deleteScreenModuleLibraryAndLibraryMediaByLibraryIds(libraryIds);
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }
}