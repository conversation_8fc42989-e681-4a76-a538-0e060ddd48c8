package com.fh.cloud.screen.service.screen.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.common.entity.dto.DictionaryData;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;
import com.fh.cloud.screen.service.common.service.IScreenDictionaryDataService;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.consts.ScreenModuleConstants;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceLabelRelService;
import com.fh.cloud.screen.service.enums.DictionaryType;
import com.fh.cloud.screen.service.enums.FestivalEnum;
import com.fh.cloud.screen.service.enums.PosterEnums;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;
import com.fh.cloud.screen.service.festival.service.IFestivalService;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo;
import com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo;
import com.fh.cloud.screen.service.label.service.ILabelFestivalRelService;
import com.fh.cloud.screen.service.label.service.ILabelLibraryRelService;
import com.fh.cloud.screen.service.screen.entity.bo.*;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryGroupVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryCollectMapper;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMediaMapper;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.fh.cloud.screen.service.utils.StringKit;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.base.attachment.entity.bo.MultipartFormData;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 云屏模块库媒体资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
@Service
public class ScreenModuleLibraryMediaServiceImpl extends
    ServiceImpl<ScreenModuleLibraryMediaMapper, ScreenModuleLibraryMedia> implements IScreenModuleLibraryMediaService {

    @Resource
    private ScreenModuleLibraryMediaMapper screenModuleLibraryMediaMapper;
    @Resource
    private IScreenModuleLibraryService screenModuleLibraryService;
    @Resource
    private IScreenDictionaryDataService screenDictionaryDataService;
    @Autowired
    private BaseDataService baseDataService;
    @Resource
    private ScreenModuleLibraryCollectMapper screenModuleLibraryCollectMapper;
    @Resource
    private IFestivalService festivalService;
    @Resource
    private ILabelLibraryRelService labelLibraryRelService;
    @Resource
    private ILabelFestivalRelService labelFestivalRelService;
    @Resource
    private IShowDeviceLabelRelService showDeviceLabelRelService;

    @Value("${poster.path:/data/jcxx/resource/poster}")
    private String POSTER_PATH;

    @Override
    public List<ScreenModuleLibraryMediaVo>
        getScreenModuleLibraryMediaListByCondition(ScreenModuleLibraryMediaBo condition) {
        return screenModuleLibraryMediaMapper.getScreenModuleLibraryMediaListByCondition(condition);
    }

    @Deprecated
    @Override
    public AjaxResult getThemePosterList(Long organizationId, Integer selectType, Integer pattern) {
        Map<String, List<LinkedHashMap>> resultMap = new LinkedHashMap<>();
        // 当前用户的收藏列表
        List<ScreenModuleLibraryCollectVo> collectVos = getMyCollectLibraryVos(organizationId);
        // 收藏列表转换Map以LibraryId作为key标识
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectVosMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(collectVos)) {
            collectVosMap = collectVos.stream()
                .collect(Collectors.groupingBy(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId));
        }

        // 热门主题海报
        if (ConstantsInteger.QUERY_HOT.equals(selectType)) {
            // 热门收藏前十模块
            List<Long> hotLibraryIds =
                screenModuleLibraryCollectMapper.getHotPosterModuleLibraryIds(ConstantsInteger.HOT_LIMIT);
            // 根据模块获取模块海报图片
            ScreenModuleLibraryMediaBo libraryMediaBo = new ScreenModuleLibraryMediaBo();
            libraryMediaBo.setScreenModuleLibraryIds(hotLibraryIds);
            libraryMediaBo.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
            List<ScreenModuleLibraryMediaVo> hotPosterList =
                screenModuleLibraryMediaMapper.getPosterList(libraryMediaBo);
            // 封装热门主题海报
            List<LinkedHashMap> hotPosterReturnList = new ArrayList<>();
            formatLibraryWithLibraryIdSort(hotPosterReturnList, hotLibraryIds, collectVosMap, hotPosterList);
            resultMap.put("热门海报主题", hotPosterReturnList);
        }

        // 海报库或校本海报所有海报
        ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo = new ScreenModuleLibraryMediaBo();
        // screenModuleLibraryMediaBo.setModuleDataType(PosterEnums.TYPE_SHOW.getCode());
        // 海报库或校本海
        screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        if (ConstantsInteger.QUERY_SCHOOL_POSTER.equals(selectType)) {
            screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(organizationId);
        }
        // 横竖版
        screenModuleLibraryMediaBo.setDevicePattern(pattern);
        List<ScreenModuleLibraryMediaVo> posterList =
            screenModuleLibraryMediaMapper.getPosterList(screenModuleLibraryMediaBo);

        // 格式化返回数据
        formatModuleGroup(resultMap, collectVosMap, posterList);
        return AjaxResult.success(resultMap);
    }

    @Override
    public AjaxResult getTopPosterList(Long organizationId, Integer selectType, Integer pattern,
        Integer moduleGroupTopLimit, Integer libraryTopLimit, Integer moduleDefaultH5LabelGroup, Long classesId) {
        Map<String, ScreenModuleLibraryGroupVo> resultMap = new LinkedHashMap<>();
        // 当前用户的收藏列表
        List<ScreenModuleLibraryCollectVo> collectVos = getMyCollectLibraryVos(organizationId);
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectVosMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(collectVos)) {
            collectVosMap = collectVos.stream()
                .collect(Collectors.groupingBy(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId));
        }

        // 热门主题海报
        if (ConstantsInteger.QUERY_HOT.equals(selectType)) {
            // 热门收藏前十模块
            List<Long> hotLibraryIds =
                screenModuleLibraryCollectMapper.getHotPosterModuleLibraryIds(ConstantsInteger.HOT_LIMIT);
            // 根据模块获取模块海报图片
            ScreenModuleLibraryMediaBo libraryMediaBo = new ScreenModuleLibraryMediaBo();
            libraryMediaBo.setScreenModuleLibraryIds(hotLibraryIds);
            libraryMediaBo.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
            libraryMediaBo.setDictionaryDataClassesId(ConstantsLong.NUM_0);
            List<ScreenModuleLibraryMediaVo> hotPosterList =
                screenModuleLibraryMediaMapper.getPosterList(libraryMediaBo);
            // 封装热门主题海报
            List<LinkedHashMap> hotPosterReturnList = new ArrayList<>();
            formatLibraryWithLibraryIdSort(hotPosterReturnList, hotLibraryIds, collectVosMap, hotPosterList);
            ScreenModuleLibraryGroupVo screenModuleLibraryGroupVo = new ScreenModuleLibraryGroupVo();
            screenModuleLibraryGroupVo.setScreenModuleLibraryMapList(hotPosterReturnList);
            screenModuleLibraryGroupVo.setLabelName("热门海报主题");
            resultMap.put("热门海报主题", screenModuleLibraryGroupVo);
        }

        // 前moduleGroupTopLimit个海报分组
        // 订阅版本改为 取标签组，及标签组下属所有标签关联的海报主题
        ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo = new ScreenModuleLibraryMediaBo();
        screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        screenModuleLibraryMediaBo.setDictionaryDataClassesId(ConstantsLong.NUM_0);
        if (ConstantsInteger.QUERY_SCHOOL_POSTER.equals(selectType)) {
            screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(organizationId);
        }
        if (ConstantsInteger.QUERY_CLASS_POSTER.equals(selectType)) {
            screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(organizationId);
            screenModuleLibraryMediaBo.setDictionaryDataClassesId(classesId);
        }
        screenModuleLibraryMediaBo.setDevicePattern(pattern);
        screenModuleLibraryMediaBo.setPageSize(moduleGroupTopLimit);
        // 仅校本海报首页逻辑：如果有指定则取消moduleGroupTopLimit限制
        if (moduleDefaultH5LabelGroup != null && !moduleDefaultH5LabelGroup.equals(ConstantsInteger.NUM_0)
            && ConstantsInteger.QUERY_HOT.equals(selectType)) {
            screenModuleLibraryMediaBo.setPageSize(ConstantsInteger.NUM_100);
            screenModuleLibraryMediaBo.setModuleDefaultH5LabelGroup(moduleDefaultH5LabelGroup);
        }
        // 【数据1】获取前20个标签
        List<ScreenModuleLibraryGroupVo> groupList =
            screenModuleLibraryMediaMapper.getModuleGroupListByCondition(screenModuleLibraryMediaBo);
        if (CollectionUtils.isEmpty(groupList)) {
            return AjaxResult.success(resultMap);
        }

        // 【数据2】每个分组的前libraryTopLimit个主题
        List<Long> conditionLibraryIds = new ArrayList<>();
        for (ScreenModuleLibraryGroupVo screenModuleLibraryGroupVo : groupList) {
            List<Long> splitLibraryIds =
                StringKit.splitString2ListLong(screenModuleLibraryGroupVo.getStringLibraryIds(), ",");
            List<Long> topNScreenModuleLibraryIds =
                splitLibraryIds.stream().limit(libraryTopLimit).collect(Collectors.toList());
            conditionLibraryIds.addAll(topNScreenModuleLibraryIds);
            screenModuleLibraryGroupVo.setTopNScreenModuleLibraryIds(topNScreenModuleLibraryIds);
        }

        // 【数据3】这libraryTopLimit个主题的图片，图片会会有重复因为一个主题可以在多个标签下
        screenModuleLibraryMediaBo.setScreenModuleLibraryIds(conditionLibraryIds);
        List<ScreenModuleLibraryMediaVo> posterList =
            screenModuleLibraryMediaMapper.getPosterLabelRel(screenModuleLibraryMediaBo);

        // 格式化返回数据
        // (labelId,海报图片列表)
        Map<Long, List<ScreenModuleLibraryMediaVo>> posterMap = Maps.newHashMap();
        Map<String, List<ScreenModuleLibraryMediaVo>> posterMapOfConcat = posterList.stream().collect(Collectors
            .groupingBy(ScreenModuleLibraryMediaVo::getLabelIdConcat, LinkedHashMap::new, Collectors.toList()));
        for (String labelIdConcat : posterMapOfConcat.keySet()) {
            if (StringUtils.isBlank(labelIdConcat)) {
                continue;
            }
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVosTemp = posterMapOfConcat.get(labelIdConcat);
            List<Long> labelIds = StringKit.splitString2ListLong(labelIdConcat, null);
            for (Long labelId : labelIds) {
                if (!posterMap.containsKey(labelId)) {
                    posterMap.put(labelId, screenModuleLibraryMediaVosTemp);
                } else {
                    List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVosExist = posterMap.get(labelId);
                    List<Long> screenModuleLibraryMediaIdsExist = screenModuleLibraryMediaVosExist.stream()
                        .map(ScreenModuleLibraryMediaVo::getScreenModuleLibraryMediaId).collect(Collectors.toList());
                    screenModuleLibraryMediaVosTemp.forEach(screenModuleLibraryMediaVo -> {
                        if (!screenModuleLibraryMediaIdsExist
                            .contains(screenModuleLibraryMediaVo.getScreenModuleLibraryMediaId())) {
                            screenModuleLibraryMediaVosExist.add(screenModuleLibraryMediaVo);
                        }
                    });
                }
            }
        }
        // 循环ModuleGroup分组
        for (ScreenModuleLibraryGroupVo groupVo : groupList) {
            // 每一个ModuleGroup下的AllModuleLibrary
            List<ScreenModuleLibraryMediaVo> moduleLibraryList = posterMap.get(groupVo.getLabelId());
            // 标签下没有海报
            if (CollectionUtils.isEmpty(moduleLibraryList)) {
                continue;
            }
            // 取出要参照排序Library顺序(这里无需再次排序，因为查询的时候已经排序)，这里增加过滤，仅保留topN的海报主题
            List<Long> libraryIds = groupVo.getTopNScreenModuleLibraryIds();
            List<LinkedHashMap> returnList = new ArrayList<>();
            // 封装AllModuleLibrary返回值
            formatLibraryWithLibraryIdSort(returnList, libraryIds, collectVosMap, moduleLibraryList);
            // 封装分组
            ScreenModuleLibraryGroupVo screenModuleLibraryGroupVo = new ScreenModuleLibraryGroupVo();
            screenModuleLibraryGroupVo.setScreenModuleLibraryMapList(returnList);
            screenModuleLibraryGroupVo.setLabelId(groupVo.getLabelId());
            screenModuleLibraryGroupVo.setLabelName(groupVo.getLabelName());
            resultMap.put(groupVo.getLabelName(), screenModuleLibraryGroupVo);
        }
        return AjaxResult.success(resultMap);
    }

    /**
     * 格式化海报分组
     *
     *
     * @param resultMap 返回map
     * @param collectVosMap 用户收藏的海报模块map key:libraryId
     * @param posterList 所有分组及分组下的模块及模块下的图片
     * @return void
     * <AUTHOR>
     * @date 2023/3/31 14:45
     */
    private void formatModuleGroup(Map<String, List<LinkedHashMap>> resultMap,
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectVosMap, List<ScreenModuleLibraryMediaVo> posterList) {
        // 按ModuleGroup分组
        Map<Long, List<ScreenModuleLibraryMediaVo>> posterMap = posterList.stream().collect(Collectors
            .groupingBy(ScreenModuleLibraryMediaVo::getModuleGroupType, LinkedHashMap::new, Collectors.toList()));
        // 循环ModuleGroup分组
        for (Long groupTypeKey : posterMap.keySet()) {
            // 每一个ModuleGroup下的AllModuleLibrary
            List<ScreenModuleLibraryMediaVo> moduleLibraryList = posterMap.get(groupTypeKey);
            // 取出要参照排序Library顺序
            List<Long> libraryIds = moduleLibraryList.stream().map(ScreenModuleLibraryMediaVo::getScreenModuleLibraryId)
                .distinct().collect(Collectors.toList());
            List<LinkedHashMap> returnList = new ArrayList<>();
            // 封装AllModuleLibrary返回值
            formatLibraryWithLibraryIdSort(returnList, libraryIds, collectVosMap, moduleLibraryList);
            // 封装分组
            resultMap.put(posterMap.get(groupTypeKey).get(0).getModuleGroupTypeName(), returnList);
        }
    }

    @Override
    public List<ScreenModuleLibraryCollectVo> getMyCollectLibraryVos(Long organizationId) {
        String currentOid = baseDataService.getCurrentUserOid();
        ScreenModuleLibraryCollectConditionBo condition = new ScreenModuleLibraryCollectConditionBo();
        condition.setUserOid(currentOid);
        condition.setOrganizationId(organizationId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return screenModuleLibraryCollectMapper.getScreenModuleLibraryCollectListByCondition(condition);
    }

    @Override
    public AjaxResult getMyCollectPosters(Long organization, Integer pattern) {
        List<LinkedHashMap> returnList = new ArrayList<>();
        // 用户收藏模块
        List<ScreenModuleLibraryCollectVo> collectVos = getMyCollectLibraryVos(organization);
        if (CollectionUtils.isEmpty(collectVos)) {
            return AjaxResult.success(returnList);
        }
        List<Long> screenModuleLibraryIds = collectVos.stream()
            .map(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId).collect(Collectors.toList());
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectMap =
            collectVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId));
        // 根据模块查找海报图片详情
        ScreenModuleLibraryMediaBo libraryMediaBo = new ScreenModuleLibraryMediaBo();
        libraryMediaBo.setScreenModuleLibraryIds(screenModuleLibraryIds);
        // 横竖版
        libraryMediaBo.setDevicePattern(pattern);
        List<ScreenModuleLibraryMediaVo> hotPosterList = screenModuleLibraryMediaMapper.getPosterList(libraryMediaBo);
        // 格式化返回数据
        formatLibraryWithLibraryIdSort(returnList, screenModuleLibraryIds, collectMap, hotPosterList);
        return AjaxResult.success(returnList);
    }

    @Override
    public AjaxResult getMyCollectPostersPage(ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo) {
        List<LinkedHashMap> returnList = new ArrayList<>();
        // 用户收藏模块分页
        libraryCollectConditionBo.setUserOid(baseDataService.getCurrentUserOid());

        if (StringUtils.isNotBlank(libraryCollectConditionBo.getModuleName())) {
            libraryCollectConditionBo
                .setModuleName(FuzzyQueryUtil.transferMean(libraryCollectConditionBo.getModuleName()));
        }
        PageHelper.startPage(libraryCollectConditionBo.getPageNo(), libraryCollectConditionBo.getPageSize());
        List<ScreenModuleLibraryCollectVo> collectVos =
            screenModuleLibraryCollectMapper.getLibraryIdPageListByUserOidAndPattern(libraryCollectConditionBo);
        if (CollectionUtils.isEmpty(collectVos)) {
            return AjaxResult.success();
        }
        PageInfo<ScreenModuleLibraryCollectVo> pageInfo = new PageInfo<>(collectVos);
        if (CollectionUtils.isEmpty(collectVos)) {
            return AjaxResult.success();
        }
        List<Long> screenModuleLibraryIds = collectVos.stream()
            .map(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId).collect(Collectors.toList());
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectMap =
            collectVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId));

        // 海报图片
        ScreenModuleLibraryMediaBo libraryMediaBo = new ScreenModuleLibraryMediaBo();
        libraryMediaBo.setScreenModuleLibraryIds(screenModuleLibraryIds);
        // 横竖版
        libraryMediaBo.setDevicePattern(libraryCollectConditionBo.getPattern());
        List<ScreenModuleLibraryMediaVo> posterList = screenModuleLibraryMediaMapper.getPosterList(libraryMediaBo);
        // 格式化返回数据
        formatLibraryWithLibraryIdSort(returnList, screenModuleLibraryIds, collectMap, posterList);
        Map<String, Object> map = new HashMap<>();
        map.put("total", pageInfo.getTotal());
        map.put("list", returnList);
        return AjaxResult.success(map);
    }

    /**
     * 按照libraryIds顺序封装返回模块列表
     *
     * @param returnList 返回列表 （排序模块顺序id对应的模块列表）
     * @param libraryIds 排序好的模块顺序id
     * @param collectVosMap 用户收藏的海报模块map key:libraryId
     * @param posterList 所有海报列表
     * <AUTHOR>
     * @date 2023/3/31 15:20
     */
    private void formatLibraryWithLibraryIdSort(List<LinkedHashMap> returnList, List<Long> libraryIds,
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectVosMap, List<ScreenModuleLibraryMediaVo> posterList) {
        // 按照海报主题分组
        Map<Long, List<ScreenModuleLibraryMediaVo>> posterMap = posterList.stream().collect(Collectors
            .groupingBy(ScreenModuleLibraryMediaVo::getScreenModuleLibraryId, LinkedHashMap::new, Collectors.toList()));
        // 查询出海报对应的标签数据<海报id,标签名称通过逗号分割>
        ScreenModuleLibraryListConditionBo conditionBo = new ScreenModuleLibraryListConditionBo();
        conditionBo.setScreenModuleLibraryIds(libraryIds);
        List<ScreenModuleLibraryVo> screenModuleLibraryVos =
            screenModuleLibraryService.getPosterListOriginal(conditionBo);
        Map<Long, String> screenModuleLibraryVosLabelMap = Maps.newLinkedHashMap();
        if (CollectionUtils.isNotEmpty(screenModuleLibraryVos)) {
            screenModuleLibraryVosLabelMap = screenModuleLibraryVos.stream()
                .collect(Collectors.toMap(ScreenModuleLibraryVo::getScreenModuleLibraryId,
                    ScreenModuleLibraryVo::getLabelNameConcat, (k1, k2) -> k2));
        }
        // 返回封装
        for (Long screenModuleLibraryId : libraryIds) {
            // 收藏过滤已删除海报
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVos = posterMap.get(screenModuleLibraryId);
            if (CollectionUtils.isEmpty(screenModuleLibraryMediaVos)) {
                continue;
            }
            // 排序里面的海报图片
            screenModuleLibraryMediaVos = screenModuleLibraryMediaVos.stream()
                .sorted(Comparator.comparing(ScreenModuleLibraryMediaVo::getScreenModuleLibraryMediaId))
                .collect(Collectors.toList());

            LinkedHashMap<String, Object> childrenMap = new LinkedHashMap<>();
            childrenMap.put("screenModuleLibraryId", screenModuleLibraryId);
            childrenMap.put("moduleGroupType", screenModuleLibraryMediaVos.get(0).getModuleGroupType());
            childrenMap.put("moduleSource", StatusEnum.YES.getCode());
            childrenMap.put("moduleName", screenModuleLibraryMediaVos.get(0).getModuleName());
            childrenMap.put("screenModuleLibraryMediaVos", screenModuleLibraryMediaVos);
            childrenMap.put("labelNameConcat", screenModuleLibraryVosLabelMap.get(screenModuleLibraryId));
            // 判断是否被收藏
            if (null != collectVosMap.get(screenModuleLibraryId)) {
                childrenMap.put("collect", true);
            } else {
                childrenMap.put("collect", false);
            }
            // 判断校本海报
            Integer isSchoolPoster = (screenModuleLibraryMediaVos.get(0).getOrganizationId() == ConstantsLong.NUM_0
                ? PosterEnums.SCHOOL_POST_NOT.getCode() : PosterEnums.SCHOOL_POST_IS.getCode());
            childrenMap.put("isSchoolPoster", isSchoolPoster);
            returnList.add(childrenMap);
        }
    }

    /**
     * 海报覆盖 根据文件目录的data——value 插入library和media(先sql脚本删除被覆盖文件对应的libra和media)
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/30 14:56
     */
    @Override
    public AjaxResult coverPoster() {
        File posterFile = new File(POSTER_PATH);
        File[] typeFiles = posterFile.listFiles();
        if (null == typeFiles || typeFiles.length == 0) {
            return AjaxResult.fail("文件不存在");
        }

        List<ScreenModuleLibrary> screenModuleLibraryList = new ArrayList<>();
        List<ScreenModuleLibraryMedia> allLibraryMediaList = new ArrayList<>();
        // 设置类型初始初始增长value
        DictionaryDataListConditionBo conditionBo = new DictionaryDataListConditionBo();
        conditionBo.setDictType(DictionaryType.MODULE_GROUP_TYPE.getValue());
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setType(PosterEnums.TYPE_SHOW.getCode());
        List<DictionaryDataVo> dataVos = screenDictionaryDataService.getDictionaryDataListByCondition(conditionBo);
        Map<String, List<DictionaryDataVo>> map =
            dataVos.stream().collect(Collectors.groupingBy(DictionaryDataVo::getDictLabel));
        // 添加library 和media
        for (File typeFile : typeFiles) {
            if (typeFile.getName().startsWith(".")) {
                continue;
            }
            File[] nameFiles = typeFile.listFiles();
            for (File nameFile : nameFiles) {
                if (nameFile.getName().startsWith(".")) {
                    continue;
                }
                String nameFileName = nameFile.getName();
                ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                screenModuleLibrary.setModuleGroupType(Long.valueOf(map.get(typeFile.getName()).get(0).getDictValue()));
                screenModuleLibrary.setIsDelete(StatusEnum.NOTDELETE.getCode());
                screenModuleLibrary.setModuleName(nameFileName);

                File[] mediaFiles = nameFile.listFiles();
                List<ScreenModuleLibraryMedia> libraryMediaList = new ArrayList<>();
                for (File mediaFile : mediaFiles) {
                    if (mediaFile.getName().startsWith(".")) {
                        continue;
                    }
                    ScreenModuleLibraryMedia screenModuleLibraryMedia = new ScreenModuleLibraryMedia();
                    try {
                        FileInputStream fileInputStream = new FileInputStream(mediaFile);
                        MultipartFile file = new MockMultipartFile(mediaFile.getName(), mediaFile.getName(),
                            MediaType.MULTIPART_FORM_DATA_VALUE, fileInputStream);
                        MultipartFormData multipartFormData = new MultipartFormData();
                        multipartFormData.setFileSize(file.getSize());
                        multipartFormData.setFileBytes(file.getBytes());
                        multipartFormData.setOriginalFilename(file.getOriginalFilename());
                        fileInputStream.close();
                        AttachmentVo attachmentVo = baseDataService.upload(multipartFormData);
                        if (null == attachmentVo) {
                            log.error("上传海报失败: " + mediaFile.getName());
                            return AjaxResult.fail("上传海报失败: " + mediaFile.getName());
                        }
                        screenModuleLibraryMedia.setScreenContentMediaId(attachmentVo.getFileOid());
                        screenModuleLibraryMedia.setScreenModuleLibraryMediaUrl(attachmentVo.getWebUrl());
                        screenModuleLibraryMedia.setScreenContentMediaMd5(attachmentVo.getMd5());
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    libraryMediaList.add(screenModuleLibraryMedia);
                }
                screenModuleLibrary.setScreenModuleLibraryMediaList(libraryMediaList);
                screenModuleLibraryList.add(screenModuleLibrary);
            }
        }
        screenModuleLibraryService.saveBatch(screenModuleLibraryList);
        screenModuleLibraryList.forEach(library -> {
            library.getScreenModuleLibraryMediaList()
                .forEach(media -> media.setScreenModuleLibraryId(library.getScreenModuleLibraryId()));
            allLibraryMediaList.addAll(library.getScreenModuleLibraryMediaList());
        });
        saveBatch(allLibraryMediaList);
        return AjaxResult.success();
    }

    /**
     * 海报导入优化
     *
     * @param organizationId 组织id ,0 主题海报导入。[1,+∞)校本海报导入
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/18 9:43
     */
    @Override
    @Transactional
    public AjaxResult posterOptimization(Long organizationId) {
        // 1.文件读取处理
        File posterFile = new File(POSTER_PATH);
        File[] typeFiles = posterFile.listFiles();
        if (null == typeFiles || typeFiles.length == 0) {
            return AjaxResult.fail("文件不存在");
        }
        // 2.海报所有分组
        DictionaryDataListConditionBo conditionBo = new DictionaryDataListConditionBo();
        conditionBo.setDictType(DictionaryType.MODULE_GROUP_TYPE.getValue());
        conditionBo.setType(PosterEnums.TYPE_SHOW.getCode());
        List<DictionaryDataVo> dictionaryDataVos =
            screenDictionaryDataService.getDictionaryDataListByCondition(conditionBo);

        // 取最后dictValue,及最先dict_sort
        dictionaryDataVos = dictionaryDataVos.stream()
            .sorted(Comparator.comparing(DictionaryDataVo::getDictValue, Comparator.comparingInt(Integer::parseInt)))
            .collect(Collectors.toList());
        long dictValue = dictionaryDataVos.size() == 0 ? ConstantsLong.NUM_0
            : Long.parseLong(dictionaryDataVos.get(dictionaryDataVos.size() - 1).getDictValue());
        // 过滤出当前组织的模块分类列表
        dictionaryDataVos = dictionaryDataVos.stream().filter(x -> organizationId.equals(x.getOrganizationId()))
            .collect(Collectors.toList());
        dictionaryDataVos = dictionaryDataVos.stream().sorted(Comparator.comparing(DictionaryDataVo::getDictSort))
            .collect(Collectors.toList());
        int dictSort = dictionaryDataVos.size() == 0 ? ConstantsInteger.NUM_0 : dictionaryDataVos.get(0).getDictSort();
        Map<String, List<DictionaryDataVo>> dictionaryDataMap =
            dictionaryDataVos.stream().collect(Collectors.groupingBy(DictionaryDataVo::getDictLabel));

        // 3.查询当前组织的所有模块
        ScreenModuleLibraryListConditionBo libraryListConditionBo = new ScreenModuleLibraryListConditionBo();
        libraryListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        libraryListConditionBo.setDictionaryDataOrganizationId(organizationId);
        List<ScreenModuleLibraryVo> screenModuleLibraryVos =
            screenModuleLibraryService.getScreenModuleLibraryListByCondition(libraryListConditionBo);
        screenModuleLibraryVos =
            screenModuleLibraryVos.stream().filter(x -> x.getModuleGroupType() > 4).collect(Collectors.toList());
        Map<Long, List<ScreenModuleLibraryVo>> libraryByGroupMap =
            screenModuleLibraryVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleGroupType));
        Map<String, List<ScreenModuleLibraryVo>> libraryByNameMap =
            screenModuleLibraryVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleName));

        // 4.对比出需要删除和新增的library和dictionaryData
        List<DictionaryDataVo> delDictionaryDataVos = new ArrayList<>();
        List<ScreenModuleLibraryVo> delLibraryVos = new ArrayList<>();
        List<DictionaryData> addDictionaryDataList = new ArrayList<>();
        List<ScreenModuleLibrary> addScreenModuleLibraryList = new ArrayList<>();

        for (File typeFile : typeFiles) {
            String typeFileName = typeFile.getName();
            if (typeFileName.startsWith(".")) {
                continue;
            }
            List<DictionaryDataVo> dataVos = dictionaryDataMap.get(typeFileName);
            // db分类存在，判断分类里模块是相同
            if (CollectionUtil.isNotEmpty(dataVos)) {
                DictionaryDataVo dictionaryDataVo = dataVos.get(0);
                File[] nameFiles = typeFile.listFiles();
                // 文件分类下的所有模块
                List<String> strings = new ArrayList<>();
                for (File nameFile : nameFiles) {
                    if (nameFile.getName().startsWith(".")) {
                        continue;
                    }
                    strings.add(nameFile.getName());
                }
                // db分类下的所有模块
                List<ScreenModuleLibraryVo> moduleLibraryVos =
                    libraryByGroupMap.get(Long.valueOf(dictionaryDataVo.getDictValue()));
                List<String> moduleNames =
                    moduleLibraryVos.stream().map(ScreenModuleLibraryVo::getModuleName).collect(Collectors.toList());
                List<String> moduleNamesCopy = new ArrayList<>(moduleNames);
                // 本地剩余需要删除的模块
                moduleNames.removeAll(strings);
                // 需要添加的模块
                strings.removeAll(moduleNamesCopy);
                // 添加删除模块
                if (CollectionUtil.isNotEmpty(moduleNames)) {
                    for (String moduleName : moduleNames) {
                        List<ScreenModuleLibraryVo> moduleLibraryVoList = libraryByNameMap.get(moduleName);
                        delLibraryVos.addAll(moduleLibraryVoList);
                    }
                }
                // 添加新增模块
                if (CollectionUtil.isNotEmpty(strings)) {
                    for (String string : strings) {
                        for (File nameFile : nameFiles) {
                            if (nameFile.getName().startsWith(".")) {
                                continue;
                            }
                            if (string.equals(nameFile.getName())) {
                                String nameFileName = nameFile.getName();
                                ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                                screenModuleLibrary.setModuleGroupType(Long.valueOf(dictionaryDataVo.getDictValue()));
                                screenModuleLibrary.setIsDelete(StatusEnum.NOTDELETE.getCode());
                                screenModuleLibrary.setModuleName(nameFileName);
                                screenModuleLibrary.setParentScreenModuleLibraryId(
                                    ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
                                addScreenModuleLibraryList.add(screenModuleLibrary);
                            }
                        }
                    }
                }
            } else {
                // db分类不存在，新增分类和模块
                dictValue++;
                dictSort--;
                DictionaryData dictionaryData = new DictionaryData();
                dictionaryData.setDictType(DictionaryType.MODULE_GROUP_TYPE.getValue());
                dictionaryData.setDictValue(Long.toString(dictValue));
                dictionaryData.setDictLabel(typeFileName);
                dictionaryData.setDictSort(dictSort);
                dictionaryData.setCreateTime(new Date());
                dictionaryData.setIsDelete(StatusEnum.NOTDELETE.getCode());
                dictionaryData.setOrganizationId(organizationId);
                dictionaryData.setType(PosterEnums.TYPE_SHOW.getCode());
                addDictionaryDataList.add(dictionaryData);
                File[] nameFiles = typeFile.listFiles();
                for (File nameFile : nameFiles) {
                    if (nameFile.getName().startsWith(".")) {
                        continue;
                    }
                    String nameFileName = nameFile.getName();
                    ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                    screenModuleLibrary.setModuleGroupType(dictValue);
                    screenModuleLibrary.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    screenModuleLibrary.setModuleName(nameFileName);
                    screenModuleLibrary
                        .setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
                    addScreenModuleLibraryList.add(screenModuleLibrary);
                }
            }
        }
        // db分类存在，文件不存在，则删除db分类和模块
        List<String> typeFileNames = new ArrayList<>();
        for (File typeFile : typeFiles) {
            typeFileNames.add(typeFile.getName());
        }
        for (DictionaryDataVo dictionaryDataVo : dictionaryDataVos) {
            if (typeFileNames.contains(dictionaryDataVo.getDictLabel())) {
                break;
            }
            delDictionaryDataVos.add(dictionaryDataVo);
            delLibraryVos.addAll(libraryByGroupMap.get(Long.valueOf(dictionaryDataVo.getDictValue())));
        }

        // 5 检查删除模块list内是否有新增的模块list，有则修改并移动
        List<ScreenModuleLibrary> addScreenModuleLibraryEmpty = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(delLibraryVos)) {
            for (ScreenModuleLibraryVo delLibraryVo : delLibraryVos) {
                delLibraryVo.setIsDelete(StatusEnum.YES.getCode());
            }
            Map<String, List<ScreenModuleLibraryVo>> map =
                delLibraryVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleName));
            for (ScreenModuleLibrary screenModuleLibrary : addScreenModuleLibraryList) {
                List<ScreenModuleLibraryVo> moduleLibraryVos = map.get(screenModuleLibrary.getModuleName());
                if (CollectionUtil.isNotEmpty(moduleLibraryVos)) {
                    ScreenModuleLibraryVo screenModuleLibraryVo = moduleLibraryVos.get(0);
                    screenModuleLibraryVo.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    screenModuleLibraryVo.setModuleGroupType(screenModuleLibrary.getModuleGroupType());
                    addScreenModuleLibraryEmpty.add(screenModuleLibrary);
                }
            }
            addScreenModuleLibraryList.removeAll(addScreenModuleLibraryEmpty);
        }
        // 6 更新数据库
        if (CollectionUtil.isNotEmpty(delDictionaryDataVos)) {
            screenDictionaryDataService.updateBatchById(delDictionaryDataVos.stream().map(x -> {
                DictionaryData dictionaryData = new DictionaryData();
                BeanUtils.copyProperties(x, dictionaryData);
                return dictionaryData;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(addDictionaryDataList)) {
            screenDictionaryDataService.saveBatch(addDictionaryDataList);
        }
        if (CollectionUtil.isNotEmpty(delLibraryVos)) {
            screenModuleLibraryService.updateBatchById(delLibraryVos.stream().map(x -> {
                ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                BeanUtils.copyProperties(x, screenModuleLibrary);
                return screenModuleLibrary;
            }).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(addScreenModuleLibraryList)) {
            screenModuleLibraryService.saveBatch(addScreenModuleLibraryList);
        }

        // 7 修改模块之后同步修改海报图片
        // 获取所有海报
        ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo = new ScreenModuleLibraryMediaBo();
        screenModuleLibraryMediaBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(organizationId);
        List<ScreenModuleLibraryMediaVo> moduleLibraryMediaVos =
            screenModuleLibraryMediaMapper.getScreenModuleLibraryMediaListByCondition(screenModuleLibraryMediaBo);
        Map<Long, List<ScreenModuleLibraryMediaVo>> mediaMap = moduleLibraryMediaVos.stream()
            .collect(Collectors.groupingBy(ScreenModuleLibraryMediaVo::getScreenModuleLibraryId));

        // 同步删除删除模块对应海报
        List<ScreenModuleLibraryMediaVo> delMediaVos = new ArrayList<>();
        delLibraryVos = delLibraryVos.stream().filter(x -> StatusEnum.ISDELETE.getCode().equals(x.getIsDelete()))
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(delLibraryVos)) {
            for (ScreenModuleLibraryVo delLibraryVo : delLibraryVos) {
                List<ScreenModuleLibraryMediaVo> mediaVos = mediaMap.get(delLibraryVo.getScreenModuleLibraryId());
                mediaVos.forEach(x -> x.setIsDelete(StatusEnum.ISDELETE.getCode()));
                delMediaVos.addAll(mediaVos);
            }
        }
        // 同步新增新增模块海报图片
        List<ScreenModuleLibraryMedia> addMedias = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(addScreenModuleLibraryList)) {
            for (File typeFile : typeFiles) {
                if (typeFile.getName().startsWith(".")) {
                    continue;
                }
                File[] nameFiles = typeFile.listFiles();
                for (File nameFile : nameFiles) {
                    if (nameFile.getName().startsWith(".")) {
                        continue;
                    }
                    for (ScreenModuleLibrary screenModuleLibrary : addScreenModuleLibraryList) {
                        if (screenModuleLibrary.getModuleName().equals(nameFile.getName())) {
                            // 新增海报图片
                            File[] mediaFiles = nameFile.listFiles();
                            for (File mediaFile : mediaFiles) {
                                if (mediaFile.getName().startsWith(".")) {
                                    continue;
                                }
                                ScreenModuleLibraryMedia media = new ScreenModuleLibraryMedia();
                                media.setScreenModuleLibraryMediaNameOri(mediaFile.getName());
                                media.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                                media.setFile(mediaFile);
                                addMedias.add(media);
                            }
                        }
                    }
                }
            }
        }

        // 8 同一模块下海报图片的新增删除
        List<ScreenModuleLibraryVo> libraryVos =
            screenModuleLibraryService.getScreenModuleLibraryListByCondition(libraryListConditionBo);
        Map<String, List<ScreenModuleLibraryVo>> libraryMapByName =
            libraryVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleName));

        for (File typeFile : typeFiles) {
            if (typeFile.getName().startsWith(".")) {
                continue;
            }
            File[] nameFiles = typeFile.listFiles();
            for (File nameFile : nameFiles) {
                if (nameFile.getName().startsWith(".")) {
                    continue;
                }
                String name = nameFile.getName();
                if (CollectionUtil.isEmpty(libraryMapByName.get(name))) {
                    break;
                }

                ScreenModuleLibraryVo moduleLibraryVo = libraryMapByName.get(name).get(0);
                List<ScreenModuleLibraryMediaVo> mediaVos = mediaMap.get(moduleLibraryVo.getScreenModuleLibraryId());
                File[] mediaFiles = nameFile.listFiles();
                // 新增图片
                List<String> fileNames = new ArrayList<>();
                for (File mediaFile : mediaFiles) {
                    if (mediaFile.getName().startsWith(".")) {
                        continue;
                    }
                    fileNames.add(mediaFile.getName());
                }
                // 已存在图片
                List<String> mediaNames = mediaVos.stream()
                    .map(ScreenModuleLibraryMediaVo::getScreenModuleLibraryMediaNameOri).collect(Collectors.toList());
                List<String> fileNamesCopy = new ArrayList<>(fileNames);
                // 需要添加的文件
                fileNames.removeAll(mediaNames);
                // 需要删除的图片
                mediaNames.removeAll(fileNamesCopy);
                // 添加图片
                for (File mediaFile : mediaFiles) {
                    if (fileNames.contains(mediaFile.getName())) {
                        ScreenModuleLibraryMedia screenModuleLibraryMedia = new ScreenModuleLibraryMedia();
                        screenModuleLibraryMedia.setScreenModuleLibraryId(moduleLibraryVo.getScreenModuleLibraryId());
                        screenModuleLibraryMedia.setScreenModuleLibraryMediaNameOri(mediaFile.getName());
                        screenModuleLibraryMedia.setFile(mediaFile);
                        addMedias.add(screenModuleLibraryMedia);
                    }
                }
                // 删除图片
                for (ScreenModuleLibraryMediaVo mediaVo : mediaVos) {
                    if (mediaNames.contains(mediaVo.getScreenModuleLibraryMediaName())) {
                        mediaVo.setIsDelete(StatusEnum.ISDELETE.getCode());
                        delMediaVos.add(mediaVo);
                    }
                }
            }
        }
        // 9 比较并移动删除与新增海报列表
        if (CollectionUtil.isNotEmpty(delMediaVos) && CollectionUtil.isNotEmpty(addMedias)) {
            List<ScreenModuleLibraryMedia> addMediasEmptyCopy = new ArrayList<>();
            for (ScreenModuleLibraryMedia addMedia : addMedias) {
                for (ScreenModuleLibraryMediaVo delMediaVo : delMediaVos) {
                    if (delMediaVo.getScreenModuleLibraryMediaNameOri() != null && delMediaVo
                        .getScreenModuleLibraryMediaNameOri().equals(addMedia.getScreenModuleLibraryMediaNameOri())) {
                        delMediaVo.setIsDelete(StatusEnum.NOTDELETE.getCode());
                        delMediaVo.setScreenModuleLibraryId(addMedia.getScreenModuleLibraryId());
                        addMediasEmptyCopy.add(addMedia);
                        break;
                    }
                }
            }
            addMedias.removeAll(addMediasEmptyCopy);
        }
        // 更新数据库，上传海报
        if (CollectionUtil.isNotEmpty(addMedias)) {
            uploadMedia(addMedias);
            saveBatch(addMedias);
        }
        // 更新数据库
        if (CollectionUtil.isNotEmpty(delMediaVos)) {
            updateBatchById(delMediaVos.stream().map(x -> {
                ScreenModuleLibraryMedia moduleLibraryMedia = new ScreenModuleLibraryMedia();
                BeanUtils.copyProperties(x, moduleLibraryMedia);
                return moduleLibraryMedia;
            }).collect(Collectors.toList()));
        }
        return AjaxResult.success();
    }

    /**
     * 海报导入 （只加不删，重复图片跳过）
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/12 17:19
     */
    @Transactional
    @Override
    public AjaxResult importPoster(Long organizationId) {
        // 1.文件读取处理
        File posterFile = new File(POSTER_PATH);
        File[] typeFiles = posterFile.listFiles();
        if (null == typeFiles || typeFiles.length == 0) {
            return AjaxResult.fail("文件不存在");
        }
        // 2.海报所有分组
        DictionaryDataListConditionBo conditionBo = new DictionaryDataListConditionBo();
        conditionBo.setDictType(DictionaryType.MODULE_GROUP_TYPE.getValue());
        conditionBo.setType(PosterEnums.TYPE_SHOW.getCode());
        List<DictionaryDataVo> dictionaryDataVos =
            screenDictionaryDataService.getDictionaryDataListByCondition(conditionBo);

        // 取最后dictValue,及最先dict_sort
        dictionaryDataVos = dictionaryDataVos.stream()
            .sorted(Comparator.comparing(DictionaryDataVo::getDictValue, Comparator.comparingInt(Integer::parseInt)))
            .collect(Collectors.toList());
        long dictValue = dictionaryDataVos.size() == 0 ? ConstantsLong.NUM_0
            : Long.parseLong(dictionaryDataVos.get(dictionaryDataVos.size() - 1).getDictValue());
        // 过滤出当前组织的分类列表（顺序为当前组织顺序）
        dictionaryDataVos = dictionaryDataVos.stream().filter(x -> organizationId.equals(x.getOrganizationId()))
            .collect(Collectors.toList());
        dictionaryDataVos = dictionaryDataVos.stream().sorted(Comparator.comparing(DictionaryDataVo::getDictSort))
            .collect(Collectors.toList());
        int dictSort = dictionaryDataVos.size() == 0 ? ConstantsInteger.NUM_0 : dictionaryDataVos.get(0).getDictSort();

        Map<String, List<DictionaryDataVo>> dictionaryDataMap =
            dictionaryDataVos.stream().collect(Collectors.groupingBy(DictionaryDataVo::getDictLabel));

        // 3.查询当前组织的所有模块
        ScreenModuleLibraryListConditionBo libraryListConditionBo = new ScreenModuleLibraryListConditionBo();
        libraryListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        libraryListConditionBo.setDictionaryDataOrganizationId(organizationId);
        List<ScreenModuleLibraryVo> screenModuleLibraryDBVos =
            screenModuleLibraryService.getScreenModuleLibraryListByCondition(libraryListConditionBo);
        Map<Long, List<ScreenModuleLibraryVo>> libraryByGroupDBMap =
            screenModuleLibraryDBVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleGroupType));

        // 4.新增的library和dictionaryData
        List<DictionaryData> addDictionaryDataList = new ArrayList<>();
        List<ScreenModuleLibrary> addScreenModuleLibraryList = new ArrayList<>();
        for (File typeFile : typeFiles) {
            String typeFileName = typeFile.getName();
            if (typeFileName.startsWith(".")) {
                continue;
            }
            List<DictionaryDataVo> dataVos = dictionaryDataMap.get(typeFileName);
            // db分类存在，判断分类里模块是相同
            if (CollectionUtil.isNotEmpty(dataVos)) {
                DictionaryDataVo dictionaryDataVo = dataVos.get(0);
                File[] nameFiles = typeFile.listFiles();
                // 文件分类下的所有模块
                List<String> strings = new ArrayList<>();
                for (File nameFile : nameFiles) {
                    if (nameFile.getName().startsWith(".")) {
                        continue;
                    }
                    strings.add(nameFile.getName());
                }
                // db分类下的所有模块
                List<ScreenModuleLibraryVo> moduleLibraryVos =
                    libraryByGroupDBMap.get(Long.valueOf(dictionaryDataVo.getDictValue()));
                List<String> moduleNames =
                    moduleLibraryVos.stream().map(ScreenModuleLibraryVo::getModuleName).collect(Collectors.toList());
                List<String> moduleNamesCopy = new ArrayList<>(moduleNames);
                // 本地剩余需要删除的模块
                moduleNames.removeAll(strings);
                // 需要添加的模块
                strings.removeAll(moduleNamesCopy);

                // 添加新增模块
                if (CollectionUtil.isNotEmpty(strings)) {
                    for (String string : strings) {
                        for (File nameFile : nameFiles) {
                            if (nameFile.getName().startsWith(".")) {
                                continue;
                            }
                            if (string.equals(nameFile.getName())) {
                                String nameFileName = nameFile.getName();
                                ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                                screenModuleLibrary.setModuleGroupType(Long.valueOf(dictionaryDataVo.getDictValue()));
                                screenModuleLibrary.setIsDelete(StatusEnum.NOTDELETE.getCode());
                                screenModuleLibrary.setModuleName(nameFileName);
                                screenModuleLibrary.setParentScreenModuleLibraryId(
                                    ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
                                addScreenModuleLibraryList.add(screenModuleLibrary);
                            }
                        }
                    }
                }
            } else {
                // db分类不存在，新增分类和模块
                dictValue++;
                dictSort--;
                DictionaryData dictionaryData = new DictionaryData();
                dictionaryData.setDictType(DictionaryType.MODULE_GROUP_TYPE.getValue());
                dictionaryData.setDictValue(Long.toString(dictValue));
                dictionaryData.setDictLabel(typeFileName);
                dictionaryData.setDictSort(dictSort);
                dictionaryData.setCreateTime(new Date());
                dictionaryData.setIsDelete(StatusEnum.NOTDELETE.getCode());
                dictionaryData.setOrganizationId(organizationId);
                dictionaryData.setType(PosterEnums.TYPE_SHOW.getCode());
                addDictionaryDataList.add(dictionaryData);
                File[] nameFiles = typeFile.listFiles();
                for (File nameFile : nameFiles) {
                    if (nameFile.getName().startsWith(".")) {
                        continue;
                    }
                    String nameFileName = nameFile.getName();
                    ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
                    screenModuleLibrary.setModuleGroupType(dictValue);
                    screenModuleLibrary.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    screenModuleLibrary.setModuleName(nameFileName);
                    screenModuleLibrary
                        .setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
                    addScreenModuleLibraryList.add(screenModuleLibrary);
                }
            }
        }
        // 5、更新数据库
        if (CollectionUtil.isNotEmpty(addDictionaryDataList)) {
            screenDictionaryDataService.saveBatch(addDictionaryDataList);
        }
        if (CollectionUtil.isNotEmpty(addScreenModuleLibraryList)) {
            screenModuleLibraryService.saveBatch(addScreenModuleLibraryList);
        }

        // 6 修改模块之后同步修改海报图片
        ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo = new ScreenModuleLibraryMediaBo();
        screenModuleLibraryMediaBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        screenModuleLibraryMediaBo.setDictionaryDataOrganizationId(organizationId);
        List<ScreenModuleLibraryMediaVo> moduleLibraryMediaVos =
            screenModuleLibraryMediaMapper.getScreenModuleLibraryMediaListByCondition(screenModuleLibraryMediaBo);
        Map<Long, List<ScreenModuleLibraryMediaVo>> mediaMap = moduleLibraryMediaVos.stream()
            .collect(Collectors.groupingBy(ScreenModuleLibraryMediaVo::getScreenModuleLibraryId));

        // 同步新增新增模块海报图片
        List<ScreenModuleLibraryMedia> addMedias = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(addScreenModuleLibraryList)) {
            for (File typeFile : typeFiles) {
                if (typeFile.getName().startsWith(".")) {
                    continue;
                }
                File[] nameFiles = typeFile.listFiles();
                for (File nameFile : nameFiles) {
                    if (nameFile.getName().startsWith(".")) {
                        continue;
                    }
                    for (ScreenModuleLibrary screenModuleLibrary : addScreenModuleLibraryList) {
                        if (screenModuleLibrary.getModuleName().equals(nameFile.getName())) {
                            // 新增海报图片
                            File[] mediaFiles = nameFile.listFiles();
                            for (File mediaFile : mediaFiles) {
                                if (mediaFile.getName().startsWith(".")) {
                                    continue;
                                }
                                ScreenModuleLibraryMedia media = new ScreenModuleLibraryMedia();
                                media.setScreenModuleLibraryMediaNameOri(mediaFile.getName());
                                media.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                                media.setFile(mediaFile);
                                addMedias.add(media);
                            }
                        }
                    }
                }
            }
        }

        // 7 同一模块下海报图片的新增删除
        List<ScreenModuleLibraryVo> libraryVos =
            screenModuleLibraryService.getScreenModuleLibraryListByCondition(libraryListConditionBo);
        Map<String, List<ScreenModuleLibraryVo>> libraryMapByName =
            libraryVos.stream().collect(Collectors.groupingBy(ScreenModuleLibraryVo::getModuleName));

        for (File typeFile : typeFiles) {
            if (typeFile.getName().startsWith(".")) {
                continue;
            }
            File[] nameFiles = typeFile.listFiles();
            for (File nameFile : nameFiles) {
                if (nameFile.getName().startsWith(".")) {
                    continue;
                }
                String name = nameFile.getName();
                if (CollectionUtil.isEmpty(libraryMapByName.get(name))) {
                    break;
                }

                ScreenModuleLibraryVo moduleLibraryVo = libraryMapByName.get(name).get(0);
                List<ScreenModuleLibraryMediaVo> mediaVos = mediaMap.get(moduleLibraryVo.getScreenModuleLibraryId());
                File[] mediaFiles = nameFile.listFiles();
                // 新增图片
                List<String> fileNames = new ArrayList<>();
                for (File mediaFile : mediaFiles) {
                    if (mediaFile.getName().startsWith(".")) {
                        continue;
                    }
                    fileNames.add(mediaFile.getName());
                }
                // 已存在图片
                List<String> mediaNames = mediaVos.stream()
                    .map(ScreenModuleLibraryMediaVo::getScreenModuleLibraryMediaNameOri).collect(Collectors.toList());
                List<String> fileNamesCopy = new ArrayList<>(fileNames);
                // 需要添加的文件
                fileNames.removeAll(mediaNames);
                // 需要删除的图片
                mediaNames.removeAll(fileNamesCopy);
                // 添加图片
                for (File mediaFile : mediaFiles) {
                    if (fileNames.contains(mediaFile.getName())) {
                        ScreenModuleLibraryMedia screenModuleLibraryMedia = new ScreenModuleLibraryMedia();
                        screenModuleLibraryMedia.setScreenModuleLibraryId(moduleLibraryVo.getScreenModuleLibraryId());
                        screenModuleLibraryMedia.setScreenModuleLibraryMediaNameOri(mediaFile.getName());
                        screenModuleLibraryMedia.setFile(mediaFile);
                        addMedias.add(screenModuleLibraryMedia);
                    }
                }
            }
        }
        // 更新数据库，上传海报
        if (CollectionUtil.isNotEmpty(addMedias)) {
            uploadMedia(addMedias);
            saveBatch(addMedias);
        }
        return AjaxResult.success();
    }

    /**
     * 上传海报
     *
     * @param addMedias
     * @return void
     * <AUTHOR>
     * @date 2022/10/19 14:22
     */
    private void uploadMedia(List<ScreenModuleLibraryMedia> addMedias) {
        for (ScreenModuleLibraryMedia addMedia : addMedias) {
            File mediaFile = addMedia.getFile();
            // FileInputStream fileInputStream = new FileInputStream(mediaFile);
            // MultipartFile file = new MockMultipartFile(mediaFile.getName(), mediaFile.getName(),
            // MediaType.MULTIPART_FORM_DATA_VALUE, fileInputStream);
            // MultipartFormData multipartFormData = new MultipartFormData();
            // multipartFormData.setFileSize(file.getSize());
            // multipartFormData.setFileBytes(file.getBytes());
            // multipartFormData.setOriginalFilename(file.getOriginalFilename());
            // fileInputStream.close();
            MultipartFile cMultiFile = null;
            try {
                cMultiFile = new MockMultipartFile("file", mediaFile.getName(), null, new FileInputStream(mediaFile));
            } catch (IOException e) {
                log.error("海报文件转换失败");
            }
            AttachmentVo attachmentVo = baseDataService.upload(cMultiFile);
            if (null == attachmentVo) {
                log.error("上传海报失败: " + mediaFile.getName());
                throw new RuntimeException("上传失败");
            }
            addMedia.setScreenContentMediaId(attachmentVo.getFileOid());
            addMedia.setScreenModuleLibraryMediaUrl(attachmentVo.getWebUrl());
            addMedia.setScreenContentMediaMd5(attachmentVo.getMd5());
        }

    }

    @Override
    public AjaxResult addScreenModuleLibraryMedia(ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo) {
        ScreenModuleLibraryMedia screenModuleLibraryMedia = new ScreenModuleLibraryMedia();
        BeanUtils.copyProperties(screenModuleLibraryMediaBo, screenModuleLibraryMedia);
        screenModuleLibraryMedia.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenModuleLibraryMedia)) {
            return AjaxResult.success(screenModuleLibraryMedia);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult addScreenModuleLibraryMediaBatch(List<ScreenModuleLibraryMediaBo> screenModuleLibraryMediaBos) {
        List<ScreenModuleLibraryMedia> libraryMediaList = screenModuleLibraryMediaBos.stream().map(x -> {
            ScreenModuleLibraryMedia libraryMedia = new ScreenModuleLibraryMedia();
            BeanUtils.copyProperties(x, libraryMedia);
            libraryMedia.setIsDelete(StatusEnum.NOTDELETE.getCode());
            return libraryMedia;
        }).collect(Collectors.toList());
        saveBatch(libraryMediaList);
        return AjaxResult.success("新增成功");
    }

    @Override
    public AjaxResult updateScreenModuleLibraryMedia(ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo) {
        ScreenModuleLibraryMedia screenModuleLibraryMedia = new ScreenModuleLibraryMedia();
        BeanUtils.copyProperties(screenModuleLibraryMediaBo, screenModuleLibraryMedia);
        if (updateById(screenModuleLibraryMedia)) {
            return AjaxResult.success("修改成功");
        } else {
            return AjaxResult.fail("修改失败");
        }
    }

    @Override
    public List<ScreenModuleLibraryMediaVo>
        getCurrentDayFestivalModuleLibraryMediaList(ScreenModuleLibraryMediaBo condition) {
        // 获取今天的节日列表
        List<FestivalVo> festivalVos = festivalService.getFestivalDurationListByDate(null);
        if (CollectionUtils.isEmpty(festivalVos)) {
            return Lists.newArrayList();
        }
        List<String> festivalCodes =
            festivalVos.stream().map(FestivalVo::getFestivalCode).distinct().collect(Collectors.toList());
        LabelFestivalRelConditionBo labelFestivalRelConditionBo = new LabelFestivalRelConditionBo();
        labelFestivalRelConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        labelFestivalRelConditionBo.setFestivalCodes(festivalCodes);
        List<LabelFestivalRelVo> labelFestivalRelListByCondition =
            labelFestivalRelService.getLabelFestivalRelListByCondition(labelFestivalRelConditionBo);
        if (CollectionUtils.isEmpty(labelFestivalRelListByCondition)) {
            return Lists.newArrayList();
        }
        List<Long> labelIds = labelFestivalRelListByCondition.stream().map(LabelFestivalRelVo::getLabelId).distinct()
            .collect(Collectors.toList());

        condition.setLabelIds(labelIds);
        condition.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVos =
            screenModuleLibraryMediaMapper.getLabelModuleLibraryMediaList(condition);
        return screenModuleLibraryMediaVos;
    }

    @Override
    public List<ScreenModuleLibraryMediaVo> getLabelModuleLibraryMediaListByShowDeviceId(Long showDeviceId) {
        if (showDeviceId == null) {
            return Lists.newArrayList();
        }
        ShowDeviceLabelRelConditionBo showDeviceLabelRelConditionBo = new ShowDeviceLabelRelConditionBo();
        showDeviceLabelRelConditionBo.setPageNo(SystemConstants.NO_PAGE);
        showDeviceLabelRelConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        showDeviceLabelRelConditionBo.setShowDeviceId(showDeviceId);
        List<ShowDeviceLabelRelVo> showDeviceLabelRelListByCondition =
            showDeviceLabelRelService.getShowDeviceLabelRelListByCondition(showDeviceLabelRelConditionBo);
        if (CollectionUtils.isEmpty(showDeviceLabelRelListByCondition)) {
            return Lists.newArrayList();
        }

        List<Long> labelIds = showDeviceLabelRelListByCondition.stream().map(ShowDeviceLabelRelVo::getLabelId)
            .distinct().collect(Collectors.toList());
        ScreenModuleLibraryMediaBo condition = new ScreenModuleLibraryMediaBo();
        condition.setLabelIds(labelIds);
        condition.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVos =
            screenModuleLibraryMediaMapper.getLabelModuleLibraryMediaList(condition);
        return screenModuleLibraryMediaVos;
    }

    @Override
    public List<ScreenModuleLibraryMediaVo> getDefaultModuleLibraryMediaList() {
        ScreenModuleLibraryMediaBo condition = new ScreenModuleLibraryMediaBo();
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setModuleGroupType(ConstantsConfig.DEFAULT_MODULE_GROUP_TYPE_VERTICAL);
        condition.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaListByCondition =
            screenModuleLibraryMediaMapper.getScreenModuleLibraryMediaListByCondition(condition);
        return screenModuleLibraryMediaListByCondition;
    }

    /**
     * 我创建的海报
     *
     * @param organizationId 组织id
     * @param pattern 1横屏 2 竖屏
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/31 11:44
     */
    // @Override
    // public AjaxResult getPersonPosters(Long organizationId, Integer pattern) {
    // // 1、查询当前用户创建的模块ids
    // ScreenModuleLibraryUserRelConditionBo userCondition = new ScreenModuleLibraryUserRelConditionBo();
    // userCondition.setUserOid(baseDataService.getCurrentUserOid());
    // userCondition.setType(LibraryUserRelTypeEnum.CREATE.getCode());
    // List<ScreenModuleLibraryUserRelVo> moduleLibraryUserRelVos =
    // screenModuleLibraryUserRelService.getScreenModuleLibraryUserRelListByCondition(userCondition);
    // if (CollectionUtils.isEmpty(moduleLibraryUserRelVos)) {
    // return AjaxResult.success();
    // }
    // List<Long> libraryIds = moduleLibraryUserRelVos.stream()
    // .map(ScreenModuleLibraryUserRelVo::getScreenModuleLibraryId).collect(Collectors.toList());
    // // 2、获取用户收藏海报模块列表
    // List<ScreenModuleLibraryCollectVo> collectVos = getMyCollectLibraryVos(organizationId);
    // // 收藏列表转换Map以LibraryId作为key标识
    // Map<Long, List<ScreenModuleLibraryCollectVo>> collectVosMap = new HashMap<>();
    // if (CollectionUtils.isNotEmpty(collectVos)) {
    // collectVosMap = collectVos.stream()
    // .collect(Collectors.groupingBy(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId));
    // }
    // // 3、根据模块id获取海报列表
    // ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo = new ScreenModuleLibraryMediaBo();
    // screenModuleLibraryMediaBo.setScreenModuleLibraryIds(libraryIds);
    // screenModuleLibraryMediaBo.setOrderBySort("orderBy");
    // // 多余条件，library_source=1,moduleType=3
    // // screenModuleLibraryMediaBo.setModuleDataType(ModuleDataTypeEnum.POSTER_NOT_SHOW.getCode());
    //
    // // 查询横竖版
    // screenModuleLibraryMediaBo.setLibraryPattern(pattern);
    // List<ScreenModuleLibraryMediaVo> posterList =
    // screenModuleLibraryMediaMapper.getPosterList(screenModuleLibraryMediaBo);
    //
    // // 4、封装返回值
    // List<LinkedHashMap> returnList = new ArrayList<>();
    // formatLibraryWithLibraryIdSort(returnList, libraryIds, collectVosMap, posterList,null);
    // return AjaxResult.success(returnList);
    // }

    /**
     * 根据推荐排序并返回新的排序后的list
     *
     * @param posterList the poster list
     * @param comparator the comparator
     * @return the sort list
     * <AUTHOR>
     * @date 2023 -03-02 15:55:02
     */
    @Deprecated
    private List<ScreenModuleLibraryMediaVo> getSortList(List<ScreenModuleLibraryMediaVo> posterList) {
        if (CollectionUtils.isEmpty(posterList)) {
            return posterList;
        }
        List<Long> screenModuleLibraryIds = posterList.stream()
            .map(ScreenModuleLibraryMediaVo::getScreenModuleLibraryId).distinct().collect(Collectors.toList());
        // 获取今天的节日列表
        List<FestivalVo> festivalVos = festivalService.getFestivalDurationListByDate(null);
        if (CollectionUtils.isEmpty(festivalVos)) {
            return posterList;
        }
        List<Long> festivalIds =
            festivalVos.stream().map(FestivalVo::getFestivalId).distinct().collect(Collectors.toList());
        // 查询今天节日的标签->查询包含标签的的海报。组装出海报 - 节日类型的map
        LabelLibraryRelConditionBo condition = new LabelLibraryRelConditionBo();
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setScreenModuleLibraryIds(screenModuleLibraryIds);
        condition.setFestivalIds(festivalIds);
        List<LabelLibraryRelVo> labelLibraryRelListByCondition =
            labelLibraryRelService.getLabelLibraryRelListByConditionOfLabel(condition);
        if (CollectionUtils.isEmpty(labelLibraryRelListByCondition)) {
            return posterList;
        }
        labelLibraryRelListByCondition.stream().forEach(labelLibraryRelVo -> {
            labelLibraryRelVo
                .setFestivalIds(StringKit.splitString2ListLong(labelLibraryRelVo.getFestivalIdConcat(), null));
            labelLibraryRelVo
                .setFestivalTypes(StringKit.splitString2ListInteger(labelLibraryRelVo.getFestivalTypesConcat(), null));
            labelLibraryRelVo.setLabelIds(StringKit.splitString2ListLong(labelLibraryRelVo.getLabelIdConcat(), null));
        });
        Map<Long, List<Integer>> ScreenModuleLibraryIdAndTypeMap = labelLibraryRelListByCondition.stream().collect(
            Collectors.toMap(LabelLibraryRelVo::getScreenModuleLibraryId, LabelLibraryRelVo::getFestivalTypes));

        // 多重比较器
        Comparator<ScreenModuleLibraryMediaVo> comparatorTradition = (o1, o2) -> {
            List<Integer> festivalTypesO1 = ScreenModuleLibraryIdAndTypeMap.get(o1.getScreenModuleLibraryId());
            List<Integer> festivalTypesO2 = ScreenModuleLibraryIdAndTypeMap.get(o2.getScreenModuleLibraryId());
            Integer code = FestivalEnum.TRADITION.getCode();
            return comparatorJudge(festivalTypesO1, festivalTypesO2, code);
        };
        Comparator<ScreenModuleLibraryMediaVo> comparatorInternational = (o1, o2) -> {
            List<Integer> festivalTypesO1 = ScreenModuleLibraryIdAndTypeMap.get(o1.getScreenModuleLibraryId());
            List<Integer> festivalTypesO2 = ScreenModuleLibraryIdAndTypeMap.get(o2.getScreenModuleLibraryId());
            Integer code = FestivalEnum.INTERNATIONAL.getCode();
            return comparatorJudge(festivalTypesO1, festivalTypesO2, code);
        };
        Comparator<ScreenModuleLibraryMediaVo> comparatorSolar = (o1, o2) -> {
            List<Integer> festivalTypesO1 = ScreenModuleLibraryIdAndTypeMap.get(o1.getScreenModuleLibraryId());
            List<Integer> festivalTypesO2 = ScreenModuleLibraryIdAndTypeMap.get(o2.getScreenModuleLibraryId());
            Integer code = FestivalEnum.SOLAR_TERMS.getCode();
            return comparatorJudge(festivalTypesO1, festivalTypesO2, code);
        };
        Comparator<ScreenModuleLibraryMediaVo> comparatorCustom = (o1, o2) -> {
            List<Integer> festivalTypesO1 = ScreenModuleLibraryIdAndTypeMap.get(o1.getScreenModuleLibraryId());
            List<Integer> festivalTypesO2 = ScreenModuleLibraryIdAndTypeMap.get(o2.getScreenModuleLibraryId());
            Integer code = FestivalEnum.CUSTOMIZE.getCode();
            return comparatorJudge(festivalTypesO1, festivalTypesO2, code);
        };

        return posterList.stream().sorted(comparatorTradition.thenComparing(comparatorSolar)
            .thenComparing(comparatorInternational).thenComparing(comparatorCustom)).collect(Collectors.toList());
    }

    /**
     * 比较器的判断逻辑
     * 
     * @param festivalTypesO1 比较器的提取参数1
     * @param festivalTypesO2 比较器提取参数2
     * @param code 节日类型
     * @return
     */
    private int comparatorJudge(List<Integer> festivalTypesO1, List<Integer> festivalTypesO2, Integer code) {
        if (CollectionUtils.isNotEmpty(festivalTypesO1) && festivalTypesO1.contains(code)
            && (CollectionUtils.isEmpty(festivalTypesO2) || !festivalTypesO2.contains(code))) {
            // 表示不需要交换，有标签的本身就在前面：o1在o2前面
            return -1;
        } else if ((CollectionUtils.isEmpty(festivalTypesO1) || !festivalTypesO1.contains(code))
            && CollectionUtils.isNotEmpty(festivalTypesO2) && festivalTypesO2.contains(code)) {
            // 1表示需要交换两者，把有标签的交换到前面来
            return 1;
        } else {
            return 0;
        }
    }
}
