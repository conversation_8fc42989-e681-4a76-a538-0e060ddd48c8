package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.consts.ScreenModuleConstants;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto;
import com.fh.cloud.screen.service.device.service.IShowDeviceLabelRelService;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.LabelEnums;
import com.fh.cloud.screen.service.enums.PosterEnums;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.service.ILabelLibraryRelService;
import com.fh.cloud.screen.service.label.service.ILabelService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryStatisticsVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMapper;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.fh.cloud.screen.service.utils.StringKit;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模块库表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Service
public class ScreenModuleLibraryServiceImpl extends ServiceImpl<ScreenModuleLibraryMapper, ScreenModuleLibrary>
    implements IScreenModuleLibraryService {

    @Resource
    private ScreenModuleLibraryMapper screenModuleLibraryMapper;
    @Resource
    private IScreenModuleLibraryMediaService screenModuleLibraryMediaService;
    @Resource
    private ILabelLibraryRelService labelLibraryRelService;
    @Resource
    private IShowDeviceService showDeviceService;
    @Resource
    private IShowDeviceLabelRelService showDeviceLabelRelService;
    @Resource
    private ILabelService labelService;

    @Override
    public List<ScreenModuleLibraryVo>
        getScreenModuleLibraryListByCondition(ScreenModuleLibraryListConditionBo condition) {
        return screenModuleLibraryMapper.getScreenModuleLibraryListByCondition(condition);
    }

    @Override
    public boolean addScreenModuleLibrary(ScreenModuleLibraryBo screenModuleLibraryBo) {
        // 设置默认顺序
        LambdaQueryWrapper<ScreenModuleLibrary> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenModuleLibrary::getModuleGroupType, screenModuleLibraryBo.getModuleGroupType());
        lqw.eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.orderByAsc(ScreenModuleLibrary::getLibrarySort);
        List<ScreenModuleLibrary> list = this.list(lqw);
        int sort = 1;
        if (list.size() != 0 && null != list.get(0).getLibrarySort()) {
            sort = list.get(0).getLibrarySort() - 1;
        }
        screenModuleLibraryBo.setLibrarySort(sort);
        // 新增
        ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
        BeanUtils.copyProperties(screenModuleLibraryBo, screenModuleLibrary);
        screenModuleLibrary.setIsDelete(StatusEnum.NOTDELETE.getCode());
        screenModuleLibrary.setIsPoster(PosterEnums.POST_IS.getCode());
        // 赋默认值
        // if (null == screenModuleLibrary.getLibraryPattern()) {
        // screenModuleLibrary.setLibraryPattern(PosterEnums.PATTERN_VERTICAL.getCode());
        // }
        // if (null == screenModuleLibrary.getPosterSource()) {
        // screenModuleLibrary.setPosterSource(PosterEnums.SOURCE_SYSTEM.getCode());
        // }
        // 判断是否是用户上传海报，生成关联表
        // if (StringUtils.isNotBlank(screenModuleLibrary.getCreateBy())) {
        // screenModuleLibrary.setPosterSource(PosterEnums.SOURCE_USER.getCode());
        // }
        boolean save = save(screenModuleLibrary);
        if (save) {
            screenModuleLibraryBo.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
            // 判断是否是用户上传海报，生成关联表
            // if (PosterEnums.SOURCE_USER.getCode().equals(screenModuleLibrary.getPosterSource())) {
            // ScreenModuleLibraryUserRelDto libraryUserRelDto = new ScreenModuleLibraryUserRelDto();
            // libraryUserRelDto.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
            // libraryUserRelDto.setUserOid(screenModuleLibrary.getCreateBy());
            // screenModuleLibraryUserRelService.save(libraryUserRelDto);
            // }
        }
        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateScreenModuleLibrary(ScreenModuleLibraryBo screenModuleLibraryBo) {
        ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
        BeanUtils.copyProperties(screenModuleLibraryBo, screenModuleLibrary);
        updateById(screenModuleLibrary);
        // 同步删除海报图片
        if (StatusEnum.ISDELETE.getCode().equals(screenModuleLibraryBo.getIsDelete())) {
            LambdaUpdateWrapper<ScreenModuleLibraryMedia> luw = new LambdaUpdateWrapper<>();
            luw.eq(ScreenModuleLibraryMedia::getScreenModuleLibraryId,
                screenModuleLibraryBo.getScreenModuleLibraryId());
            luw.eq(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.NOTDELETE.getCode());
            luw.set(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.ISDELETE.getCode());
            screenModuleLibraryMediaService.update(luw);
            // 同步删除关联标签关系
            LambdaUpdateWrapper<LabelLibraryRelDto> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(LabelLibraryRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
            updateWrapper.eq(LabelLibraryRelDto::getScreenModuleLibraryId,
                screenModuleLibrary.getScreenModuleLibraryId());
            labelLibraryRelService.update(updateWrapper);
        }
        return true;
    }

    @Override
    public ScreenModuleLibraryVo getDetail(Long screenModuleLibraryId) {
        if (screenModuleLibraryId == null) {
            return null;
        }
        LambdaQueryWrapper<ScreenModuleLibrary> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenModuleLibrary::getScreenModuleLibraryId, screenModuleLibraryId);
        ScreenModuleLibrary screenModuleLibrary = getOne(lqw);
        ScreenModuleLibraryVo screenModuleLibraryVo = new ScreenModuleLibraryVo();
        BeanUtils.copyProperties(screenModuleLibrary, screenModuleLibraryVo);
        return screenModuleLibraryVo;
    }

    /**
     * 海报条件查询主题列表 (暂时无排序，采用运营默认排序)
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 16:43
     */
    @Override
    public AjaxResult getPosterList(ScreenModuleLibraryListConditionBo conditionBo) {
        // 1 检索标签关联的screenModuleLibraryIds
        List<Long> screenModuleLibraryIds =
            getScreenModuleLibraryIdsByLabelIds(conditionBo.getLabelIds(), conditionBo.isQueryLabelJust());
        if (CollectionUtils.isNotEmpty(conditionBo.getLabelIds()) && CollectionUtils.isEmpty(screenModuleLibraryIds)) {
            return AjaxResult.success();
        }

        // 2 根据创建的模块主题 id获取模块主题列表
        return getConfigLabelScreenModuleLibraryResult(conditionBo, screenModuleLibraryIds);

    }

    @Override
    public List<ScreenModuleLibraryVo> getPosterListOriginal(ScreenModuleLibraryListConditionBo conditionBo) {
        return baseMapper.getPosterList(conditionBo);
    }

    /**
     * 根据labelIds查询关联的screenModuleLibraryIds
     * 
     * @param labelIds
     * @param isQueryJust 此海报的所有标签 >=条件标签列表，此主题海报id为条件----> 该逻辑是全包含查询，即多个id必须同时满足的数据
     * @return
     */
    private List<Long> getScreenModuleLibraryIdsByLabelIds(List<Long> labelIdsParam, boolean isQueryJust) {
        // 1 检索标签关联的screenModuleLibraryIds
        List<Long> screenModuleLibraryIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(labelIdsParam)) {
            return screenModuleLibraryIds;
        }

        // 查询数据返回
        LambdaQueryWrapper<LabelLibraryRelDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.in(LabelLibraryRelDto::getLabelId, labelIdsParam);
        lqw.select(LabelLibraryRelDto::getScreenModuleLibraryId, LabelLibraryRelDto::getLabelId);
        List<LabelLibraryRelDto> labelLibraryRelDtos = labelLibraryRelService.list(lqw);
        if (CollectionUtils.isEmpty(labelLibraryRelDtos)) {
            return screenModuleLibraryIds;
        }
        // 多标签逻辑与关系
        Map<Long, List<LabelLibraryRelDto>> labelLibraryMap =
            labelLibraryRelDtos.stream().collect(Collectors.groupingBy(LabelLibraryRelDto::getScreenModuleLibraryId));

        // 所有标签关联的每一个海报主题
        for (Long libraryIdKey : labelLibraryMap.keySet()) {
            List<LabelLibraryRelDto> labelLibraryRelBeanList = labelLibraryMap.get(libraryIdKey);
            List<Long> labelIds =
                labelLibraryRelBeanList.stream().map(LabelLibraryRelDto::getLabelId).collect(Collectors.toList());
            // 此海报的所有标签 >=条件标签列表，此主题海报id为条件----> 该逻辑是全包含查询，即多个id必须同时满足的数据
            if (isQueryJust) {
                if (labelIds.containsAll(labelIdsParam)) {
                    screenModuleLibraryIds.add(libraryIdKey);
                }
            } else {
                screenModuleLibraryIds.add(libraryIdKey);
            }
        }
        return screenModuleLibraryIds;
    }

    /**
     * 只根据selIds查询主题海报并返回
     * 
     * @param conditionBo the condition bo
     * @return
     */
    @Override
    public AjaxResult getPosterListSel(ScreenModuleLibraryListConditionBo conditionBo) {
        return getModuleLibraryPageMapSel(conditionBo, Lists.newArrayList());
    }

    /**
     * 设备关联标签关联海报主题列表(订阅海报列表)
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 9:42
     */
    @Override
    public AjaxResult getDevicePosterListByDeviceNumber(ScreenModuleLibraryListConditionBo conditionBo) {
        ShowDevice byDeviceNum = showDeviceService.getByDeviceNum(conditionBo.getDeviceNumber());
        if (null == byDeviceNum) {
            return AjaxResult.fail("设备不存在");
        }
        // 设备关联的标签
        LambdaQueryWrapper<ShowDeviceLabelRelDto> lqwd = new LambdaQueryWrapper<>();
        lqwd.eq(ShowDeviceLabelRelDto::getShowDeviceId, byDeviceNum.getShowDeviceId());
        lqwd.eq(ShowDeviceLabelRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqwd.select(ShowDeviceLabelRelDto::getLabelId);
        List<ShowDeviceLabelRelDto> list = showDeviceLabelRelService.list(lqwd);
        if (list.isEmpty()) {
            return AjaxResult.success();
        }
        // 标签关联的海报主题
        List<Long> labelIds = list.stream().map(ShowDeviceLabelRelDto::getLabelId).collect(Collectors.toList());
        LambdaQueryWrapper<LabelLibraryRelDto> lqwl = new LambdaQueryWrapper<>();
        lqwl.in(LabelLibraryRelDto::getLabelId, labelIds);
        lqwl.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqwl.select(LabelLibraryRelDto::getScreenModuleLibraryId, LabelLibraryRelDto::getLabelId);
        List<LabelLibraryRelDto> labelLibraryRelDtos = labelLibraryRelService.list(lqwl);
        if (CollectionUtils.isEmpty(labelLibraryRelDtos)) {
            return AjaxResult.success();
        }
        // 封装返回
        List<Long> libraryIds =
            labelLibraryRelDtos.stream().map(LabelLibraryRelDto::getScreenModuleLibraryId).collect(Collectors.toList());
        return getConfigLabelScreenModuleLibraryResult(conditionBo, libraryIds);
    }

    /**
     * 运营海报库列表（未配置标签、已配置标签）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 13:53
     */
    @Override
    public AjaxResult getLabelPosterListByCondition(ScreenModuleLibraryListConditionBo conditionBo) {
        // 系统海报
        conditionBo.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        // 已配置标签-列表
        if (PosterEnums.QUERY_LABEL.getCode().equals(conditionBo.getQueryLabelConfig())) {
            // 已配置标签-根据标签查询
            List<Long> screenModuleLibraryIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(conditionBo.getLabelIds())) {
                screenModuleLibraryIds =
                    getScreenModuleLibraryIdsByLabelIds(conditionBo.getLabelIds(), conditionBo.isQueryLabelJust());
            } else {
                screenModuleLibraryIds = getConfigLabelForPoster(null);
            }
            if (CollectionUtils.isEmpty(screenModuleLibraryIds)) {
                return AjaxResult.success();
            }
            conditionBo.setScreenModuleLibraryIds(screenModuleLibraryIds);
            // 查询配置标签的海报张数统计信息
            return getConfigLabelScreenModuleLibraryResult(conditionBo, screenModuleLibraryIds);
        }

        // 未配置标签-列表
        if (PosterEnums.QUERY_NOT_LABEL.getCode().equals(conditionBo.getQueryLabelConfig())) {
            return getNotConfigLabelScreenModuleLibraryResult(conditionBo);
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult getLabelPosterStatistics(ScreenModuleLibraryListConditionBo conditionBo) {
        // 系统海报
        conditionBo.setDictionaryDataOrganizationId(ConstantsLong.NUM_0);
        // 已配置标签-列表
        if (PosterEnums.QUERY_LABEL.getCode().equals(conditionBo.getQueryLabelConfig())) {
            // 已配置标签-根据标签查询
            List<Long> screenModuleLibraryIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(conditionBo.getLabelIds())) {
                screenModuleLibraryIds =
                    getScreenModuleLibraryIdsByLabelIds(conditionBo.getLabelIds(), conditionBo.isQueryLabelJust());
            } else {
                screenModuleLibraryIds = getConfigLabelForPoster(null);
            }
            if (CollectionUtils.isEmpty(screenModuleLibraryIds)) {
                return AjaxResult.success();
            }
            conditionBo.setScreenModuleLibraryIds(screenModuleLibraryIds);
            // 查询配置标签的海报张数统计信息
            return getConfigLabelScreenModuleLibraryStatistics(conditionBo, screenModuleLibraryIds);
        }

        // 未配置标签-列表
        if (PosterEnums.QUERY_NOT_LABEL.getCode().equals(conditionBo.getQueryLabelConfig())) {
            // 查询未配置标签的海报张数统计信息
            return getNotConfigLabelScreenModuleLibraryStatistics(conditionBo);
        }
        return AjaxResult.success();
    }

    /**
     * 运营海报库-未配置标签的海报列表查询
     *
     * @param conditionBo the condition bo
     * @return not config label screen module library result
     * <AUTHOR>
     * @date 2023 -06-05 14:19:57
     */
    private AjaxResult getNotConfigLabelScreenModuleLibraryResult(ScreenModuleLibraryListConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())) {
            List<ScreenModuleLibraryVo> screenModuleLibraryVos =
                this.baseMapper.getNotConfigLabelScreenModuleLibrary(conditionBo);
            map.put("list", screenModuleLibraryVos);
        } else {
            PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize());
            PageInfo<ScreenModuleLibraryVo> pageInfo =
                new PageInfo<>(this.baseMapper.getNotConfigLabelScreenModuleLibrary(conditionBo));
            map.put("list", pageInfo.getList());
            map.put("total", pageInfo.getTotal());
        }
        // 仅复用方法里面的查询出海报库图片的功能
        posterAssembling(conditionBo, (List<ScreenModuleLibraryVo>)map.get("list"));
        return AjaxResult.success(map);
    }

    /**
     * 运营海报库-未配置标签的海报列表查询
     *
     * @param conditionBo the condition bo
     * @return not config label screen module library result
     * <AUTHOR>
     * @date 2023 -06-05 14:19:57
     */
    private AjaxResult getNotConfigLabelScreenModuleLibraryStatistics(ScreenModuleLibraryListConditionBo conditionBo) {
        ScreenModuleLibraryStatisticsVo notConfigLabelScreenModuleLibraryStatistics =
            screenModuleLibraryMapper.getNotConfigLabelScreenModuleLibraryStatistics(conditionBo);
        return AjaxResult.success(notConfigLabelScreenModuleLibraryStatistics);
    }

    /**
     * 获取标签关联的海报库主题id列表，返回数据库里面所有有标签的海报主题id集合
     *
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2023/5/10 10:03
     */
    private List<Long> getConfigLabelForPoster(Long organizationId) {
        if (organizationId == null) {
            organizationId = ConstantsLong.NUM_0;
        }
        List<Long> libraryIds = new ArrayList<>();
        // 获取海报库标签
        LambdaQueryWrapper<LabelDto> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LabelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lambdaQueryWrapper.eq(LabelDto::getType, LabelEnums.POSTER.getCode());
        lambdaQueryWrapper.eq(LabelDto::getOrganizationId, organizationId);
        lambdaQueryWrapper.select(LabelDto::getLabelId);
        List<LabelDto> labelDtoList = labelService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(labelDtoList)) {
            return libraryIds;
        }
        List<Long> labelIds = labelDtoList.stream().map(LabelDto::getLabelId).collect(Collectors.toList());
        // 标签关联主题ids
        LambdaQueryWrapper<LabelLibraryRelDto> lqw = new LambdaQueryWrapper<>();
        lqw.in(LabelLibraryRelDto::getLabelId, labelIds);
        lqw.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        List<LabelLibraryRelDto> labelLibraryRelDtos = labelLibraryRelService.list(lqw);
        if (CollectionUtils.isNotEmpty(labelLibraryRelDtos)) {
            libraryIds = labelLibraryRelDtos.stream().map(LabelLibraryRelDto::getScreenModuleLibraryId).distinct()
                .collect(Collectors.toList());
        }
        return libraryIds;
    }

    /**
     * 新增或编辑主题海报
     *
     * @param screenModuleLibraryBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/9 14:04
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateLibrary(ScreenModuleLibraryBo screenModuleLibraryBo) {
        ScreenModuleLibrary screenModuleLibrary = new ScreenModuleLibrary();
        BeanUtils.copyProperties(screenModuleLibraryBo, screenModuleLibrary);
        List<LabelBo> labelBos = screenModuleLibraryBo.getLabelBos();
        List<ScreenModuleLibraryMediaBo> libraryMediaBos = screenModuleLibraryBo.getScreenModuleLibraryMediaBos();

        // 新增
        if (null == screenModuleLibrary.getScreenModuleLibraryId()) {
            int sort = getSortByConfigLabelPoster(labelBos);
            screenModuleLibrary.setParentScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
            screenModuleLibrary.setIsPoster(PosterEnums.POST_IS.getCode());
            screenModuleLibrary.setLibrarySort(sort);
            screenModuleLibrary.setModuleGroupType(ConstantsLong.NUM_0);
            save(screenModuleLibrary);
            // 新增关联海报图片
            if (CollectionUtils.isNotEmpty(libraryMediaBos)) {
                libraryMediaBos
                    .forEach(x -> x.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId()));
                screenModuleLibraryMediaService.addScreenModuleLibraryMediaBatch(libraryMediaBos);
            }
            // 新增关联标签
            if (CollectionUtils.isNotEmpty(labelBos)) {
                List<LabelLibraryRelDto> labelLibraryRelDtos = new ArrayList<>();
                LabelLibraryRelDto labelLibraryRelDto = null;
                for (LabelBo labelBo : labelBos) {
                    labelLibraryRelDto = new LabelLibraryRelDto();
                    labelLibraryRelDto.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                    labelLibraryRelDto.setLabelId(labelBo.getLabelId());
                    labelLibraryRelDtos.add(labelLibraryRelDto);
                }
                labelLibraryRelService.saveBatch(labelLibraryRelDtos);
            }
            return AjaxResult.success(screenModuleLibrary);
        }

        // 修改 主题关联海报图片
        LambdaQueryWrapper<ScreenModuleLibraryMedia> mediaLambdaQueryWrapper = new LambdaQueryWrapper<>();
        mediaLambdaQueryWrapper.eq(ScreenModuleLibraryMedia::getScreenModuleLibraryId,
            screenModuleLibrary.getScreenModuleLibraryId());
        mediaLambdaQueryWrapper.eq(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.NOTDELETE);
        // 传参为空，删除所有图片
        if (CollectionUtils.isEmpty(libraryMediaBos)) {
            ScreenModuleLibraryMedia screenModuleLibraryMedia = new ScreenModuleLibraryMedia();
            screenModuleLibraryMedia.setIsDelete(StatusEnum.ISDELETE.getCode());
            screenModuleLibraryMediaService.update(screenModuleLibraryMedia, mediaLambdaQueryWrapper);
        } else { // 传参不为空，修改
            libraryMediaBos.forEach(x -> x.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId()));
            List<ScreenModuleLibraryMedia> screenModuleLibraryMediaPojoList =
                screenModuleLibraryMediaService.list(mediaLambdaQueryWrapper);
            // 已有为空，新增
            if (CollectionUtils.isEmpty(screenModuleLibraryMediaPojoList)) {
                screenModuleLibraryMediaService.addScreenModuleLibraryMediaBatch(libraryMediaBos);
            } else { // 已有不为空，修改
                List<ScreenModuleLibraryMediaBo> addMediaList = new ArrayList<>();
                List<ScreenModuleLibraryMedia> delMediaList = new ArrayList<>(screenModuleLibraryMediaPojoList);
                List<ScreenModuleLibraryMedia> updateMediaList = Lists.newArrayList();
                for (ScreenModuleLibraryMediaBo libraryMediaBo : libraryMediaBos) {
                    for (ScreenModuleLibraryMedia screenModuleLibraryMedia : screenModuleLibraryMediaPojoList) {
                        // 新增的记录
                        if (null == libraryMediaBo.getScreenModuleLibraryMediaId()) {
                            addMediaList.add(libraryMediaBo);
                            break;
                        }
                        // 修改的记录
                        if (libraryMediaBo.getScreenModuleLibraryMediaId()
                            .equals(screenModuleLibraryMedia.getScreenModuleLibraryMediaId())) {
                            // 从删除中剔除
                            delMediaList.remove(screenModuleLibraryMedia);
                            // 应该更新信息(例如sort信息)
                            screenModuleLibraryMedia.setMediaSort(libraryMediaBo.getMediaSort());
                            updateMediaList.add(screenModuleLibraryMedia);
                            break;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(delMediaList)) {
                    delMediaList.forEach(x -> x.setIsDelete(StatusEnum.ISDELETE.getCode()));
                    screenModuleLibraryMediaService.updateBatchById(delMediaList);
                }
                if (CollectionUtils.isNotEmpty(addMediaList)) {
                    addMediaList
                        .forEach(x -> x.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId()));
                    screenModuleLibraryMediaService.addScreenModuleLibraryMediaBatch(addMediaList);
                }
                if (CollectionUtils.isNotEmpty(updateMediaList)) {
                    screenModuleLibraryMediaService.updateBatchById(updateMediaList);
                }
            }
        }
        // 修改 主题关联标签（包括顺序）
        LambdaUpdateWrapper<LabelLibraryRelDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LabelLibraryRelDto::getScreenModuleLibraryId, screenModuleLibrary.getScreenModuleLibraryId());
        updateWrapper.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        int count = labelLibraryRelService.count(updateWrapper);
        updateWrapper.set(LabelLibraryRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        labelLibraryRelService.update(updateWrapper);
        if (CollectionUtils.isNotEmpty(labelBos)) {
            List<LabelLibraryRelDto> labelLibraryRelDtoList = new ArrayList<>();
            labelBos.forEach(labelBo -> {
                LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
                labelLibraryRelDto.setScreenModuleLibraryId(screenModuleLibrary.getScreenModuleLibraryId());
                labelLibraryRelDto.setLabelId(labelBo.getLabelId());
                labelLibraryRelDtoList.add(labelLibraryRelDto);
            });
            labelLibraryRelService.saveBatch(labelLibraryRelDtoList);
            if (count == 0) {
                // 无标签转有标签首位
                int sort = getSortByConfigLabelPoster(labelBos);
                screenModuleLibrary.setLibrarySort(sort);

            }
        } else if (count > 0) {
            // 有标签转无标签首位
            int sort = getSortByConfigLabelPoster(null);
            screenModuleLibrary.setLibrarySort(sort);
        }
        // 修改
        updateById(screenModuleLibrary);
        return AjaxResult.success(screenModuleLibrary);
    }

    /**
     * 获取海报首位顺序，区分是否绑定标签的主题
     *
     * @param labelBos 是否绑定标签的主题
     * @return int
     * <AUTHOR>
     * @date 2023/5/24 17:22
     */
    private int getSortByConfigLabelPoster(List<LabelBo> labelBos) {
        int sort = ConstantsInteger.NUM_1;
        Long organizationId = ConstantsLong.NUM_0;
        if (CollectionUtils.isNotEmpty(labelBos)) {
            LabelDto labelDto = labelService.getById(labelBos.get(ConstantsInteger.NUM_0).getLabelId());
            organizationId = labelDto.getOrganizationId();
        }
        List<Long> libraryIds = getConfigLabelForPoster(organizationId);
        // 配置标签的主题 顺序第一位
        if (CollectionUtils.isNotEmpty(labelBos)) {
            if (CollectionUtils.isNotEmpty(libraryIds)) {
                LambdaQueryWrapper<ScreenModuleLibrary> lqw = new LambdaQueryWrapper<>();
                lqw.in(ScreenModuleLibrary::getScreenModuleLibraryId, libraryIds);
                lqw.eq(ScreenModuleLibrary::getIsPoster, PosterEnums.POST_IS.getCode());
                lqw.orderByAsc(ScreenModuleLibrary::getLibrarySort);
                List<ScreenModuleLibrary> screenModuleLibraryList = this.list(lqw);
                if (CollectionUtils.isNotEmpty(screenModuleLibraryList)) {
                    sort = screenModuleLibraryList.get(0).getLibrarySort() - 1;
                }
            }
        } else {// 未配置标签的主题 顺序第一位
            ScreenModuleLibraryListConditionBo conditionBo = new ScreenModuleLibraryListConditionBo();
            conditionBo.setIsPoster(PosterEnums.POST_IS.getCode());
            if (CollectionUtils.isNotEmpty(libraryIds)) {
                conditionBo.setNotInScreenModuleLibraryIds(libraryIds);
            }
            List<ScreenModuleLibraryVo> posterList = baseMapper.getNotConfigLabelScreenModuleLibrary(null);
            if (CollectionUtils.isNotEmpty(posterList)) {
                sort = posterList.get(0).getLibrarySort() - 1;
            }
        }
        return sort;
    }

    /**
     * 根据模块ids获取海报分页map，最终根据SelIds和libraryIds查询返回主题海报，必有是关联了标签的海报
     *
     * @param conditionBo，这里面也可以指定SelIds
     * @param libraryIds 指定的主题id
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/6 17:23
     */
    private AjaxResult getConfigLabelScreenModuleLibraryResult(ScreenModuleLibraryListConditionBo conditionBo,
        List<Long> libraryIds) {
        // 查询所有主题
        Map<String, Object> map = new HashMap<>();
        conditionBo.setIsPoster(PosterEnums.POST_IS.getCode());
        conditionBo.setScreenModuleLibraryIds(libraryIds);
        List<Long> selScreenModuleLibraryIds =
            StringKit.splitString2ListLong(conditionBo.getScreenModuleLibrarySelIds(), null);
        if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())) {
            conditionBo.getScreenModuleLibraryIds().addAll(selScreenModuleLibraryIds);
        }
        if (StringUtil.isNotBlank(conditionBo.getModuleName())) {
            conditionBo.setModuleName(FuzzyQueryUtil.transferMean(conditionBo.getModuleName()));
        }
        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())) {
            List<ScreenModuleLibraryVo> posterList = this.baseMapper.getPosterList(conditionBo);
            if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())
                && conditionBo.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                // 排序
                posterList = sortResultWithScreenModuleLibrarySelIds(selScreenModuleLibraryIds, posterList);
            }
            map.put("list", posterList);
            map.put("total", posterList.size());
        } else {
            PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize());
            PageInfo<ScreenModuleLibraryVo> pageInfo = new PageInfo<>(this.baseMapper.getPosterList(conditionBo));
            List<ScreenModuleLibraryVo> list = pageInfo.getList();
            if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())
                && conditionBo.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                // 排序
                list = sortResultWithScreenModuleLibrarySelIds(selScreenModuleLibraryIds, list);
            }
            map.put("list", list);
            map.put("total", pageInfo.getTotal());
        }
        List<ScreenModuleLibraryVo> posterList = (List<ScreenModuleLibraryVo>)map.get("list");
        if (CollectionUtils.isEmpty(posterList)) {
            return AjaxResult.success(map);
        }

        // 组装图片，及是否收藏标识
        posterAssembling(conditionBo, posterList);
        return AjaxResult.success(map);
    }

    /**
     * 根据模块ids获取海报分页map，最终根据SelIds和libraryIds查询返回主题海报，必有是关联了标签的海报
     *
     * @param conditionBo，这里面也可以指定SelIds
     * @param libraryIds 指定的主题id
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/6 17:23
     */
    private AjaxResult getConfigLabelScreenModuleLibraryStatistics(ScreenModuleLibraryListConditionBo conditionBo,
        List<Long> libraryIds) {
        conditionBo.setIsPoster(PosterEnums.POST_IS.getCode());
        conditionBo.setScreenModuleLibraryIds(libraryIds);
        List<Long> selScreenModuleLibraryIds =
            StringKit.splitString2ListLong(conditionBo.getScreenModuleLibrarySelIds(), null);
        if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())) {
            conditionBo.getScreenModuleLibraryIds().addAll(selScreenModuleLibraryIds);
        }
        if (StringUtil.isNotBlank(conditionBo.getModuleName())) {
            conditionBo.setModuleName(FuzzyQueryUtil.transferMean(conditionBo.getModuleName()));
        }
        ScreenModuleLibraryStatisticsVo configLabelScreenModuleLibraryStatistics =
            screenModuleLibraryMapper.getConfigLabelScreenModuleLibraryStatistics(conditionBo);
        return AjaxResult.success(configLabelScreenModuleLibraryStatistics);
    }

    /**
     * 根据模块ids获取海报分页map，最终根据SelIds和libraryIds查询返回主题海报，只根据selIds查询。不关心是否有标签
     *
     * @param conditionBo，这里面也可以指定SelIds
     * @param libraryIds 指定的主题id
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/6 17:23
     */
    private AjaxResult getModuleLibraryPageMapSel(ScreenModuleLibraryListConditionBo conditionBo,
        List<Long> libraryIds) {
        // 查询所有主题
        Map<String, Object> map = new HashMap<>();
        conditionBo.setIsPoster(PosterEnums.POST_IS.getCode());
        conditionBo.setScreenModuleLibraryIds(libraryIds);
        List<Long> selScreenModuleLibraryIds =
            StringKit.splitString2ListLong(conditionBo.getScreenModuleLibrarySelIds(), null);
        if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())) {
            conditionBo.getScreenModuleLibraryIds().addAll(selScreenModuleLibraryIds);
        }
        if (StringUtil.isNotBlank(conditionBo.getModuleName())) {
            conditionBo.setModuleName(FuzzyQueryUtil.transferMean(conditionBo.getModuleName()));
        }
        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())) {
            List<ScreenModuleLibraryVo> posterList = this.baseMapper.getPosterListSel(conditionBo);
            if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())
                && conditionBo.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                // 排序
                posterList = sortResultWithScreenModuleLibrarySelIds(selScreenModuleLibraryIds, posterList);
            }
            map.put("list", posterList);
            map.put("total", posterList.size());
        } else {
            PageHelper.startPage(conditionBo.getPageNo(), conditionBo.getPageSize());
            PageInfo<ScreenModuleLibraryVo> pageInfo = new PageInfo<>(this.baseMapper.getPosterListSel(conditionBo));
            List<ScreenModuleLibraryVo> list = pageInfo.getList();
            if (StringUtils.isNotBlank(conditionBo.getScreenModuleLibrarySelIds())
                && conditionBo.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                // 排序
                list = sortResultWithScreenModuleLibrarySelIds(selScreenModuleLibraryIds, list);
            }
            map.put("list", list);
            map.put("total", pageInfo.getTotal());
        }
        List<ScreenModuleLibraryVo> posterList = (List<ScreenModuleLibraryVo>)map.get("list");
        if (CollectionUtils.isEmpty(posterList)) {
            return AjaxResult.success(map);
        }

        // 组装图片，及是否收藏标识
        posterAssembling(conditionBo, posterList);
        return AjaxResult.success(map);
    }

    /**
     * 海报主题组装图片及收藏等标识
     *
     * @param conditionBo, posterList
     * @return void
     * <AUTHOR>
     * @date 2023/5/24 14:24
     */
    private void posterAssembling(ScreenModuleLibraryListConditionBo conditionBo,
        List<ScreenModuleLibraryVo> posterList) {
        List<Long> returnLibraryIds =
            posterList.stream().map(ScreenModuleLibraryVo::getScreenModuleLibraryId).collect(Collectors.toList());

        // 查询海报图片
        if (conditionBo.isQueryPosterMedia() && CollectionUtils.isNotEmpty(returnLibraryIds)) {
            LambdaQueryWrapper<ScreenModuleLibraryMedia> lqw = new LambdaQueryWrapper<>();
            lqw.in(ScreenModuleLibraryMedia::getScreenModuleLibraryId, returnLibraryIds);
            lqw.eq(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.NOTDELETE.getCode());
            lqw.orderByAsc(ScreenModuleLibraryMedia::getMediaSort,
                ScreenModuleLibraryMedia::getScreenModuleLibraryMediaId);
            List<ScreenModuleLibraryMedia> mediaList = screenModuleLibraryMediaService.list(lqw);
            if (CollectionUtils.isNotEmpty(mediaList)) {
                List<ScreenModuleLibraryMediaVo> mediaVos = mediaList.stream().map(x -> {
                    ScreenModuleLibraryMediaVo vo = new ScreenModuleLibraryMediaVo();
                    BeanUtils.copyProperties(x, vo);
                    return vo;
                }).collect(Collectors.toList());
                Map<Long, List<ScreenModuleLibraryMediaVo>> mediaVoMap =
                    mediaVos.stream().collect(Collectors.groupingBy(
                        ScreenModuleLibraryMediaVo::getScreenModuleLibraryId, LinkedHashMap::new, Collectors.toList()));
                posterList.forEach(x -> x.setScreenModuleLibraryMediaVos(mediaVoMap.get(x.getScreenModuleLibraryId())));
            }
        }
        // 设置收藏标识
        if (conditionBo.isQueryCollect()) {
            setIsCollect(posterList, conditionBo.getOrganizationId());
        }
    }

    /**
     * 我创建的海报列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/31 11:44
     */
    @Override
    public AjaxResult getPersonPosters(ScreenModuleLibraryListConditionBo conditionBo) {
        // 1、查询当前用户创建的模块ids
        // ScreenModuleLibraryUserRelConditionBo userCondition = new ScreenModuleLibraryUserRelConditionBo();
        // userCondition.setUserOid(conditionBo.getCreateBy());
        // userCondition.setType(LibraryUserRelTypeEnum.CREATE.getCode());
        // List<ScreenModuleLibraryUserRelVo> moduleLibraryUserRelVos =
        // screenModuleLibraryUserRelService.getScreenModuleLibraryUserRelListByCondition(userCondition);
        // if (CollectionUtils.isEmpty(moduleLibraryUserRelVos)) {
        // return AjaxResult.success();
        // }
        // List<Long> createLibraryIds = moduleLibraryUserRelVos.stream()
        // .map(ScreenModuleLibraryUserRelVo::getScreenModuleLibraryId).collect(Collectors.toList());
        //
        // // 2、根据模块id获取海报列表
        // return getModuleLibraryPageMap(conditionBo, createLibraryIds);
        return AjaxResult.fail();
    }

    /**
     * 删除主题列表及主题对应的海报图片
     *
     * @param libraryIds 主题id集合
     * @return boolean
     * <AUTHOR>
     * @date 2023/5/16 14:33
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteScreenModuleLibraryAndLibraryMediaByLibraryIds(List<Long> libraryIds) {
        if (CollectionUtils.isEmpty(libraryIds)) {
            return true;
        }

        // 删除主题
        LambdaUpdateWrapper<ScreenModuleLibrary> uw = new LambdaUpdateWrapper<>();
        uw.in(ScreenModuleLibrary::getScreenModuleLibraryId, libraryIds);
        uw.eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode());
        uw.set(ScreenModuleLibrary::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(uw);
        // 删除资源
        LambdaUpdateWrapper<ScreenModuleLibraryMedia> luw = new LambdaUpdateWrapper<>();
        luw.in(ScreenModuleLibraryMedia::getScreenModuleLibraryId, libraryIds);
        luw.eq(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.NOTDELETE.getCode());
        luw.set(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.ISDELETE.getCode());
        screenModuleLibraryMediaService.update(luw);
        // 同步删除关联标签关系
        LambdaUpdateWrapper<LabelLibraryRelDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(LabelLibraryRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        updateWrapper.in(LabelLibraryRelDto::getScreenModuleLibraryId, libraryIds);
        return labelLibraryRelService.update(updateWrapper);
    }

    @Override
    public List<ScreenModuleLibraryNumVo> getPosterSchoolNum(ScreenModuleLibraryListConditionBo conditionBo) {
        if(CollectionUtils.isEmpty(conditionBo.getOrganizationIds())){
            return Lists.newArrayList();
        }
        return screenModuleLibraryMapper.getPosterSchoolNum(conditionBo);
    }

    @Override
    public List<ScreenModuleLibraryNumVo> getPosterMediaSchoolNum(ScreenModuleLibraryListConditionBo conditionBo) {
        if (CollectionUtils.isEmpty(conditionBo.getOrganizationIds()) && conditionBo.getDictionaryDataOrganizationId() == null) {
            return Lists.newArrayList();
        }
        return screenModuleLibraryMapper.getPosterMediaNum(conditionBo);
    }

    /**
     * 设置模块是否被收藏
     *
     * @param posterList 海报模块列表
     * @return void
     * <AUTHOR>
     * @date 2023/4/4 16:55
     */
    private void setIsCollect(List<ScreenModuleLibraryVo> posterList, Long organizationId) {
        List<ScreenModuleLibraryCollectVo> myCollectLibraryVos =
            screenModuleLibraryMediaService.getMyCollectLibraryVos(organizationId);
        if (CollectionUtils.isEmpty(myCollectLibraryVos)) {
            return;
        }
        Map<Long, List<ScreenModuleLibraryCollectVo>> collectMap = myCollectLibraryVos.stream()
            .collect(Collectors.groupingBy(ScreenModuleLibraryCollectVo::getScreenModuleLibraryId));
        for (ScreenModuleLibraryVo vo : posterList) {
            // 判断是否被收藏
            if (null != collectMap.get(vo.getScreenModuleLibraryId())) {
                vo.setCollect(true);
            } else {
                vo.setCollect(false);
            }
        }
    }

    /**
     * 对ScreenModuleLibraryVos进行排序，按照screenModuleLibrarySelIds值顺序进行排序
     *
     * @param screenModuleLibraryIds 主题id的集合
     * @param screenModuleLibraryVos the screen module library vos
     * @return list list
     * <AUTHOR>
     * @date 2023 -04-20 13:47:22
     */
    private List<ScreenModuleLibraryVo> sortResultWithScreenModuleLibrarySelIds(List<Long> screenModuleLibraryIds,
        List<ScreenModuleLibraryVo> screenModuleLibraryVos) {
        if (CollectionUtils.isEmpty(screenModuleLibraryIds) || screenModuleLibraryIds.size() <= ConstantsInteger.NUM_1
            || CollectionUtils.isEmpty(screenModuleLibraryVos)) {
            return screenModuleLibraryVos;
        }
        // 剔除已被删除的主题id
        List<Long> newScreenModuleLibraryIds = new ArrayList<>();
        for (Long screenModuleLibraryId : screenModuleLibraryIds) {
            for (ScreenModuleLibraryVo screenModuleLibraryVo : screenModuleLibraryVos) {
                if (screenModuleLibraryId.equals(screenModuleLibraryVo.getScreenModuleLibraryId())) {
                    newScreenModuleLibraryIds.add(screenModuleLibraryId);
                    break;
                }
            }
        }

        List<ScreenModuleLibraryVo> screenModuleLibraryVoListResult = Lists.newArrayList();

        Map<Long, ScreenModuleLibraryVo> ScreenModuleLibraryVoMap = screenModuleLibraryVos.stream()
            .collect(Collectors.toMap(ScreenModuleLibraryVo::getScreenModuleLibraryId, a -> a, (k1, k2) -> k1));
        newScreenModuleLibraryIds.forEach(screenModuleLibraryId -> {
            screenModuleLibraryVoListResult.add(ScreenModuleLibraryVoMap.get(screenModuleLibraryId));
        });
        return screenModuleLibraryVoListResult;
    }
}