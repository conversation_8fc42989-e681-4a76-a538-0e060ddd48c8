package com.fh.cloud.screen.service.screen.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneThirdDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneThirdService;
import com.fh.cloud.screen.service.screen.mapper.ScreenSceneThirdMapper;
import com.light.core.entity.AjaxResult;

/**
 * 第三方对接云屏场景信息表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
@Service
public class ScreenSceneThirdServiceImpl extends ServiceImpl<ScreenSceneThirdMapper, ScreenSceneThirdDto>
    implements IScreenSceneThirdService {

    @Resource
    private ScreenSceneThirdMapper screenSceneThirdMapper;

    @Override
    public List<ScreenSceneThirdVo> getScreenSceneThirdListByCondition(ScreenSceneThirdConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenSceneThirdMapper.getScreenSceneThirdListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenSceneThird(ScreenSceneThirdBo screenSceneThirdBo) {
        ScreenSceneThirdDto screenSceneThird = new ScreenSceneThirdDto();
        BeanUtils.copyProperties(screenSceneThirdBo, screenSceneThird);
        screenSceneThird.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenSceneThird)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenSceneThird(ScreenSceneThirdBo screenSceneThirdBo) {
        ScreenSceneThirdDto screenSceneThird = new ScreenSceneThirdDto();
        BeanUtils.copyProperties(screenSceneThirdBo, screenSceneThird);
        if (updateById(screenSceneThird)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenSceneThirdVo getDetail(Long screenSceneThirdId) {
        ScreenSceneThirdConditionBo condition = new ScreenSceneThirdConditionBo();
        condition.setScreenSceneThirdId(screenSceneThirdId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenSceneThirdVo> list = screenSceneThirdMapper.getScreenSceneThirdListByCondition(condition);
        ScreenSceneThirdVo vo = new ScreenSceneThirdVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public boolean deleteByIdAndAppCode(Long screenSceneThirdId, String appCode) {
        LambdaUpdateWrapper<ScreenSceneThirdDto> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.eq(ScreenSceneThirdDto::getScreenSceneThirdId, screenSceneThirdId);
        lambdaUpdateWrapper.eq(ScreenSceneThirdDto::getAppCode, appCode);
        lambdaUpdateWrapper.set(ScreenSceneThirdDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        return update(lambdaUpdateWrapper);
    }

    @Override
    public List<ScreenSceneThirdVo> getScreenSceneThirdVoListBySpaceAndDeviceAndDate(Integer spaceGroupUseType,
        Long spaceInfoId, Long showDeviceId, Date date) {
        return screenSceneThirdMapper.getScreenSceneThirdVoListBySpaceAndDeviceAndDate(spaceGroupUseType, spaceInfoId,
            showDeviceId, date);
    }
}