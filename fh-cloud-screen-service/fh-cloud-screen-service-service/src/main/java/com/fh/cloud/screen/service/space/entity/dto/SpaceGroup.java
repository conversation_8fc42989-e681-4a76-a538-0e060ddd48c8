package com.fh.cloud.screen.service.space.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 区域组表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("space_group")
public class SpaceGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "space_group_id", type = IdType.AUTO)
    private Long spaceGroupId;

    /**
     * 区域分组类型：1校园使用，2社区使用
     */
    @TableField("space_group_type")
    private Integer spaceGroupType;

    /**
     * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
     */
    @TableField("space_group_use_type")
    private Integer spaceGroupUseType;

    /**
     * 区域组名称
     */
    @TableField("space_group_name")
    private String spaceGroupName;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
