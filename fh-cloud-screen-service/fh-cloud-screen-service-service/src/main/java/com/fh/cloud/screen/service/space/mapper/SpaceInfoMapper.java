package com.fh.cloud.screen.service.space.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;

import java.util.List;

/**
 * 区域信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface SpaceInfoMapper extends BaseMapper<SpaceInfo> {

    List<SpaceInfoVo> getSpaceInfoListByCondition(SpaceInfoListConditionBo condition);

}
