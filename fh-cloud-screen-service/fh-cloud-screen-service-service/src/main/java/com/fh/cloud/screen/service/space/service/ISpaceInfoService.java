package com.fh.cloud.screen.service.space.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.light.core.entity.AjaxResult;

/**
 * 区域信息表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ISpaceInfoService extends IService<SpaceInfo> {

    List<SpaceInfoVo> getSpaceInfoListByCondition(SpaceInfoListConditionBo condition);

    AjaxResult addSpaceInfo(SpaceInfoBo spaceInfoBo);

    boolean updateSpaceInfo(SpaceInfoBo spaceInfoBo);

    SpaceInfoVo getDetail(Long spaceInfoId);

    /**
     * 根据地点id集合查询地点数据-行政
     * 
     * @param spaceInfoIds
     * @return
     */
    List<ClazzInfoVo> getSpaceInfoVosByIdsXz(List<Long> classesIds);

    /**
     * 根据地点id集合查询地点数据-非行政
     * 
     * @param classesIds
     * @return
     */
    List<SpaceInfoVo> getSpaceInfoVosByIdsNotXz(List<Long> spaceInfoIds);

    /**
     * 查询行政班级或者空间的地点信息
     *
     * @param spaceInfoId 地点id或者班级id，必填
     * @param spaceGroupUseType {@link com.fh.cloud.screen.service.enums.SpaceGroupUseType}，必填
     * @return detail vo by use type
     * <AUTHOR>
     * @date 2023 -03-15 14:47:01
     */
    SpaceInfoVo getDetailVoByUseType(Long spaceInfoId, Integer spaceGroupUseType);

    /**
     * 非行政教室的slow分页查询
     *
     * @param condition the condition
     * @return space info list by condition slow
     * <AUTHOR>
     * @date 2023 -03-30 13:56:01
     */
    List<SpaceInfoVo> getSpaceInfoListByConditionSlow(SpaceInfoListConditionBo condition);
}
