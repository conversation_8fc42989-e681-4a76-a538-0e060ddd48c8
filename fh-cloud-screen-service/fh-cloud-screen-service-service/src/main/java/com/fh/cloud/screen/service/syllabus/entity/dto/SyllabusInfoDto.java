package com.fh.cloud.screen.service.syllabus.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课表信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:22:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("syllabus_info")
public class SyllabusInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键自动增长
	 */
	@TableId(value = "syllabus_id", type = IdType.AUTO)
	private Long syllabusId;

	/**
	 * 学校id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 星期几:1-7,注意周一是1
	 */
	@TableField("week_id")
	private Long weekId;

	/**
	 * 排序0,1,2，3,4,5,6,7,8...... 特别说明0可能指早读，注意顺序需要与作息时间对应上
	 */
	@TableField("sort")
	private Long sort;

	/**
	 * 1表示启用 0表示禁用
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 课表来源：1同步，2导入，3排课插入
	 */
	@TableField("source")
	private Integer source;

	/**
	 * 班级id
	 */
	@TableField("classes_id")
	private Long classesId;

	/**
	 * 班级名称
	 */
	@TableField("classes_name")
	private String classesName;

	/**
	 * 科目的code
	 */
	@TableField("subject_code")
	private String subjectCode;

	/**
	 * 科目的名称
	 */
	@TableField("subject_name")
	private String subjectName;

	/**
	 * 授课老师
	 */
	@TableField("teacher_name")
	private String teacherName;

	/**
	 * 双周科目的code
	 */
	@TableField("double_subject_code")
	private String doubleSubjectCode;

	/**
	 * 双周科目的名称
	 */
	@TableField("double_subject_name")
	private String doubleSubjectName;

	/**
	 * 授课老师
	 */
	@TableField("double_teacher_name")
	private String doubleTeacherName;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
