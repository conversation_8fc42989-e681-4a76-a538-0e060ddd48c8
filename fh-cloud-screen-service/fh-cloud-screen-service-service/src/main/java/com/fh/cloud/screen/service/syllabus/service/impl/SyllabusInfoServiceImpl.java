package com.fh.cloud.screen.service.syllabus.service.impl;

import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.dto.SyllabusInfoDto;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.fh.cloud.screen.service.syllabus.mapper.SyllabusInfoMapper;
import com.fh.cloud.screen.service.syllabus.service.ISyllabusInfoService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.light.core.entity.AjaxResult;
/**
 * 课表信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:22:20
 */
@Service
public class SyllabusInfoServiceImpl extends ServiceImpl<SyllabusInfoMapper, SyllabusInfoDto> implements ISyllabusInfoService {

	@Resource
	private SyllabusInfoMapper syllabusInfoMapper;
	
    @Override
	public List<SyllabusInfoVo> getSyllabusInfoListByCondition(SyllabusInfoConditionBo condition) {
		FuzzyQueryUtil.transferMeanBean(condition);
        return syllabusInfoMapper.getSyllabusInfoListByCondition(condition);
	}

	@Override
	public AjaxResult addSyllabusInfo(SyllabusInfoBo syllabusInfoBo) {
		SyllabusInfoDto syllabusInfo = new SyllabusInfoDto();
		BeanUtils.copyProperties(syllabusInfoBo, syllabusInfo);
		if(save(syllabusInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSyllabusInfo(SyllabusInfoBo syllabusInfoBo) {
		SyllabusInfoDto syllabusInfo = new SyllabusInfoDto();
		BeanUtils.copyProperties(syllabusInfoBo, syllabusInfo);
		if(updateById(syllabusInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SyllabusInfoVo getSyllabusInfoByCondition(SyllabusInfoConditionBo condition) {
		SyllabusInfoVo vo = syllabusInfoMapper.getSyllabusInfoByCondition(condition);
		return vo;
	}

}