package com.fh.cloud.screen.service.tts.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.lang.UUID;
import com.fh.cloud.screen.service.tts.dto.BuguResultDto;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.nacos.common.utils.IoUtils;
import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.entity.AjaxResult;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 文字转语音service
 * 
 * <AUTHOR>
 * @date 2023/11/2 14:24
 */
@Slf4j
@Service
public class TtsService {
    @Resource
    private AttachmentApi attachmentApi;
    @Resource
    private TtsForestClient ttsForestClient;

    /**
     * 布谷，文字转语音地址
     *
     * @param text the text
     * @return the string
     * <AUTHOR>
     * @date 2023 -11-02 15:14:18
     */
    public String buguTextToVoiceUrl(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("text", text);
        param.put("bgid", "");
        param.put("bg_volume", 5);
        param.put("format", "mp3");
        param.put("voice", "");
        param.put("volume", 10);
        param.put("speech_rate", 5);
        param.put("pitch_rate", 5);
        param.put("title", "");
        param.put("deviceid", "62e4901cc19d44758361dca8b644ca58");
        param.put("client", "web");
        param.put("client_ver", "3.0.3.0");
        param.put("soft_version", "1.0.0.0");
        param.put("source", 437);
        try {
            BuguResultDto buguResultDto = ttsForestClient.textToVoiceUrl(param);
            if (buguResultDto == null || buguResultDto.getCode() != 0 || buguResultDto.getData() == null) {
                return null;
            }
            return buguResultDto.getData().getFile_link();
        } catch (Exception e) {
            log.error("textToVoiceUrl exception:", e);
            return null;
        }
    }

    /**
     * 语音文件地址保存到我们系统中
     *
     * @param onlineUrl
     *            在线的文件地址，例如：http://oss-liuchengtu.hudunsoft.com/text-to-voice/05cf976e02bde2ff2996f481cd60f0f7.mp3
     * @param fileName 文件名称，例如：05cf976e02bde2ff2996f481cd60f0f7.mp3
     * @return the attachment vo
     * <AUTHOR>
     * @date 2023 -11-02 14:32:29
     */
    public AttachmentVo saveOnlineFile(String onlineUrl, String fileName) {
        if (StringUtils.isBlank(onlineUrl)) {
            return null;
        }
        if (onlineUrl.contains("\\")) {
            onlineUrl = onlineUrl.replace("\\", "");
        }
        if (StringUtils.isBlank(fileName)) {
            // 从url中获取文件名称
            try {
                fileName = onlineUrl.substring(onlineUrl.lastIndexOf("/") + 1);
            } catch (Exception e) {
                fileName = UUID.fastUUID().toString(true) + ".mp3";
            }
        }
        InputStream contentStream = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outStream = null;
        try {
            URL url = new URL(onlineUrl);
            inputStream = url.openStream();
            outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            byte[] bytes = outStream.toByteArray();
            contentStream = new ByteArrayInputStream(bytes);
            IoUtils.closeQuietly(outStream);
            IoUtils.closeQuietly(inputStream);
            if (contentStream == null) {
                log.error("contentStream is null, function stack return!");
                return null;
            }

            MultipartFile cMultiFile = new MockMultipartFile("file", fileName, null, contentStream);
            AjaxResult result = attachmentApi.upload(cMultiFile);
            if (null == result || result.isFail() || null == result.getData()) {
                log.error("attachmentApi.upload fail , function stack return!");
                return null;
            }
            final Map<String, Object> map = (Map<String, Object>)result.getData();
            AttachmentVo attachmentVo = BeanUtil.mapToBean(map, AttachmentVo.class, true);
            return attachmentVo;
        } catch (Exception e) {
            log.error("saveOnlineFile exception:", e);
        } finally {
            IoUtils.closeQuietly(outStream);
            IoUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(contentStream);
        }
        return null;
    }
}
