<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.attendance.mapper.AttendanceRuleDayMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRuleDay" id="BaseResultMap">
	        <result property="attendanceRuleDayId" column="attendance_rule_day_id"/>
	        <result property="attendanceRuleId" column="attendance_rule_id"/>
	        <result property="grade" column="grade"/>
	        <result property="week" column="week"/>
	        <result property="signInTime" column="sign_in_time"/>
	        <result property="signOutTime" column="sign_out_time"/>
	        <result property="attendanceRuleDayIndex" column="attendance_rule_day_index"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	        <result property="signLeftMinute" column="sign_left_minute"/>
	        <result property="signRightMinute" column="sign_right_minute"/>
	    </resultMap>

	<select id="getAttendanceRuleDayListByCondition" resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo">
		select a.* from attendance_rule_day a
	    <where>
	    				    <if test="attendanceRuleDayId != null ">and a.attendance_rule_day_id = #{attendanceRuleDayId}</if>
						    <if test="attendanceRuleId != null ">and a.attendance_rule_id = #{attendanceRuleId}</if>
						    <if test="grade != null and grade != ''">and a.grade = #{grade}</if>
						    <if test="week != null ">and a.week = #{week}</if>
						    <if test="signInTime != null and signInTime != ''">and a.sign_in_time = #{signInTime}</if>
						    <if test="signOutTime != null and signOutTime != ''">and a.sign_out_time = #{signOutTime}</if>
						    <if test="attendanceRuleDayIndex != null ">and a.attendance_rule_day_index = #{attendanceRuleDayIndex}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
						    <if test="signLeftMinute != null ">and a.sign_left_minute = #{signLeftMinute}</if>
						    <if test="signRightMinute != null ">and a.sign_right_minute = #{signRightMinute}</if>
				    </where>
	</select>
</mapper>