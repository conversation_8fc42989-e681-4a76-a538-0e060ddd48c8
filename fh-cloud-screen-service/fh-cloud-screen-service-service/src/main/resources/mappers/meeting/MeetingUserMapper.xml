<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.meeting.mapper.MeetingUserMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto" id="BaseResultMap">
        <result property="meetingUserId" column="meeting_user_id"/>
        <result property="meetingId" column="meeting_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="signTime" column="sign_time"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="grade" column="grade"/>
        <result property="classesId" column="classes_id"/>
        <result property="classesName" column="classes_name"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="meetingUserId != null ">and meeting_user_id = #{meetingUserId}</if>
            <if test="meetingId != null ">and meeting_id = #{meetingId}</if>
            <if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
            <if test="signTime != null ">and sign_time = #{signTime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="grade != null and grade != ''">and grade = #{grade}</if>
            <if test="classesId != null ">and classes_id = #{classesId}</if>
            <if test="classesName != null and classesName != ''">and classes_name = #{classesName}</if>
        </where>
    </sql>

    <sql id="common_select">
		select
	 		t.meeting_user_id
	 		,t.meeting_id
	 		,t.user_oid
	 		,t.sign_time
	 		,t.status
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
            ,t.grade
            ,t.classes_id
            ,t.classes_name
		from (
			 select a.* from meeting_user a
		 ) t

	</sql>

    <select id="getMeetingUserListByCondition" resultType="com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>
</mapper>