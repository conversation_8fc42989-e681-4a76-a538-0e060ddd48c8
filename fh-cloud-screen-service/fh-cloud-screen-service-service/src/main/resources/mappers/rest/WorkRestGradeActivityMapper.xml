<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.rest.mapper.WorkRestGradeActivityMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.rest.entity.dto.WorkRestGradeActivity" id="BaseResultMap">
	        <result property="workRestGradeActivityId" column="work_rest_grade_activity_id"/>
	        <result property="workRestGradeId" column="work_rest_grade_id"/>
	        <result property="workRestId" column="work_rest_id"/>
	        <result property="activityName" column="activity_name"/>
	        <result property="activityPosition" column="activity_position"/>
	        <result property="activitySortType" column="activity_sort_type"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getWorkRestGradeActivityListByCondition" resultType="com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeActivityVo">
		select a.* from work_rest_grade_activity a
	    <where>
	    				    <if test="workRestGradeActivityId != null ">and a.work_rest_grade_activity_id = #{workRestGradeActivityId}</if>
						    <if test="workRestGradeId != null ">and a.work_rest_grade_id = #{workRestGradeId}</if>
						    <if test="workRestId != null ">and a.work_rest_id = #{workRestId}</if>
						    <if test="activityName != null and activityName != ''">and a.activity_name = #{activityName}</if>
						    <if test="activityPosition != null ">and a.activity_position = #{activityPosition}</if>
						    <if test="activitySortType != null ">and a.activity_sort_type = #{activitySortType}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>