<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenContentDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenContentDetail" id="BaseResultMap">
	        <result property="screenContentDetailId" column="screen_content_detail_id"/>
	        <result property="screenContentId" column="screen_content_id"/>
	        <result property="screenContentIndex" column="screen_content_index"/>
	        <result property="screenContentTitle" column="screen_content_title"/>
	        <result property="screenContentTxt" column="screen_content_txt"/>
	        <result property="screenContentUrl" column="screen_content_url"/>
	        <result property="screenContentMediaUrl" column="screen_content_media_url"/>
	        <result property="screenContentMediaUrlCompress" column="screen_content_media_url_compress"/>
	        <result property="screenContentMediaUrlCover" column="screen_content_media_url_cover"/>
	        <result property="screenContentMediaName" column="screen_content_media_name"/>
	        <result property="screenContentMediaNameOri" column="screen_content_media_name_ori"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	        <result property="callContent" column="call_content"/>
	        <result property="signContent" column="sign_content"/>
	        <result property="screenContentMediaId" column="screen_content_media_id"/>
	        <result property="screenContentMediaIdCompress" column="screen_content_media_id_compress"/>
			<result property="screenDevicePattern" column="screen_device_pattern"/>
			<result property="screenContentMediaMd5" column="screen_content_media_md5"/>
			<result property="screenContentMediaMd5Compress" column="screen_content_media_md5_compress"/>

	    </resultMap>

	<select id="getScreenContentDetailListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo">
		select a.* from screen_content_detail a
	    <where>
	    				    <if test="screenContentDetailId != null ">and a.screen_content_detail_id = #{screenContentDetailId}</if>
						    <if test="screenContentId != null ">and a.screen_content_id = #{screenContentId}</if>
						    <if test="screenContentIndex != null ">and a.screen_content_index = #{screenContentIndex}</if>
						    <if test="screenContentTitle != null and screenContentTitle != ''">and a.screen_content_title = #{screenContentTitle}</if>
						    <if test="screenContentTxt != null and screenContentTxt != ''">and a.screen_content_txt = #{screenContentTxt}</if>
						    <if test="screenContentUrl != null and screenContentUrl != ''">and a.screen_content_url = #{screenContentUrl}</if>
						    <if test="screenContentMediaUrl != null and screenContentMediaUrl != ''">and a.screen_content_media_url = #{screenContentMediaUrl}</if>
						    <if test="screenContentMediaUrlCompress != null and screenContentMediaUrlCompress != ''">and a.screen_content_media_url_compress = #{screenContentMediaUrlCompress}</if>
						    <if test="screenContentMediaUrlCover != null and screenContentMediaUrlCover != ''">and a.screen_content_media_url_cover = #{screenContentMediaUrlCover}</if>
						    <if test="screenContentMediaName != null and screenContentMediaName != ''">and a.screen_content_media_name = #{screenContentMediaName}</if>
						    <if test="screenContentMediaNameOri != null and screenContentMediaNameOri != ''">and a.screen_content_media_name_ori = #{screenContentMediaNameOri}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
						    <if test="callContent != null and callContent != ''">and a.call_content = #{callContent}</if>
						    <if test="signContent != null and signContent != ''">and a.sign_content = #{signContent}</if>
						    <if test="screenContentMediaId != null and screenContentMediaId != ''">and a.screen_content_media_id = #{screenContentMediaId}</if>
						    <if test="screenContentMediaIdCompress != null and screenContentMediaIdCompress != ''">and a.screen_content_media_id_compress = #{screenContentMediaIdCompress}</if>
							<if test="screenDevicePattern != null">and a.screen_device_pattern=#{screenDevicePattern}</if>
							<if test="screenContentMediaMd5 != null">and a.screen_content_media_md5=#{screenContentMediaMd5}</if>
							<if test="screenContentMediaMd5Compress != null">and a.screen_content_media_md5_compress=#{screenContentMediaMd5Compress}</if>
				    </where>
	</select>

	<select id="listScreenContentDetailByScreenContentIds" parameterType="map" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo">
		select * from screen_content_detail
		where is_delete=0 and screen_content_id in
		<foreach collection="screenContentIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>
</mapper>