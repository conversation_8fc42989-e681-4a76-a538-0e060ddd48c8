<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenPoetryContentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryContentDto" id="BaseResultMap">
        <result property="screenPoetryContentId" column="screen_poetry_content_id"/>
        <result property="screenModuleDataId" column="screen_module_data_id"/>
        <result property="screenPoetryContentTitle" column="screen_poetry_content_title"/>
        <result property="screenPoetryContentTxt" column="screen_poetry_content_txt"/>
        <result property="classesId" column="classes_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="createUserClassesName" column="create_user_classes_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenPoetryContentId != null ">and screen_poetry_content_id = #{screenPoetryContentId}</if>
			<if test="screenModuleDataId != null ">and screen_module_data_id = #{screenModuleDataId}</if>
			<if test="screenPoetryContentTitle != null and screenPoetryContentTitle != '' ">and screen_poetry_content_title like concat('%', #{screenPoetryContentTitle}, '%')</if>
			<if test="screenPoetryContentTxt != null and screenPoetryContentTxt != '' ">and screen_poetry_content_txt like concat('%', #{screenPoetryContentTxt}, '%')</if>
			<if test="classesId != null ">and classes_id = #{classesId}</if>
			<if test="createUserName != null and createUserName != '' ">and create_user_name like concat('%', #{createUserName}, '%')</if>
			<if test="createUserClassesName != null and createUserClassesName != '' ">and create_user_classes_name like concat('%', #{createUserClassesName}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="organizationId != null">and organization_id=#{organizationId} </if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_poetry_content_id
	 		,t.screen_module_data_id
	 		,t.screen_poetry_content_title
	 		,t.screen_poetry_content_txt
	 		,t.classes_id
	 		,t.create_time
	 		,t.create_by
	 		,t.create_user_name
	 		,t.create_user_classes_name
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.likes_num as likesNum
			,t.organization_id
		from (
			 select a.*,spl.likes_num,smd.organization_id from screen_poetry_content a
			 join screen_module_data smd on a.screen_module_data_id = smd.screen_module_data_id
			 left join screen_poetry_likes spl on a.screen_poetry_content_id = spl.screen_poetry_content_id
		 ) t

	</sql>

	<select id="getScreenPoetryContentListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenPoetryContentByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>