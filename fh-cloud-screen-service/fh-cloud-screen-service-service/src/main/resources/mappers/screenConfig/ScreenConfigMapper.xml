<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screenConfig.mapper.ScreenConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screenConfig.entity.dto.ScreenConfigDto" id="BaseResultMap">
        <result property="screenConfigId" column="screen_config_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="configValue" column="config_value"/>
        <result property="brandConfigValue" column="brand_config_value"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenConfigId != null ">and screen_config_id = #{screenConfigId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="configValue != null and configValue != '' ">and config_value like concat('%', #{configValue}, '%')</if>
			<if test="brandConfigValue != null and brandConfigValue != '' ">and brand_config_value like concat('%', #{brandConfigValue}, '%')</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="screenConfigId != null ">and screen_config_id = #{screenConfigId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="configValue != null and configValue != '' ">and config_value like concat('%', #{configValue}, '%')</if>
			<if test="brandConfigValue != null and brandConfigValue != '' ">and brand_config_value like concat('%', #{brandConfigValue}, '%')</if>
			<if test="remark != null and remark != '' ">and remark like concat('%', #{remark}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_config_id
	 		,t.organization_id
	 		,t.config_value
	 		,t.brand_config_value
	 		,t.remark
	 		,t.type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from screen_config a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getScreenConfigListByCondition" resultType="com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenConfigByCondition" resultType="com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>