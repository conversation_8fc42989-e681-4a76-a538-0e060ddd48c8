<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.wx.mapper.WxMsgSubConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.wx.entity.dto.WxMsgSubConfigDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="week" column="week"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="week != null and week !=''">and week = #{week}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="week != null and week !=''">and week = #{week}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.user_oid
	 		,t.week
	 		,t.organization_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from wx_msg_sub_config a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getWxMsgSubConfigListByCondition" resultType="com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getWxMsgSubConfigByCondition" resultType="com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getWxMsgSubDeviceVoOfConfig"
			resultType="com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubDeviceVo">
		select wmsd.*
		from wx_msg_sub_config wmsc
	    join wx_msg_sub_device wmsd on wmsc.user_oid = wmsd.user_oid and wmsd.is_delete=0
		where wmsc.is_delete=0
		<if test="organizationIds != null and organizationIds.size() > 0">
			and wmsc.organization_id in
			<foreach collection="organizationIds" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
		</if>
		<if test="week != null and week != ''">
			and FIND_IN_SET(#{week},wmsc.`week`)
		</if>
	</select>

</mapper>