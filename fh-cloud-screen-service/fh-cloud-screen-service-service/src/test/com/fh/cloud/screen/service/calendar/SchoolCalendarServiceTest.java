package com.fh.cloud.screen.service.calendar;

import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarService;
import com.light.redis.component.RedisComponent;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SchoolCalendarServiceTest
 * 
 * <AUTHOR>
 * @date 2023/6/14 11:04
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
class SchoolCalendarServiceTest {

    @Resource
    private RedisComponent redisComponent;
    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private ISchoolCalendarService schoolCalendarService;

    @Test
    void testLock() throws Exception {
        boolean resultTotal = true;
        CountDownLatch c = new CountDownLatch(2);
        for (int i = 0; i < 2; i++) {
            new Thread(() -> {
                testLockMethod();
                c.countDown();
            }).start();
        }

        c.await();
        // Verify the results
        assertTrue(resultTotal);
    }

    @Test
    void testLockRedisson() throws Exception {
        boolean resultTotal = true;
        CountDownLatch c = new CountDownLatch(2);
        for (int i = 0; i < 2; i++) {
            new Thread(() -> {
                testLockMethodRedisson();
                c.countDown();
            }).start();
        }

        c.await();
        // Verify the results
        assertTrue(resultTotal);
    }

    boolean testLockMethod() {
        // redis锁校历+学校id
        boolean lock = redisComponent.lockKey("a__");
        boolean result = false;
        try {
            if (lock) {
                System.out.println("get lock");
                result = true;
            }
        } catch (Exception e) {
            result = false;
        } finally {
            // redisComponent.releaseKey("a__");
        }
        return result;
    }

    boolean testLockMethodRedisson() {
        // redisson的锁
        RLock lock = redissonClient.getLock("a__");
        boolean result = false;
        try {
            if (lock.tryLock(100, 10, TimeUnit.SECONDS)) {
                System.out.println("get lock redisson");
                result = true;
            }
        } catch (Exception e) {
            result = false;
        } finally {
            // redisComponent.releaseKey("a__");
        }
        return result;
    }

    /**
     * 单元测试不上课的接口
     * 
     * @throws Exception
     */
    @Test
    void testCheckNotWork() throws Exception {
        boolean result = schoolCalendarService.checkNotWork(321371000019L);
        assertTrue(result);
    }
}
