package com.fh.cloud.screen.web.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarApi;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarDayApi;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarWeekApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekSaveUpdateConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.enums.FestivalEnum;
import com.fh.cloud.screen.service.festival.api.FestivalApi;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;
import com.fh.cloud.screen.web.utils.DateUtils;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.fh.cloud.screen.web.enums.SchoolCalendarEnum;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/5/10
 */
@Api(tags = "校历接口")
@RequestMapping("/calendar")
@RestController
public class CalendarController {

    @Resource
    private SchoolCalendarApi schoolCalendarApi;

    @Resource
    private SchoolCalendarWeekApi schoolCalendarWeekApi;

    @Resource
    private SchoolCalendarDayApi schoolCalendarDayApi;

    @Resource
    private FestivalApi festivalApi;

    /**
     * 通过组织id查询校历主表详情
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/10 11:02
     */
    @GetMapping("/detail-by-organizationId")
    @ApiOperation(value = "通过组织id查询校历主表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("organizationId") Long organizationId) {
        return schoolCalendarApi.getDetail(organizationId);
    }

    /**
     * 查询校历上课日星期表列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/10 11:18
     */
    @PostMapping("/week/list")
    @ApiOperation(value = "查询校历上课日星期表列表", httpMethod = "POST")
    public AjaxResult getSchoolCalendarWeekListByCondition(@RequestBody SchoolCalendarWeekListConditionBo condition) {
        AjaxResult schoolCalendarWeekListResult = schoolCalendarWeekApi.getSchoolCalendarWeekListByCondition(condition);

        if (!schoolCalendarWeekListResult.isFail()) {
            Map<String, Object> map = (Map<String, Object>)schoolCalendarWeekListResult.getData();
            List<SchoolCalendarWeekVo> schoolCalendarWeekList = JSONObject
                .parseArray(JSONObject.toJSONString(map.get("schoolCalendarWeekList")), SchoolCalendarWeekVo.class);
            if (CollectionUtils.isEmpty(schoolCalendarWeekList)) {
                for (int i = 1; i < 8; i++) {
                    SchoolCalendarWeekVo schoolCalendarWeekVo = new SchoolCalendarWeekVo();
                    schoolCalendarWeekVo.setSchoolCalendarId(condition.getSchoolCalendarId());
                    schoolCalendarWeekVo.setType(SchoolCalendarEnum.TYPE_NOT.getValue());
                    schoolCalendarWeekVo.setWeek(i);
                    schoolCalendarWeekVo.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    schoolCalendarWeekList.add(schoolCalendarWeekVo);
                }
                map.put("schoolCalendarWeekList", schoolCalendarWeekList);
                map.put("count", 7);
                map.put("isDefault", true);
                return AjaxResult.success(map);
            }
            map.put("isDefault", false);
        }
        return schoolCalendarWeekListResult;
    }

    /**
     * 查询校历上课日日期表列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/10 11:17
     */
    @PostMapping("/day/list")
    @ApiOperation(value = "查询校历上课日日期表列表", httpMethod = "POST")
    public AjaxResult getSchoolCalendarDayListByCondition(@RequestBody SchoolCalendarDayListConditionBo condition) {
        // 获取不上课日期
        AjaxResult schoolCalendarDayListResult = schoolCalendarDayApi.getSchoolCalendarDayListByCondition(condition);
        if (schoolCalendarDayListResult.isFail()) {
            return schoolCalendarDayListResult;
        }
        Map<String, Object> map = (Map<String, Object>)schoolCalendarDayListResult.getData();
        List<SchoolCalendarDayVo> schoolCalendarDayList =
            JSONObject.parseArray(JSONObject.toJSONString(map.get("schoolCalendarDayList")), SchoolCalendarDayVo.class);
        if (CollectionUtils.isEmpty(schoolCalendarDayList)) {
            schoolCalendarDayList = new ArrayList<>();
        }
        // 获取节假日
        getFestivalVoListByMonth(condition.getOrganizationId(), condition.getMonth(), schoolCalendarDayList);
        // 组装数据并返回
        Map<Integer, Object> returnMap = new HashMap<>();
        if (CollectionUtils.isEmpty(schoolCalendarDayList)) {
            return AjaxResult.success(returnMap);
        }
        Map<Date, List<SchoolCalendarDayVo>> dateListMap =
            schoolCalendarDayList.stream().collect(Collectors.groupingBy(SchoolCalendarDayVo::getDay));
        Set<Date> keys = dateListMap.keySet();
        // 循环同一天里的多个节日 或 是否上课日
        for (Date key : keys) {
            List<SchoolCalendarDayVo> schoolCalendarDayVos = dateListMap.get(key);
            SchoolCalendarDayVo returnVo = new SchoolCalendarDayVo();
            returnVo.setType(schoolCalendarDayVos.get(0).getType());
            List<String> festivalNames = new ArrayList<>();
            List<String> nodeNames = new ArrayList<>();
            for (SchoolCalendarDayVo schoolCalendarDayVo : schoolCalendarDayVos) {
                returnVo.setDay(key);
                // 判断这一天是否是节点及节假日，是加入显示，否->同步该是否上课日
                if (StringUtils.isNotBlank(schoolCalendarDayVo.getFestivalName())) {
                    festivalNames.add(schoolCalendarDayVo.getFestivalName());
                } else if (StringUtils.isNotBlank(schoolCalendarDayVo.getNodeName())) {
                    nodeNames.add(schoolCalendarDayVo.getNodeName());
                } else {
                    returnVo.setSchoolCalendarDayId(schoolCalendarDayVo.getSchoolCalendarDayId());
                    returnVo.setType(schoolCalendarDayVo.getType());
                    returnVo.setWeek(schoolCalendarDayVo.getWeek());
                }
            }
            returnVo.setFestivalNames(festivalNames);
            returnVo.setNodeNames(nodeNames);
            returnMap.put(DateUtil.dayOfMonth(key), returnVo);
        }

        return AjaxResult.success(returnMap);
    }

    /**
     * 获取指定月份的所有节假日及自定义节点，并转换为schoolCalendarDay（上课日）
     *
     * @param organizationId 组织id
     * @param month 查询的月份
     * @param schoolCalendarDayList 节日添加到的地址
     * @return void
     * <AUTHOR>
     * @date 2023/3/7 10:08
     */
    private void getFestivalVoListByMonth(Long organizationId, String month,
        List<SchoolCalendarDayVo> schoolCalendarDayList) {
        // 节假日生成的‘天’ 先取周几。判断周几是否上课赋值。在判断具体日（唯一）有值则覆盖
        SchoolCalendarWeekListConditionBo condition = new SchoolCalendarWeekListConditionBo();
        condition.setOrganizationId(organizationId);
        AjaxResult ajaxResult = schoolCalendarWeekApi.getSchoolCalendarWeekListByCondition(condition);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return;
        }
        Map<String, Object> map = (Map<String, Object>)ajaxResult.getData();
        List<SchoolCalendarWeekVo> schoolCalendarWeekList = JSONObject
            .parseArray(JSONObject.toJSONString(map.get("schoolCalendarWeekList")), SchoolCalendarWeekVo.class);
        if (CollectionUtils.isEmpty(schoolCalendarWeekList)) {
            return;
        }
        Map<Integer, List<SchoolCalendarWeekVo>> weekMap =
            schoolCalendarWeekList.stream().collect(Collectors.groupingBy(SchoolCalendarWeekVo::getWeek));
        // 节假日列表
        FestivalConditionBo festivalConditionBo = new FestivalConditionBo();
        festivalConditionBo.setNeType(FestivalEnum.CUSTOMIZE.getCode());
        festivalConditionBo.setFestivalMonth(month);
        List<FestivalVo> festivalVos = festivalApi.getFestivalListByCondition(festivalConditionBo).getData();
        if (CollectionUtils.isNotEmpty(festivalVos)) {
            List<SchoolCalendarDayVo> finalSchoolCalendarDayList = schoolCalendarDayList;
            festivalVos.forEach(x -> {
                SchoolCalendarDayVo schoolCalendarDayVo = new SchoolCalendarDayVo();
                schoolCalendarDayVo.setDay(x.getFestivalDay());
                // schoolCalendarDayVo.setType(SchoolCalendarEnum.TYPE_IS.getValue());
                List<SchoolCalendarWeekVo> weekVoList = weekMap.get(DateUtils.getWeekIdByDate(x.getFestivalDay()));
                if (weekVoList.size() > 0) {
                    schoolCalendarDayVo.setType(weekVoList.get(0).getType());
                }
                schoolCalendarDayVo.setFestivalName(x.getName());
                finalSchoolCalendarDayList.add(schoolCalendarDayVo);
            });
        }
        // 获取自定义节点 --->日期拆分多个展示
        festivalConditionBo = new FestivalConditionBo();
        festivalConditionBo.setType(FestivalEnum.CUSTOMIZE.getCode());
        festivalConditionBo.setCustomizeStartAndEedMonth(month);
        festivalVos = festivalApi.getFestivalListByCondition(festivalConditionBo).getData();
        if (CollectionUtils.isNotEmpty(festivalVos)) {
            List<SchoolCalendarDayVo> finalSchoolCalendarDayVos = schoolCalendarDayList;
            festivalVos.forEach(x -> {
                // 获取自定义节点开始和结束之间的所有日期
                List<Date> days = DateUtils.getDateListByStartAndEndDate(x.getStartDay(), x.getEndDay(), month);
                days.forEach(y -> {
                    SchoolCalendarDayVo schoolCalendarDayVo = new SchoolCalendarDayVo();
                    schoolCalendarDayVo.setDay(y);
                    // schoolCalendarDayVo.setType(SchoolCalendarEnum.TYPE_IS.getValue());
                    List<SchoolCalendarWeekVo> weekVoList = weekMap.get(DateUtils.getWeekIdByDate(y));
                    if (weekVoList.size() > 0) {
                        schoolCalendarDayVo.setType(weekVoList.get(0).getType());
                    }
                    schoolCalendarDayVo.setNodeName(x.getName());
                    finalSchoolCalendarDayVos.add(schoolCalendarDayVo);
                });
            });
        }
    }

    @PostMapping("/week/save-or-update")
    @ApiOperation(value = "保存或修改校历上课日星期表", httpMethod = "POST")
    AjaxResult
        saveOrUpdateSchoolCalendarWeek(@Validated @RequestBody SchoolCalendarWeekSaveUpdateConditionBo conditionBo) {
        return schoolCalendarWeekApi.saveOrUpdateSchoolCalendarWeek(conditionBo);
    }

    /**
     * 保存或修改校历上课日日期表
     *
     * @param schoolCalendarDayBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/1 11:55
     */
    @PostMapping("/day/save-or-update")
    @ApiOperation(value = "保存或修改校历上课日日期表", httpMethod = "POST")
    AjaxResult saveOrUpdateSchoolCalendarDay(@Validated @RequestBody SchoolCalendarDayBo schoolCalendarDayBo) {
        return schoolCalendarDayApi.saveOrUpdateSchoolCalendarDay(schoolCalendarDayBo);
    }
}
