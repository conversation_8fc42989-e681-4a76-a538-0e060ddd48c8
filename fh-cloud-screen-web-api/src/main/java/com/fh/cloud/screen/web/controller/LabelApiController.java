package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.label.service.LabelApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
@RestController
@Validated
@RequestMapping("label")
@Api(value = "", tags = "标签表接口")
@Slf4j
public class LabelApiController {

    @Autowired
    private LabelApiService labelApiService;

    /**
     * 查询标签表分页列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询标签表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getLabelPageListByCondition(@RequestBody LabelConditionBo condition) {
        PageInfo<LabelVo> page = labelApiService.getLabelPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询标签表列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询标签表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getLabelListByCondition(@RequestBody LabelConditionBo condition) {
        List<LabelVo> list = labelApiService.getLabelListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }

    /**
     * 新增标签表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增标签表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addLabel(@RequestBody LabelBo labelBo) {
        return labelApiService.addLabel(labelBo);
    }

    /**
     * 修改标签表
     * 
     * @param labelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新标签表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateLabel(@RequestBody LabelBo labelBo) {
        if (null == labelBo.getLabelId()) {
            return AjaxResult.fail("标签表id不能为空");
        }
        return labelApiService.updateLabel(labelBo);
    }

    /**
     * 查询标签表详情
     * 
     * @param labelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询标签表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "labelId", value = "标签表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<LabelVo> getDetail(@RequestParam("labelId") Long labelId) {
        if (null == labelId) {
            return AjaxResult.fail("标签表id不能为空");
        }
        LabelVo vo = labelApiService.getDetail(labelId).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("labelVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除标签表
     * 
     * @param labelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除标签表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "labelId", value = "标签表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long labelId) {
        return labelApiService.delete(labelId);
    }

    /**
     * 根据标签类型 获取二级目录结构标
     *
     * @param conditionBo type 标签类型 默认1，海报标签
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 9:33
     */
    @PostMapping("/tree")
    public AjaxResult getLabelTreeByCondition(@RequestBody LabelConditionBo conditionBo) {
        return labelApiService.getLabelTreeByCondition(conditionBo);
    }

    /**
     * 按照id顺序批量更新顺序
     *
     * @param preLabelId the pre label id
     * @param nextLabelId the next label id
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2023 /3/14 9:33
     */
    @GetMapping("/exchange-sort")
    public AjaxResult exchangeSort(@RequestParam("preLabelId") Long preLabelId,
        @RequestParam("nextLabelId") Long nextLabelId, Long organizationId) {
        return labelApiService.exchangeLabelSort(preLabelId, nextLabelId, organizationId);
    }
}
