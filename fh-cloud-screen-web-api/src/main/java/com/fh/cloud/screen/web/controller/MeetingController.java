package com.fh.cloud.screen.web.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.card.entity.bo.StudentCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.enums.TeacherImportTemplateType;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingStudentImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingTeacherImportBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingImportVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;
import com.fh.cloud.screen.service.meeting.service.MeetingApiService;
import com.google.common.collect.Lists;
import com.light.redis.utils.ExcelUtils;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;

import com.light.core.entity.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会议表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-08-16 17:51:00
 */
@Slf4j
@RestController
@Validated
@RequestMapping("meeting")
@Api(value = "", tags = "会议表接口")
public class MeetingController {

    @Autowired
    private MeetingApiService meetingApiService;

    /**
     * 查询会议表分页列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询会议表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getMeetingPageListByCondition(@RequestBody MeetingConditionBo condition) {
        condition.setOrderBy("create_time DESC");
        return meetingApiService.getMeetingPageListByCondition(condition);
    }

    /**
     * 查询我参与的会议列表
     *
     * <AUTHOR>
     * @date 2022/8/22 9:29
     */
    @PostMapping("/my/list")
    @ApiOperation(value = "查询我参与的会议列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getMyMeetingPageListByCondition(@RequestBody MeetingConditionBo condition) {
        condition.setOrderBy("create_time DESC");
        return meetingApiService.getMyMeetingPageListByCondition(condition);
    }

    /**
     * 按日期查看会议室列表
     *
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/list")
    @ApiOperation(value = "按日期查看会议室列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getMeetingListByDate(@RequestBody MeetingConditionBo condition) {
        return meetingApiService.getMeetingListByDate(condition);
    }

    /**
     * 新增或修改会议表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增或修改会议表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addMeeting(@RequestBody MeetingBo meetingBo) {
        return meetingApiService.addMeeting(meetingBo);
    }

    /**
     * 新增或修改会议表-批量
     *
     * <AUTHOR>
     * @date 2023-09-14 17:51:00
     */
    @PostMapping("/add-batch")
    @ApiOperation(value = "新增或修改会议表-批量", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addMeetingBatch(@RequestBody MeetingBo meetingBo) {
        return meetingApiService.addMeetingBatch(meetingBo);
    }

    /**
     * 查询会议表详情
     * 
     * @param meetingId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询会议表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "meetingId", value = "会议表meetingId", required = true,
        dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult getDetail(@RequestParam("meetingId") Long meetingId) {
        if (null == meetingId) {
            return AjaxResult.fail("会议表id不能为空");
        }
        return meetingApiService.getDetail(meetingId);
    }

    /**
     * 删除会议表
     * 
     * @param meetingBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除会议表", httpMethod = SystemConstants.POST_REQUEST)
    @ApiImplicitParam(name = "id", value = "会议表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@RequestBody MeetingBo meetingBo) {
        if (null == meetingBo.getMeetingId()) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        return meetingApiService.delete(meetingBo);
    }

    /**
     * 获取教师列表
     *
     * @param teacherConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/29 11:27
     */
    @PostMapping("/teacher-list")
    @ApiOperation(value = "获取教师列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getTeacherListByCondition(@RequestBody TeacherConditionBo teacherConditionBo) {
        return meetingApiService.getTeacherListByCondition(teacherConditionBo);
    }

    /**
     * 下载教师场地预约导入模板
     *
     * @param request the request
     * @param response the response
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -03-30 15:50:18
     */
    @GetMapping("/teacher-template")
    @ApiOperation(value = "下载教师场地预约导入模板", httpMethod = "GET")
    public void teacherTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String teacherTemplateName = "场地预约教师导入模板.xlsx";
            URI uri = new URI(null, null, teacherTemplateName, null);
            OutputStream out = response.getOutputStream();
            response.reset();
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-Disposition",
                "attachment; filename=" + uri.toASCIIString() + ";filename*=utf-8''" + uri.toASCIIString());
            response.setContentType("application/vnd.ms-excel");
            String templateFileName = "meeting_teacher_import_template.xlsx";
            InputStream in = ResourceUtil.getStream("templates/" + templateFileName);
            // 后续如果需要修改模板可以在这里改写流
            IoUtil.copy(in, out);
        } catch (SecurityException | IllegalArgumentException | URISyntaxException e) {
            log.error("/teacher/template error1:", e);
        } catch (Exception e) {
            log.error("/teacher/template error2:", e);
        }
    }

    /**
     * 下载学生场地预约导入模板
     *
     * @param request the request
     * @param response the response
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -03-30 15:50:18
     */
    @GetMapping("/student-template")
    @ApiOperation(value = "下载学生场地预约导入模板", httpMethod = "GET")
    public void studentTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String teacherTemplateName = "场地预约学生导入模板.xlsx";
            URI uri = new URI(null, null, teacherTemplateName, null);
            OutputStream out = response.getOutputStream();
            response.reset();
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-Disposition",
                "attachment; filename=" + uri.toASCIIString() + ";filename*=utf-8''" + uri.toASCIIString());
            response.setContentType("application/vnd.ms-excel");
            String templateFileName = "meeting_student_import_template.xlsx";
            InputStream in = ResourceUtil.getStream("templates/" + templateFileName);
            // 后续如果需要修改模板可以在这里改写流
            IoUtil.copy(in, out);
        } catch (SecurityException | IllegalArgumentException | URISyntaxException e) {
            log.error("/student/template error1:", e);
        } catch (Exception e) {
            log.error("/student/template error2:", e);
        }
    }

    /**
     * 场地预约教师导入（实际只是查询校验，并不会插入db）
     *
     * @param file the file
     * @param organizationId the organization id
     * @return ajax result
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2024 -05-29 15:54:25
     */
    @ApiOperation(value = "场地预约教师导入", httpMethod = "POST")
    @PostMapping("/teacher-import")
    public AjaxResult teacherImport(MultipartFile file, Long organizationId) throws IOException {
        List<MeetingTeacherImportBo> list =
            ExcelUtils.importExcel(file.getInputStream(), 2, 1, true, MeetingTeacherImportBo.class);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.fail("导入数据为空");
        }
        MeetingImportBo<MeetingTeacherImportBo> meetingImportBo = new MeetingImportBo<>();
        meetingImportBo.setList(list);
        meetingImportBo.setOrganizationId(organizationId);
        AjaxResult teacherImportCheckResult = meetingApiService.getTeacherImportCheckResult(meetingImportBo);
        return teacherImportCheckResult;
    }

    /**
     * 场地预约学生导入（实际只是查询校验，并不会插入db）
     *
     * @param file the file
     * @param organizationId the organization id
     * @return ajax result
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2024 -05-29 15:54:25
     */
    @ApiOperation(value = "场地预约学生导入", httpMethod = "POST")
    @PostMapping("/student-import")
    public AjaxResult studentImport(MultipartFile file, Long organizationId) throws IOException {
        List<MeetingStudentImportBo> list =
            ExcelUtils.importExcel(file.getInputStream(), 3, 1, true, MeetingStudentImportBo.class);
        MeetingImportBo<MeetingStudentImportBo> meetingImportBo = new MeetingImportBo<>();
        meetingImportBo.setList(list);
        meetingImportBo.setOrganizationId(organizationId);
        return meetingApiService.getStudentImportCheckResult(meetingImportBo);
    }
}
