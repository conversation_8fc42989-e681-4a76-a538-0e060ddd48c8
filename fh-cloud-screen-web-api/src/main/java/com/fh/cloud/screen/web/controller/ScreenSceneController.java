package com.fh.cloud.screen.web.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenSceneApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 云屏内容controller
 *
 * <AUTHOR>
 * @date 2022/5/7 17:05
 */
@RestController
@RequestMapping("/screen/scene")
@Api(value = "云屏场景管理", tags = "云屏场景管理")
public class ScreenSceneController {

    @Resource
    private ScreenSceneApi screenSceneApi;

    /**
     * 查询云屏场景表列表
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏场景表列表", httpMethod = "POST")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public AjaxResult listScene(@RequestBody ScreenSceneListConditionBo conditionBo) throws Exception {
        conditionBo.setSingleShow(true);
        return screenSceneApi.getScreenSceneListByCondition(conditionBo);
    }

    // /**
    // * 查询云屏场景详情
    // *
    // * @param screenSceneId the screen scene id
    // * @return org detail
    // * @throws Exception the exception
    // * <AUTHOR>
    // * @date 2022 -03-29 14:44:04
    // */
    // @ApiOperation(value = "查询云屏场景表详情", httpMethod = "GET")
    // @RequestMapping(value = "/detail", method = RequestMethod.GET)
    // public AjaxResult listScene(@RequestParam("screenSceneId") Long screenSceneId) throws Exception {
    // return screenSceneApi.getDetail(screenSceneId);
    // }

    /**
     * 保存云屏场景（含更新操作）
     *
     * @param screenSceneBo the screen scene bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "保存云屏场景表详情", httpMethod = "POST")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public AjaxResult saveScene(@RequestBody ScreenSceneBo screenSceneBo) throws Exception {
        return screenSceneApi.saveOrUpdateScreenScene(screenSceneBo);
    }

    /**
     * 保存云屏场景（含更新操作）-批量
     *
     *
     * @param screenSceneBos the screen scene bos
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "保存云屏场景表详情批量", httpMethod = "POST")
    @RequestMapping(value = "/save-batch", method = RequestMethod.POST)
    public AjaxResult saveSceneBatch(@RequestBody List<ScreenSceneBo> screenSceneBos) throws Exception {
        return screenSceneApi.saveOrUpdateScreenSceneBath(screenSceneBos);
    }

    /**
     * 删除云屏场景
     *
     * @param screenSceneId 云屏id
     * @param organizationId 组织id
     * @return ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -05-30 15:51:31
     */
    @ApiOperation(value = "删除云屏场景", httpMethod = "GET")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public AjaxResult deleteScene(@RequestParam("screenSceneId") Long screenSceneId,
                                  @RequestParam(value = "organizationId", required = false) Long organizationId,
                                  @RequestParam(value = "parentOrganizationId", required = false) Long parentOrganizationId)
        throws Exception {
        return screenSceneApi.delete(screenSceneId, organizationId, parentOrganizationId);
    }
}
