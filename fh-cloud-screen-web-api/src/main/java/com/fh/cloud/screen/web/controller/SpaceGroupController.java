package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.space.api.SpaceGroupApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 地点组控制器
 *
 * <AUTHOR>
 * @date 2022/5/11 14:34
 */
@RestController
@RequestMapping("/space/group")
@Api(value = "云屏地点组管理", tags = "云屏地点组管理")
public class SpaceGroupController {
    @Resource
    private SpaceGroupApi spaceGroupApi;

    /**
     * 查询云屏所有地点组信息
     *
     * @param screenContentListConditionBo the screen content list condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏所有地点组信息", httpMethod = "GET")
    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public AjaxResult listAll() throws Exception {
        return spaceGroupApi.findAll();
    }
}
