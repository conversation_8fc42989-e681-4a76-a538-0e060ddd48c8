package com.fh.cloud.screen.websocekt.api.message.api;

import com.fh.cloud.screen.websocekt.api.message.entity.bo.WsSendBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 消息api
 */
public interface MessageApi {

    /**
     * 查询考勤流水表，不用于业务查询列表
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/ws/clients")
    @ApiOperation(value = "查询所有客户端", httpMethod = "GET")
    AjaxResult sessionLook();

    /**
     * session查询
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/ws/channel-session")
    @ApiOperation(value = "channel-session查询", httpMethod = "GET")
    AjaxResult channelSessionDto();

    /**
     * session查询
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/ws/client-session")
    @ApiOperation(value = "client-session查询", httpMethod = "GET")
    AjaxResult clientSessionDto();


    /**
     * 查看指定房间的所有连接
     *
     * @return
     */
    @GetMapping("/ws/client")
    @ApiOperation(value = "查询指定房间的所有连接", httpMethod = "GET")
    AjaxResult sessionLookOne(@RequestParam("roomId") String roomId);

    /**
     * 查看当前的webSocket的session
     *
     * @param wsSendBo the ws send bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -05-06 10:04:40
     */
    @PostMapping("/ws/send-ws")
    @ApiOperation(value = "发送ws消息", httpMethod = "POST")
    AjaxResult sendWs(@RequestBody WsSendBo wsSendBo);

    /**
     * 批量查询房间的所有连接
     *
     * @param roomIds
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/2 11:32
     **/
    @GetMapping("/ws/client-batch")
    @ApiOperation(value = "批量查询房间的所有连接", httpMethod = "GET")
    AjaxResult sessionLookBatch(@RequestParam("roomIds") List<String> roomIds);

}
