package com.fh.cloud.screen.websocekt.api.message.service;

import com.fh.cloud.screen.websocekt.api.consts.ConstServiceName;
import com.fh.cloud.screen.websocekt.api.message.api.MessageApi;
import com.fh.cloud.screen.websocekt.api.message.entity.bo.WsSendBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "messageApiService", value = ConstServiceName.FH_CLOUD_SCREEN_WEB_SOCKET,
    configuration = FeignClientInterceptor.class, fallbackFactory = MessageApiService.MessageApiFallbackFactory.class)
@Component
public interface MessageApiService extends MessageApi {
    @Component
    class MessageApiFallbackFactory implements FallbackFactory<MessageApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(MessageApiFallbackFactory.class);

        @Override
        public MessageApiService create(Throwable cause) {
            MessageApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new MessageApiService() {
                public AjaxResult sessionLook() {
                    return AjaxResult.fail("查询所有客户端");
                }

                public AjaxResult sessionLookOne(String roomId) {
                    return AjaxResult.fail("查询指定房间的所有连接");
                }

                public AjaxResult sendWs(WsSendBo wsSendBo) {
                    return AjaxResult.fail("发送ws消息");
                }

                @Override
                public AjaxResult sessionLookBatch(List<String> roomIds) {
                    return AjaxResult.fail("批量查询房间的所有连接失败");
                }

                @Override
                public AjaxResult channelSessionDto() {
                    return AjaxResult.fail("channelSessionDto报错");
                }

                @Override
                public AjaxResult clientSessionDto() {
                    return AjaxResult.fail("clientSessionDto报错");
                }
            };
        }
    }
}
