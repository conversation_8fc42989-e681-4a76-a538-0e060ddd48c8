package com.fh.cloud.screen.websocket.verticle;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.websocket.consts.ClientConstantUtil;
import com.fh.cloud.screen.websocket.consts.ConstantsRedis;
import com.fh.cloud.screen.websocket.dto.ClientDto;
import com.fh.cloud.screen.websocket.dto.HeaderDto;
import com.fh.cloud.screen.websocket.dto.SessionDto;
import com.fh.cloud.screen.websocket.dto.WebSocketData;
import com.fh.cloud.screen.websocket.enums.CmdType;
import com.fh.cloud.screen.websocket.manager.SessionManager;
import com.light.redis.component.RedisComponent;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpServer;
import io.vertx.core.http.HttpServerResponse;
import io.vertx.core.http.ServerWebSocket;
import io.vertx.core.http.WebSocketFrame;
import io.vertx.core.json.Json;
import io.vertx.ext.web.Router;
import lombok.extern.slf4j.Slf4j;

/**
 * websocket verticle
 *
 * <AUTHOR>
 * @date 2021 /1/29 15:53
 */
@Slf4j
@Component
public class WebSocketVerticle extends AbstractVerticle {
    /**
     * 会话管理器
     */
    private SessionManager sessionManager = SessionManager.getInstance();

    /**
     * 连接会话 channel --> session
     */
    private Map<String, SessionDto> channelSessionMap = sessionManager.getChannelSessionMap();
    /**
     * 连接会话 clientId --> session
     */
    private Map<String, SessionDto> clientSessionMap = sessionManager.getClientSessionMap();

    /**
     * 业务存储 roomId --> clientIds
     */
    private static Map<String, List<String>> roomClientMap = new ConcurrentHashMap<>();

    @Value("${web.socket.route}")
    private String webSocketRoute;

    @Value("${web.socket.port}")
    private String webSocketPort;

    @Autowired
    private RedisComponent redisComponent;

    @Override
    public void start() throws Exception {
        super.start();

        HttpServer server = vertx.createHttpServer();
        Router router = Router.router(vertx);
        router.route(webSocketRoute).handler(routingContext -> {
            HttpServerResponse response = routingContext.response();
            response.putHeader("content-type", "text/plain");
            response.end("Connected!");
        });

        webSocketMethod(server);
        server.requestHandler(router).listen(Integer.parseInt(webSocketPort)).onSuccess(res -> {
            log.info("websocket start success");
            System.out.println("websocket start success");
        });
    }

    @Override
    public void stop() throws Exception {
        super.stop();
    }

    /**
     * websocket处理方法
     *
     * @param server the server
     * <AUTHOR>
     * @date 2021 -01-29 16:52:39
     */
    public void webSocketMethod(HttpServer server) {
        server.webSocketHandler(webSocket -> {
            String token = webSocket.query();
            // token校验失败则reject
            boolean tokenValidate = checkTokenValidate(token);
            if (!tokenValidate) {
                webSocket.reject(403);
            }

            // 获取每一个链接的Id
            String channelId = webSocket.binaryHandlerID();
            // 判断当前连接的ID是否存在于map集合中，如果不存在则添加进map集合中
            if (!checkId(channelId)) {
                channelSessionMap.put(channelId, new SessionDto(channelId, webSocket));
            }

            // WebSocket连接处理
            webSocket.frameHandler(webSocketFrame -> {
                // 无会话直接结束，否则绑定到用户会话的分片数据
                if (channelSessionMap.get(channelId) == null) {
                    log.info("channelSessionMap.get(channelId) is null, channelId:" + channelId);
                    return;
                }
                StringBuffer sliceData = channelSessionMap.get(channelId).getData();

                if (webSocketFrame.isPing()) {
                    final String finalTempClientId = getClientIdFromChannelId(channelId);
                    log.info("server receive ping from channelId :" + channelId + " clientId:" + finalTempClientId);
                    pongSocketFrameHandler(webSocket, channelId, webSocketFrame);
                    // 放入缓存：2分钟，2分钟客户端未发心跳，表示断开连接（拔网线或者断电这两种情况协议层感知不到，因此需要通过下面逻辑处理）
                    String cacheKey = StringUtils.join(ConstantsRedis.client_cache_prefix, channelId);
                    redisComponent.set(cacheKey, channelId, ConstantsRedis.client_cache_expire_in);
                } else if (webSocketFrame.isContinuation()) {
                    sliceData.append(webSocketFrame.textData());
                    if (webSocketFrame.isFinal()) {
                        webSocketFrame = WebSocketFrame.continuationFrame(Buffer.buffer(sliceData.toString()), true);
                        sliceData.setLength(0);
                        webSocketFrameHandler(webSocket, channelId, webSocketFrame);
                    }
                } else if (webSocketFrame.isText()) {
                    if (webSocketFrame.isFinal()) {
                        webSocketFrameHandler(webSocket, channelId, webSocketFrame);
                    } else {
                        sliceData.append(webSocketFrame.textData());
                    }
                } else if (webSocketFrame.isClose()) {
                    closeHandler(channelId);
                }
            }).exceptionHandler(throwable -> {
                // 用于记录日志的clientId，有可能为空，比如创建刚开始建立连接的时候以及异常断开
                final String finalTempClientId = getClientIdFromChannelId(channelId);
                log.info("exceptionHandler:" + throwable + " clientId:" + finalTempClientId);
                closeHandler(channelId);
            }).closeHandler(handler -> {
                final String finalTempClientId = getClientIdFromChannelId(channelId);
                log.info("closeHandler channelId:" + channelId + " clientId:" + finalTempClientId);
                closeHandler(channelId);
            });
        });
    }

    /**
     * 根据channelId获取clientId
     * 
     * @param channelId
     * @return
     */
    private String getClientIdFromChannelId(String channelId) {
        String tempClientId = "";
        try {
            if (channelSessionMap != null && channelSessionMap.get(channelId) != null
                && channelSessionMap.get(channelId).getClientId() != null) {
                tempClientId = channelSessionMap.get(channelId).getClientId();
            }
        } catch (Exception e) {
            return "";
        }
        return tempClientId;
    }

    /**
     * 检查当前ID是否已经存在与map中
     *
     * @param channelId 链接id
     * @return the boolean
     * <AUTHOR>
     * @date 2021 -03-18 10:04:45
     */
    public boolean checkId(String channelId) {
        return channelSessionMap.containsKey(channelId);
    }

    /**
     * websocket ping 消息处理
     *
     * @param webSocket the web socket
     * <AUTHOR>
     * @date 2021 -03-03 11:17:38
     */
    public void pongSocketFrameHandler(ServerWebSocket webSocket, String channelId, WebSocketFrame webSocketFrame) {
        webSocket.writePong(webSocketFrame.binaryData());
    }

    /**
     * websocket主业务处理(客户端连接事件)
     *
     * @param webSocket the web socket
     * @param channelId the channel id
     * @param webSocketFrame the web socket frame
     * <AUTHOR>
     * @date 2021 -03-02 15:36:55
     */
    public void webSocketFrameHandler(ServerWebSocket webSocket, String channelId, WebSocketFrame webSocketFrame) {
        // 默认消息类型-广播消息
        Integer cmd = CmdType.BROADCAST.getValue();
        String from = "";
        String to = "";
        String textData = webSocketFrame.textData();
        if (StringUtils.isBlank(textData)) {
            return;
        }

        try {
            if (log.isInfoEnabled()) {
                log.info("textData:{}", textData);
            }

            WebSocketData webSocketData = Json.decodeValue(textData, WebSocketData.class);
            SessionDto sessionDto = channelSessionMap.get(channelId);
            cmd = webSocketData.getCmd();
            HeaderDto header = webSocketData.getHeader();
            Object body = webSocketData.getBody();
            String clientId = "";
            Integer clientType = null;
            String roomId = "";
            if (header != null) {
                clientId = header.getClientId();
                clientType = header.getClientType();
                roomId = header.getRoomId();
                to = header.getTo();
                from = clientId;
            }

            if (CmdType.LOGIN.getValue().equals(cmd)) {
                // 会话处理
                sessionDto.setClientId(clientId);
                sessionDto.setClientType(clientType);
                sessionDto.setRoomId(roomId);
                if (!roomClientMap.containsKey(roomId)) {
                    roomClientMap.put(roomId, new ArrayList<>());
                }
                List<String> clientIds = roomClientMap.get(roomId);
                if (!clientIds.contains(clientId) && StringUtils.isNotBlank(clientId)) {
                    clientIds.add(clientId);
                }
                sessionDto.setClientIds(clientIds);
                clientSessionMap.put(clientId, sessionDto);

                // 登录返回数据
                sendMessage(webSocket, CmdType.LOGIN_RESPONSE.getValue(), ClientConstantUtil.SUCCESS, "websocket登录成功",
                    sessionDto.getClientId(), sessionDto.getClientId());
            } else if (CmdType.BROADCAST.getValue().equals(cmd)) {
                // 广播消息。本次也没有。没有客户端给客户端发送消息，因此不用处理该消息类型。2022-04-28
                Stream<String> stream = sessionDto.getClientIds().stream();
                stream.forEach(s -> sendMessage(clientSessionMap.get(s).getWebSocket(), CmdType.BROADCAST.getValue(),
                    ClientConstantUtil.SUCCESS, body, sessionDto.getClientId(), s));
            }
        } catch (Exception e) {
            log.error("webSocketFrameHandler error:", e);
            sendMessage(webSocket, cmd, ClientConstantUtil.FAIL, e.getLocalizedMessage(), from, to);
        }
    }

    /**
     * websocket连接断开处理（客户端断开事件）
     *
     * @param channelId the channel id
     * <AUTHOR>
     * @date 2021 -03-02 15:41:22
     */
    public void closeHandler(String channelId) {
        log.info("close channel:" + channelId);
        // 给同一个房间的其他客户端推送客户端下线消息
        SessionDto sessionDto = channelSessionMap.get(channelId);
        if (sessionDto == null) {
            return;
        }

        // 主动关闭有问题的连接
        try {
            if (sessionDto.getWebSocket() != null) {
                sessionDto.getWebSocket().close();
            }
        } catch (Exception e) {
            // 主动关闭有问题则忽略
        }
        // 会话移除
        String clientId = sessionDto.getClientId();
        // 连接直接移除
        if (StringUtils.isNotBlank(channelId)) {
            channelSessionMap.remove(channelId);
        }
        // 会话需要判断后移除场景判断：客户端未退出直接登录，此时会有2个channel,1个client。关闭第一个channel的时候判断此时client里面的channel和第一个是否相等
        if (StringUtils.isNotBlank(clientId) && clientSessionMap.containsKey(clientId)) {
            SessionDto sessionDtoNew = clientSessionMap.get(clientId);
            if (sessionDtoNew != null && channelId.equals(sessionDtoNew.getId())) {
                // 移除会话
                clientSessionMap.remove(clientId);
                // 移除房间里面的会话id
                List<String> clientIds = null;
                if (StringUtils.isNotBlank(sessionDto.getRoomId())) {
                    clientIds = roomClientMap.get(sessionDto.getRoomId());
                }
                if (CollectionUtils.isEmpty(clientIds)) {
                    return;
                }
                clientIds.remove(clientId);
                // 会话移除后，房间为空，则删除房间
                if (CollectionUtils.isEmpty(clientIds) && StringUtils.isNotBlank(sessionDto.getRoomId())) {
                    roomClientMap.remove(sessionDto.getRoomId());
                }
            }
        }
    }

    /**
     * 调用websocket发送消息
     *
     * @param webSocket websocket实例，给谁发就要用谁的实例
     * @param cmd {@link CmdType}
     * @param code 业务数据响应code
     * @param value 业务数据值
     * @param from 消息发送方
     * @param to 消息接收方
     * <AUTHOR>
     * @date 2021 -03-02 15:52:55
     */
    public void sendMessage(ServerWebSocket webSocket, Integer cmd, String code, Object value, String from, String to) {
        if (StringUtils.isBlank(from) || StringUtils.isBlank(to)) {
            return;
        }
        try {
            WebSocketData webSocketData = new WebSocketData();
            webSocketData.setCmd(cmd);
            ClientDto clientDto = new ClientDto();
            clientDto.setCode(code);
            clientDto.setValue(value);
            webSocketData.setBody(clientDto);
            HeaderDto header = new HeaderDto();
            header.setFrom(from);
            header.setTo(to);
            webSocketData.setHeader(header);
            String writeMessage = Json.encode(webSocketData);
            if (log.isInfoEnabled()) {
                log.info("sendMessage:{}", writeMessage);
            }
            webSocket.writeTextMessage(writeMessage);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.info("sendMessage error:{}", e.getLocalizedMessage());
            }
        }
    }

    /**
     * token校验。使用班牌key校验
     *
     * <AUTHOR>
     * @date 2022 -04-28 16:07:26
     */
    public boolean checkTokenValidate(String token) {
        if (StringUtils.isBlank(token)) {
            return false;
        }
        return true;
    }

    /**
     * 输出roomClient
     *
     * @return room client map
     * <AUTHOR>
     * @date 2021 -03-24 19:25:34
     */
    public static Map<String, List<String>> getRoomClientMap() {
        return roomClientMap;
    }

    /**
     * 输出roomClient
     *
     * @return room client map
     * <AUTHOR>
     * @date 2021 -03-24 19:25:34
     */
    public Map<String, SessionDto> getClientSessionMap() {
        return this.clientSessionMap;
    }

    /**
     * 输出channelSessionMap
     *
     * @return the channel session map
     * <AUTHOR>
     * @date 2021 -03-24 19:25:34
     */
    public Map<String, SessionDto> getChannelSessionMap() {
        return this.channelSessionMap;
    }

    /**
     * 服务端主动发送消息给客户端
     *
     * @param roomId 房间id，必填
     * @param clientIds 客户端，选填
     * @param data
     * <AUTHOR>
     * @date 2022 -04-28 15:43:01
     */
    public void sendServer2Client(String roomId, List<String> clientIds, Object value) {
        if (StringUtils.isBlank(roomId)) {
            return;
        }
        List<String> clientIdsTemp = roomClientMap.get(roomId);
        if (CollectionUtils.isEmpty(clientIdsTemp)) {
            return;
        }

        if (CollectionUtils.isNotEmpty(clientIds)) {
            // 指定给客户端发送
            clientIds.stream()
                .filter(clientId -> clientSessionMap.get(clientId) != null
                    && clientSessionMap.get(clientId).getWebSocket() != null)
                .forEach(clientId -> sendMessage(clientSessionMap.get(clientId).getWebSocket(),
                    CmdType.BROADCAST.getValue(), ClientConstantUtil.SUCCESS, value, clientId, clientId));
        } else {
            // 给roomId内全部客户端发送
            clientIdsTemp.stream()
                .filter(clientId -> clientSessionMap.get(clientId) != null
                    && clientSessionMap.get(clientId).getWebSocket() != null)
                .forEach(clientId -> sendMessage(clientSessionMap.get(clientId).getWebSocket(),
                    CmdType.BROADCAST.getValue(), ClientConstantUtil.SUCCESS, value, clientId, clientId));
        }
    }
}
