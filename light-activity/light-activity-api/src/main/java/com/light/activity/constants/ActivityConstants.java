package com.light.activity.constants;

import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/6/16
 */
@Data
public class ActivityConstants {

    public static final String PHONE = "手机号";

    public static final String EMAIL = "邮箱";

    public static final String IDENTITY = "身份";

    public static final String NAME = "姓名";

    public static final String SEX = "性别";

    public static final String AREA = "地区";

    public static final String DEPARTMENT = "单位";

    public static final String TEACHER = "教师";

    public static final String PROFESSOR = "专家";

    public static final String ID_CARD_NUM = "身份证";

    public static final String PAY_TYPE = "缴费方式";

    public static final String USER_UNIQUE_TAG = "sysPhone";
    public static final String USER_UNIQUE_TAG_NAME = "用户标识";

    public static final String PROPERTIES = "properties";
    public static final String TITLE = "title";
    public static final String INFO_CONTENT = "infoContent";

    public static final String FORM_PROPERTY_KEY_DATALABEL = "dataLabel";
    public static final String FORM_PROPERTY_KEY_DICTVALUE = "dictValue";
    public static final String FORM_PROPERTY_KEY_DICTLABEL = "dictLabel";
    public static final String FORM_PROPERTY_KEY_WIDGET = "widget";
    public static final String FORM_PROPERTY_KEY_CASCADER = "Cascader";
    public static final String FORM_PROPERTY_KEY_CASCADER_REGION = "cascader-region";
    public static final String FORM_PROPERTY_KEY_CASCADER_CATEGORY = "cascader-category";
    public static final String FORM_PROPERTY_KEY_SELECT_DICT = "select-dict";
    public static final String FORM_PROPERTY_KEY_DESCRIPTION = "description";
    public static final String FORM_PROPERTY_KEY_PARENTIDS = "parentIds";
    public static final String FORM_PROPERTY_KEY_PARENTNAMES = "parentNames";
    public static final String FORM_PROPERTY_KEY_PARENTAREAIDS = "parentAreaIds";
    public static final String FORM_PROPERTY_KEY_PARENTAREANAMES = "parentAreaNames";
    public static final String FORM_PROPERTY_KEY_LABEL = "label";
    public static final String FORM_PROPERTY_KEY_VALUE = "value";
    public static final String FORM_PROPERTY_KEY_DATA = "data";
    public static final String FORM_PROPERTY_KEY_ENUM = "enum";
    public static final String FORM_PROPERTY_KEY_ENUM_NAMES = "enumNames";


    public static final String EMPTY_DATA = "数据为空";

    public static final String SEX_KEY = "sex";
    public static final String PHONE_KEY = "phone";
    public static final String USER_OID = "userOid";
    public static final String USER_NAME = "name";

    public static final String ORDER_NO = "orderNo";

    public static final List<String> FORM_DATA_ARRAY_WIDGET = Arrays.asList("select","multiSelect","radio","checkboxes");
    public static final List<String> FORM_VALUE_ARRAY_WIDGET = Arrays.asList("multiSelect","checkboxes");

    public static final Integer SINGUP_INFO_NORMAL = 1;//正常
    public static final Integer SINGUP_INFO_REJECT = 2;//拒绝
    public static final Integer SINGUP_INFO_CANCEL = 3;//取消

    public static final Long SINGUP_FORM_TYPE_ACTIVITY = 1l;//活动动态表单
    public static final Long SINGUP_FORM_TYPE_COURSE = 2l;//课程动态表单
    public static final Long SINGUP_FORM_TYPE_RESEARCH = 3l;//调查动态表单



    private ActivityConstants() {

    }
}
