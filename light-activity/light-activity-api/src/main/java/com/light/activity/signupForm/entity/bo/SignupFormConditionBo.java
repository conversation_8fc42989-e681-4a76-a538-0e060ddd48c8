package com.light.activity.signupForm.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 报名表单
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-04-03 14:49:20
 */
@Data
public class SignupFormConditionBo extends PageLimitBo{

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 表单标题
	 */
	@ApiModelProperty("表单标题")
	private String title;

	/**
	 * 活动ID
	 */
	@ApiModelProperty("活动ID")
	private Long relationId;

	/**
	 * 类型（1、活动，2、课程，3、调查）
	 */
	@ApiModelProperty("类型（1、活动，2、课程，3、调查）")
	private Long type;

	/**
	 * 表单内容json
	 */
	@ApiModelProperty("表单内容json")
	private String infoContent;

	/**
	 * 状态 1 启用  0 禁用
	 */
	@ApiModelProperty("状态 1 启用  0 禁用")
	private Integer state;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createTime;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateTime;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String updateBy;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Integer isDelete;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private String createBy;

}
