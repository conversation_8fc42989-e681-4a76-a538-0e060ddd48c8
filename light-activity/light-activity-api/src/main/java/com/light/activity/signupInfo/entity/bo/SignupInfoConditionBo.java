package com.light.activity.signupInfo.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 报名用户采集表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-04-03 14:49:20
 */
@Data
public class SignupInfoConditionBo extends PageLimitBo{

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 用户ID
	 */
	@ApiModelProperty("用户ID")
	private String userOid;

	/**
	 * 活动ID
	 */
	@ApiModelProperty("活动ID")
	private Long relationId;

	@ApiModelProperty("活动ID")
	private List<Long> relationIdList;
	/**
	 * 类型（1、活动，2、课程，3、调查）
	 */
	@ApiModelProperty("类型（1、活动，2、课程，3、调查）")
	private Long relationType;

	/**
	 * 用户数据json
	 */
	@ApiModelProperty("用户数据json")
	private String infoContent;

	/**
	 * 报名状态（0 报名中 1：报名成功，2：取消中，3：已取消）
	 */
	@ApiModelProperty("报名状态（0 报名中 1：报名成功，2：取消中，3：已取消））")
	private Integer state;

    @ApiModelProperty("报名状态（0 报名中 1：报名成功，2：取消中，3：已取消）")
    private List<Integer> stateList;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 *
	 */
	@ApiModelProperty("")
	private String updateBy;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Integer isDelete;

	/**
	 *
	 */
	@ApiModelProperty("")
	private String createBy;

    /**
     * 是否是考试活动 0 否  1 是
     */
    @ApiModelProperty("是否是考试活动 0 否  1 是")
    private Integer isExamActivity;

	/**
	 * 参数
	 */
	@ApiModelProperty("参数")
	private Map<String, String> params;


    /**
     * 用户OID集合
     */
    private List<String> userOidList;

    private List<String> notUserOidList;

    /**
     * 动态 查询参数
     */
    private String dynamicQueryParam;

    /**
     * 活动是否收费
     */
    private String activityIsFree;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 支付类型：1 线上支付  2 线下支付  3 免费
     */
    @ApiModelProperty("支付类型：1 线上支付  2 线下支付  3 免费")
    public Integer payType;

    /**
     * 支付方式：1 微信  2 支付宝 3 对公账号 4 线下付款
     */
    @ApiModelProperty("支付方式：1 微信  2 支付宝 3 对公账号 4 线下付款")
    public Integer payMode;

    /**
     *  支付状态： 1 待支付  2 已支付  3 未支付  4 待退款 5 退款中 6 已退款 7 退款失败
     */
    @ApiModelProperty("支付状态： 1 待支付  2 已支付  3 未支付  4 待退款 5 退款中 6 已退款 7 退款失败")
    private Integer payStatus;

    /**
     * 开票状态 1 待申请 2 申请中 3 申请成功 4 申请失败
     */
    @ApiModelProperty("开票状态 1 待申请 2 申请中 3 申请成功 4 申请失败")
    private Integer invoiceStatus;

    /**
     *支付类型数组 1 线上支付  2 线下支付  3 免费
     */
    @ApiModelProperty("支付类型数组 1 线上支付  2 线下支付  3 免费")
    private List<Integer> payTypeList;


    /**
     *  支付状态： 1 待支付  2 已支付  3 未支付  4 待退款 5 退款中 6 已退款 7 退款失败
     */
    @ApiModelProperty("支付状态数组： 1 待支付  2 已支付  3 未支付  4 待退款 5 退款中 6 已退款 7 退款失败")
    private List<Integer> payStatusList;

    /**
     * 排除该报名状态
     */
    @ApiModelProperty("排除该报名状态")
    private Integer neState;

    /**
     * 排除支付方式
     */
    @ApiModelProperty("排除支付方式")
    private Integer nePayType;

}
