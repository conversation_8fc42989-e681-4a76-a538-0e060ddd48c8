package com.light.activity.activity.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.activity.activity.entity.dto.ActivityDto;
import com.light.activity.activity.entity.bo.ActivityConditionBo;
import com.light.activity.activity.entity.vo.ActivityVo;

/**
 * 活动Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-04-03 14:49:20
 */
public interface ActivityMapper extends BaseMapper<ActivityDto> {

	List<ActivityVo> getActivityListByCondition(ActivityConditionBo condition);

    ActivityVo getActivityByCondition(ActivityConditionBo condition);
}
