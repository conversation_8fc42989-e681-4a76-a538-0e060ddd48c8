package com.light.activity.activity.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.light.activity.activity.enums.ActivityStateEnums;
import com.light.activity.certificateUser.service.ICertificateUserService;
import com.light.activity.enums.SignupInfoEnums;
import com.light.activity.signupForm.entity.dto.SignupFormDto;
import com.light.activity.signupForm.service.ISignupFormService;
import com.light.activity.signupInfo.entity.dto.SignupInfoDto;
import com.light.activity.signupInfo.service.ISignupInfoService;
import com.light.core.enums.SignupFormEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.security.service.CurrentUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.activity.activity.entity.dto.ActivityDto;
import com.light.activity.activity.entity.bo.ActivityConditionBo;
import com.light.activity.activity.entity.bo.ActivityBo;
import com.light.activity.activity.entity.vo.ActivityVo;
import com.light.activity.activity.service.IActivityService;
import com.light.activity.activity.mapper.ActivityMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 活动接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-04-03 14:49:20
 */
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, ActivityDto> implements IActivityService {

	@Resource
	private ActivityMapper activityMapper;

	@Resource
	private ISignupFormService signupFormService;

	@Resource
	private ISignupInfoService signupInfoService;

	@Resource
	private CurrentUserService currentUserService;

    @Resource
    private ICertificateUserService iCertificateUserService;

    @Override
	public List<ActivityVo> getActivityListByCondition(ActivityConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return activityMapper.getActivityListByCondition(condition);
	}

	@Override
	public AjaxResult addActivity(ActivityBo activityBo) {
		ActivityDto activity = new ActivityDto();
		BeanUtils.copyProperties(activityBo, activity);
		activity.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(activity)){
			//表单信息不为空则保存表单
			if(!StringUtils.isEmpty(activityBo.getInfoContent())) {
				SignupFormDto form = new SignupFormDto();
				form.setInfoContent(activityBo.getInfoContent());
				form.setRelationId(activity.getId());
				form.setType(1l);
				form.setState(1);
				signupFormService.save(form);
			}
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateActivity(ActivityBo activityBo) {
		ActivityDto activity = new ActivityDto();
		BeanUtils.copyProperties(activityBo, activity);
		if(updateById(activity)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ActivityVo getDetail(Long id) {
		LambdaQueryWrapper<ActivityDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ActivityDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(ActivityDto::getId, id);
		ActivityDto activity = getOne(lqw);
		ActivityVo vo = new ActivityVo();
		if(null != activity) {
            BeanUtils.copyProperties(activity, vo);

            // 活动开始状态
            final int status = ActivityStateEnums.buildStatus(activity.getStartTime(), activity.getEndTime());
            vo.setActivityStartState(status);

            // 活动报名状态
            vo.setActivityApplyState(ActivityStateEnums.buildStatus(activity.getApplyStartTime(),activity.getApplyEndTime()));

            //查询是否参与
			LambdaQueryWrapper<SignupInfoDto> lqws = new LambdaQueryWrapper<SignupInfoDto>();
			lqws.eq(SignupInfoDto::getRelationId, activity.getId());
			lqws.eq(SignupInfoDto::getRelationType, 1);
			lqws.ne(SignupInfoDto::getState, SignupInfoEnums.State.CANCELED.getVal());
			lqws.eq(SignupInfoDto::getUserOid, currentUserService.getCurrentOid());
			lqws.eq(SignupInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            final List<SignupInfoDto> list = signupInfoService.list(lqws);
            if(!CollectionUtils.isEmpty(list)) {
                vo.setSignupInfoId(list.get(0).getId());
				vo.setIsJoin(1);
			}else{
				vo.setIsJoin(0);
			}
            vo.setUserCertificateCount(0);
            // 用户证书证书数量
            final Long certificateId = activity.getCertificateId();
            final String userOid = currentUserService.getCurrentOid();
            if(certificateId != null && StrUtil.isNotEmpty(userOid)){
                Integer count = this.iCertificateUserService.getCountByCertificateIdAndUserOid(certificateId, userOid);
                vo.setUserCertificateCount(count);
            }
        }
		return vo;
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteActivityById(Long id) {

        final ActivityDto dto = this.activityMapper.selectById(id);
        if(dto == null){
            return AjaxResult.fail("活动不存在");
        }

        final Integer isPublish = dto.getIsPublish();
        if(StatusEnum.YES.getCode().equals(isPublish)){
            return AjaxResult.fail("活动已上架，无法删除");
        }

        // 删除活动
        ActivityDto activityDto = new ActivityDto();
        activityDto.setId(id);
        activityDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        this.activityMapper.updateById(activityDto);

        // 删除活动表单
        this.signupFormService.deleteByRelationIdAndType(id, SignupFormEnum.ACTIVITY.getVal());

        // 删除报名人员
        this.signupInfoService.deleteByRelationIdAndType(id, SignupFormEnum.ACTIVITY.getVal());

        return AjaxResult.success();
    }

	@Override
	public ActivityVo getActivityByCondition(ActivityConditionBo condition) {
    	condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return activityMapper.getActivityByCondition(condition);
	}

	@Override
    public Integer getCountByTaskIdAndNeId(Long taskId, Long id) {
        QueryWrapper<ActivityDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityDto::getTaskId, taskId)
                .eq(ActivityDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .ne(ActivityDto::getId, id);
        return this.activityMapper.selectCount(queryWrapper);
    }

    @Override
    public Integer getCountByExamIdAndNeId(Long examId, Long id) {
        QueryWrapper<ActivityDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityDto::getExamId, examId)
                .eq(ActivityDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .ne(ActivityDto::getId, id);
        return this.activityMapper.selectCount(queryWrapper);
    }

    @Override
    public Integer getCountByExamId(Long examId) {
        QueryWrapper<ActivityDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityDto::getExamId, examId)
                .eq(ActivityDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.activityMapper.selectCount(queryWrapper);
    }

    @Override
    public boolean unbindExamId(Long id) {
        UpdateWrapper<ActivityDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ActivityDto::getId, id)
                .set(ActivityDto::getExamId, null);
        return this.update(updateWrapper);
    }
}
