package com.light.activity.certificateUser.thread;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.activity.certificateUser.entity.dto.CertificateUserDto;
import com.light.activity.certificateUser.service.CertificateUserApiService;
import com.light.activity.certificateUser.service.ICertificateUserService;
import com.light.activity.constants.ActivityConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.entity.Progress;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 证书导入用户
 * @date 2023/6/17
 */
@Slf4j
public class CertificateUserImportThread extends Thread {

    private RedisComponent redisComponent = SpringUtil.getBean(RedisComponent.class);

    private ICertificateUserService certificateUserService = SpringUtil.getBean(ICertificateUserService.class);

    private String redisKey;

    private List<Map<String, Object>> list;

    private Long certificateId;

    private String certificateOid;

    public CertificateUserImportThread(List<Map<String, Object>> list, String redisKey,
                                       Long certificateId, String certificateOid) {
        this.redisKey = redisKey;
        this.list = list;
        this.certificateId = certificateId;
        this.certificateOid = certificateOid;
    }

    @Override
    @Async
    public void run() {
        Object object = redisComponent.get(redisKey);
        Progress progress = JSON.parseObject(object.toString(), Progress.class);
        Integer total = progress.getTotal();
        Integer successCount = progress.getSuccess();
        Integer failureCount = progress.getFail();
        StringBuilder error = new StringBuilder("");
        List<CertificateUserDto> arr = new ArrayList();
        int i = 0;
        for (Map<String, Object> map : list) {
            i++;
            try{
                CertificateUserDto certificateUserDto = new CertificateUserDto();
                certificateUserDto.setCertificateId(certificateId);
                certificateUserDto.setCertificateOid(certificateOid);
                certificateUserDto.setPhone(null != map.get(ActivityConstants.PHONE)?map.get(ActivityConstants.PHONE).toString():null);
                Object name = map.get(ActivityConstants.NAME);
                if (name == null || StringUtils.isEmpty(name.toString())) {
                    error.append("第" + i + "行姓名不能为空" + "\n");
                    continue;
                }
                certificateUserDto.setWinnersName(name.toString());
                Object user = map.get(ActivityConstants.USER_OID);
                if (user != null) {
                    certificateUserDto.setUserOid(user.toString());
                }
                Object award = map.get("奖项");
                if (award != null) {
                    certificateUserDto.setAwardsName(award.toString());
                }
                successCount ++;
                progress.setSuccess(successCount);
                if(i<total) {
                    progress.setProgress((i * 100d) / total);
                    redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
                }
                arr.add(certificateUserDto);
            }catch (Exception e) {
                log.error("证书人员导入线程错误：{}", getStackTrace(e));
                failureCount ++;
                progress.setFail(failureCount);
                if(i<total) {
                    progress.setProgress((i * 100d) / total);
                    redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
                }
            }
        }
        certificateUserService.update(null, new LambdaUpdateWrapper<CertificateUserDto>()
                .set(CertificateUserDto::getIsDelete, StatusEnum.ISDELETE.getCode())
                .eq(CertificateUserDto::getIsGenerator, 0)
                .eq(CertificateUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(CertificateUserDto::getCertificateId, certificateId)
        );
        certificateUserService.saveBatch(arr);
        progress.setProgress(100d);
        progress.setErrorMessage(error.toString());
        redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
    }

    /**
     * 获取堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }

}
