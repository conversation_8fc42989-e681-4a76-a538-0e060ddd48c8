package com.light.activity.signupInfo.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.activity.activity.entity.dto.ActivityDto;
import com.light.activity.activity.service.IActivityService;
import com.light.activity.constants.ActivityConstants;
import com.light.activity.enums.SignupInfoRelationTypeEnum;
import com.light.activity.signupInfo.entity.dto.SignupInfoDto;
import com.light.core.utils.StringUtils;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.light.activity.signupInfo.entity.bo.SignupInfoConditionBo;
import com.light.activity.signupInfo.entity.bo.SignupInfoBo;
import com.light.activity.signupInfo.entity.vo.SignupInfoVo;
import com.light.activity.signupInfo.service.ISignupInfoService;

import com.light.activity.signupInfo.api.SignupInfoApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 报名用户采集表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-04-03 14:49:20
 */
@RestController
@Validated
@Api(value = "", tags = "报名用户采集表接口")
public class SignupInfoController implements SignupInfoApi {

    @Autowired
    private ISignupInfoService signupInfoService;

    @Resource
    private IActivityService iActivityService;

    @Autowired
    private CurrentUserService currentUserService;

    /**
     * 查询报名用户采集表分页列表
     * <AUTHOR>
     * @date 2023-04-18 16:15:58
     */
    @Override
    public AjaxResult<PageInfo<SignupInfoVo>> getSignupInfoPageListByCondition(@RequestBody SignupInfoConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SignupInfoDto> pageInfo = new PageInfo<>(signupInfoService.getSignupInfoListByCondition(condition));
        PageInfo<SignupInfoVo> page = new PageInfo<>();
        page.setList(JSON.parseArray(JSON.toJSONString(pageInfo.getList()), SignupInfoVo.class));
        page.setTotal(pageInfo.getTotal());
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询报名用户采集表列表
     * <AUTHOR>
     * @date 2023-04-18 16:15:58
     */
    @Override
    public AjaxResult<List<SignupInfoVo>> getSignupInfoListByCondition(@RequestBody SignupInfoConditionBo condition){
        List<SignupInfoDto> list = signupInfoService.getSignupInfoListByCondition(condition);
        //List<SignupInfoVo> voList = JSON.parseArray(JSON.toJSONString(list), SignupInfoVo.class);
        return AjaxResult.success(list);
    }


    public AjaxResult<SignupInfoVo> addSignupInfo(@Validated @RequestBody SignupInfoBo signupInfoBo) {
        return signupInfoService.addSignupInfo(signupInfoBo);
    }

    public AjaxResult updateSignupInfo(@Validated @RequestBody SignupInfoBo signupInfoBo) {
        if (null == signupInfoBo.getId()) {
            return AjaxResult.fail("id不能为空");
        }
        return signupInfoService.updateSignupInfo(signupInfoBo);
    }

    /**
     * 查询报名用户采集表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-18 16:15:58
     */
    @Override
    public AjaxResult<SignupInfoVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("报名用户采集表id不能为空");
        }
        SignupInfoVo vo = signupInfoService.getDetail(id);
        return AjaxResult.success(vo);
    }

    public AjaxResult delete(@RequestParam("id") Long id) {
        String userOid = currentUserService.getCurrentOid();
        if(StringUtils.isEmpty(userOid)) {
            return AjaxResult.fail("获取当前用户失败");
        }
        final SignupInfoDto signupInfoDto = this.signupInfoService.getById(id);
        if(signupInfoDto == null){
            return AjaxResult.success();
        }
        final Long relationType = signupInfoDto.getRelationType();
        final Long relationId = signupInfoDto.getRelationId();
        // 活动 校验活动是否报名结束
        if(relationType != null && relationType.equals(SignupInfoRelationTypeEnum.ACTIVITY.getVal())){
            final ActivityDto activityDto = this.iActivityService.getById(relationId);
            if(activityDto != null){
                final Date applyEndTime = activityDto.getApplyEndTime();
                if(applyEndTime != null) {
                    // 活动报名结束
                    if (DateUtil.date().getTime() > applyEndTime.getTime()) {
                        return AjaxResult.fail("活动报名已结束，无法取消报名");
                    }
                }
            }
        }
        LambdaUpdateWrapper<SignupInfoDto> luw = new LambdaUpdateWrapper<>();
        luw.eq(SignupInfoDto::getId, id);
        luw.eq(SignupInfoDto::getUserOid, userOid);
        luw.set(SignupInfoDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        if(signupInfoService.update(luw)) {
            return AjaxResult.success("取消报名成功");
        }
        return AjaxResult.success("取消报名失败");
    }

    @Override
    public AjaxResult importUser(SignupInfoBo signupInfoBo) {
        return signupInfoService.importUser(signupInfoBo);
    }

    @Override
    public AjaxResult deleteByAdmin(@RequestParam("id")Long id) {
        LambdaUpdateWrapper<SignupInfoDto> luw = new LambdaUpdateWrapper<>();
        luw.eq(SignupInfoDto::getId, id);
        luw.set(SignupInfoDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        if(signupInfoService.update(luw)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.success("删除失败");
    }

    @Override
    public AjaxResult batchDeleteByIds(@RequestBody List<Long> ids) {
        LambdaUpdateWrapper<SignupInfoDto> luw = new LambdaUpdateWrapper<>();
        luw.in(SignupInfoDto::getId, ids);
        luw.set(SignupInfoDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        if(signupInfoService.update(luw)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.success("删除失败");
    }

    @Override
    public AjaxResult<SignupInfoVo> getSignupInfoByCondition(@RequestBody SignupInfoConditionBo condition) {
        return AjaxResult.success(signupInfoService.getSignupInfoByCondition(condition));
    }

    @Override
    public AjaxResult<PageInfo<SignupInfoVo>> getActivitySignupInfoPageListByCondition(@RequestBody SignupInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SignupInfoVo> pageInfo = new PageInfo<>(signupInfoService.getSignupInfoPageListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    @Override
    public AjaxResult<List<SignupInfoVo>> getActivitySignupInfoListByCondition(SignupInfoConditionBo condition) {
        return AjaxResult.success(signupInfoService.getSignupInfoPageListByCondition(condition));
    }

    @Override
    public AjaxResult<PageInfo<SignupInfoVo>> getMyActivityPageListByCondition(@RequestBody SignupInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SignupInfoVo> pageInfo = new PageInfo<>(signupInfoService.getSignupInfoPageListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    @Override
    public AjaxResult<List<SignupInfoVo>> getActivityListByCondition(@RequestBody SignupInfoConditionBo condition) {
        return AjaxResult.success(signupInfoService.getSignupInfoPageListByCondition(condition));
    }

    @Override
    public AjaxResult<Long> getSignupCount(@RequestParam("relationId") Long relationId,
                                           @RequestParam("relationType") Integer relationType) {
        LambdaQueryWrapper<SignupInfoDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SignupInfoDto::getRelationId, relationId);
        lqw.eq(SignupInfoDto::getRelationType,relationType);
        lqw.eq(SignupInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(SignupInfoDto::getState, 1);
        int count = signupInfoService.count(lqw);
        return AjaxResult.success(Long.valueOf(count));
    }

    @Override
    public AjaxResult<List<SignupInfoVo>> getUserActivitySignupInfo(@RequestBody SignupInfoConditionBo condition) {
        if(CollUtil.isEmpty(condition.getUserOidList())){
            return AjaxResult.fail("用户OID集合不能为空");
        }
        if(condition.getRelationId() == null){
            return AjaxResult.fail("关系ID不能为空");
        }
        condition.setRelationType(SignupInfoRelationTypeEnum.ACTIVITY.getVal().longValue());
        List<SignupInfoVo> signupInfoVo  = signupInfoService.getUserSignupInfo(condition );
        return AjaxResult.success(signupInfoVo);
    }

    @Override
    public AjaxResult<List<Map<String, Object>>> getSignupInfoListByActivityId(@RequestParam("activityId") Long activityId,
                                                                               @RequestParam("type") Long type) {
        SignupInfoConditionBo condition = new SignupInfoConditionBo();
        condition.setRelationId(activityId);
        condition.setState(ActivityConstants.SINGUP_INFO_NORMAL);
        condition.setRelationType(type);
        return AjaxResult.success(signupInfoService.getSignupInfoMapByCondition(condition));
    }

    @Override
    public AjaxResult<SignupInfoVo> getById(@RequestParam("id") Long id) {
        return AjaxResult.success(this.signupInfoService.getSignupInfoVoById(id));
    }

    @Override
    public AjaxResult<Integer> getSignedCountByRelationId(@RequestParam("relationId") Long relationId, @RequestParam("relationType") Long relationType) {
        return AjaxResult.success(this.signupInfoService.getSignedCountByRelationId(relationId,relationType));
    }

    @Override
    public AjaxResult<SignupInfoVo> getByOrderNo(@RequestParam("orderNo") String orderNo) {
        SignupInfoVo signupInfoVo = this.signupInfoService.getByOrderNo(orderNo);
        return AjaxResult.success(signupInfoVo);
    }

    @Override
    public AjaxResult<Integer> cancelOnlineExpirePaySignInfo() {
        return AjaxResult.success(this.signupInfoService.cancelOnlineExpirePaySignInfo());
    }

    @Override
    public AjaxResult updateBatchByInvoiceRecordId(@RequestBody List<SignupInfoBo> signupInfoBoList) {
        return this.signupInfoService.updateBatchByInvoiceRecordId(signupInfoBoList);
    }
}
