package com.light.activity.signupInfo.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.activity.signupInfo.entity.dto.SignupInfoDto;
import com.light.activity.signupInfo.entity.bo.SignupInfoConditionBo;
import com.light.activity.signupInfo.entity.vo.SignupInfoVo;

/**
 * 报名用户采集表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-04-03 14:49:20
 */
public interface SignupInfoMapper extends BaseMapper<SignupInfoDto> {

	List<SignupInfoVo> getSignupInfoPageListByCondition(SignupInfoConditionBo condition);

    List<Map<String, Object>> getSignupInfoMapByCondition(SignupInfoConditionBo condition);

    /**
     *  取消 超时未支付的线上报名信息
     *
     * @return int
     */
    int cancelOnlineExpirePaySignInfo();

}
