ALTER TABLE `p_activity`
    ADD COLUMN `task_id` bigint(20) NULL DEFAULT NULL COMMENT '作品征集任务ID' AFTER `is_publish`,
    ADD COLUMN `exam_id` bigint(20) NULL DEFAULT NULL COMMENT '考试ID' AFTER `task_id`,
    ADD COLUMN `is_has_file` tinyint(1) NULL DEFAULT NULL COMMENT '是否有附件 0 否 1 是' AFTER `exam_id`,
    ADD COLUMN `file_tips` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件提示' AFTER `is_has_file`;

ALTER TABLE `p_signup_info`
    ADD COLUMN `file_oids` varchar(255) NULL COMMENT '附件OID 多个逗号分隔' AFTER `remark`;
