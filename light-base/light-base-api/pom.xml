<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>light-base</artifactId>
        <groupId>com.light</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>light-base-api</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>基础服务api</name>
    <description>基础服务api</description>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.light</groupId>
            <artifactId>light-swagger</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.light</groupId>
            <artifactId>light-redis</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.light</groupId>
            <artifactId>light-feign</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


    </dependencies>

</project>
