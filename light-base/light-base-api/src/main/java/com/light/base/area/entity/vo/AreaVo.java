package com.light.base.area.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 平台地区信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-09 11:18:25
 */
@Data
public class AreaVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 地区名称
     */
    @ApiModelProperty("地区名称")
    private String areaName;

    /**
     * 地区级别
     */
    @ApiModelProperty("地区级别")
    private Long areaLevel;

    /**
     * 父级地区ID
     */
    @ApiModelProperty("父级地区ID")
    private Long parentAreaId;

    /**
     * 父级地区名称
     */
    @ApiModelProperty("父级地区名称")
    private String parentAreaName;

    /**
     * 父级地区ids
     */
    @ApiModelProperty("父级地区ids")
    private String parentAreaIds;

    /**
     * 父级地区名称
     */
    @ApiModelProperty("父级地区名称")
    private String parentAreaNames;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

}
