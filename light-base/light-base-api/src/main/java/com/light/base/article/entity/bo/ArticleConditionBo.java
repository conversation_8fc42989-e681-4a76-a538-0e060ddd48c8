package com.light.base.article.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文章表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-31 13:50:54
 */
@Data
public class ArticleConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 文章标题
	 */
	@ApiModelProperty("文章标题")
	private String title;

    /**
     * 封面URL
     */
    @ApiModelProperty("封面URL")
    private String coverUrl;

	/**
	 * 文章内容
	 */
	@ApiModelProperty("文章内容")
	private String content;

    /**
     * 通用分类ID，多个逗号分隔
     */
    @ApiModelProperty("通用分类ID")
    private String categoryId;

    /**
     * 是否发布 - 0 否  1 是
     */
    @ApiModelProperty("是否发布 0 否 1 是")
    private Integer isPublish;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sortNo;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 组织机构ID
     */
    @ApiModelProperty("组织机构ID")
    private Long organizationId;


	/**
	 * 是否删除 0 否 1是
	 */
	@ApiModelProperty("是否删除 0 否 1是")
	private Integer isDelete;

    /**
     * 模块CODE
     */
    @ApiModelProperty("模块CODE")
    private String moduleCode;

}
