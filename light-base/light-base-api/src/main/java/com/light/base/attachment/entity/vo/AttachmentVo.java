package com.light.base.attachment.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 附件表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-09-15 17:48:48
 */
@Data
public class AttachmentVo implements Serializable {

    public static final long serialVersionUID = 1l;

    /**
     * id
     */
    private Long id;

    /**
     * 文件目标标识
     */
    private String fileOid;

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 原文件名
     */
    private String originalName;

    /**
     * 新文件名
     */
    private String newName;

    /**
     * 文件后缀名
     */
    private String fileExtName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小(单位k)
     */
    private Long fileSize;

    /**
     * 视频 音频文件时长
     */
    private Long duration;

    /**
     * 文件相对路径
     */
    private String filePath;

    /**
     * 文件预览路径
     */
    private String previewPath;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否需要权限
     */
    private Integer needAuth;

    /**
     * 存储位置
     */
    private Integer storeLocation;

    /**
     * 文件请求地址
     */
    private String webUrl;

    /**
     * 移动云请求地址
     */
    private String ecloudUrl;


    private String kkPreview;

    /**
     * 全路径地址
     */
    private String previewAllPath;

    /**
     *  文件存储全路径地址
     */
    private String allPath;

    /**
     * bucket
     */
    private String bucket;

    private String nginxUrl;


    private String version;

}
