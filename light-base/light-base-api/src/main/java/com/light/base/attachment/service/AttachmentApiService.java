package com.light.base.attachment.service;

import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.bo.*;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/1612:48
 */
@FeignClient(contextId = "attachmentApiService", value= ServiceNameConstants.LIGHT_BASE, configuration = FeignClientInterceptor.class, fallbackFactory = AttachmentApiService.AttachmentApiFallbackFactory.class)
@Component
public interface AttachmentApiService extends AttachmentApi {

    @Component
    class AttachmentApiFallbackFactory implements FallbackFactory<AttachmentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(AttachmentApiService.AttachmentApiFallbackFactory.class);

        @Override
        public AttachmentApiService create(Throwable cause) {
            AttachmentApiService.AttachmentApiFallbackFactory.LOGGER.error("基础服务调用失败:{}", cause.getMessage());
            return new AttachmentApiService()
            {
                @Override
                public AjaxResult<AttachmentVo> addAttachment(AttachmentBo attachmentBo) {
                    return AjaxResult.fail("保存附件失败");
                }

                @Override
                public AjaxResult<AttachmentVo> uploadFile(MultipartFile file) {
                    return AjaxResult.fail("上传失败");
                }

                @Override
                public AjaxResult getAttachmentListByCondition(AttachmentConditionBo condition) {
                    return AjaxResult.fail("查询附件列表失败");
                }

                @Override
                public AjaxResult getKKPrefix() {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult updateAttachment(AttachmentBo attachmentBo) {
                    return AjaxResult.fail("更新附件失败");
                }

                @Override
                public AjaxResult getDetail(String fileOid) {
                    return AjaxResult.fail("获取附件详情失败");
                }

                @Override
                public AjaxResult<AttachmentVo> getByFileOid(String fileOid) {
                    return AjaxResult.fail("获取附件失败");
                }

                @Override
                public AjaxResult delete(Long attachmentId) {
                    return AjaxResult.fail("删除附件失败");
                }

                @Override
                public AjaxResult upload(MultipartFile file) throws IOException {
                    return AjaxResult.fail("上传附件失败");
                }

                @Override
                public AjaxResult uploadInspect(MultipartFormData data) throws IOException {
                    return AjaxResult.fail("上传附件失败");
                }

                @Override
                public AjaxResult<AttachmentVo> uploadByUrl(FileBo bo) {
                    return AjaxResult.fail("上传附件失败");
                }

                @Override
                public AjaxResult sliceUpload(FileChunkParam file) {
                    return AjaxResult.fail("上传附件失败");
                }

                @Override
                public AjaxResult sliceMerge(FileChunkParam file) {
                    return AjaxResult.fail("上传附件失败");
                }

                @Override
                public AjaxResult upload(MultipartFormData formData) throws IOException {
                    return AjaxResult.fail("上传附件失败");
                }

                @Override
                public AjaxResult<byte[]> getImgThumbnails(String oid, Integer width, Integer height) {
                    return AjaxResult.fail("获取失败");
                }

                @Override
                public AjaxResult<byte[]> getImgScaleThumbnails(String oid, Double scale) {
                    return AjaxResult.fail("获取失败");
                }

                @Override
                public byte[] preview(String fileOid) {
                    return null;
                }

                @Override
                public AjaxResult readFileNoLog(String fileOid)  {
                    return AjaxResult.fail("附件下载失败");
                }

                @Override
                public AjaxResult<List<AttachmentVo>> getByFileOids(List<String> fileOidList) {
                    return AjaxResult.fail("附件获取失败");
                }

                @Override
                public AjaxResult delFile(String fileOid) {
                    return AjaxResult.fail("附件删除失败");
                }

                @Override
                public AjaxResult<AttachmentVo> localFileCopy(FileBo fileBo) {
                    return AjaxResult.fail("文件处理失败");
                }

                @Override
                public AjaxResult<AttachmentVo> uploadEcloud(EcloudFileBo ecloudFileBo) {
                    return AjaxResult.fail("文件处理失败");
                }
            };
        }
    }
}
