package com.light.base.banner.entity.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/8/9
 */
@Data
public class BannerActivityExportVo implements Serializable {

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String name;

    /**
     * 链接地址
     */
    @Excel(name = "跳转地址")
    private String link;

    /**
     * 参会人员
     */
    @Excel(name = "参会人员")
    private String description;

    /**
     * 创建时间
     */
    @Excel(name = "添加时间")
    private String createTimeStr;

}
