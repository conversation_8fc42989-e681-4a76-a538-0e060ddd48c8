package com.light.base.category.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资源类别
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-16 11:17:11
 */
@Data
public class CategoryBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 资源类别id
	 */
	@ApiModelProperty("资源类别id")
	private Long categoryId;
	
	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;
	/**
	 * 父级ID
	 */
	@ApiModelProperty("父级ID")
	private Long parentId;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private String name;

	/**
	 *
	 */
	@ApiModelProperty("code")
	private String code;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private String description;
	/**
	 * 层级
	 */
	@ApiModelProperty("层级")
	private Integer level;
	/**
	 * 是否有子节点（1、是，2、否）
	 */
	@ApiModelProperty("是否有子节点（1、是，2、否）")
	private String hasChild;
	/**
	 * 所属模块ID
	 */
	@ApiModelProperty("所属模块ID")
	private String moduleCode;
	/**
	 * 类型
	 */
	@ApiModelProperty("类型")
	private Long typeId;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private String remark;
	/**
	 * 状态（1、正常，2、禁用 0 删除）
	 */
	@ApiModelProperty("状态（1、正常，2、禁用 0 删除）")
	private Long state;
	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;
	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Long sequence;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private String createBy;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date createTime;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private String updateBy;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private Date updateTime;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private Integer isDelete;

}
