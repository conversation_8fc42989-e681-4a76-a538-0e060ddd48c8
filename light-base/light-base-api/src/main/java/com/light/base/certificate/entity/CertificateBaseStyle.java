package com.light.base.certificate.entity;

import com.light.core.utils.BeanUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;

/**
 * <AUTHOR>
 * @description: 证书基础样式
 * @date 2023/4/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CertificateBaseStyle {

    @ApiModelProperty("字体")
    private String fontFamily;

    @ApiModelProperty("字体大小")
    private Integer fontSize;

    @ApiModelProperty("字体样式，0：PLAIN,1:BOLD,2:ITALIC")
    private Integer fontStyle;

    @ApiModelProperty("字体颜色")
    private Color color;

    @ApiModelProperty("左边距")
    private Integer left;

    @ApiModelProperty("字体颜色:16进制")
    private String fontColor;

    @ApiModelProperty("距离顶部距离")
    private Integer top;

    @ApiModelProperty("行间距")
    private Integer lineHeight;

    @ApiModelProperty("前一部分内容高度")
    private Integer preHeight;

    public static CertificateBaseStyle getStyle(CertificateBaseStyle style, String type) {
        CertificateBaseStyle base = getBaseStyle(type);
        if(style == null) {
            style = new CertificateBaseStyle();
        }
        BeanUtils.copyPropertiesWithNull(base, style);
        if(!StringUtils.isEmpty(style.getFontColor())) {
            style.setColor(toColorFromString(style.getFontColor()));
        }
        return style;
    }

    public static CertificateBaseStyle getBaseStyle(String type) {
        switch(type) {
            case "number":
                return (new CertificateBaseStyle()).setFontFamily("微软雅黑").setFontSize(15).setFontStyle(0)
                        .setColor(toColorFromString("#000000")).setLeft(40).setTop(60);
            case "title":
                return (new CertificateBaseStyle()).setFontFamily("楷体").setFontSize(70).setFontStyle(1)
                        .setColor(toColorFromString("#FF0000")).setTop(120);
            case "object": case "content": case "award": case "text":
                return (new CertificateBaseStyle()).setFontFamily("微软雅黑 粗体").setLineHeight(20)
                        .setFontSize(20).setFontStyle(0)
                        .setColor(toColorFromString("#000000")).setLeft(70).setTop(220);
            case "unit": case "date": case "signet":
                return (new CertificateBaseStyle()).setFontFamily("微软雅黑 粗体").setFontSize(20).setFontStyle(0)
                        .setColor(toColorFromString("#000000")).setTop(220).setLineHeight(5)
                        .setPreHeight(40);
            default:
                return (new CertificateBaseStyle()).setFontFamily("微软雅黑 粗体").setFontSize(20).setFontStyle(0)
                        .setColor(toColorFromString("#000000")).setLeft(70).setTop(220);
        }

    }


    /**
     * 字符串转换成Color对象
     * @param colorStr 16进制颜色字符串
     * @return Color对象
     * */
    public static Color toColorFromString(String colorStr){
        int length = colorStr.length();
        //需要处理一下那些缩写的，例如#ff0，我们要将他转为#ffff00
        if(length <= 4){
            char c1 = colorStr.charAt(1);
            char c2 = colorStr.charAt(2);
            char c3 = colorStr.charAt(3);
            colorStr = "#"+c1+c1+c2+c2+c3+c3;
        }
        String str1 = colorStr.substring(1, 3);
        String str2 = colorStr.substring(3, 5);
        String str3 = colorStr.substring(5, 7);
        int red = Integer.parseInt(str1, 16);
        int green = Integer.parseInt(str2, 16);
        int blue = Integer.parseInt(str3, 16);
        Color color =  new Color(red,green,blue) ;
        return color;
    }

}
