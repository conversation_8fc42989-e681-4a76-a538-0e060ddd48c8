package com.light.base.certificate.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 证书电子章
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-30 13:58:25
 */
@Data
public class CertificateSignetVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 电子章名称
     */
    @ApiModelProperty("电子章名称")
    private String name;

    /**
     * 电子章单位
     */
    @ApiModelProperty("电子章单位")
    private String unit;

    /**
     * 电子章图片
     */
    @ApiModelProperty("电子章图片")
    private String file;

    /**
     * 电子章状态，0：禁用，1：启用
     */
    @ApiModelProperty("电子章状态，0：禁用，1：启用")
    private Integer status;

    /**
     * 电子章使用说明
     */
    @ApiModelProperty("电子章使用说明")
    private String description;

    /**
     * 电子章宽度
     */
    @ApiModelProperty("电子章宽度")
    private Integer width;

    /**
     * 电子章高度
     */
    @ApiModelProperty("电子章高度")
    private Integer height;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("电子章图片预览地址")
    private String signetUrl;

    @ApiModelProperty("文件路径")
    private String filePath;

}
