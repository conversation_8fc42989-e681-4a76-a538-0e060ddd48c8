package com.light.base.config.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.util.Date;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-12-08 10:29:32
 */
@Data
public class ConfigConditionBo extends PageLimitBo{

	/**
	 * 参数配置id
	 */
	private Long id;
	
	/**
	 * 参数配置key
	 */
	private String configKey;
	
	/**
	 * 参数配置value
	 */
	private String configValue;
	
	/**
	 * 参数配置说明
	 */
	private String configDescription;
	
	/**
	 * 创建时间
	 */
	private Date createTime;
	
	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 更新人
	 */
	private String updateBy;

}
