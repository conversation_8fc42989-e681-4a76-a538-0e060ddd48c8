package com.light.base.dictionary.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 字典数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-13 16:04:30
 */
@Data
public class DictionaryDataBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典值id
     */
    private Long id;

    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    private String dictType;

    /**
     * 字典值
     */
    @NotBlank(message = "字典值不能为空")
    private String dictValue;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    private String dictLabel;

    /**
     * 字典排序
     */
    @NotNull(message = "字典排序不能为空")
    private Long dictSort;

    /**
     * 字典值状态：(0:禁用 1:启用)
     */
    private Integer dictDataStatus;

    /**
     *  字典值备注
     */
    private String dictDataRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
