package com.light.base.dictionary.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 字典数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-13 16:04:30
 */
@Data
public class DictionaryDataListConditionBo extends PageLimitBo {

	/**
	 * 字典值id
	 */
	private Long id;
	
	/**
	 * 字典类型
	 */
	private String dictType;

	/**
	 *  多字典类型
	 */
	private List<String> dictTypes;
	
	/**
	 * 字典值
	 */
	private String dictValue;
	
	/**
	 * 字典名称
	 */
	private String dictLabel;
	
	/**
	 * 字典排序
	 */
	private Long dictSort;
	
	/**
	 * 字典值状态：(0:禁用 1:启用)
	 */
	private Integer dictDataStatus;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 更新人
	 */
	private String updateBy;

}
