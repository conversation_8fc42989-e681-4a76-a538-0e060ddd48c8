package com.light.base.dictionary.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 字典类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-13 16:03:52
 */
@Data
public class DictionaryTypeBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字典类型id
     */
    private Long id;

    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    private String dictType;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    private String dictName;

    /**
     * 字典状态：(0:禁用 1:启用)
     */
    private Integer dictStatus;

    /**
     *  字典值备注
     */
    private String dictRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
