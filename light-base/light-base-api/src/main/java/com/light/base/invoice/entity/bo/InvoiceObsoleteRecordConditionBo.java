package com.light.base.invoice.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 发票申请作废记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-23 14:10:39
 */
@Data
public class InvoiceObsoleteRecordConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 申请ID
	 */
	@ApiModelProperty("申请ID")
	private Long invoiceRecordId;

	/**
	 * 红冲发票代码
	 */
	@ApiModelProperty("红冲发票代码")
	private String invoiceCode;

	/**
	 * 红冲发票号码
	 */
	@ApiModelProperty("红冲发票号码")
	private String invoiceNum;

	/**
	 * 红冲之后的发票地址
	 */
	@ApiModelProperty("红冲之后的发票地址")
	private String invoiceUrl;

	/**
	 * 作废申请时间
	 */
	@ApiModelProperty("作废申请时间")
	private Date applyTime;

	/**
	 * 作废时间
	 */
	@ApiModelProperty("作废时间")
	private Date obsoleteTime;

	/**
	 * 作废用户类型 1 前端用户 2 管理人员
	 */
	@ApiModelProperty("作废用户类型 1 前端用户 2 管理人员")
	private Integer userType;

	/**
	 * 作废用户名称
	 */
	@ApiModelProperty("作废用户名称")
	private String userName;

	/**
	 * 作废人OID
	 */
	@ApiModelProperty("作废人OID")
	private String userOid;

	/**
	 * 状态 1 作废中 2 已作废
	 */
	@ApiModelProperty("状态 1 作废中 2 已作废")
	private Integer status;

	/**
	 * 是否删除 0 否 1 是
	 */
	@ApiModelProperty("是否删除 0 否 1 是")
	private Integer isDelete;

    /**
     * 发票编号
     */
    private String recordSno;


    /**
     * 发票申请名称
     */
    private String recordName;

    /**
     * 发票申请用户名称
     */
    private String recordUserName;

    /**
     * 作废申请开始时间
     */
    private String obsoleteApplyBeginTime;

    /**
     * 作废申请结束时间
     */
    private String obsoleteApplyEndTime;


    private Long appliedDuration;
}
