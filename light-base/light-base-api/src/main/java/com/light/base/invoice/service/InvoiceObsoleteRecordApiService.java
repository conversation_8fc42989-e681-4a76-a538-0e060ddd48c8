package com.light.base.invoice.service;


import com.light.base.invoice.api.InvoiceObsoleteRecordApi;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordBo;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordConditionBo;
import com.light.base.invoice.entity.vo.InvoiceObsoleteRecordVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 发票申请作废记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-23 14:10:39
 */
@FeignClient(contextId = "invoiceObsoleteRecordApiService", value= "light-base", configuration = FeignClientInterceptor.class, fallbackFactory = InvoiceObsoleteRecordApiService.InvoiceObsoleteRecordApiFallbackFactory.class)
@Component
public interface InvoiceObsoleteRecordApiService extends InvoiceObsoleteRecordApi {

    @Component
    class InvoiceObsoleteRecordApiFallbackFactory implements FallbackFactory<InvoiceObsoleteRecordApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceObsoleteRecordApiFallbackFactory.class);
        @Override
        public InvoiceObsoleteRecordApiService create(Throwable cause) {
            InvoiceObsoleteRecordApiFallbackFactory.LOGGER.error("invoiceObsoleteRecordService服务调用失败:{}", cause.getMessage());
            return new InvoiceObsoleteRecordApiService() {
                public AjaxResult getInvoiceObsoleteRecordPageListByCondition(InvoiceObsoleteRecordConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getInvoiceObsoleteRecordListByCondition(InvoiceObsoleteRecordConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult<InvoiceObsoleteRecordVo> addInvoiceObsoleteRecord(InvoiceObsoleteRecordBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateInvoiceObsoleteRecord(InvoiceObsoleteRecordBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult updateBatchInvoiceObsoleteRecord(List<InvoiceObsoleteRecordBo> boList) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<InvoiceObsoleteRecordVo> getByBillId(String billId) {
                    return AjaxResult.fail("数据获取失败");
                }
            };
        }
    }
}
