package com.light.base.slow.api;

import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/21
 */
public interface BaseOpenSlowApi {

    public final String prefix = "base-open-slow";


    /**
     *  生成单个用户证书
     *
     * @param map the param map {oid : 证书OID , object: 证书称呼对象 可为空, award : 奖项 可为空, number： 编号 }
     * @return {@link AjaxResult}<{@link AttachmentVo}>
     */
    @PostMapping(prefix + "/certificate/generate/byUser")
    AjaxResult<AttachmentVo> generateByUser(@RequestBody Map<String, String> map);

}
