package com.light.base.slow.service;


import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.slow.api.BaseOpenSlowApi;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * BASE服务慢处理
 *
 */
@FeignClient(contextId = "base-open-slow-api", value = ServiceNameConstants.LIGHT_BASE, configuration = FeignClientInterceptor.class, fallbackFactory = BaseOpenSlowApiService.OpenSlowApiFallbackFactory.class)
@Component
public interface BaseOpenSlowApiService extends BaseOpenSlowApi {

    @Component
    class OpenSlowApiFallbackFactory implements FallbackFactory<BaseOpenSlowApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(OpenSlowApiFallbackFactory.class);

        @Override
        public BaseOpenSlowApiService create(Throwable cause) {
            OpenSlowApiFallbackFactory.LOGGER.error("BASE服务慢处理:{}", cause.getMessage());
            return new BaseOpenSlowApiService() {

                @Override
                public AjaxResult<AttachmentVo> generateByUser(Map<String, String> map) {
                    return AjaxResult.fail("生成失败");
                }
            };
        }
    }
}
