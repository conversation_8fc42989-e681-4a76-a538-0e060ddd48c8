package com.light.base.statistics.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/12/26
 */
@Data
public class StatisticsTrackingBo {


    /**
     * 平台 1 数字教材 2 凤凰易教
     */
    @NotEmpty(message = "平台不能为空", groups = {StatisticsTrackingBo.VIEW.class, StatisticsTrackingBo.DURATION.class})
    @ApiModelProperty("平台 1 数字教材 2 凤凰易教 ")
    private String platform;

    /**
     * 平台 1 数字教材 2 凤凰易教
     */
    @NotEmpty(message = "端口不能为空", groups = {StatisticsTrackingBo.VIEW.class, StatisticsTrackingBo.DURATION.class})
    @ApiModelProperty("端口 1 web 2 IOS 3 安卓  4 IPAD")
    private String client;

    /**
     * 访问时常
     */
    @NotNull(message = "访问时常不能为空", groups = {StatisticsTrackingBo.DURATION.class})
    @ApiModelProperty("访问时常")
    private Long viewDuration;


    public @interface VIEW{}

    public @interface DURATION{}



}
