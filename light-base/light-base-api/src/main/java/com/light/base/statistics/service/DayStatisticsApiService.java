package com.light.base.statistics.service;


import com.light.base.statistics.api.DayStatisticsApi;
import com.light.base.statistics.entity.bo.DayStatisticsBo;
import com.light.base.statistics.entity.bo.DayStatisticsConditionBo;
import com.light.base.statistics.entity.vo.DayStatisticsVo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.DatePeriodUtil;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 每日PV UV统计信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
@FeignClient(contextId = "dayStatisticsApiService", value= ServiceNameConstants.LIGHT_BASE, configuration = FeignClientInterceptor.class, fallbackFactory = DayStatisticsApiService.DayStatisticsApiFallbackFactory.class)
@Component
public interface DayStatisticsApiService extends DayStatisticsApi {

    @Component
    class DayStatisticsApiFallbackFactory implements FallbackFactory<DayStatisticsApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(DayStatisticsApiFallbackFactory.class);
        @Override
        public DayStatisticsApiService create(Throwable cause) {
            DayStatisticsApiFallbackFactory.LOGGER.error("基础服务库服务调用失败:{}", cause.getMessage());
            return new DayStatisticsApiService() {
                public AjaxResult getDayStatisticsPageListByCondition(DayStatisticsConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getDayStatisticsListByCondition(DayStatisticsConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addDayStatistics(DayStatisticsBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateDayStatistics(DayStatisticsBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<List<DayStatisticsVo>> getDayStatisticsByCondForGroupDate(DayStatisticsConditionBo conditionBo) {
                    return AjaxResult.fail("查询失败");
                }

                @Override
                public AjaxResult batchSave(List<DayStatisticsBo> dayStatisticsBoList) {
                    return AjaxResult.fail("保存失败");
                }
            };
        }
    }
}
