package com.light.base.statistics.service;


import com.light.base.statistics.api.MonthStatisticsApi;
import com.light.base.statistics.entity.bo.MonthStatisticsBo;
import com.light.base.statistics.entity.bo.MonthStatisticsConditionBo;
import com.light.base.statistics.entity.vo.MonthStatisticsVo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 每月 PV UV 总量统计信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
@FeignClient(contextId = "monthStatisticsApiService", value= ServiceNameConstants.LIGHT_BASE, configuration = FeignClientInterceptor.class, fallbackFactory = MonthStatisticsApiService.MonthStatisticsApiFallbackFactory.class)
@Component
public interface MonthStatisticsApiService extends MonthStatisticsApi {

    @Component
    class MonthStatisticsApiFallbackFactory implements FallbackFactory<MonthStatisticsApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(MonthStatisticsApiFallbackFactory.class);
        @Override
        public MonthStatisticsApiService create(Throwable cause) {
            MonthStatisticsApiFallbackFactory.LOGGER.error("基础服务库服务调用失败:{}", cause.getMessage());
            return new MonthStatisticsApiService() {
                public AjaxResult getMonthStatisticsPageListByCondition(MonthStatisticsConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getMonthStatisticsListByCondition(MonthStatisticsConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addMonthStatistics(MonthStatisticsBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateMonthStatistics(MonthStatisticsBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<List<MonthStatisticsVo>> getMonthStatisticsByCondForGroupDate(MonthStatisticsConditionBo conditionBo) {
                    return AjaxResult.fail("查询失败");
                }

                @Override
                public AjaxResult batchSaveOrUpdate(List<MonthStatisticsBo> monthStatisticsBoList) {
                    return AjaxResult.fail("保存失败");
                }
            };
        }
    }
}
