package com.light.base.tag.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 平台标签表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-06 13:53:05
 */
@Data
public class TagBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 *
	 */
	@ApiModelProperty("")
	private String name;

	/**
	 *
	 */
	@ApiModelProperty("")
	private String description;

	/**
	 *
	 */
	@ApiModelProperty("")
	private String remark;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 状态，0:禁用，1：启用
	 */
	@ApiModelProperty("状态，0:禁用，1：启用")
	private Integer state;

	/**
	 * 所属模块code
	 */
	@ApiModelProperty("所属模块code")
	private String moduleCode;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	private Long sequence;


    /**
     * 创建用户类型 1 管理员 2 前端用户
     */
    @ApiModelProperty("创建用户类型 1 管理员 2 前端用户")
    private Integer createType;

    @ApiModelProperty("创建用户")
    private String createBy;

}
