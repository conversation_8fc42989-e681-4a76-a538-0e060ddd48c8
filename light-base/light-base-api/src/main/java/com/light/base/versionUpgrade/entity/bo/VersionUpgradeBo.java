package com.light.base.versionUpgrade.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-16 09:41:02
 */
@Data
public class VersionUpgradeBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long versionUpgradeId;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String name;

	/**
	 * 终端类型（1：安卓、2：iOS、3：web、4：PAD、5：H5）
	 */
	private Integer terminalType;

	/**
	 * 当前apk版本号
	 */
	private String version;

	/**
	 * 1:windows 2:mac os 3：ios 4:android  5:小程序
	 */
	@ApiModelProperty("1:windows 2:mac os 3：ios 4:android  5:小程序")
	private Integer operatingSystem;
	/**
	 * 文件大小
	 */
	@ApiModelProperty("文件大小")
	private Long fileSize;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String upgradeDescription;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String currentVersion;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String minVersion;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String downloadUrl;
	/**
	 * 状态 1:未发布 2：已发布 3：删除
	 */
	@ApiModelProperty("状态 1:未发布 2：已发布 3：删除")
	private Integer state;
	/**
	 * 组织ID
	 */
	@ApiModelProperty("组织ID")
	private Long organizationId;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String clientType;
	/**
	 *
	 */
	@ApiModelProperty("")
	private Date publishTime;
	/**
	 * 1 未发布  2 已发布
	 */
	@ApiModelProperty("1 未发布  2 已发布")
	private Integer isPublished;
    /**
     * 自定义字段
     */
    @ApiModelProperty("自定义字段")
    private String key;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String createBy;
	/**
	 *
	 */
	@ApiModelProperty("")
	private Date createTime;
	/**
	 *
	 */
	@ApiModelProperty("")
	private String updateBy;
	/**
	 *
	 */
	@ApiModelProperty("")
	private Date updateTime;
	/**
	 *
	 */
	@ApiModelProperty("")
	private Integer isDelete;

	@ApiModelProperty("文件oid")
	private String fileOid;

}
