package com.light.base.area.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.base.area.api.AreaApi;
import com.light.base.area.entity.bo.AreaBo;
import com.light.base.area.entity.bo.AreaConditionBo;
import com.light.base.area.entity.dto.AreaDto;
import com.light.base.area.entity.vo.AreaVo;
import com.light.base.area.service.IAreaService;
import com.light.core.entity.AjaxResult;
import com.light.feign.annotation.FeignValidatorAnnotation;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台地区信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-09 11:18:25
 */
@RestController
@Validated
@Api(value = "", tags = "平台地区信息表接口" )
public class AreaController implements AreaApi {
	
    @Autowired
    private IAreaService areaService;

    @Autowired
	private RedisComponent redisComponent;

    /**
     * 查询平台地区信息表列表
     * <AUTHOR>
     * @date 2022-03-09 11:18:25
     */
	@FeignValidatorAnnotation
    public AjaxResult getAreaListByCondition(@RequestBody AreaConditionBo condition){
		List<AreaVo> list = areaService.getAreaListByCondition(condition);
		Map<String, Object> map = new HashMap<String, Object>(4);
		map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增平台地区信息表
     * <AUTHOR>
     * @date 2022-03-09 11:18:25
     */
	@FeignValidatorAnnotation
    public AjaxResult addArea(@Validated @RequestBody AreaBo areaBo){
		return areaService.addArea(areaBo);
    }

    /**
	 * 修改平台地区信息表
	 * @param areaBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-09 11:18:25
	 */
	@FeignValidatorAnnotation
	public AjaxResult updateArea(@Validated @RequestBody AreaBo areaBo) {
		if(null == areaBo.getId()) {
			return AjaxResult.fail("平台地区信息表id不能为空");
		}
		return areaService.updateArea(areaBo);
	}

	/**
	 * 查询平台地区信息表详情
	 * @param areaId
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-09 11:18:25
	 */
	@FeignValidatorAnnotation
	public AjaxResult getDetail(@NotNull(message = "请选择数据") Long areaId) {
		Map<String, Object> map = areaService.getDetail(areaId);
		return AjaxResult.success(map);
	}
    
    /**
	 * 删除平台地区信息表
	 * @param areaId
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-09 11:18:25
	 */
	@FeignValidatorAnnotation
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long areaId) {
		AreaBo areaBo = new AreaBo();
		areaBo.setAreaId(areaId);
		return areaService.updateArea(areaBo);
	}

	/**
	 * 从缓存中查询地区名称
	 * @param id
	 * @return
	 */
	@Override
	public AjaxResult<String> getAreaNameFromCache(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择地区id");
		}
		Object object = redisComponent.hget(RedisKeyEnum.AREA_KEY.getValue(), String.valueOf(id));
		if(null != object) {
			AreaDto dto = JSON.parseObject(JSON.toJSONString(object), AreaDto.class);
			return AjaxResult.success(dto.getAreaName());
		}
		return AjaxResult.success(null);
	}

	@Override
	public AjaxResult<List<String>> getAreaParentNames(@RequestBody AreaConditionBo condition) {
		return AjaxResult.success(areaService.getAreaParentNames(condition));
	}

	@Override
	public AjaxResult<List<AreaVo>> getOrderdAreaListByParentId(@RequestParam("parentAreaId") Long parentAreaId) {
		LambdaQueryWrapper<AreaDto> lqw = new LambdaQueryWrapper<AreaDto>();
		lqw.eq(AreaDto::getParentAreaId, parentAreaId);
		lqw.orderByAsc(AreaDto::getId);
		List<AreaDto> list = areaService.list(lqw);
		if(!CollectionUtils.isEmpty(list)) {
			List<AreaVo> areas = JSONArray.parseArray(JSON.toJSONString(list), AreaVo.class);
			return AjaxResult.success(areas);
		}else{
			return AjaxResult.success(new ArrayList<AreaVo>());
		}

	}
}
