package com.light.base.area.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.base.area.entity.bo.AreaConditionBo;
import com.light.base.area.entity.dto.AreaDto;
import com.light.base.area.entity.vo.AreaVo;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 平台地区信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-09 11:18:25
 */
public interface AreaMapper extends BaseMapper<AreaDto> {

	List<AreaVo> getAreaListByCondition(AreaConditionBo condition);

    List<String> getAreaParentNamesByCondition(AreaConditionBo condtion);
}
