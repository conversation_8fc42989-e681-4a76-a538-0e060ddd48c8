package com.light.base.attachment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.base.attachment.entity.bo.AttachmentConditionBo;
import com.light.base.attachment.entity.dto.AttachmentDto;
import com.light.base.attachment.entity.vo.AttachmentVo;

import java.util.List;

/**
 * 附件表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-09-15 17:48:48
 */
public interface AttachmentMapper extends BaseMapper<AttachmentDto> {

	List<AttachmentVo> getAttachmentListByCondition(AttachmentConditionBo condition);

}
