package com.light.base.attachment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.attachment.entity.bo.AttachmentBo;
import com.light.base.attachment.entity.bo.AttachmentConditionBo;
import com.light.base.attachment.entity.dto.AttachmentDto;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 附件表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-09-15 17:48:48
 */
public interface IAttachmentService extends IService<AttachmentDto> {

    List<AttachmentVo> getAttachmentListByCondition(AttachmentConditionBo condition);

	AjaxResult<AttachmentVo> addAttachment(AttachmentBo attachmentBo);

	AjaxResult updateAttachment(AttachmentBo attachmentBo);

	AttachmentVo getDetail(String fileOid);

    AttachmentDto getByFileOid(String fileOid);

    List<AttachmentVo> getByFileOids(List<String> fileOidList);
}

