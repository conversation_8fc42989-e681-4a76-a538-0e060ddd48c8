package com.light.base.certificate.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 证书
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-30 13:58:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_certificate")
public class CertificateDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 证书oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 证书名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 证书类型
	 */
	@TableField("type")
	private String type;

	/**
	 * 证书称呼对象
	 */
	@TableField("object")
	private String object;

	/**
	 * 证书内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 是否显示证书奖项，0：否，1：是
	 */
	@TableField("is_show_award")
	private Integer isShowAward;

	/**
	 * 证书文本
	 */
	@TableField("text")
	private String text;

	/**
	 * 是否默认背景
	 */
	@TableField("is_default_background")
	private Integer isDefaultBackground;

	/**
	 * 证书背景图片
	 */
	@TableField("background")
	private String background;

	/**
	 * 证书背景图片宽度
	 */
	@TableField(value = "background_width", strategy = FieldStrategy.IGNORED)
	private Integer backgroundWidth;

	/**
	 * 证书背景图片高度
	 */
	@TableField(value = "background_height", strategy = FieldStrategy.IGNORED)
	private Integer backgroundHeight;

	/**
	 * 证书发放单位
	 */
	@TableField("issuing_unit")
	private String issuingUnit;

	/**
	 * 证书发放日期
	 */
	@TableField("issuing_date")
	private String issuingDate;

	/**
	 * 是否有编号
	 */
	@TableField("is_number_count")
	private Integer isNumberCount;

	/**
	 * 证书编号总位数
	 */
	@TableField(value = "number_count", strategy = FieldStrategy.IGNORED)
	private Integer numberCount;

	/**
	 * 证书编号前缀
	 */
	@TableField(value = "number_prefix", strategy = FieldStrategy.IGNORED)
	private String numberPrefix;

	/**
	 * 证书编号起始
	 */
	@TableField(value = "number_start", strategy = FieldStrategy.IGNORED)
	private Long numberStart;

	/**
	 * 证书当前编号
	 */
	@TableField("current_number")
	private Long currentNumber;

	/**
	 * 是否配置电子章，0：否，1：是
	 */
	@TableField("is_signet")
	private Integer isSignet;

	/**
	 * 证书电子章
	 */
	@TableField("signet")
	private String signet;

	/**
	 * 组织机构id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 证书预览地址
	 */
	@TableField("preview_url")
	private String previewUrl;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
