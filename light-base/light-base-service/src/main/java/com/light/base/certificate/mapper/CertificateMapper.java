package com.light.base.certificate.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.base.certificate.entity.dto.CertificateDto;
import com.light.base.certificate.entity.bo.CertificateConditionBo;
import com.light.base.certificate.entity.vo.CertificateVo;
import org.apache.ibatis.annotations.Param;

/**
 * 证书Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-30 13:58:25
 */
public interface CertificateMapper extends BaseMapper<CertificateDto> {

	List<CertificateVo> getCertificateListByCondition(CertificateConditionBo condition);

}
