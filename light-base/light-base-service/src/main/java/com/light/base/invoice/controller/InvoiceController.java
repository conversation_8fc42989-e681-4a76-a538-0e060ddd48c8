package com.light.base.invoice.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.base.invoice.api.InvoiceApi;
import com.light.base.invoice.entity.bo.InvoiceBo;
import com.light.base.invoice.entity.bo.InvoiceConditionBo;
import com.light.base.invoice.entity.dto.InvoiceDto;
import com.light.base.invoice.entity.vo.InvoiceVo;
import com.light.base.invoice.service.IInvoiceService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 开票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-06 14:14:20
 */
@RestController
@Validated
public class InvoiceController implements InvoiceApi{

    @Autowired
    private IInvoiceService invoiceService;

    /**
     * 查询开票信息分页列表
     * <AUTHOR>
     * @date 2023-11-06 14:14:20
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<InvoiceVo>> getInvoicePageListByCondition(@RequestBody InvoiceConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<InvoiceVo> pageInfo = new PageInfo<>(invoiceService.getInvoiceListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询开票信息列表
	 * <AUTHOR>
	 * @date 2023-11-06 14:14:20
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<InvoiceVo>> getInvoiceListByCondition(@RequestBody InvoiceConditionBo condition){
		List<InvoiceVo> list = invoiceService.getInvoiceListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增开票信息
     * <AUTHOR>
     * @date 2023-11-06 14:14:20
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addInvoice(@Validated @RequestBody InvoiceBo invoiceBo){
		return invoiceService.addInvoice(invoiceBo);
    }

    /**
	 * 修改开票信息
	 * @param invoiceBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-06 14:14:20
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateInvoice(@Validated @RequestBody InvoiceBo invoiceBo) {
		if(null == invoiceBo.getId()) {
			return AjaxResult.fail("开票信息id不能为空");
		}
		return invoiceService.updateInvoice(invoiceBo);
	}

	/**
	 * 查询开票信息详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-06 14:14:20
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<InvoiceVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("开票信息id不能为空");
		}
		InvoiceConditionBo condition = new InvoiceConditionBo();
		condition.setId(id);
		InvoiceVo vo = invoiceService.getInvoiceByCondition(condition);
		return AjaxResult.success(vo);
	}


    /**
	 * 删除开票信息
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-06 14:14:20
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		InvoiceDto invoiceDto = new InvoiceDto();
		invoiceDto.setId(id);
		invoiceDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(invoiceService.updateById(invoiceDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
