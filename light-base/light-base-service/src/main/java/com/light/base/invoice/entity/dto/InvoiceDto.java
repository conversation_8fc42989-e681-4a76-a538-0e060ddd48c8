package com.light.base.invoice.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 开票信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-06 14:14:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_invoice")
public class InvoiceDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 申请人OID
	 */
	@TableField("user_oid")
	private String userOid;

    /**
     * 名称
     */
    @TableField("name")
    private String name;


	/**
	 * 发票抬头
	 */
	@TableField("title")
	private String title;

	/**
	 * 税号
	 */
	@TableField("tax_id")
	private String taxId;

	/**
	 * 开户银行
	 */
	@TableField("bank_name")
	private String bankName;

	/**
	 * 银行账号
	 */
	@TableField("bank_no")
	private String bankNo;

	/**
	 * 电话
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 地址
	 */
	@TableField("address")
	private String address;

	/**
	 * 是否删除 0 否 1 是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}
