package com.light.base.invoice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordBo;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordConditionBo;
import com.light.base.invoice.entity.dto.InvoiceObsoleteRecordDto;
import com.light.base.invoice.entity.vo.InvoiceObsoleteRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 发票申请作废记录接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-23 14:10:39
 */
public interface IInvoiceObsoleteRecordService extends IService<InvoiceObsoleteRecordDto> {

    List<InvoiceObsoleteRecordVo> getInvoiceObsoleteRecordListByCondition(InvoiceObsoleteRecordConditionBo condition);

	AjaxResult<InvoiceObsoleteRecordVo> addInvoiceObsoleteRecord(InvoiceObsoleteRecordBo invoiceObsoleteRecordBo);

	AjaxResult updateInvoiceObsoleteRecord(InvoiceObsoleteRecordBo invoiceObsoleteRecordBo);

	InvoiceObsoleteRecordVo getInvoiceObsoleteRecordByCondition(InvoiceObsoleteRecordConditionBo condition);

    InvoiceObsoleteRecordVo getByBillId(String billId);
}

