package com.light.base.nav.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.base.nav.entity.dto.NavDto;
import com.light.base.nav.entity.bo.NavConditionBo;
import com.light.base.nav.entity.vo.NavVo;
import com.light.core.entity.TreeNode;

/**
 * 自定义导航Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-14 17:59:59
 */
public interface NavMapper extends BaseMapper<NavDto> {

	List<NavVo> getNavListByCondition(NavConditionBo condition);

    List<String> getDistinctModuleCodes();
}
