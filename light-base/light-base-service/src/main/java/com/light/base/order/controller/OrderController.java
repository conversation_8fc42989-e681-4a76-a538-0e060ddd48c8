package com.light.base.order.controller;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.base.enums.OrderEnums;
import com.light.base.order.api.OrderApi;
import com.light.base.order.entity.bo.OrderBo;
import com.light.base.order.entity.bo.OrderConditionBo;
import com.light.base.order.entity.dto.OrderDto;
import com.light.base.order.entity.vo.OrderVo;
import com.light.base.order.service.IOrderService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 10:33:22
 */
@Slf4j
@RestController
@Validated
public class OrderController implements OrderApi{

    @Autowired
    private IOrderService orderService;



    /**
     * 查询订单表分页列表
     * <AUTHOR>
     * @date 2023-11-09 10:33:22
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<OrderVo>> getOrderPageListByCondition(@RequestBody OrderConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<OrderVo> pageInfo = new PageInfo<>(orderService.getOrderListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询订单表列表
	 * <AUTHOR>
	 * @date 2023-11-09 10:33:22
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<OrderVo>> getOrderListByCondition(@RequestBody OrderConditionBo condition){
		List<OrderVo> list = orderService.getOrderListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增订单表
     *
     * <AUTHOR>
     * @date 2023-11-09 10:33:22
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult<OrderVo> addOrder(@Validated @RequestBody OrderBo orderBo){
		return orderService.addOrder(orderBo);
    }

    @Override
    public AjaxResult<List<OrderVo>> batchAddOrder(@RequestBody List<OrderBo> orderBo) {
        return this.orderService.batchAddOrder(orderBo);
    }

    @Override
    @FeignValidatorAnnotation
    public AjaxResult<OrderVo> addWxOrder(@RequestBody OrderBo bo) {
        bo.setPayMode(String.valueOf(OrderEnums.PayMode.WX_PAY.getVal()));
        return orderService.addOrder(bo);
    }

    @Override
    @FeignValidatorAnnotation
    public AjaxResult refund(@RequestBody OrderBo bo) {
        return  this.orderService.refunds(bo);
    }

    /**
	 * 修改订单表
	 * @param orderBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-09 10:33:22
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateOrder(@Validated @RequestBody OrderBo orderBo) {
		if(null == orderBo.getId()) {
			return AjaxResult.fail("订单表id不能为空");
		}
		return orderService.updateOrder(orderBo);
	}

	/**
	 * 查询订单表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-09 10:33:22
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<OrderVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("订单表id不能为空");
		}
		OrderConditionBo condition = new OrderConditionBo();
		condition.setId(id);
		OrderVo vo = orderService.getOrderByCondition(condition);
		return AjaxResult.success(vo);
	}


    /**
	 * 删除订单表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-09 10:33:22
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		OrderDto orderDto = new OrderDto();
		orderDto.setId(id);
		orderDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(orderService.updateById(orderDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}


    @Override
    public AjaxResult<String> getOrderStatusByOrderNo(@RequestParam("orderNo") String orderNo) {
        Object orderStatus = this.orderService.getStatusByOrderNo(orderNo);
        return AjaxResult.success(orderStatus);
    }

    @Override
    public AjaxResult<OrderVo> getOrderVoByOrderNo(@RequestParam("orderNo") String orderNo) {
        OrderVo orderVo = this.orderService.getOrderVoByOrderNo(orderNo);
        return AjaxResult.success(orderVo);
    }

    @Override
    public AjaxResult updateByOrderNo(@RequestBody OrderBo orderBo) {
        final String orderNo = orderBo.getOrderNo();
        if(StrUtil.isEmpty(orderNo)){
            return AjaxResult.fail("订单编号不能为空");
        }
        final String orderState = orderBo.getOrderState();
        if(StrUtil.isEmpty(orderState)){
            return AjaxResult.fail("订单状态不能为空");
        }
        boolean b = this.orderService.updateByOrderNo(orderBo);
        return AjaxResult.success(b);
    }

    @Override
    public AjaxResult cancelExpireOrder() {
        return this.orderService.cancelExpireOrder();
    }
}
