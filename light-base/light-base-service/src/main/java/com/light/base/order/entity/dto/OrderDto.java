package com.light.base.order.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 订单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-09 10:33:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_order")
public class OrderDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 订单编号
	 */
	@TableField("order_no")
	private String orderNo;

	/**
	 * 订单名称
	 */
	@TableField("order_name")
	private String orderName;

	/**
	 * 交易流水号
	 */
	@TableField("transaction_id")
	private String transactionId;

	/**
	 * 订单状态（ 1：待付款、2：已付款、3：已取消 4 退款中  5 已退款  6 退款失败 )
	 */
	@TableField("order_state")
	private String orderState;

	/**
	 * 支付方式：1支付宝、2微信  3 线下支付
	 */
	@TableField("pay_mode")
	private String payMode;

	/**
	 * 订单金额
	 */
	@TableField("order_amount")
	private BigDecimal orderAmount;

	/**
	 * 实际支付金额
	 */
	@TableField("pay_amount")
	private BigDecimal payAmount;

	/**
	 * 支付结束时间
	 */
	@TableField("pay_end_time")
	private Date payEndTime;

    /**
     * 订单失效时间
     */
    @TableField("expire_time")
    private Date expireTime;

    /**
     * 订单失效时长
     */
    @TableField("expire_duration")
    private Long expireDuration;

	/**
	 * 备注
	 */
	@TableField("note")
	private String note;

	/**
	 * 用户oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 关系ID
	 */
	@TableField("relation_id")
	private Long relationId;

	/**
	 * 关系类型
	 */
	@TableField("relation_type")
	private Integer relationType;

	/**
	 * 是否删除  0 否 1 是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人OID
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 修改时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人OID
	 */
	@TableField("update_by")
	private String updateBy;

}
