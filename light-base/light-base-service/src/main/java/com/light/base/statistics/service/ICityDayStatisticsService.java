package com.light.base.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.statistics.entity.dto.CityDayStatisticsDto;
import com.light.base.statistics.entity.bo.CityDayStatisticsConditionBo;
import com.light.base.statistics.entity.bo.CityDayStatisticsBo;
import com.light.base.statistics.entity.vo.CityDayStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 以学段为最小粒度 每日PV UV统计信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
public interface ICityDayStatisticsService extends IService<CityDayStatisticsDto> {

    List<CityDayStatisticsVo> getCityDayStatisticsListByCondition(CityDayStatisticsConditionBo condition);

	AjaxResult addCityDayStatistics(CityDayStatisticsBo cityDayStatisticsBo);

	AjaxResult updateCityDayStatistics(CityDayStatisticsBo cityDayStatisticsBo);

	CityDayStatisticsVo getDetail(Long id);

    List<CityDayStatisticsVo> getListByCondForGroupCity(CityDayStatisticsConditionBo bo);
}

