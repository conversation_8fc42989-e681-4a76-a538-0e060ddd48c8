package com.light.base.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.base.statistics.entity.dto.MonthStatisticsDto;
import com.light.base.statistics.entity.bo.MonthStatisticsConditionBo;
import com.light.base.statistics.entity.bo.MonthStatisticsBo;
import com.light.base.statistics.entity.vo.MonthStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 每月 PV UV 总量统计信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
public interface IMonthStatisticsService extends IService<MonthStatisticsDto> {

    List<MonthStatisticsVo> getMonthStatisticsListByCondition(MonthStatisticsConditionBo condition);

	AjaxResult addMonthStatistics(MonthStatisticsBo monthStatisticsBo);

	AjaxResult updateMonthStatistics(MonthStatisticsBo monthStatisticsBo);

	MonthStatisticsVo getDetail(Long id);

    List<MonthStatisticsVo> getMonthStatisticsByCondForGroupDate(MonthStatisticsConditionBo conditionBo);
}

