package com.light.base.statistics.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.base.statistics.entity.dto.SectionDayStatisticsDto;
import com.light.base.statistics.entity.bo.SectionDayStatisticsConditionBo;
import com.light.base.statistics.entity.bo.SectionDayStatisticsBo;
import com.light.base.statistics.entity.vo.SectionDayStatisticsVo;
import com.light.base.statistics.service.ISectionDayStatisticsService;
import com.light.base.statistics.mapper.SectionDayStatisticsMapper;
import com.light.core.entity.AjaxResult;
/**
 * 以城市为最小粒度 每日PV UV统计信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
@Service
public class SectionDayStatisticsServiceImpl extends ServiceImpl<SectionDayStatisticsMapper, SectionDayStatisticsDto> implements ISectionDayStatisticsService {

	@Resource
	private SectionDayStatisticsMapper sectionDayStatisticsMapper;

    @Override
	public List<SectionDayStatisticsVo> getSectionDayStatisticsListByCondition(SectionDayStatisticsConditionBo condition) {
		FuzzyQueryUtil.transferMeanBean(condition);
        return sectionDayStatisticsMapper.getSectionDayStatisticsListByCondition(condition);
	}

	@Override
	public AjaxResult addSectionDayStatistics(SectionDayStatisticsBo sectionDayStatisticsBo) {
		SectionDayStatisticsDto sectionDayStatistics = new SectionDayStatisticsDto();
		BeanUtils.copyProperties(sectionDayStatisticsBo, sectionDayStatistics);
		if(save(sectionDayStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSectionDayStatistics(SectionDayStatisticsBo sectionDayStatisticsBo) {
		SectionDayStatisticsDto sectionDayStatistics = new SectionDayStatisticsDto();
		BeanUtils.copyProperties(sectionDayStatisticsBo, sectionDayStatistics);
		if(updateById(sectionDayStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SectionDayStatisticsVo getDetail(Long id) {
		SectionDayStatisticsConditionBo condition = new SectionDayStatisticsConditionBo();
		condition.setId(id);
		List<SectionDayStatisticsVo> list = sectionDayStatisticsMapper.getSectionDayStatisticsListByCondition(condition);
		SectionDayStatisticsVo vo = new SectionDayStatisticsVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

    @Override
    public List<SectionDayStatisticsVo> getListByCondForGroupSection(SectionDayStatisticsConditionBo bo) {
        return this.sectionDayStatisticsMapper.getListByCondForGroupSection(bo);
    }
}
