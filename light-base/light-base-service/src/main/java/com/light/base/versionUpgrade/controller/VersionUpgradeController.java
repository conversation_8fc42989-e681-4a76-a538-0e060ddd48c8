package com.light.base.versionUpgrade.controller;

import java.util.HashMap;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.light.base.versionUpgrade.entity.dto.VersionUpgradeDto;
import com.light.feign.annotation.FeignValidatorAnnotation;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.base.versionUpgrade.entity.bo.VersionUpgradeConditionBo;
import com.light.base.versionUpgrade.entity.bo.VersionUpgradeBo;
import com.light.base.versionUpgrade.entity.vo.VersionUpgradeVo;
import com.light.base.versionUpgrade.service.IVersionUpgradeService;
import com.light.base.versionUpgrade.api.VersionUpgradeApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;
/**
 * 
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-16 09:41:02
 */
@RestController
@Validated
@Api(value = "", tags = "接口" )
public class VersionUpgradeController implements VersionUpgradeApi{
	
    @Autowired
    private IVersionUpgradeService versionUpgradeService;

	@FeignValidatorAnnotation
    public AjaxResult getVersionUpgradeListByCondition(@RequestBody VersionUpgradeConditionBo condition){
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(SystemConstants.NO_PAGE.equals(condition.getPageNo())){
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("list",versionUpgradeService.getVersionUpgradeListByCondition(condition));
			return AjaxResult.success(map);
		}else {
			PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
			PageInfo<VersionUpgradeVo> pageInfo = new PageInfo<>(versionUpgradeService.getVersionUpgradeListByCondition(condition));
			return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
		}
    }

	@FeignValidatorAnnotation
    public AjaxResult addVersionUpgrade(@Validated @RequestBody VersionUpgradeBo versionUpgradeBo){
		return versionUpgradeService.addVersionUpgrade(versionUpgradeBo);
    }

	@FeignValidatorAnnotation
	public AjaxResult updateVersionUpgrade(@Validated @RequestBody VersionUpgradeBo versionUpgradeBo) {
		if(null == versionUpgradeBo.getId()) {
			return AjaxResult.fail("id不能为空");
		}
		return versionUpgradeService.updateVersionUpgrade(versionUpgradeBo);
	}

	@FeignValidatorAnnotation
	public AjaxResult getDetail(@NotNull(message = "请选择数据") Long id) {
		Map<String, Object> map = versionUpgradeService.getDetail(id);
		return AjaxResult.success(map);
	}

	@FeignValidatorAnnotation
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long id) {
		VersionUpgradeBo versionUpgradeBo = new VersionUpgradeBo();
		versionUpgradeBo.setId(id);
		versionUpgradeBo.setIsDelete(StatusEnum.ISDELETE.getCode());
		return versionUpgradeService.updateVersionUpgrade(versionUpgradeBo);
	}

	@FeignValidatorAnnotation
	public AjaxResult getSysInfo(@RequestBody VersionUpgradeBo versionUpgradeBo){
		return versionUpgradeService.getSysInfo(versionUpgradeBo);
	}

	@Override
	public AjaxResult<VersionUpgradeVo> getUpgradeInfoByTerminal(String terminalType) {
		VersionUpgradeVo dto = versionUpgradeService.getUpgradeInfoByTerminal(terminalType);
		return AjaxResult.success(dto);
	}
}
