package com.light.base.weakPassword.controller;

import com.light.base.weakPassword.api.WeakPasswordApi;
import com.light.base.weakPassword.service.IWeakPasswordService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;


/**
 * 
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-16 09:41:02
 */
@RestController
@Validated
@Api(value = "", tags = "接口" )
public class WeakPasswordController implements WeakPasswordApi {
	
    @Autowired
    private IWeakPasswordService weakPasswordService;

	@Override
	public AjaxResult<String> getWeakPasswordList() {
		return AjaxResult.success(weakPasswordService.getCacheList());
	}
}
