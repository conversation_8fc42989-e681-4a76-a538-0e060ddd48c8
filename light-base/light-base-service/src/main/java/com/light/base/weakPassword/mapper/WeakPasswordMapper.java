package com.light.base.weakPassword.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.light.base.weakPassword.dto.WeakPassword;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 弱密码表;(weak_password)表数据库访问层
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-12-10
 */
@Mapper
public interface WeakPasswordMapper extends BaseMapper<WeakPassword> {
}