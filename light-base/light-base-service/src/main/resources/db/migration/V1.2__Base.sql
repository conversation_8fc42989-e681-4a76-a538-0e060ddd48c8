CREATE TABLE `p_city_day_statistics` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                         `date_month` varchar(20) DEFAULT NULL COMMENT '月份 yyyy-MM',
                                         `date_day` date DEFAULT NULL COMMENT '日期 yyyy-MM-dd',
                                         `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
                                         `province_name` varchar(255) DEFAULT NULL COMMENT '省份名称',
                                         `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
                                         `city_name` varchar(255) DEFAULT NULL COMMENT '城市名称',
                                         `pv_num` int(11) DEFAULT '0' COMMENT 'PV数量',
                                         `uv_num` int(11) DEFAULT '0' COMMENT 'UV数量',
                                         `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                         `update_time` datetime DEFAULT NULL,
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='以城市为最小粒度 每日PV UV统计信息';
CREATE TABLE `p_day_statistics` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                    `date_month` varchar(50) DEFAULT NULL COMMENT '月份 yyyy-MM',
                                    `date_day` date DEFAULT NULL COMMENT '日期 yyyy-MM-dd',
                                    `platform` tinyint(2) DEFAULT NULL COMMENT '平台 1 数字教材 2 凤凰易教',
                                    `client` tinyint(2) DEFAULT NULL COMMENT '端口 1 web  2 IOS 3 安卓  4 IPAD',
                                    `pv_num` int(11) DEFAULT '0' COMMENT 'PV数量',
                                    `uv_num` int(11) DEFAULT '0' COMMENT 'UV数量',
                                    `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                    `update_time` datetime DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='每日PV UV统计信息';

CREATE TABLE `p_day_total_statistics` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `date_day` date DEFAULT NULL COMMENT 'yyyy-MM-dd',
                                          `pv_num` int(11) DEFAULT '0' COMMENT 'PV数量',
                                          `uv_num` int(11) DEFAULT '0' COMMENT 'UV数量',
                                          `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                          `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                          `update_time` datetime DEFAULT NULL,
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='每天PV UV 总统计信息（不区分平台）';

CREATE TABLE `p_month_statistics` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `date_month` varchar(20) DEFAULT NULL COMMENT '月份 yyyy-MM',
                                      `platform` tinyint(2) DEFAULT NULL COMMENT '平台 1 数字教材 2 凤凰易教',
                                      `client` tinyint(2) DEFAULT NULL COMMENT '端口 1 web  2 IOS 3 安卓  4 IPAD',
                                      `pv_num` int(11) DEFAULT '0' COMMENT 'PV数量',
                                      `uv_num` int(11) DEFAULT '0' COMMENT 'UV数量',
                                      `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                      `update_time` datetime DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='每月 PV UV 总量统计信息';

CREATE TABLE `p_section_day_statistics` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                            `date_month` varchar(20) DEFAULT NULL COMMENT '月份 yyyy-MM',
                                            `date_day` date DEFAULT NULL COMMENT '日期 yyyy-MM-dd',
                                            `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
                                            `province_name` varchar(255) DEFAULT NULL COMMENT '省份名称',
                                            `user_identity_type` tinyint(2) DEFAULT NULL COMMENT '用户身份',
                                            `section` varchar(64) DEFAULT NULL COMMENT '学段',
                                            `section_name` varchar(50) DEFAULT NULL COMMENT '学段名称',
                                            `pv_num` int(11) DEFAULT '0' COMMENT 'PV数量',
                                            `uv_num` int(11) DEFAULT '0' COMMENT 'UV数量',
                                            `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                            `update_time` datetime DEFAULT NULL,
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='以学段为最小粒度 每日PV UV统计信息';

CREATE TABLE `p_user_day_statistics` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                         `month_date` varchar(20) DEFAULT NULL COMMENT '月份 yyyy-MM',
                                         `day_date` varchar(20) DEFAULT NULL COMMENT '日期 yyyy-MM-dd',
                                         `platform` tinyint(2) DEFAULT NULL COMMENT '平台 1 数字教材 2 凤凰易教',
                                         `client` tinyint(2) DEFAULT NULL COMMENT '端口 1 web  2 IOS 3 安卓  4 IPAD',
                                         `user_oid` varchar(64) DEFAULT NULL COMMENT '用户OID',
                                         `user_identity_type` tinyint(1) DEFAULT NULL COMMENT '用户身份',
                                         `section` varchar(20) DEFAULT NULL COMMENT '用户学段',
                                         `section_name` varchar(20) DEFAULT NULL COMMENT '用户学段名称',
                                         `province_id` bigint(20) DEFAULT NULL COMMENT '用户省份ID',
                                         `province_name` varchar(80) DEFAULT NULL COMMENT '用户省份名称',
                                         `city_id` bigint(20) DEFAULT NULL COMMENT '用户城市ID',
                                         `city_name` varchar(80) DEFAULT NULL COMMENT '用户城市名称',
                                         `area_id` bigint(20) DEFAULT NULL COMMENT '用户区域ID',
                                         `area_name` varchar(80) DEFAULT NULL COMMENT '用户区域名称',
                                         `view_num` int(11) DEFAULT '0' COMMENT '访问次数',
                                         `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                         `update_time` datetime DEFAULT NULL,
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日访问元数据统计信息';

CREATE TABLE `p_user_day_statistics_202212` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                `month_date` varchar(20) DEFAULT NULL COMMENT '月份 yyyy-MM',
                                                `day_date` varchar(20) DEFAULT NULL COMMENT '日期 yyyy-MM-dd',
                                                `platform` tinyint(2) DEFAULT NULL COMMENT '平台 1 数字教材 2 凤凰易教',
                                                `client` tinyint(2) DEFAULT NULL COMMENT '端口 1 web  2 IOS 3 安卓  4 IPAD',
                                                `user_oid` varchar(64) DEFAULT NULL COMMENT '用户OID',
                                                `user_identity_type` tinyint(1) DEFAULT NULL COMMENT '用户身份',
                                                `section` varchar(20) DEFAULT NULL COMMENT '用户学段',
                                                `section_name` varchar(20) DEFAULT NULL COMMENT '用户学段名称',
                                                `province_id` bigint(20) DEFAULT NULL COMMENT '用户省份ID',
                                                `province_name` varchar(80) DEFAULT NULL COMMENT '用户省份名称',
                                                `city_id` bigint(20) DEFAULT NULL COMMENT '用户城市ID',
                                                `city_name` varchar(80) DEFAULT NULL COMMENT '用户城市名称',
                                                `area_id` bigint(20) DEFAULT NULL COMMENT '用户区域ID',
                                                `area_name` varchar(80) DEFAULT NULL COMMENT '用户区域名称',
                                                `view_num` int(11) DEFAULT '0' COMMENT '访问次数',
                                                `view_duration` bigint(20) DEFAULT '0' COMMENT '访问时长（秒）',
                                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                                `update_time` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='用户每日访问元数据统计信息';
