ALTER TABLE `p_invoice_record`
    ADD COLUMN `user_name` varchar(255) NULL COMMENT '申请人姓名' AFTER `user_oid`,
    ADD COLUMN `name` varchar(255) NULL COMMENT '记录名称' AFTER `user_name`,
    ADD COLUMN `apply_time` datetime NULL COMMENT '申请时间' AFTER `invoice_url`,
    ADD COLUMN `obsolete_apply_time` datetime NULL COMMENT '作废申请时间' AFTER `apply_time`,
    ADD COLUMN `obsolete_time` datetime NULL COMMENT '作废时间' AFTER `obsolete_apply_time`,
    ADD COLUMN `obsolete_user_type` tinyint(1) NULL COMMENT '作废用户类型 1 前端用户 2 管理人员' AFTER `obsolete_time`,
    ADD COLUMN `obsolete_user_name` varchar(255) NULL COMMENT '作废用户名称' AFTER `obsolete_user_type`,
    ADD COLUMN `ob_solete_user_oid` varchar(64) NULL COMMENT '作废人OID' AFTER `obsolete_user_name`;



ALTER TABLE `p_invoice`
    ADD COLUMN `name` varchar(255) NULL COMMENT '名称' AFTER `user_oid`;
