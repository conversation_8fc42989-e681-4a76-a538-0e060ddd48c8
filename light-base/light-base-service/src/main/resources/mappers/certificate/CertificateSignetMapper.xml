<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.base.certificate.mapper.CertificateSignetMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.base.certificate.entity.dto.CertificateSignetDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="unit" column="unit"/>
        <result property="file" column="file"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="unit != null and unit != '' ">and unit like concat('%', #{unit}, '%')</if>
			<if test="description != null and description != '' ">and description like concat('%', #{description}, '%')</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="width != null ">and width = #{width}</if>
			<if test="height != null ">and height = #{height}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.name
	 		,t.unit
	 		,t.file
	 		,t.status
			,t.description
	 		,t.width
	 		,t.height
	 		,t.is_delete
	 		,t.create_time
	 		,t.update_time
	 		,t.create_by
	 		,t.update_by
		from (
			 select a.* from p_certificate_signet a
		 ) t

	</sql>

	<select id="getCertificateSignetListByCondition" resultType="com.light.base.certificate.entity.vo.CertificateSignetVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

    <select id="getCertificateSignetsByIds" resultType="com.light.base.certificate.entity.vo.CertificateSignetVo">
		<include refid="common_select"> </include>
		where id in
		<foreach collection="ids.split(',')" index="index" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
		order by field
		<foreach collection="ids.split(',')" index="index" item="item" open="(id," close=")" separator=",">
			#{item}
		</foreach>
    </select>
</mapper>