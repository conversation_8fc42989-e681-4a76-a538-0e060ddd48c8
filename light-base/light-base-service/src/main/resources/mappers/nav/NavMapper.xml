<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.base.nav.mapper.NavMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.base.nav.entity.dto.NavDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="moduleCode" column="module_code"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="subjectTitle" column="subject_title"/>
        <result property="url" column="url"/>
        <result property="target" column="target"/>
        <result property="available" column="available"/>
        <result property="sequence" column="sequence"/>
        <result property="level" column="level"/>
        <result property="icon" column="icon"/>
        <result property="navtype" column="navtype"/>
        <result property="logo" column="logo"/>
        <result property="organizationId" column="organization_id"/>
        <result property="canDelete" column="can_delete"/>
    </resultMap>

	<select id="getNavListByCondition" resultType="com.light.base.nav.entity.vo.NavVo">
		select t.* from (
			select a.* from p_nav a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
			<if test="parentId != null">and parent_id = #{parentId}</if>
			<if test="name != null and name != ''">and name = #{name}</if>
			<if test="moduleCode != null and moduleCode != ''">and module_code = #{moduleCode}</if>
			<if test="title != null and title != ''">and title = #{title}</if>
			<if test="subjectTitle != null and subjectTitle != ''">and subject_title = #{subjectTitle}</if>
			<if test="url != null and url != ''">and url = #{url}</if>
			<if test="target != null and target != ''">and target = #{target}</if>
			<if test="available != null ">and available = #{available}</if>
			<if test="sequence != null ">and sequence = #{sequence}</if>
			<if test="level != null ">and level = #{level}</if>
			<if test="navtype != null">and navtype = #{navtype}</if>
			<if test="logo != null and logo != ''">and logo = #{logo}</if>
			<if test="organizationId != null">and organization_id = #{organizationId}</if>
			<if test="canDelete != null">and can_delete = #{canDelete}</if>
	    </where>
		order by sequence asc
	</select>

    <select id="getDistinctModuleCodes" resultType="java.lang.String">
		SELECT
			module_code
		FROM
			p_nav
		GROUP BY
			module_code
    </select>
</mapper>