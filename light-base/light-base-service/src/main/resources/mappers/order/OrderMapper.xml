<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.base.order.mapper.OrderMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.base.order.entity.dto.OrderDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderName" column="order_name"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="orderState" column="order_state"/>
        <result property="payMode" column="pay_mode"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payEndTime" column="pay_end_time"/>
        <result property="note" column="note"/>
        <result property="userOid" column="user_oid"/>
        <result property="relationId" column="relation_id"/>
        <result property="relationType" column="relation_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="orderNo != null and orderNo != '' ">and order_no like concat('%', #{orderNo}, '%')</if>
			<if test="orderName != null and orderName != '' ">and order_name like concat('%', #{orderName}, '%')</if>
			<if test="transactionId != null and transactionId != '' ">and transaction_id like concat('%', #{transactionId}, '%')</if>
			<if test="orderState != null and orderState != '' ">and order_state like concat('%', #{orderState}, '%')</if>
			<if test="payMode != null and payMode != '' ">and pay_mode like concat('%', #{payMode}, '%')</if>
			<if test="orderAmount != null ">and order_amount = #{orderAmount}</if>
			<if test="payAmount != null ">and pay_amount = #{payAmount}</if>
			<if test="payEndTime != null ">and pay_end_time = #{payEndTime}</if>
			<if test="note != null and note != '' ">and note like concat('%', #{note}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="relationId != null ">and relation_id = #{relationId}</if>
			<if test="relationType != null ">and relation_type = #{relationType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>

            <if test="nePayMode != null and nePayMode != '' ">and pay_mode != #{nePayMode}</if>
            <if test="expireMinuteTime != null">
                and expire_time is not null
                and TIMESTAMPDIFF(MINUTE, expire_time, now()) &gt; #{expireMinuteTime}
            </if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="orderNo != null and orderNo != '' ">and order_no like concat('%', #{orderNo}, '%')</if>
			<if test="orderName != null and orderName != '' ">and order_name like concat('%', #{orderName}, '%')</if>
			<if test="transactionId != null and transactionId != '' ">and transaction_id like concat('%', #{transactionId}, '%')</if>
			<if test="orderState != null and orderState != '' ">and order_state like concat('%', #{orderState}, '%')</if>
			<if test="payMode != null and payMode != '' ">and pay_mode like concat('%', #{payMode}, '%')</if>
			<if test="orderAmount != null ">and order_amount = #{orderAmount}</if>
			<if test="payAmount != null ">and pay_amount = #{payAmount}</if>
			<if test="payEndTime != null ">and pay_end_time = #{payEndTime}</if>
			<if test="note != null and note != '' ">and note like concat('%', #{note}, '%')</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="relationId != null ">and relation_id = #{relationId}</if>
			<if test="relationType != null ">and relation_type = #{relationType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.order_no
	 		,t.order_name
	 		,t.transaction_id
	 		,t.order_state
	 		,t.pay_mode
	 		,t.order_amount
	 		,t.pay_amount
	 		,t.pay_end_time
	 		,t.note
	 		,t.user_oid
	 		,t.relation_id
	 		,t.relation_type
	 		,t.is_delete
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			select a.* from p_order a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrderListByCondition" resultType="com.light.base.order.entity.vo.OrderVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getOrderByCondition" resultType="com.light.base.order.entity.vo.OrderVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>
