<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.base.statistics.mapper.SectionDayStatisticsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.base.statistics.entity.dto.SectionDayStatisticsDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="dateMonth" column="date_month"/>
        <result property="dateDay" column="date_day"/>
        <result property="provinceId" column="province_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="userIdentityType" column="user_identity_type"/>
        <result property="section" column="section"/>
        <result property="pvNum" column="pv_num"/>
        <result property="uvNum" column="uv_num"/>
        <result property="viewDuration" column="view_duration"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="dateMonth != null ">and date_month = #{dateMonth}</if>
			<if test="dateDay != null ">and date_day = #{dateDay}</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and province_name like concat('%', #{provinceName}, '%')</if>
			<if test="userIdentityType != null ">and user_identity_type = #{userIdentityType}</if>
			<if test="section != null and section != '' ">and section like concat('%', #{section}, '%')</if>
			<if test="pvNum != null ">and pv_num = #{pvNum}</if>
			<if test="uvNum != null ">and uv_num = #{uvNum}</if>
			<if test="viewDuration != null ">and view_duration = #{viewDuration}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.date_month
	 		,t.date_day
	 		,t.province_id
	 		,t.province_name
	 		,t.user_identity_type
	 		,t.section
	 		,t.pv_num
	 		,t.uv_num
	 		,t.view_duration
	 		,t.create_time
	 		,t.update_time
		from (
			 select a.* from p_section_day_statistics a
		 ) t

	</sql>

	<select id="getSectionDayStatisticsListByCondition" resultType="com.light.base.statistics.entity.vo.SectionDayStatisticsVo">
		 select a.* from p_section_day_statistics a
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="dateMonth != null ">and date_month = #{dateMonth}</if>
			<if test="dateDay != null ">and date_day = #{dateDay}</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and province_name like concat('%', #{provinceName}, '%')</if>
			<if test="userIdentityType != null ">and user_identity_type = #{userIdentityType}</if>
			<if test="section != null and section != '' ">and section like concat('%', #{section}, '%')</if>
			<if test="pvNum != null ">and pv_num = #{pvNum}</if>
			<if test="uvNum != null ">and uv_num = #{uvNum}</if>
			<if test="viewDuration != null ">and view_duration = #{viewDuration}</if>
			<if test="startTime != null and endTime != null">
                and date_day between  #{startTime} and #{endTime}
            </if>
		</where>
	</select>
	<select id="getListByCondForGroupSection" resultType="com.light.base.statistics.entity.vo.SectionDayStatisticsVo">
        select a.section,ifnull(sum(a.uv_num),0) as uv_num,ifnull(sum(a.pv_num),0) as pv_num,ifnull(sum(a.view_duration),0) as view_duration
        from p_section_day_statistics a
        <where>
			<if test="id != null ">and id = #{id}</if>
			<if test="dateMonth != null ">and date_month = #{dateMonth}</if>
			<if test="dateDay != null ">and date_day = #{dateDay}</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and province_name like concat('%', #{provinceName}, '%')</if>
			<if test="userIdentityType != null ">and user_identity_type = #{userIdentityType}</if>
			<if test="section != null and section != '' ">and section like concat('%', #{section}, '%')</if>
			<if test="pvNum != null ">and pv_num = #{pvNum}</if>
			<if test="uvNum != null ">and uv_num = #{uvNum}</if>
			<if test="viewDuration != null ">and view_duration = #{viewDuration}</if>
			<if test="startTime != null and endTime != null">
                and date_day between  #{startTime} and #{endTime}
            </if>
		</where>
		group by a.section
    </select>
</mapper>
