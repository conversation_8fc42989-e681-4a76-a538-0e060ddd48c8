<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.base.versionUpgrade.mapper.VersionUpgradeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.base.versionUpgrade.entity.dto.VersionUpgradeDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="operatingSystem" column="operating_system"/>
        <result property="fileSize" column="file_size"/>
        <result property="upgradeDescription" column="upgrade_description"/>
        <result property="currentVersion" column="current_version"/>
        <result property="minVersion" column="min_version"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="state" column="state"/>
        <result property="organizationId" column="organization_id"/>
        <result property="clientType" column="client_type"/>
        <result property="publishTime" column="publish_time"/>
        <result property="isPublished" column="is_published"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getVersionUpgradeListByCondition" resultType="com.light.base.versionUpgrade.entity.vo.VersionUpgradeVo">
		select t.* from (
			select a.* from p_version_upgrade a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
			<if test="name != null and name != ''">and name = #{name}</if>
			<if test="operatingSystem != null and operatingSystem != ''">and operating_system = #{operatingSystem}</if>
			<if test="fileSize != null and fileSize != ''">and file_size = #{fileSize}</if>
			<if test="upgradeDescription != null and upgradeDescription != ''">and upgrade_description = #{upgradeDescription}</if>
			<if test="currentVersion != null and currentVersion != ''">and current_version = #{currentVersion}</if>
			<if test="minVersion != null and minVersion != ''">and min_version = #{minVersion}</if>
			<if test="downloadUrl != null and downloadUrl != ''">and download_url = #{downloadUrl}</if>
			<if test="state != null and state != ''">and state = #{state}</if>
			<if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
			<if test="clientType != null and clientType != ''">and client_type = #{clientType}</if>
			<if test="publishTime != null and publishTime != ''">and publish_time = #{publishTime}</if>
			<if test="isPublished != null and isPublished != ''">and is_published = #{isPublished}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
			<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
			<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
			<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
	    </where>
	</select>
</mapper>