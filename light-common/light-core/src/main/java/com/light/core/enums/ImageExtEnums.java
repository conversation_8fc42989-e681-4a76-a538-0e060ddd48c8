package com.light.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/8/10 9:50 上午
 * @description：
 */
@Getter
@AllArgsConstructor
public enum ImageExtEnums {

    JPEG ("jpg"),
    PNG ("png"),
    GIF ("gif"),
    TIFF ("tif"),
    BMP("bmp");

    private String val;


    public static boolean isImg(String ext){
        final long count = Arrays.stream(values()).filter(x -> x.getVal().equalsIgnoreCase(ext)).count();
        return count > 0;
    }
}
