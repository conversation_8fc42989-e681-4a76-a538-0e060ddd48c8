package com.light.core.exception;

import cn.hutool.http.HttpStatus;

/**
 * 业务层统一异常
 *
 * <AUTHOR>
 */
public class UnifiedException extends RuntimeException {
    private Integer code = HttpStatus.HTTP_INTERNAL_ERROR;

    public UnifiedException() {
        super();
    }

    public UnifiedException(String message) {
        super(message);
    }

    public UnifiedException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }


}
