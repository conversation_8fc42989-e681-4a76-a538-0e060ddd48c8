package com.light.core.file;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Paths;

/**
 * 文件上传工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUploadUtils {


    /**
     * 文件上传
     *
     * @param baseDir          相对应用的基目录
     * @param file             上传的文件
     * @return 返回上传成功的文件名
     * @throws IOException                          比如读写文件出错时
     */
    public static final FileModel upload(String baseDir, MultipartFile file)  {
        final String originalFilename = file.getOriginalFilename();
        String fileName = extractFilename(originalFilename);
        long duration = 0;
        String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
        try {
            long start = System.currentTimeMillis();
            file.transferTo(Paths.get(absPath));
            log.info("========== 上传文件用时： {} 秒 ===========" , (System.currentTimeMillis()- start) / 1000);

            // 获取音视频时长
            //duration = VideoUtil.getDuration(absPath);

        } catch (IOException e) {
            e.printStackTrace();
        }
        return FileModel.builder().originalName(originalFilename).filename(FileUtil.getName(fileName))
                .filePath(fileName).extName(FileUtil.extName(originalFilename)).size(file.getSize())
                .duration(duration).build();
    }


    public static final FileModel upload(String baseDir, String originalFilename,long size, InputStream inputStream)  {
        String fileName = extractFilename(originalFilename);
        long duration = 0;
        String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
        FileOutputStream fileOutputStream = null;
        String md5 = null;
        try {


            long start = System.currentTimeMillis();
            fileOutputStream = new FileOutputStream(absPath);
            StreamUtils.copy(inputStream, fileOutputStream);
            log.info("========== 上传文件用时： {} 秒 ===========" , (System.currentTimeMillis()- start) / 1000);

            md5 = SecureUtil.md5(new File(absPath));
            // 获取音视频时长
            //duration = VideoUtil.getDuration(absPath);

        } catch (IOException e) {
            e.printStackTrace();
        }finally{
            IoUtil.close(fileOutputStream);
            IoUtil.close(inputStream);
        }

        return FileModel.builder().originalName(originalFilename).filename(FileUtil.getName(fileName))
                .filePath(fileName).extName(FileUtil.extName(originalFilename)).size(size)
                .md5(md5)
                .duration(duration).build();
    }


    /**
     * 编码文件名
     */
    public static final String extractFilename(String filename) {
        final String extName = FileUtil.extName(filename);
        return StrUtil.format("/{}"+"/"+"{}.{}", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_FORMAT),
                IdUtil.simpleUUID(), extName);
    }

    public static void main(String[] args) {
        String filename = "五年级 水电费收费 胜多负少.png";
        System.out.println(extractFilename(filename));
    }


    public static final File getAbsoluteFile(String uploadDir, String fileName) {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.exists()) {
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
        }
        return desc;
    }




}
