package com.light.core.file.model;

import cn.hutool.core.util.StrUtil;
import com.light.core.file.enums.FileUploadTypeEnums;
import lombok.Data;

@Data
public class FileUploadConfig {


    /**
     *  上传类型 （不区分大小写）
     * @see FileUploadTypeEnums.name()
     */
    private String uploadType;



    public FileUploadTypeEnums getUploadTypeEnum() {
        if(StrUtil.isEmpty(uploadType)) {
            return FileUploadTypeEnums.LOCAL;
        }
        return FileUploadTypeEnums.valueOf(uploadType.toUpperCase());
    }
}
