package com.light.core.file.tencent;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.crypto.SecureUtil;
import com.light.core.file.AbsFileServiceImpl;
import com.light.core.file.FileModel;
import com.light.core.file.FileService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import lombok.AllArgsConstructor;

import java.io.InputStream;

@AllArgsConstructor
public class TencentCOSFileServiceImpl extends AbsFileServiceImpl implements FileService {

    private final COSClient cosClient;

    private final TencentCOSProperties tencentCOSProperties;


    @Override
    public FileModel upload(String baseDir, String originalFilename, long size, InputStream inputStream) {
        String fileName = this.extractFilename(originalFilename);
        String path = baseDir + fileName;
        try{
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(size);
            PutObjectRequest putObjectRequest = new PutObjectRequest(tencentCOSProperties.getBucketName(), path, inputStream, objectMetadata);
            PutObjectResult result = cosClient.putObject(putObjectRequest);

            String md5 = SecureUtil.md5(inputStream);
            return FileModel.builder().originalName(originalFilename).filename(FileUtil.getName(fileName))
                    .uniqueKey(result.getETag())
                    .filePath(path).extName(FileUtil.extName(originalFilename)).size(size)
                    .md5(md5)
                    .duration(0L).build();
        } catch (CosClientException e) {
            throw new RuntimeException(e);
        } finally {
            IoUtil.close(inputStream);
        }
    }


}
