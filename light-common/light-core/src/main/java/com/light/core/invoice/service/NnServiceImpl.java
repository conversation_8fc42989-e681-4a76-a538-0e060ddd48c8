package com.light.core.invoice.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.light.core.exception.UnifiedException;
import com.light.core.invoice.config.NuonuoInvoiceProperties;
import com.light.core.invoice.constants.NuoNuoInvoiceConstants;
import com.light.core.invoice.constants.NuonuoInvoiceResponseConstants;
import com.light.core.invoice.model.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import nuonuo.open.sdk.NNOpenSDK;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Slf4j
@Data
public class NnServiceImpl implements InvoiceClientService {

    /**
     * 参数
     */
    private NuonuoInvoiceProperties nuonuoInvoiceProperties;

    /**
     * 诺诺token缓存key
     */
    private String tokenCacheKey = "cache:nuonuo:token";

    /**
     *
     */
    private NuonuoCacheService cacheService = new NuonuoCacheDefaultServiceImpl();

    public NnServiceImpl(NuonuoInvoiceProperties nuonuoInvoiceProperties){
        this.nuonuoInvoiceProperties = nuonuoInvoiceProperties;
    }


    /**
     *  获取token
     *
     * @return {@link String}
     */
    public String getToken(){

        String token = this.cacheService.get(this.tokenCacheKey);
        if(StrUtil.isNotEmpty(token)){
            return token;
        }

        final String appKey = this.nuonuoInvoiceProperties.getAppKey();
        final String appSecret = this.nuonuoInvoiceProperties.getAppSecret();

        final String merchantToken = NNOpenSDK.getIntance().getMerchantToken(appKey, appSecret, NuoNuoInvoiceConstants.TOKEN_URL);
        final JSONObject jsonObject = JSON.parseObject(merchantToken);
        log.info("【诺诺开票】 TOKEN 获取 ，{}", merchantToken);
        token = jsonObject.getString(NuoNuoInvoiceConstants.ACCESS_KEY);
        if(StrUtil.isEmpty(token)){
            log.error("【开票获取token失败，返回:{}】", merchantToken);
            throw new UnifiedException("【开票获取Token失败】: " + merchantToken );
        }
        String string = jsonObject.getString(NuoNuoInvoiceConstants.EXPIRES_IN);
        log.info("【诺诺开票】 TOKEN 获取 ，有效时间字符：{}", string);

        final Long expiresIn = jsonObject.getLong(NuoNuoInvoiceConstants.EXPIRES_IN);
        log.info("【诺诺开票】 TOKEN 获取 ，有效时间数字：{}", expiresIn);
        // 放入缓存
        this.cacheService.set(this.tokenCacheKey,token,24 * 60 * 60 - 5000L);

        return token;
    }

    /**
     *  开票
     *
     * @param request the request 请求参数
     * @return {@link String}
     */
    @Override
    public String openInvoice(NuonuoInvoiceCommitRequest request) {
        // 校验order信息
        String method = NuoNuoInvoiceConstants.Method.OPEN;
        request.getOrder().setSalerTaxNum(nuonuoInvoiceProperties.getTaxNum());
        request.getOrder().setInvoiceType(NuoNuoInvoiceConstants.InvoiceType.BLUE.toString());
        //调用诺诺接口的时间
        final NuonuoInvoiceCommitResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceCommitResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺开票】开票错误 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException(baseInvoiceResponse.getDescribe());
        }

        // 返回序列号
        return baseInvoiceResponse.getResult().getInvoiceSerialNum();
    }

    @Override
    public String redConfirmApply(NuonuoInvoiceRedConfirmRequest request) {
        // 校验order信息
        String method = NuoNuoInvoiceConstants.Method.RED_CONFIRM_APPLY;
        request.setSellerTaxNo(nuonuoInvoiceProperties.getTaxNum());
        request.setSellerName(nuonuoInvoiceProperties.getTaxName());
        request.setApplySource(NuoNuoInvoiceConstants.ApplySource.SELLE);
        request.setRedReason(NuoNuoInvoiceConstants.RedReason.OPEN_ERROR);
        log.info("【诺诺开票】 红字确认单申请，请求报文：{}",JSON.toJSONString(request));
        //调用诺诺接口的时间
        final NuonuoInvoiceRedConfirmResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceRedConfirmResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺开票】增加红字申请单 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException(baseInvoiceResponse.getDescribe());
        }

        // 返回序列号
        return baseInvoiceResponse.getResult();
    }

    @Override
    public List<NuonuoInvoiceQueryRedConfirmResponse.RedConfirm> queryConfirmApply(NuonuoInvoiceQueryRedConfirmRequest request) {

        String method = NuoNuoInvoiceConstants.Method.QUERY_RED_CONFIRM;
        request.setIdentity(NuoNuoInvoiceConstants.ApplySource.SELLE);

        //调用诺诺接口的时间
        final NuonuoInvoiceQueryRedConfirmResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceQueryRedConfirmResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺开票】红字申请单查询 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException(baseInvoiceResponse.getDescribe());
        }

        // 返回数据
        return baseInvoiceResponse.getResult().getList();
    }

    @Override
    public String fastInvoiceRed(NuonuoInvoiceFastRedRequest request) {

        String method = NuoNuoInvoiceConstants.Method.FAST_INVOICE_RED;
        request.setTaxNum(nuonuoInvoiceProperties.getTaxNum());

        //调用诺诺接口的时间
        final NuonuoInvoiceFastRedResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceFastRedResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺开票】快捷红冲 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException(baseInvoiceResponse.getDescribe());
        }

        // 返回数据
        return baseInvoiceResponse.getResult();
    }

    /**
     *  作废
     *
     * @param request
     * @return {@link String}
     */
    @Override
    public String obsoleteInvoice(NuonuoInvoiceObsoleteCommitRequest request) {
        String method = NuoNuoInvoiceConstants.Method.OBSOLETE;
        // 获取token
        final NuonuoInvoiceObsoleteCommitResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceObsoleteCommitResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺开票】作废发票错误 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException("作废失败");
        }
        // 返回序列号
        return baseInvoiceResponse.getResult().getInvoiceId();
    }

    /**
     *  冲红
     *
     * @param request the commit request 冲红发票参数
     * @return {@link String}
     */
    @Override
    public String writesBackInvoice(NuonuoInvoiceCommitRequest request) {
        // 校验order信息
        String method = NuoNuoInvoiceConstants.Method.OPEN;
        request.getOrder().setInvoiceType(NuoNuoInvoiceConstants.InvoiceType.RED.toString());
        request.getOrder().setSalerTaxNum(nuonuoInvoiceProperties.getTaxNum());
        //调用诺诺接口的时间
        final NuonuoInvoiceCommitResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceCommitResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺冲红】冲红错误 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException(baseInvoiceResponse.getDescribe());
        }
        // 返回序列号
        return baseInvoiceResponse.getResult().getInvoiceSerialNum();
    }

    @Override
    public List<NuonuoInvoiceQueryResponse.InvoiceInfo> queryInvoice(NuonuoInvoiceQueryRequest request) {

        // 校验order信息
        String method = NuoNuoInvoiceConstants.Method.QUERY;
        //调用诺诺接口的时间
        final NuonuoInvoiceQueryResponse baseInvoiceResponse = this.execute(request, method,NuonuoInvoiceQueryResponse.class);
        if(!baseInvoiceResponse.getCode().equals(NuonuoInvoiceResponseConstants.ResponseConst.SUCCESS)){
            log.error("【诺诺发票查询】查询错误 , CODE：{}, 描述：{}", baseInvoiceResponse.getCode(),baseInvoiceResponse.getDescribe());
            throw new UnifiedException("冲红失败");
        }
        // 返回序列号
        return baseInvoiceResponse.getResult();
    }

    /**
     *  执行调用
     * @param request the request 参数
     * @param method the method 方法
     * @return {@link BaseInvoiceResponse}
     */
    private <S extends BaseInvoiceResponse> S execute(BaseInvoiceRequest request, String method, Class<S> clazz) {
        String token = this.getToken();
        String content = JSON.toJSONString(request);
        String senId = IdUtil.simpleUUID();

        NNOpenSDK sdk = NNOpenSDK.getIntance();
        final String appKey = this.nuonuoInvoiceProperties.getAppKey();
        final String appSecret = this.nuonuoInvoiceProperties.getAppSecret();
        final String taxNum = this.nuonuoInvoiceProperties.getTaxNum();

        String url = nuonuoInvoiceProperties.isTest() ? NuoNuoInvoiceConstants.TEST_OPEN_URL : NuoNuoInvoiceConstants.OPEN_URL;
        String result = sdk.sendPostSyncRequest(url, senId, appKey, appSecret, token, taxNum, method, content);
        log.info("invoice result = " + result);
        return JSON.parseObject(result, clazz);
    }



}
