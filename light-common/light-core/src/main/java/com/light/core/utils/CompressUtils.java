package com.light.core.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ZipUtil;

/**
 * 字符串压缩工具类,先进行zlib压缩后进行base64编码
 *
 * <AUTHOR>
 * @date 2020/12/16 22:40
 **/
public class CompressUtils {

    /**
     * 字符串压缩
     *
     * @param str 需要被压缩的字符串
     * @return 压缩后的结果
     */
    public static String compress(String str) {
        byte[] zlib = ZipUtil.zlib(str, "UTF-8", 8);
        return Base64.encode(zlib);
    }

    /**
     * 字符串解压
     *
     * @param str 需要被解压的字符串
     * @return 解压后的结果
     */
    public static String uncompress(String str) {
        byte[] decode = Base64.decode(str);
        return ZipUtil.unZlib(decode, "UTF-8");
    }
}
