package com.light.core.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class SignUtil {


    private final static String MAC_ALGORITHM = "HmacSHA256";

    /**
     *
     * @param data the data str
     * @return {@link String}
     */
    public static String signStr(Map<String,String> data){
        return data.keySet().stream().sorted().filter(k-> !k.equalsIgnoreCase("sign"))
                .filter(k -> ObjectUtil.isNotEmpty(data.get(k)))
                .map(k -> k + "=" + data.get(k)).collect(Collectors.joining("&"));
    }


    /**
     *  加签
     * @param secretKey the secret key
     * @param signStr sign string
     * @return {@link String}
     */
    public static String sign(String secretKey, String signStr) {
//        signStr = signStr.concat("&secret=").concat(secretKey);
        log.info("【签名加签】 密钥:{}, 字符串：{}", secretKey , signStr);
        try {
            Mac hmac = Mac.getInstance(MAC_ALGORITHM);
            hmac.init(new SecretKeySpec(secretKey.getBytes(), MAC_ALGORITHM));
            byte[] hash = hmac.doFinal(signStr.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     *  验签
     * @param secretKey the secret key
     * @param signStr the sign str 签名 str
     * @param sign the sign 签名KEY
     * @return boolean
     */
    public static boolean verifySign(String secretKey, String signStr, String sign) {
        String sign1 = sign(secretKey, signStr);
        log.info("【签名校验】 密钥:{}, 待验签名：{}, 签名：{}", secretKey , sign, sign1);
        return sign1.equals(sign);
    }

    public static void main(String[] args) {
//        Map<String, String>  map = Maps.newHashMap();
//        map.put("pageSize", "10");
//        map.put("pageNo", "1");
//        long timestamp = System.currentTimeMillis();
//        System.out.println(timestamp);
//        map.put("timestamp", timestamp + "");
//        String randomNumberString = RandomUtil.getRandomNumberString(32);
//        System.out.println(randomNumberString);
//        map.put("nonce", randomNumberString);
//        String signStr = signStr(map);
//        System.out.println(signStr);
//        String secretKey = "cQ8lZ2jB4jL6cP0dN6jP1nQ1wG8fH7eE7zR6xC0gA1pW2lT0uO2fQ2xQ4hH8vS4o";
//        String sign = sign(secretKey, signStr);
//        System.out.println(sign);
//        //zxRzOjVxp15pJ89TS-VQ6Q
//
//        System.out.println(sign("abc123", "nonce=a1b2c3&timestamp=*************&userId=123"));
        //eNWATMi0/Xe6oSsVMqMUf4TwK+Jfv1nbmxxNXMO4lDk=
//        String str = "{\"accountOid\":\"0il9wj9xa2uoii6s502dt8pl0ljox76jbjsztwhhhx2yr0p80k480hvk0tduh153\",\"accountUserId\":525507,\"area\":[************,************],\"email\":\"<EMAIL>\",\"homeAddressCity\":************,\"homeAddressProvince\":************,\"isActivation\":1,\"isCompleted\":0,\"isDelete\":0,\"isLocked\":0,\"lastChooseOrgId\":************,\"loginUserIdentities\":[{\"accountOid\":\"0il9wj9xa2uoii6s502dt8pl0ljox76jbjsztwhhhx2yr0p80k480hvk0tduh153\",\"identityType\":2,\"isLocked\":0,\"userOid\":\"wjhoqpdkz4iqz13qtiivva60oralz53mhhaj1s9z3xbgnr7qq6k3fbzdt76b6aww\"}],\"loginUserOrgs\":[{\"address\":\"江苏省南京市高淳区\",\"areaId\":************,\"areaName\":\"高淳区\",\"cityId\":************,\"cityName\":\"南京市\",\"code\":\"0202624\",\"createBy\":\"2e4f1sazbf0hsx5g3rqi4xzq7pzzsncf77peayahrjyhn2dq95t9mnxr5c60q5mr\",\"createTime\":\"2023-08-01 15:02:57\",\"id\":************,\"isDelete\":0,\"isUsed\":1,\"level\":4,\"loginType\":0,\"name\":\"凤凰特种中学\",\"num\":1,\"organizationId\":************,\"parentId\":************,\"provinceId\":************,\"provinceName\":\"江苏省\",\"section\":\"02\",\"sourceType\":1,\"superiorsIds\":\"0,************,************,************\",\"type\":2,\"updateTime\":\"2023-08-01 15:02:57\",\"userOid\":\"wjhoqpdkz4iqz13qtiivva60oralz53mhhaj1s9z3xbgnr7qq6k3fbzdt76b6aww\"}],\"loginUserRoles\":[],\"nonce\":\"8efda597e935495bb7c5a7dc3c7e959f\",\"oid\":\"wjhoqpdkz4iqz13qtiivva60oralz53mhhaj1s9z3xbgnr7qq6k3fbzdt76b6aww\",\"orgAreaIds\":\"************\",\"orgAreaNames\":\"高淳区\",\"orgCityIds\":\"************\",\"orgCityNames\":\"南京市\",\"orgProvinceIds\":\"************\",\"orgProvinceNames\":\"江苏省\",\"organizationId\":************,\"organizationIds\":\"************\",\"organizationNames\":\"凤凰特种中学\",\"phone\":\"19119119101\",\"realName\":\"磊测试_教师_送审_1\",\"registerTime\":\"2025-02-10 09:50:26\",\"schoolGender\":\"02\",\"schoolId\":************,\"section\":\"04\",\"sex\":2,\"timestamp\":\"1742465684233\",\"userId\":529256,\"userIdentityType\":2,\"userOid\":\"wjhoqpdkz4iqz13qtiivva60oralz53mhhaj1s9z3xbgnr7qq6k3fbzdt76b6aww\",\"userOrg\":{\"address\":\"江苏省南京市高淳区\",\"areaId\":************,\"areaName\":\"高淳区\",\"cityId\":************,\"cityName\":\"南京市\",\"code\":\"0202624\",\"createBy\":\"2e4f1sazbf0hsx5g3rqi4xzq7pzzsncf77peayahrjyhn2dq95t9mnxr5c60q5mr\",\"createTime\":\"2023-08-01 15:02:57\",\"id\":************,\"isDelete\":0,\"isUsed\":1,\"level\":4,\"loginType\":0,\"name\":\"凤凰特种中学\",\"num\":1,\"orgAreaId\":************,\"orgAreaName\":\"高淳区\",\"orgCityId\":************,\"orgCityName\":\"南京市\",\"orgProvinceId\":************,\"orgProvinceName\":\"江苏省\",\"organizationId\":************,\"organizationName\":\"凤凰特种中学\",\"parentId\":************,\"provinceId\":************,\"provinceName\":\"江苏省\",\"schoolGender\":\"02\",\"section\":\"02\",\"sourceType\":1,\"superiorsIds\":\"0,************,************,************\",\"type\":2,\"updateTime\":\"2023-08-01 15:02:57\",\"userOid\":\"wjhoqpdkz4iqz13qtiivva60oralz53mhhaj1s9z3xbgnr7qq6k3fbzdt76b6aww\"}}";
//        System.out.println(str);
//        LinkedHashMap<String, Object> map = JSON.parseObject(str, new TypeReference<LinkedHashMap<String, Object>>(){}, Feature.OrderedField);
//
//        System.out.println(JSON.toJSONString(map));
//        LinkedHashMap<String, String>  paramMaps = Maps.newLinkedHashMap();
//        map.entrySet().stream().filter(x-> x.getValue() != null).forEach((x)-> {
//            paramMaps.put(x.getKey(), x.getValue().toString());
//        });
//        System.out.println(signStr(paramMaps));
//        System.out.println(JSON.toJSONString(paramMaps));

        System.out.println(sign("d8dcd8510e0d11f08eb6000c2995c7be", "noncestr=4fab7a78-853a-4a03&time=1743559413296&timestamp=1743559413300"));

    }
}
