package com.light.log.aspect;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.util.StringUtil;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.CompressUtils;
import com.light.core.utils.IpUtils;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.entity.OperationLog;
import com.light.mq.component.RabbitMqComponent;
import com.light.mq.constants.RabbitMqConstants;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.admin.entity.vo.LoginAdminVo;
import com.light.user.user.entity.vo.LoginUserVo;
import com.light.user.user.entity.vo.UserOrgVo;
import com.light.user.user.entity.vo.UserRoleVo;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Array;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Title:
 * @Package
 * @Description:
 * <AUTHOR>
 * @date 2021/7/99:41
 */
@Aspect
@Component
@Order(1)
@ConditionalOnProperty(name = {"common.operation.log.open", "operation.log.open"}, havingValue = "true")
public class OperationLogAspect {

    public static final Logger logger = LoggerFactory.getLogger(OperationLogAspect.class);

    private static final String MODULE_NAME = "moduleName";
    private static final String OPERATION_TERMINAL = "operationTerminal";
    private static final String ARGS = "args";
    private static final String OPERATION_TYPE = "operationType";

    @Resource
    private RabbitMqComponent rabbitMqComponent;

    /**
     * 获取方法参数
     */
    private final LocalVariableTableParameterNameDiscoverer parameterNameDiscoverer = new LocalVariableTableParameterNameDiscoverer();

    @Pointcut("@annotation(com.light.log.annotation.OperationLogAnnotation)")
    public void OperationLogAop() {
    }

    @Around("OperationLogAop()")
    public Object doAround(ProceedingJoinPoint point) throws Throwable {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        Object result = null;
        String requestURI = request.getRequestURI();
        OperationLog log = new OperationLog();
        try{
            log.setRequestUrl(requestURI);
            log.setOperationTime(new Date());
            log.setOperationIp(IpUtils.getIpAddr(request));
            log.setMethodName(point.getSignature().getName());
            log.setRequestType(request.getMethod());
            //具体保存log
            Map<String, Object> map = this.getMethodDescription(point);
            log.setModuleName(null!=map.get(MODULE_NAME)?map.get(MODULE_NAME).toString():"未知模块");
            log.setOperationType(null!=map.get(OPERATION_TYPE) ?map.get(OPERATION_TYPE).toString():null);
            log.setOperationTerminal(null!=map.get(OPERATION_TERMINAL) ?map.get(OPERATION_TERMINAL).toString():null);
            log.setRequestParams(null!=map.get(ARGS)?map.get(ARGS).toString():"未知参数");
            this.setOperationUserInfo(log);
            long startTime = System.currentTimeMillis();
            result = point.proceed();
            long endTime = System.currentTimeMillis();
            log.setRequestResult(JSON.toJSONString(result));
            log.setUseTime(endTime - startTime);
            log.setRequestStatus(SystemConstants.YES);
        } catch (Exception e) {
            log.setRequestResult("请求失败");
            log.setRequestErrorMessage(e.getMessage());
            log.setRequestStatus(SystemConstants.NO);
            logger.debug("处理操作日志失败{}", e.getMessage());
        } finally {
            //日志存入rabbitmq
            rabbitMqComponent.publishExchange(RabbitMqConstants.LOG_EXCHANGE, RabbitMqConstants.OPERATION_LOG_QUEUE, JSON.toJSONString(log));
        }
        return result;
    }

    /**
     * 设置用户相关信息
     * @param log
     */
    private void setOperationUserInfo(OperationLog log) {
        String info = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader("currentUser");
        if(com.light.core.utils.StringUtils.isNotEmpty(info)){
            String json = CompressUtils.uncompress(info);
            final LoginAccountVo accountVo = JSON.parseObject(json, LoginAccountVo.class);
            if(null != accountVo) {
                log.setOperationAccountOid(accountVo.getOid());
                log.setOperationAccountName(accountVo.getAccountName());
                final LoginUserVo currentUser = accountVo.getCurrentUser();
                if(currentUser != null) {
                    log.setOperationUserOid(currentUser.getOid());
                    log.setOperationUserName(currentUser.getRealName());
                    if(null!=currentUser.getUserRole()) {
                        UserRoleVo role = currentUser.getUserRole();
                        log.setOperationRoleId(role.getRoleId());
                        log.setOperationRoleName(role.getName());
                    }
                    if(null != currentUser.getUserOrg()) {
                        UserOrgVo org = currentUser.getUserOrg();
                        log.setOperationOrganizationId(org.getOrganizationId());
                        log.setOperationOrganizationName(org.getName());
                    }
                }
            }
        }
        if(StringUtils.isEmpty(info)) {
            info = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader("currentAdmin");
            if (StringUtil.isNotEmpty(info)) {
                String json = CompressUtils.uncompress(info);
                LoginAdminVo admin = JSON.parseObject(json, LoginAdminVo.class);
                if(null!=admin) {
                    log.setOperationAccountName(admin.getAccountName());
                    log.setOperationAccountOid(admin.getOid());
                    log.setOperationUserOid(admin.getOid());
                    log.setOperationUserName(admin.getAdminName());
                    log.setOperationRoleId(admin.getRoleId());
                    log.setOperationRoleName(admin.getRoleName());
                    log.setOperationOrganizationId(admin.getOrganizationId());
                    log.setOperationOrganizationName(admin.getOrganizationName());
                }
            }
        }
    }

    /**
     * 获取切点中的请求参数
     *
     * @param point 切点
     * @return 请求参数的json字符串
     */
    private String getJsonArgs(ProceedingJoinPoint point, HttpServletRequest request) {
        String contentType = request.getContentType();
        //如果为上传文件类型直接跳过
        if (contentType != null && contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            return "";
        }
        List<Object> logArgs;
        try {
            //去除切点中携带有Request或者Response对象(防止序列化错误)
            Object[] args = point.getArgs();
            Stream<?> stream = ArrayUtils.isEmpty(args) ? Stream.empty() : Arrays.stream(args);
            logArgs = stream
                    .filter(arg -> (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse))).collect(Collectors.toList());
            return JSON.toJSONString(logArgs);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取注解描述
     *
     * @param joinPoint
     * @return
     * @throws ClassNotFoundException
     */
    private Map<String, Object> getMethodDescription(ProceedingJoinPoint joinPoint) throws ClassNotFoundException {
        Map<String, Object> map = new HashMap<>();
        //注解所在类名
        String targetName = joinPoint.getTarget().getClass().getName();
        //注解method
        String methodName = joinPoint.getSignature().getName();
        //方法参数
        Object[] arguments = joinPoint.getArgs();
        Class<?> targetClass = Class.forName(targetName);
        //该类下所有方法
        Method[] methods = targetClass.getMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class[] clazzs = method.getParameterTypes();
                if (clazzs.length == arguments.length) {
                    //String args = this.getArgs(method, arguments);
                    map.put(ARGS, JSON.toJSONString(arguments));
                    String moduleName = method.getAnnotation(OperationLogAnnotation.class).moduleName();
                    map.put(MODULE_NAME, moduleName);
                    String operationType = method.getAnnotation(OperationLogAnnotation.class).operationType();
                    map.put(OPERATION_TYPE, operationType);
                    String operationTerminal = method.getAnnotation(OperationLogAnnotation.class).operationTerminal();
                    map.put(OPERATION_TERMINAL, operationTerminal);
                    break;
                }
            }
        }
        return map;
    }

    /**
     * 获取参数
     *
     * @param method
     * @param arguments
     * @return
     */
    private String getArgs(Method method, Object[] arguments) {
        StringBuilder builder = new StringBuilder("{");
        String[] params = parameterNameDiscoverer.getParameterNames(method);
        for (int i = 0; i < params.length; i++) {
            if (!StringUtils.equals("password", params[i])) {
                if (arguments[i].getClass().isArray()) {
                    arguments[i] = Arrays.toString(makeArrayObject(arguments[i]).toArray());
                }
                builder.append(params[i]).append(":").append(arguments[i].toString());
                if (i != params.length - 1) {
                    builder.append("; ");
                }
            }
        }
        return builder.append("}").toString();
    }

    private List<Object> makeArrayObject(Object array) {
        List<Object> tem = new ArrayList<Object>();
        for (int i = 0; i < Array.getLength(array); i++) {
            tem.add(Array.get(array, i));
        }
        return tem;
    }
}
