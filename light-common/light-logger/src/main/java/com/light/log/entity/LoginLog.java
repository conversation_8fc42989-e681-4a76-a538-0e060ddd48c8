package com.light.log.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/1114:34
 */
@Data
public class LoginLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方式
     */
    private String requestType;

    /**
     * 登录终端
     */
    private String loginTerminal;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * ip地址
     */
    private String loginIp;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 返回结果
     */
    private String requestResult;

    /**
     * 请求状态（1:正确，0:错误）
     */
    private String requestStatus;

    /**
     * 错误消息
     */
    private String requestErrorMessage;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 用时
     */
    private Long useTime;

    /**
     * 账号来源
     */
    private String accountSource;

}
