package com.light.mq.config;

import com.light.mq.constants.RabbitMqConstants;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/1118:05
 */
@Configuration
public class DirectRabbitMQConfig {

    // Direct交换机
    @Bean
    DirectExchange directExchange() {
        return new DirectExchange(RabbitMqConstants.LOG_EXCHANGE, true, false);
    }

    // 登陆日志
    @Bean
    Queue loginLogQueue() {
        return new Queue(RabbitMqConstants.LOGIN_LOG_QUEUE, true);
    }

    // 操作日志
    @Bean
    Queue operationLogQueue() {
        return new Queue(RabbitMqConstants.OPERATION_LOG_QUEUE, true);
    }

    // competiiton日志
    @Bean
    Queue competitionLogQueue() {
        return new Queue(RabbitMqConstants.COMPETITION_LOG_QUEUE, true);
    }

    // 绑定队列和交换机
    @Bean
    Binding directLoginBinding() {
        return BindingBuilder.bind(loginLogQueue()).to(directExchange()).withQueueName();
    }

    @Bean
    Binding directOperationBinding() {
        return BindingBuilder.bind(operationLogQueue()).to(directExchange()).withQueueName();
    }

    @Bean
    Binding directCompetitionBinding() {
        return BindingBuilder.bind(competitionLogQueue()).to(directExchange()).withQueueName();
    }
}
