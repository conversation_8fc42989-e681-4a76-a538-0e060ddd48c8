package com.light.redis.component;

import cn.hutool.core.text.UnicodeUtil;
import com.light.redis.enums.RedisKeyEnum;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2021/7/715:23
 */
@Component("redisComponent")
public class RedisComponent {

    @Autowired
    private RedisTemplate redisTemplate;

    private final Logger log = LoggerFactory.getLogger(RedisComponent.class);

    private final ThreadLocal<String> lockFlag = new ThreadLocal<>();

    public String UNLOCK_LUA;
    //锁过期时间10秒
    private final long EXPIRE = 10;
    //锁重试次数
    private final int RETRYTIMES = 1;
    //重试间隔时间(毫秒)
    private final long SLEEPMILLIS = 1000;

    {
        UNLOCK_LUA = "if redis.call(\"get\",KEYS[1]) == ARGV[1] " +
                "then " +
                "    return redis.call(\"del\",KEYS[1]) " +
                "else " +
                "    return 0 " +
                "end ";
    }

    /**
     * redis锁
     *
     * @param key         key值
     * @param expire      过期时间(秒)
     * @param retryTimes  重试次数
     * @param sleepMillis 重试间隔时间(毫秒)
     * @return
     * <AUTHOR> zhi
     * @date
     */
    public boolean lock(String key, long expire, int retryTimes, long sleepMillis) {
        key = "lock:" + key;
        boolean result = setRedis(key, expire);
        // 如果获取锁失败，按照传入的重试次数进行重试
        while ((!result) && retryTimes-- > 0) {
            try {
                log.debug("lock failed, retrying...{}", retryTimes);
                Thread.sleep(sleepMillis);
            } catch (InterruptedException e) {
                return false;
            }
            result = setRedis(key, expire);
        }
        return result;
    }

    private boolean setRedis(String key, long expire) {
        //try {
        //    String result = (String) redisTemplate.execute((RedisCallback<String>) connection -> {
        //        Jedis jedis = (Jedis) connection.getNativeConnection();
        //        String uuid = UUID.randomUUID().toString();
        //        lockFlag.set(uuid);
        //        return jedis.set(key, uuid);
        //    });
        //    return StringUtils.isNotBlank(result);
        //} catch (Exception e) {
        //    log.error("set redis occured an exception", e);
        //}
        return true;
    }

    /**
     * redis锁
     *
     * @param key key值
     * @return
     * <AUTHOR> zhi
     * @date
     */
    public boolean releaseLock(String key) {
        key = "lock:" + key;
        // 释放锁的时候，有可能因为持锁之后方法执行时间大于锁的有效期，此时有可能已经被另外一个线程持有锁，所以不能直接删除
        try {
            List<String> keys = new ArrayList<>();
            keys.add(key);
            List<String> args = new ArrayList<>();
            args.add(lockFlag.get());

            // 使用lua脚本删除redis中匹配value的key，可以避免由于方法执行时间过长而redis锁自动过期失效的时候误删其他线程的锁
            // spring自带的执行脚本方法中，集群模式直接抛出不支持执行脚本的异常，所以只能拿到原redis的connection来执行脚本


            return true;
        } catch (Exception e) {
            log.error("release lock occured an exception", e);
        }
        return false;
    }

    /**
     * @param key 需要被锁住的key
     * @return 是否获取到锁
     * <AUTHOR> zhi
     */
    public boolean lockKey(String key) {
        return lock(key, EXPIRE, RETRYTIMES, SLEEPMILLIS);
    }

    /**
     * @param key 需要解锁的key
     * @return 是否获取到锁
     * <AUTHOR> zhi
     */
    public boolean releaseKey(String key) {
        return lock(key, EXPIRE, RETRYTIMES, SLEEPMILLIS);
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("RedisUtils expire(String key,long time) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("RedisUtils hasKey(String key) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils set(String key,Object value) failure." + e.getMessage());
            return false;
        }

    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("RedisUtils set(String key,Object value,long time) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta
     * @return
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 获取hash的所有key
     * @param key
     * @return
     */
    public List<String> hmgetKeys(String key) {
        Map<String, Object> map = redisTemplate.opsForHash().entries(key);
        List<String> list = new ArrayList<String>();
        if(!MapUtils.isEmpty(map)) {
            list = map.keySet().stream().map(String::toString).collect(Collectors.toList());
        }
        return list;
    }

    /**
     * 获取hash的所有key
     * @param key
     * @return
     */
    public List<String> hmgetKeysUnicode(String key) {
        Map<String, Object> map = redisTemplate.opsForHash().entries(key);
        List<String> list = new ArrayList<String>();
        if(!MapUtils.isEmpty(map)) {
            for(String mapKey : map.keySet()) {
                list.add(UnicodeUtil.toString(mapKey));
            }
        }
        return list;
    }

    /**
     * 获取hashKey对应的所有键的随机一个键
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public String hmgetOneKey(String key) {
        Map<String, Object> map = redisTemplate.opsForHash().entries(key);
        if(map.size() > 0) {
            Set<String> set = map.keySet();
            String[] keys = new String[set.size()];
            set.toArray(keys);
            return keys[0];
        }
        return null;
    }

    /**
     * 设置redisson lock 的 value
     * @param key
     * @param value
     * @return
     */
    public boolean setRedissonLockValue(String key, Integer value) {
        String hkey = this.hmgetOneKey(key);
        if(!StringUtils.isEmpty(hkey)) {
            return this.hset(key, hkey, value);
        }else{
            return false;
        }
    }

    /**
     * 获取redisson lock 的 value
     * @param key
     * @return
     */
    public Integer getRedissonLockValue(String key) {
        String hkey = this.hmgetOneKey(key);
        Object o = this.hget(key, hkey);
        if(null != o) {
            return Integer.valueOf(o.toString());
        }
        return null;
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils hmset(String key, Map<String,Object> map) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("RedisUtils hmset(String key, Map<String,Object> map, long time) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils hset(String key,String item,Object value) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒)  注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("RedisUtils hset(String key,String item,Object value,long time) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        try {
            redisTemplate.opsForHash().delete(key, item);
        } catch (Exception e) {
            log.error("redis hdel errorkey{},e{}", key, e.getMessage());
        }
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    public double hincr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, by);
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return
     */
    public double hdecr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, -by);
    }

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return
     */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error("RedisUtils sGet(String key) failure." + e.getMessage());
            return null;
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error("RedisUtils sHasKey(String key,Object value) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            log.error("RedisUtils sSet(String key, Object...values) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetExpire(String key,long time,  Object... values) {
        try {
            Long add = redisTemplate.opsForSet().add(key, values);
            redisTemplate.expire(key, time, TimeUnit.SECONDS);
            return add;
        } catch (Exception e) {
            log.error("RedisUtils sSet(String key, Object...values) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return count;
        } catch (Exception e) {
            log.error("RedisUtils sSetAndTime(String key,long time,Object...values) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return
     */
    public long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            log.error("RedisUtils sGetSetSize(String key) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            log.error("RedisUtils setRemove(String key, Object ...values) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束  0 到 -1代表所有值
     * @return
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("RedisUtils lGet(String key, long start, long end) failure." + e.getMessage());
            return null;
        }
    }

    /**
     * 左边pop数据
     * @param key
     * @return
     */
    public Object lPop(String key) {
        try{
            return redisTemplate.opsForList().leftPop(key);
        } catch (Exception e) {
            log.error("RedisUtils lPop(String key) failure." + e.getMessage());
            return null;
        }
    }

    public Long rPush(String key, String value) {
        try{
            return redisTemplate.opsForList().rightPush(key, value);
        } catch (Exception e) {
            log.error("RedisUtils rpush(String key, String value) failure." + e.getMessage());
            return null;
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return
     */
    public long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error("RedisUtils lGetListSize(String key) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引  index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            log.error("RedisUtils lGetIndex(String key,long index) failure." + e.getMessage());
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils lSet(String key, Object value) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("RedisUtils lSet(String key, Object value, long time) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSetAll(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils lSet(String key, List<Object> value) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("RedisUtils lSet(String key, List<Object> value, long time) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            log.error("RedisUtils lUpdateIndex(String key, long index,Object value) failure." + e.getMessage());
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            log.error("RedisUtils lRemove(String key,long count,Object value) failure." + e.getMessage());
            return 0;
        }
    }

    /**
     * 模糊匹配key删除
     * @param prex
     */
    public void deleteByPrex(String prex) {
        Set<String> keys = redisTemplate.keys(prex+ RedisKeyEnum.STAR.getValue());
        if (!CollectionUtils.isEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 获取hash对象的属性值
     * @param key
     * @param item
     * @param objectKey
     * @return
     */
    public String hgetObjectValue(String key, String item, String objectKey) {
        Map<String, Object> map = (Map<String, Object>) this.hget(key, item);
        if(null == map || null == map.get(objectKey)) {
            return null;
        }else{
           return map.get(objectKey).toString();
        }
    }

    /**
     * 模糊查询 key 前缀
     * @param prefix 前缀
     * */
    public Set<String> keys(String prefix) {
        // 获取所有的key
        Set<String> keys = redisTemplate.keys(prefix);
        return keys;
    }

    /**
     * 缓存添加有序集合
     * @param key
     * @param sort
     * @param value
     * @param <T>
     */
    public <T> void setSortSet(String key, double sort, final T value) {
        redisTemplate.opsForZSet().add(key, value, sort);
    }


    /**
     * 有序集合 增加分数
     * @param key the redis key
     * @param score the score
     * @param value the value
     */
    public <T> void incrementScore(String key, double score, final T value) {
        redisTemplate.opsForZSet().incrementScore(key, value, score);
    }

    /**
     * 倒序获取缓存有序集合
     * @param key
     * @param start
     * @param end
     * @param <T>
     * @return
     */
    public Set<ZSetOperations.TypedTuple> getReverseSortSet(String key, long start, long end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
    }

    /**
     * 正序获取缓存有序集合
     * @param key
     * @param start
     * @param end
     * @param <T>
     * @return
     */
    public Set<ZSetOperations.TypedTuple> getSortCacheSet(String key, long start, long end) {
        return redisTemplate.opsForZSet().rangeWithScores(key, start, end);
    }
}
