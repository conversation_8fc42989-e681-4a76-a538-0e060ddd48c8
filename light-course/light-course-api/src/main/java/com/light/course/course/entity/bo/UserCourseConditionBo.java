package com.light.course.course.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 课程人员自主学习表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-27 17:38:16
 */
@Data
public class UserCourseConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 用户oID
	 */
	@ApiModelProperty("用户oID")
	private String userOid;

	/**
	 * 课程ID
	 */
	@ApiModelProperty("课程ID")
	private Long courseId;

	/**
	 * 用户名称
	 */
	@ApiModelProperty("用户名称")
	private String userName;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 所属单位
	 */
	@ApiModelProperty("所属单位")
	private String organizationName;





	/**
	 * 学习时长
	 */
	@ApiModelProperty("学习时长")
	private Long learningDuration;

	/**
	 * 1、未完成，2、已经完成
	 */
	@ApiModelProperty("1、未完成，2、已经完成")
	private Integer isFinished;

	/**
	 * 学习总时长
	 */
	@ApiModelProperty("学习总时长")
	private Long studyTotalTime;

	/**
	 * 是否完成课程： 0: 否 1 ：是
	 */
	@ApiModelProperty("是否完成课程： 0: 否 1 ：是")
	private Integer isStudyFinished;

	/**
	 * 考试是否完成：0 否 1是
	 */
	@ApiModelProperty("考试是否完成：0 否 1是")
	private Integer isExamFinished;

	/**
	 * 来源：1、来自培训班，2、来自自主学习
	 */
	@ApiModelProperty("来源：1、来自培训班，2、来自自主学习")
	private Integer source;

}
