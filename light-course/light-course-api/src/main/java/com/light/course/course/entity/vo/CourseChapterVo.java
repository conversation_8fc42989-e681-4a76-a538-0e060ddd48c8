package com.light.course.course.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 课程目录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-24 15:59:17
 */
@Data
public class CourseChapterVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 目录名称
     */
    @ApiModelProperty("目录名称")
    private String name;

    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 总时长
     */
    @ApiModelProperty("总时长")
    private Long totalTime;

    /**
     * 是否试看 0 否 1 是
     */
    @ApiModelProperty("是否试看 0 否 1 是")
    private Integer isTry;

    /**
     * 课程ID
     */
    @ApiModelProperty("课程ID")
    private Long courseId;

    /**
     * 父级ID
     */
    @ApiModelProperty("父级ID")
    private Long parentId;

    /**
     * 所有父级集合 , 分割
     */
    @ApiModelProperty("所有父级集合 , 分割")
    private String superiorsIds;

    /**
     * 是否删除 ：  0 否 1是
     */
    @ApiModelProperty("是否删除 ：  0 否 1是")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 子章节列表
     */
    @ApiModelProperty("子章节列表")
    private List<CourseChapterVo> childrenList;

}
