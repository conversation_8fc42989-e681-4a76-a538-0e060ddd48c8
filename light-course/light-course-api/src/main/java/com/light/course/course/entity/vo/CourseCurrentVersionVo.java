package com.light.course.course.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 课程最新版本
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-24 15:59:42
 */
@Data
public class CourseCurrentVersionVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 课程code码
     */
    @ApiModelProperty("课程code码")
    private String courseCode;

    /**
     * 课程最新版本
     */
    @ApiModelProperty("课程最新版本")
    private String lastVersion;

    /**
     * 0是正常1是删除
     */
    @ApiModelProperty("0是正常1是删除")
    private Integer isDelete;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String updateBy;

}
