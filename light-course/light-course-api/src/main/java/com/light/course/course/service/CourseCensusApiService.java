package com.light.course.course.service;


import com.light.course.course.api.CourseCensusApi;
import com.light.course.course.entity.bo.CourseCensusBo;
import com.light.course.course.entity.bo.CourseCensusConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 课程相关数量统计表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-24 15:58:50
 */
@FeignClient(contextId = "courseCensusApiService", value= ServiceNameConstants.LIGHT_COURSE, configuration = FeignClientInterceptor.class, fallbackFactory = CourseCensusApiService.CourseCensusApiFallbackFactory.class)
@Component
public interface CourseCensusApiService extends CourseCensusApi {

    @Component
    class CourseCensusApiFallbackFactory implements FallbackFactory<CourseCensusApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CourseCensusApiFallbackFactory.class);
        @Override
        public CourseCensusApiService create(Throwable cause) {
            CourseCensusApiFallbackFactory.LOGGER.error("课程服务调用失败:{}", cause.getMessage());
            return new CourseCensusApiService() {
                public AjaxResult getCourseCensusPageListByCondition(CourseCensusConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCourseCensusListByCondition(CourseCensusConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCourseCensus(CourseCensusBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCourseCensus(CourseCensusBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}