package com.light.course.task.api;


import com.light.course.task.entity.bo.TaskUserResultConditionBo;
import com.light.course.task.entity.bo.TaskUserResultBo;
import com.light.course.task.entity.bo.TaskUserResultEvalBo;
import com.light.course.task.entity.vo.TaskUserResultVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;

import java.util.Collection;
import java.util.List;
/**
 * 作业用户结果表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
public interface TaskUserResultApi {

    /**
     * 查询作业用户结果表分页列表
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/task/user/result/page/list")
    public AjaxResult<PageInfo<TaskUserResultVo>> getTaskUserResultPageListByCondition(@RequestBody TaskUserResultConditionBo condition);

    /**
     * 查询作业用户结果表列表
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/task/user/result/list")
    public AjaxResult<List<TaskUserResultVo>> getTaskUserResultListByCondition(@RequestBody TaskUserResultConditionBo condition);

    @PostMapping("/task/user/resultEvel/list")
    public AjaxResult getTaskUserResultAndEvelByCondition(@RequestBody TaskUserResultConditionBo condition);


    /**
     * 新增作业用户结果表
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/task/user/result/add")
    public AjaxResult addTaskUserResult(@Validated @RequestBody TaskUserResultBo taskUserResultBo);

    /**
     * 修改作业用户结果表
     * @param taskUserResultBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/task/user/result/update")
    public AjaxResult updateTaskUserResult(@Validated @RequestBody TaskUserResultBo taskUserResultBo);

    /**
     * 查询作业用户结果表详情
     * @param id
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @GetMapping("/task/user/result/detail/{id}")
    public AjaxResult<TaskUserResultVo> getDetail(@PathVariable("id") Long id);

    /**
     * 根据taskId、用户OID 获取作品数量
     *
     * @param taskId  the task id
     * @param userOid the user oid
     * @return {@link AjaxResult}<{@link Integer}>
     */
    @GetMapping("/task/user/result/getCountByTaskIdAndUserOid")
    public AjaxResult<Integer> getCountByTaskIdAndUserOid(@RequestParam("taskId") Long taskId, @RequestParam("userOid") String userOid);

    /**
     * 删除作业用户结果表
     * @param id
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/task/user/result/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id);

    /**
     * 根据ID列表获取 作业作答信息集合
     * @param idList the task user result id list
     * @return
     */
    @PostMapping("/task/user/result/getByTaskUserResultIds")
    AjaxResult<List<TaskUserResultVo>> getByTaskUserResultIds(@RequestBody Collection<Long> idList);

    /**
     *  增加作业作答结果下载数量
     * @param id the task result id 作业作答ID
     * @param num the num 增加数量
     * @return
     */
    @PostMapping("/task/user/result/addDownloadNum")
    AjaxResult addDownloadNum(@RequestParam("id") Long id,@RequestParam("num") int num);

    /**
     *  增加作业作答结果下载数量
     * @param id the task result id 作业作答ID
     * @param num the num 增加数量
     * @return
     */
    @PostMapping("/task/user/result/addViewNum")
    AjaxResult addViewNum(@RequestParam("id") Long id,@RequestParam("num") int num);

    /**
     * 推荐列表
     * @param condition the task user result condition 作业作答结果搜索条件
     * @return AjaxResult
     */
    @PostMapping("/task/user/result/recommend-list")
    AjaxResult<List<TaskUserResultVo>> getRecommendListByCondition(@RequestBody TaskUserResultConditionBo condition);


    /**
     * 评分
     * @param id the task user result id 作业作答结果ID
     * @return AjaxResult
     */
    @PostMapping("/task/user/result/eval/{id}")
    AjaxResult eval(@PathVariable("id") Long id, @RequestBody TaskUserResultEvalBo taskUserResultEvalBo);

    /**
     * 取消评分
     * @return AjaxResult
     */
    @PostMapping("/task/user/result/cancelEval")
    AjaxResult cancelEval(@RequestParam("taskUserResultId")Long taskUserResultId, @RequestParam("userOid")String userOid);
    /**
     *  评分列表
     * @param taskUserResultConditionBo the task user result condition
     * @return
     */
    @PostMapping("/task/user/result/eval-page-list")
    AjaxResult<PageInfo<TaskUserResultVo>> getEvalTaskUserResultPageList(@RequestBody TaskUserResultConditionBo taskUserResultConditionBo);

    /**
     * 批量删除
     * @param ids the task user result id list 作业案例ID集合
     * @return
     */
    @PostMapping("/task/user/result/batchDelete")
    AjaxResult batchDelete(@RequestBody List<Long> ids);
}
