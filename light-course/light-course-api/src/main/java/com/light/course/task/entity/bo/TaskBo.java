package com.light.course.task.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 作业任务
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 作业ID
	 */
	@ApiModelProperty("作业ID")
	private Long taskTempId;

	/**
	 * 编码
	 */
	@ApiModelProperty("编码")
	private String code;

	/**
	 * 作业任务标题
	 */
	@ApiModelProperty("作业任务标题")
	private String title;

	/**
	 * 作业开始时间
	 */
	@ApiModelProperty("作业开始时间")
	private Date startTime;

	/**
	 * 作业任务结束时间
	 */
	@ApiModelProperty("作业任务结束时间")
	private Date endTime;

	/**
	 * 参于方式 ： 1 开放 2 指定单位
	 */
	@ApiModelProperty("参于方式 ： 1 开放 2 指定单位")
	private Integer joinModel;

	/**
	 * 评分人数
	 */
	@ApiModelProperty("评分人数")
	private Integer evalNum;

	/**
	 * 评分开始时间
	 */
	@ApiModelProperty("评分开始时间")
	private Date evalStartTime;

	/**
	 * 评分结束时间
	 */
	@ApiModelProperty("评分结束时间")
	private Date evalEndTime;

	/**
	 *  加入的组织机构ID
	 */
	@ApiModelProperty("加入的组织机构ID")
	private List<Long> joinOrgList;

	/**
	 * 专家OID列表
	 */
	@ApiModelProperty("专家OID集合")
	private List<String> evalUserOids;


}
