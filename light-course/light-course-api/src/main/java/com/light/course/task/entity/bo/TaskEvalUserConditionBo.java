package com.light.course.task.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业评分专家
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskEvalUserConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 活动ID
	 */
	@ApiModelProperty("活动ID")
	private Long taskId;

	/**
	 * 用户OID
	 */
	@ApiModelProperty("用户OID")
	private String userOid;

	/**
	 * 分类id
	 */
	@ApiModelProperty("分类id")
	private String categoryId;

	@ApiModelProperty("查询名称")
	private String searchName;

}
