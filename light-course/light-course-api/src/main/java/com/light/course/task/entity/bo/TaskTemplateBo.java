package com.light.course.task.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 作业
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskTemplateBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 编号
	 */
	@ApiModelProperty("编号")
	private String code;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String name;

	/**
	 * 封面地址
	 */
	@ApiModelProperty("封面地址")
	private String coverUrl;

	/**
	 * 备注描述说明
	 */
	@ApiModelProperty("备注描述说明")
	private String remark;

	/**
	 * 是否限制单个用户提交数量 0 否 1 是
	 */
	@ApiModelProperty("是否限制单个用户提交数量 0 否 1 是")
	private Integer isRestrict;

	/**
	 * 单个用户最多提交数量
	 */
	@ApiModelProperty("单个用户最多提交数量")
	private Long maxCommitNum;

	/**
	 * 状态 1 草稿 2 启用 3 下发
	 */
	@ApiModelProperty("状态 1 草稿 2 启用 3 下发")
	private Integer status;

	/**
	 * 资源文件
	 */
	@ApiModelProperty("资源文件")
	private String fileOids;

	/**
	 * 规则配置
	 */
	@ApiModelProperty("规则配置")
	private String ruleJson;

	/**
	 * 归属组织机构ID
	 */
	@ApiModelProperty("归属组织机构ID")
	private Long organizationId;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Integer isDelete;

	/**
	 * 作业规则
	 */
	@ApiModelProperty("作业规则")
	private TaskTemplateRuleBo taskTemplateRule;

	/**
	 * 作业模版评分项
	 */
	@ApiModelProperty("作业评分项")
	private List<TaskTempItemBo> taskTempItemList;

}
