package com.light.course.task.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 作业作品推荐
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskUserResultRecommendConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 作业作品ID
	 */
	@ApiModelProperty("作业作品ID")
	private Long taskUserResultId;

	/**
	 * 封面图片
	 */
	@ApiModelProperty("封面图片")
	private String coverUrl;

	/**
	 * 基础下载量
	 */
	@ApiModelProperty("基础下载量")
	private Long downloadNum;

	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	private Long sortNo;


	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;



	/**
	 * 是否删除 0 否 1 是
	 */
	@ApiModelProperty("是否删除 0 否 1 是")
	private Integer isDelete;

	/**
	 * 分类IDS
	 */
	@ApiModelProperty("分类ID")
	private String categoryIds;
}
