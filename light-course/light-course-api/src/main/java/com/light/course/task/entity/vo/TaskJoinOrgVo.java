package com.light.course.task.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 作业任务参与机构
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskJoinOrgVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 作业任务ID
     */
    @ApiModelProperty("作业任务ID")
    private Long taskId;

    /**
     * 组织机构ID
     */
    @ApiModelProperty("组织机构ID")
    private Long organizationId;

    /**
     * 组织机构名称
     */
    @ApiModelProperty("组织机构名称")
    private String orgName;

    /**
     * 组织机构教育级别
     */
    @ApiModelProperty("组织机构教育级别")
    private String orgSection;


    /**
     * 组织机构区域信息
     */
    @ApiModelProperty("组织机构区域信息")
    private String orgAreaName;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

}
