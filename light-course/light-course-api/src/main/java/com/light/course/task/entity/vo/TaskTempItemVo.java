package com.light.course.task.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 作业评分项
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskTempItemVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long taskTempId;

    /**
     * 项目名称/维度名称
     */
    @ApiModelProperty("项目名称/维度名称")
    private String name;

    /**
     * 分值
     */
    @ApiModelProperty("分值")
    private BigDecimal maxScore;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sortNo;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

}
