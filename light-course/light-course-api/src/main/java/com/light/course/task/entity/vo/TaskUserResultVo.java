package com.light.course.task.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 作业用户结果表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
public class TaskUserResultVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 作业模板ID
     */
    @ApiModelProperty("作业模板ID")
    private Long tempId;

    /**
     * 作业任务ID
     */
    @ApiModelProperty("作业任务ID")
    private Long taskId;

    /**
     * 推荐封面
     */
    @ApiModelProperty("推荐封面")
    private String recommendCoverUrl;
    /**
     * CODE码
     */
    @ApiModelProperty("CODE码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 作者
     */
    @ApiModelProperty("作者")
    private String author;

    /**
     * 分类ID列表 ，（逗号隔开,）
     */
    @ApiModelProperty("分类ID列表 ，（逗号隔开,）")
    private String categoryIds;

    /**
     * 附件OIDS
     */
    @ApiModelProperty("附件OIDS")
    private String fileOids;

    /**
     * 附件数量
     */
    @ApiModelProperty("附件数量")
    private Long fileNum;

    /**
     * 用户OID
     */
    @ApiModelProperty("用户OID")
    private String userOid;

    /**
     * 状态 ： 0  待评分 1 评分中 2 已评分
     */
    @ApiModelProperty("状态 ： 0  待评分 1 评分中 2 已评分")
    private Integer status;

    /**
     * 总分
     */
    @ApiModelProperty("总分")
    private Double scoreTotal;

    /**
     * 平均分
     */
    @ApiModelProperty("平均分")
    private Double scoreAvg;

    /**
     * 所属组织机构（学校）
     */
    @ApiModelProperty("所属组织机构（学校）")
    private Long organizationId;

    /**
     *
     */
    @ApiModelProperty("")
    private Date createTime;

    /**
     *
     */
    @ApiModelProperty("")
    private Date updateTime;

    /**
     *
     */
    @ApiModelProperty("")
    private Integer isDelete;

    @ApiModelProperty("下载数量")
    private Integer downloadNum;

    @ApiModelProperty("预览量")
    private Integer viewNum;

    /**
     * 组织机构名称
     */
    @ApiModelProperty("组织机构名称")
    private String organizationName;

    /**
     * 组织机构区县信息
     */
    @ApiModelProperty("组织机构区县信息")
    private String organizationAreaName;

    /**
     * 评分规则
     */
    @ApiModelProperty("评分规则")
    private TaskTemplateRuleVo ruleVo;

    /**
     *  评分维度项目
     */
    @ApiModelProperty("评分维度项目")
    private List<TaskTempItemVo> taskTempItemVoList;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private List<String> categoryNames;


    /**
     * 是否评分
     */
    @ApiModelProperty("是否评分 ： 0  待评分 1 已评分")
    private Integer isEval;

    /**
     *作业任务编号
     */
    @ApiModelProperty("作业任务编号")
    private String taskCode;

    /**
     * 作业任务名称
     */
    @ApiModelProperty("作业任务名称")
    private String taskTitle;

    /**
     *  作业任务评分开始时间
     */
    private Date taskEvalStartTime;

    /**
     *  作业任务评分结束时间
     */
    private Date taskEvalEndTime;

    /**
     * 当前用户评审分数
     */
    private Double currentTotalScore;

    private Integer evalState;

    /**
     * 活动开始状态
     */
    private Integer taskState;

    @ApiModelProperty("活动开始时间")
    private Date taskStartTime;

    @ApiModelProperty("活动结束时间")
    private Date taskEndTime;

    @ApiModelProperty("是否分配专家")
    private Integer isAlloc;

    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("省市区名称")
    private String provinceCityAreaName;

    @ApiModelProperty("工作单位")
    private String work;

}
