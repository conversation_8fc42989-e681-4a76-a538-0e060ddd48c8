package com.light.course.training.api;


import com.light.course.training.entity.bo.TrainingPlanConditionBo;
import com.light.course.training.entity.bo.TrainingPlanBo;
import com.light.course.training.entity.vo.TrainingClassMemberVo;
import com.light.course.training.entity.vo.TrainingPlanVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 培训计划表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-27 17:41:41
 */
public interface TrainingPlanApi {

    /**
     * 查询培训计划表分页列表
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @PostMapping("/training/plan/page/list")
    public AjaxResult<PageInfo<TrainingPlanVo>> getTrainingPlanPageListByCondition(@RequestBody TrainingPlanConditionBo condition);

    /**
     * 查询培训计划表列表
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @PostMapping("/training/plan/list")
    public AjaxResult<List<TrainingPlanVo>> getTrainingPlanListByCondition(@RequestBody TrainingPlanConditionBo condition);

    /**
     * 查询用户培训计划表列表
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @PostMapping("/user/training/plan/list")
    public AjaxResult<PageInfo<TrainingClassMemberVo>> getUserTrainingPlanListByCondition(@RequestBody TrainingPlanConditionBo condition);


    /**
     * 新增培训计划表
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @PostMapping("/training/plan/add")
    public AjaxResult addTrainingPlan(@Validated @RequestBody TrainingPlanBo trainingPlanBo);

    /**
     * 修改培训计划表
     * @param trainingPlanBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @PostMapping("/training/plan/update")
    public AjaxResult updateTrainingPlan(@Validated @RequestBody TrainingPlanBo trainingPlanBo);

    /**
     * 查询培训计划表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @GetMapping("/training/plan/detail")
    public AjaxResult<TrainingPlanVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除培训计划表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    @GetMapping("/training/plan/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
