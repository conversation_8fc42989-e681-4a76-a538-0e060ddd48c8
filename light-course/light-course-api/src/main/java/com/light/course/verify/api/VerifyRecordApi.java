package com.light.course.verify.api;


import com.light.course.verify.entity.bo.VerifyRecordConditionBo;
import com.light.course.verify.entity.bo.VerifyRecordBo;
import com.light.course.verify.entity.vo.VerifyRecordVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 审核记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-27 09:28:37
 */
public interface VerifyRecordApi {

    /**
     * 查询审核记录分页列表
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/verify/record/page/list")
    public AjaxResult<PageInfo<VerifyRecordVo>> getVerifyRecordPageListByCondition(@RequestBody VerifyRecordConditionBo condition);

    /**
     * 查询审核记录列表
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/verify/record/list")
    public AjaxResult<List<VerifyRecordVo>> getVerifyRecordListByCondition(@RequestBody VerifyRecordConditionBo condition);


    /**
     * 新增审核记录
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/verify/record/add")
    public AjaxResult addVerifyRecord(@Validated @RequestBody VerifyRecordBo verifyRecordBo);

    /**
     * 修改审核记录
     * @param verifyRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/verify/record/update")
    public AjaxResult updateVerifyRecord(@Validated @RequestBody VerifyRecordBo verifyRecordBo);

    /**
     * 查询审核记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @GetMapping("/verify/record/detail")
    public AjaxResult<VerifyRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除审核记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @GetMapping("/verify/record/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
