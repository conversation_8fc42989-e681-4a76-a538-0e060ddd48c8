package com.light.course.course.controller;

import com.light.course.course.api.CourseCensusApi;
import com.light.course.course.entity.dto.CourseCensusDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.course.course.entity.bo.CourseCensusConditionBo;
import com.light.course.course.entity.bo.CourseCensusBo;
import com.light.course.course.entity.vo.CourseCensusVo;
import com.light.course.course.service.ICourseCensusService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 课程相关数量统计表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-24 16:16:12
 */
@RestController
@Validated
public class CourseCensusController implements CourseCensusApi{
	
    @Autowired
    private ICourseCensusService courseCensusService;

    /**
     * 查询课程相关数量统计表分页列表
     * <AUTHOR>
     * @date 2022-06-24 16:16:12
     */
    @Override
    public AjaxResult<PageInfo<CourseCensusVo>> getCourseCensusPageListByCondition(@RequestBody CourseCensusConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CourseCensusVo> pageInfo = new PageInfo<>(courseCensusService.getCourseCensusListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询课程相关数量统计表列表
	 * <AUTHOR>
	 * @date 2022-06-24 16:16:12
	 */
	@Override
	public AjaxResult<List<CourseCensusVo>> getCourseCensusListByCondition(@RequestBody CourseCensusConditionBo condition){
		List<CourseCensusVo> list = courseCensusService.getCourseCensusListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增课程相关数量统计表
     * <AUTHOR>
     * @date 2022-06-24 16:16:12
     */
	@Override
    public AjaxResult addCourseCensus(@Validated @RequestBody CourseCensusBo courseCensusBo){
		return courseCensusService.addCourseCensus(courseCensusBo);
    }

    /**
	 * 修改课程相关数量统计表
	 * @param courseCensusBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-24 16:16:12
	 */
	@Override
	public AjaxResult updateCourseCensus(@Validated @RequestBody CourseCensusBo courseCensusBo) {
		if(null == courseCensusBo.getId()) {
			return AjaxResult.fail("课程相关数量统计表id不能为空");
		}
		return courseCensusService.updateCourseCensus(courseCensusBo);
	}

	/**
	 * 查询课程相关数量统计表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-24 16:16:12
	 */
	@Override
	public AjaxResult<CourseCensusVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("课程相关数量统计表id不能为空");
		}
		CourseCensusVo vo = courseCensusService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除课程相关数量统计表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-24 16:16:12
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CourseCensusDto courseCensusDto = new CourseCensusDto();
		courseCensusDto.setId(id);
		courseCensusDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(courseCensusService.updateById(courseCensusDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
