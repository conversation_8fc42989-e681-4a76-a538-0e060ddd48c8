package com.light.course.task.controller;

import com.light.course.task.api.TaskJoinOrgApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.course.task.entity.bo.TaskJoinOrgConditionBo;
import com.light.course.task.entity.bo.TaskJoinOrgBo;
import com.light.course.task.entity.vo.TaskJoinOrgVo;
import com.light.course.task.service.ITaskJoinOrgService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import java.util.List;
/**
 * 作业任务参与机构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@RestController
@Validated
public class TaskJoinOrgController implements TaskJoinOrgApi {
	
    @Autowired
    private ITaskJoinOrgService taskOrgService;

    /**
     * 查询作业任务参与机构分页列表
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @Override
    public AjaxResult<PageInfo<TaskJoinOrgVo>> getTaskOrgPageListByCondition(@RequestBody TaskJoinOrgConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<TaskJoinOrgVo> pageInfo = new PageInfo<>(taskOrgService.getTaskOrgListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询作业任务参与机构列表
	 * <AUTHOR>
	 * @date 2022-06-30 10:10:39
	 */
	@Override
	public AjaxResult<List<TaskJoinOrgVo>> getTaskOrgListByCondition(@RequestBody TaskJoinOrgConditionBo condition){
		List<TaskJoinOrgVo> list = taskOrgService.getTaskOrgListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增作业任务参与机构
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
	@Override
    public AjaxResult addTaskOrg(@Validated @RequestBody TaskJoinOrgBo taskJoinOrgBo){
		return taskOrgService.addTaskOrg(taskJoinOrgBo);
    }

    /**
	 * 修改作业任务参与机构
	 * @param taskJoinOrgBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
	 */
	@Override
	public AjaxResult updateTaskOrg(@Validated @RequestBody TaskJoinOrgBo taskJoinOrgBo) {
		if(null == taskJoinOrgBo.getId()) {
			return AjaxResult.fail("作业任务参与机构id不能为空");
		}
		return taskOrgService.updateTaskOrg(taskJoinOrgBo);
	}

	/**
	 * 查询作业任务参与机构详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
	 */
	@Override
	public AjaxResult<TaskJoinOrgVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("作业任务参与机构id不能为空");
		}
		TaskJoinOrgVo vo = taskOrgService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除作业任务参与机构
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		if(taskOrgService.removeById(id)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
