package com.light.course.task.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业用户结果表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_task_user_result")
public class TaskUserResultDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 作业模板ID
	 */
	@TableField("temp_id")
	private Long tempId;

	/**
	 * 作业任务ID
	 */
	@TableField("task_id")
	private Long taskId;

	/**
	 * CODE码
	 */
	@TableField("code")
	private String code;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 作者
	 */
	@TableField("author")
	private String author;

	/**
	 * 分类ID列表 ，（逗号隔开,）
	 */
	@TableField("category_ids")
	private String categoryIds;

	/**
	 * 附件OIDS
	 */
	@TableField("file_oids")
	private String fileOids;

	/**
	 * 附件数量
	 */
	@TableField("file_num")
	private Long fileNum;

	/**
	 * 用户OID
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 状态 ： 0  待评分 1 评分中 2 已评分
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 总分
	 */
	@TableField("score_total")
	private Double scoreTotal;

	/**
	 * 平均分
	 */
	@TableField("score_avg")
	private Double scoreAvg;

	/**
	 * 所属组织机构（学校）
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 是否分配，0未分，1已分
	 */
	@TableField("is_alloc")
	private Integer isAlloc;

	/**
	 *
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 *
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 *
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
