package com.light.course.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.course.task.entity.dto.TaskTempItemDto;
import com.light.course.task.entity.bo.TaskTempItemConditionBo;
import com.light.course.task.entity.bo.TaskTempItemBo;
import com.light.course.task.entity.vo.TaskTempItemVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业评分项接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
public interface ITaskTempItemService extends IService<TaskTempItemDto> {

    List<TaskTempItemVo> getTaskTempItemListByCondition(TaskTempItemConditionBo condition);

	AjaxResult addTaskTempItem(TaskTempItemBo taskTempItemBo);

	AjaxResult updateTaskTempItem(TaskTempItemBo taskTempItemBo);

	TaskTempItemVo getDetail(Long id);

	/**
	 * 根据作业模版删除 作业项目
	 * @param taskTemplateId
	 * @return
	 */
    boolean deleteByTaskTempId(Long taskTemplateId);

	/**
	 * 根据作业模板ID获取 评分项列表
	 * @param taskTemplateId
	 * @return
	 */
	List<TaskTempItemVo> getVoListByTaskTemplateId(Long taskTemplateId);
}

