package com.light.course.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.course.task.entity.dto.TaskTemplateDto;
import com.light.course.task.entity.bo.TaskTemplateConditionBo;
import com.light.course.task.entity.bo.TaskTemplateBo;
import com.light.course.task.entity.vo.TaskTemplateVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
public interface ITaskTemplateService extends IService<TaskTemplateDto> {

    List<TaskTemplateVo> getTaskTemplateListByCondition(TaskTemplateConditionBo condition);

	AjaxResult addTaskTemplate(TaskTemplateBo taskTemplateBo);

	AjaxResult updateTaskTemplate(TaskTemplateBo taskTemplateBo);

	TaskTemplateVo getDetail(Long id);

	/**
	 * 根据ID删除作业模板
	 * @param id
	 * @return
	 */
	AjaxResult deleteById(Long id);

	TaskTemplateDto getByTaskTemplateId(Long id);
}

