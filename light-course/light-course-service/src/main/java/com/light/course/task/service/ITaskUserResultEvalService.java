package com.light.course.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.course.task.entity.dto.TaskUserResultEvalDto;
import com.light.course.task.entity.bo.TaskUserResultEvalConditionBo;
import com.light.course.task.entity.bo.TaskUserResultEvalBo;
import com.light.course.task.entity.vo.TaskUserResultEvalVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 作业任务作品评分记录接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
public interface ITaskUserResultEvalService extends IService<TaskUserResultEvalDto> {

    List<TaskUserResultEvalVo> getTaskUserResultEvalListByCondition(TaskUserResultEvalConditionBo condition);

	/**
	 * 增加评分记录
	 * @param taskUserResultEvalBo the task yser result eval BO
	 * @return
	 */
	AjaxResult addTaskUserResultEval(TaskUserResultEvalBo taskUserResultEvalBo);

	AjaxResult updateTaskUserResultEval(TaskUserResultEvalBo taskUserResultEvalBo);

	TaskUserResultEvalVo getDetail(Long id);

	/**
	 * 根据作业作答ID 获取评分信息
	 * @param taskUserResultId the task user result id 作业用户结果ID
	 * @return
	 */
    List<TaskUserResultEvalVo> getByTaskUserResultId(Long taskUserResultId);

    List<TaskUserResultEvalVo> getByTaskUserResultIdList(List<Long> ids);

	/**
	 * 根据作业作答ID 获取评分数量
	 * @param taskUserResultId the task user result id 作业用户结果ID
	 * @param isEval  the is_eval 是否评分 0 否 1 是
	 * @return
	 */
	int getCountByTaskUserResultIdAndIsEval(Long taskUserResultId,Integer isEval);

	/**
	 * 根据作业作答ID 获取评分
	 * @param taskUserResultId the task user result id 作业用户结果ID
	 * @param isEval  the is_eval 是否评分 0 否 1 是
	 * @return
	 */
	List<TaskUserResultEvalDto> getByTaskUserResultIdAndIsEval(Long taskUserResultId,Integer isEval);

	/**
	 *  根据作业作答ID 获取评分信息
	 * @param taskUserResultId the task user result id
	 * @return
	 */
	List<TaskUserResultEvalVo> getVoByTaskUserResultId(Long taskUserResultId);

	/**
	 *  根据作业作答结果ID、评分用户OID 获取 评分信息
	 * @param taskUserResultId the task user result id 作业任务ID
	 * @param evalUserOid  the eval user oid 用户OID
	 * @return
	 */
	TaskUserResultEvalVo getVoByTaskUserResultIdAndEvalUserOid(Long taskUserResultId, String evalUserOid);

	/**
	 *  根据作业作答结果ID、评分用户OID 获取 评分信息
	 * @param taskUserResultId the task user result id 作业任务ID
	 * @param evalUserOid  the eval user oid 用户OID
	 * @return
	 */
	TaskUserResultEvalDto getByTaskUserResultIdAndEvalUserOid(Long taskUserResultId, String evalUserOid);
}

