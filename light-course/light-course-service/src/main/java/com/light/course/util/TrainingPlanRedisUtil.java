package com.light.course.util;

import com.alibaba.fastjson.JSON;
import com.light.core.constants.SystemConstants;
import com.light.course.training.entity.bo.TrainingPlanConditionBo;
import com.light.course.training.entity.vo.TrainingPlanVo;
import com.light.course.training.service.ITrainingPlanService;
import com.light.redis.component.RedisComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 培训计划表Redis组件
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-27 17:41:41
 */
@Component
@Slf4j
public class TrainingPlanRedisUtil {

    public static RedisComponent redisComponent;

    public static ITrainingPlanService trainingPlanService;

    @Resource
    public void setRedisComponent(RedisComponent redisComponent){
        TrainingPlanRedisUtil.redisComponent = redisComponent;
    }

    @Resource
    public void setTrainingPlanService(ITrainingPlanService trainingPlanService){
        TrainingPlanRedisUtil.trainingPlanService = trainingPlanService;
    }

    private static final String REDIS_KEY = "training:plan";


    /**
     * 刷新单个培训计划表数据到redis
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    public static boolean refreshTrainingPlan(TrainingPlanVo vo) {
        boolean result = false;
        try {
            if(null != vo && null != vo.getId()) {
                                redisComponent.set(REDIS_KEY + SystemConstants.SEPERATOR_COLON + vo.getId().toString(), JSON.toJSONString(vo), SystemConstants.REDIS_COMMON_EXPIRE_TIME);
            }
            result = true;
        }catch (Exception e){
            log.error("刷新单个培训计划表数据到redis失败{}", e.getMessage());
            result = false;
        }finally {
            return result;
        }
    }

    /**
     * 根据id刷新单个培训计划表数据到redis
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    public static boolean refreshTrainingPlanById(Long id) {
        boolean result = false;
        try {
            //查询数据
            TrainingPlanVo vo = trainingPlanService.getDetail(id);
            if(null != vo && null != vo.getId()) {
                //非空时更新缓存数据
                redisComponent.set(REDIS_KEY + SystemConstants.SEPERATOR_COLON + id.toString(), JSON.toJSONString(vo), SystemConstants.REDIS_COMMON_EXPIRE_TIME);
            }else {
                //空时清理缓存数据
                redisComponent.del(REDIS_KEY + SystemConstants.SEPERATOR_COLON + id.toString());
            }
            result = true;
        }catch (Exception e){
            log.error("根据id刷新单个培训计划表数据到redis失败{}", e.getMessage());
            result = false;
        }finally {
            return result;
        }
    }



    /**
     * 根据id删除单个培训计划表redis数据
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    public static boolean delTrainingPlanById(Long id) {
        boolean result = false;
        try {
            //删除缓存数据
            redisComponent.del(REDIS_KEY + SystemConstants.SEPERATOR_COLON + id.toString());
            result = true;
        }catch (Exception e){
            log.error("根据id删除单个培训计划表redis数据失败{}", e.getMessage());
            result = false;
        }finally {
            return result;
        }
    }



    /**
     * 根据id查询单个培训计划表redis数据
     * <AUTHOR>
     * @date 2024-12-27 17:41:41
     */
    public static TrainingPlanVo getTrainingPlanById(Long id) {
        TrainingPlanVo vo = new TrainingPlanVo();
        try {
            //查询缓存数据
            Object object = redisComponent.get(REDIS_KEY + SystemConstants.SEPERATOR_COLON + id.toString());
            if(null != object) {
                vo = JSON.parseObject(object.toString(), TrainingPlanVo.class);
            }
        }catch (Exception e){
            log.error("根据id查询单个培训计划表redis数据失败{}", e.getMessage());
        }finally {
            return vo;
        }
    }


}