<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.course.course.mapper.CourseChapterMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.course.course.entity.dto.CourseChapterDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="title" column="title"/>
        <result property="totalTime" column="total_time"/>
        <result property="isTry" column="is_try"/>
        <result property="courseId" column="course_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="superiorsIds" column="superiors_ids"/>
        <result property="isDelete" column="is_delete"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="totalTime != null ">and total_time = #{totalTime}</if>
			<if test="isTry != null ">and is_try = #{isTry}</if>
			<if test="courseId != null ">and course_id = #{courseId}</if>
			<if test="parentId != null ">and parent_id = #{parentId}</if>
			<if test="superiorsIds != null and superiorsIds != '' ">and superiors_ids like concat('%', #{superiorsIds}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.name
	 		,t.title
	 		,t.total_time
	 		,t.is_try
	 		,t.course_id
	 		,t.parent_id
	 		,t.superiors_ids
	 		,t.is_delete
	 		,t.update_time
	 		,t.create_time
	 		,t.create_by
	 		,t.update_by
		from (
			 select a.* from p_course_chapter a
		 ) t

	</sql>

	<select id="getCourseChapterListByCondition" resultType="com.light.course.course.entity.vo.CourseChapterVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
    <select id="selectChildrenById" resultType="com.light.course.course.entity.dto.CourseChapterDto">
		select a.*
		from p_course_chapter a
		where is_delete = 0
		and find_in_set(#{id},a.superiors_ids)
	</select>
    <select id="selectByCourseId" resultType="com.light.course.course.entity.vo.CourseChapterVo">
		select a.*
		from p_course_chapter a
		where a.is_delete = 0
		 and a.course_id = #{courseId}
	</select>
</mapper>