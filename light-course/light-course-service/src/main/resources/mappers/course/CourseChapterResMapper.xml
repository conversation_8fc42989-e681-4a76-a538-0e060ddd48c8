<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.course.course.mapper.CourseChapterResMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.course.course.entity.dto.CourseChapterResDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="resName" column="res_name"/>
        <result property="fileOid" column="file_oid"/>
        <result property="fileName" column="file_name"/>
        <result property="type" column="type"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="courseId" column="course_id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="resName != null and resName != '' ">and res_name like concat('%', #{resName}, '%')</if>
			<if test="fileOid != null and fileOid != '' ">and file_oid like concat('%', #{fileOid}, '%')</if>
			<if test="fileName != null and fileName != '' ">and file_name like concat('%', #{fileName}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="chapterId != null ">and chapter_id = #{chapterId}</if>
			<if test="courseId != null ">and course_id = #{courseId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.res_name
	 		,t.file_oid
	 		,t.file_name
	 		,t.file_size
	 		,t.type
	 		,t.chapter_id
	 		,t.course_id
		    ,t.sort_no
	 		,t.is_delete
	 		,t.update_time
	 		,t.create_time
	 		,t.create_by
	 		,t.update_by
		from (
			 select a.* from p_course_chapter_res a
		 ) t

	</sql>

	<select id="getCourseChapterResListByCondition" resultType="com.light.course.course.entity.vo.CourseChapterResVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by sort_no asc
	</select>
</mapper>