<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.course.course.mapper.CourseInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.course.course.entity.dto.CourseInfoDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="courseId" column="course_id"/>
        <result property="introduction" column="introduction"/>
        <result property="speakerDesc" column="speaker_desc"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="courseId != null ">and course_id = #{courseId}</if>
			<if test="introduction != null and introduction != '' ">and introduction like concat('%', #{introduction}, '%')</if>
			<if test="speakerDesc != null and speakerDesc != '' ">and speaker_desc like concat('%', #{speakerDesc}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.course_id
	 		,t.introduction
	 		,t.speaker_desc
	 		,t.update_time
	 		,t.create_time
	 		,t.create_by
	 		,t.update_by
		from (
			 select a.* from p_course_info a
		 ) t

	</sql>

	<select id="getCourseInfoListByCondition" resultType="com.light.course.course.entity.vo.CourseInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>