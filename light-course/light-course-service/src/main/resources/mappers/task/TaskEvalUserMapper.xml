<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.course.task.mapper.TaskEvalUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.course.task.entity.dto.TaskEvalUserDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="taskId != null ">and task_id = #{taskId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.task_id
	 		,t.user_oid
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
		from (
			 select a.* from p_task_eval_user a
		 ) t

	</sql>

	<select id="getTaskEvalUserListByCondition" resultType="com.light.course.task.entity.vo.TaskEvalUserVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getExpertListByCondition" resultType="com.light.course.task.entity.vo.TaskEvalUserVo">
		select
		t.id
		,t.task_id
		,t.title
		,t.user_oid
		,t.evalUserOid
		,t.category_ids
		,t.organization_id
		,t.create_time
		,t.update_time
		from (
		select a.*,b.user_oid as evalUserOid,c.title from p_task_user_result a
		left join p_task c on a.task_id = c.id
		left join p_task_user_result_evel_alloc b on b.task_user_result_id = a.id and b.user_oid like concat('%', #{userOid}, '%')
		) t
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="categoryId != null ">and find_in_set(#{categoryId},category_ids)</if>
		</where>
	</select>
</mapper>