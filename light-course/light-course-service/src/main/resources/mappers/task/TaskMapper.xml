<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.course.task.mapper.TaskMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.course.task.entity.dto.TaskDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="joinModel" column="join_model"/>
        <result property="evalStartTime" column="eval_start_time"/>
        <result property="evalEndTime" column="eval_end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="taskTempId != null ">and task_temp_id = #{taskTempId}</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="startTime != null ">and start_time = #{startTime}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>

			<if test="startTime != null and endTime != null ">
				and (
					start_time between #{startTime} and #{endTime}
					or end_time between #{startTime} and #{endTime}
				)
			</if>

			<if test="joinModel != null ">and join_model = #{joinModel}</if>
			<if test="evalStartTime != null ">and eval_start_time = #{evalStartTime}</if>
			<if test="evalEndTime != null ">and eval_end_time = #{evalEndTime}</if>
			<if test="state != null ">
				<if test="state == 0 ">
					and start_time &gt; now()
				</if>
				<if test="state == 1 ">
					and  now() between start_time and end_time
				</if>
				<if test="state == 2 ">
					and  end_time &lt; now()
				</if>
			</if>
			<if test="evalState != null ">
				<if test="evalState == 0 ">
					and eval_start_time &gt; now()
				</if>
				<if test="evalState == 1 ">
					and  now() between eval_start_time and eval_end_time
				</if>
				<if test="evalState == 2 ">
					and  eval_end_time &lt; now()
				</if>
			</if>
		</where>
	</sql>



	<select id="getTaskListByCondition" resultType="com.light.course.task.entity.vo.TaskVo">

		select
		*
		from (
			select a.*,tt.cover_url,tjo.organization_id as join_organization_id,tt.remark as templateRemark
			from p_task a inner join p_task_template tt on a.task_temp_id = tt.id
			left join p_task_join_org tjo on a.id = tjo.task_id
		) t
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="taskTempId != null ">and task_temp_id = #{taskTempId}</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="code != null and code != '' ">and code like concat('%', #{code}, '%')</if>
			<if test="searchKey != null and searchKey != ''">
				and (code like concat('%',#{searchKey},'%') or title like concat('%', #{searchKey}, '%'))
			</if>
			<if test="startTime != null and endTime != null ">
				and (
					start_time between #{startTime} and #{endTime}
					or end_time between #{startTime} and #{endTime}
				)
			</if>
			<if test="createStartTime != null and createStartTime != ''">
				and create_time &gt;= #{createStartTime}
			</if>
			<if test="createEndTime != null and createEndTime != ''">
				and create_time &lt;= #{createEndTime}
			</if>

			<if test="joinModel != null ">and join_model = #{joinModel}</if>
			<if test="evalStartTime != null ">and eval_start_time = #{evalStartTime}</if>
			<if test="evalEndTime != null ">and eval_end_time = #{evalEndTime}</if>
			<if test="state != null ">
				<if test="state == 0 ">
					and start_time &gt; now()
				</if>
				<if test="state == 1 ">
					and  now() between start_time and end_time
				</if>
				<if test="state == 2 ">
					and  end_time &lt; now()
				</if>
			</if>
			<if test="notState != null">
                 <if test="notState == 2">
                    and  now() &lt; end_time
                </if>
            </if>
			<if test="evalState != null ">
				<if test="evalState == 0 ">
					and eval_start_time &gt; now()
				</if>
				<if test="evalState == 1 ">
					and  now() between eval_start_time and eval_end_time
				</if>
				<if test="evalState == 2 ">
					and  eval_end_time &lt; now()
				</if>
			</if>
			<if test="isUserTaskList != null and isUserTaskList == 1">
				and (
					<if test="joinOrgIdList != null and joinOrgIdList.size() != 0">
					    join_organization_id in
						<foreach collection="joinOrgIdList" index="index" item="item" open="(" close=")" separator=",">
							#{item}
						</foreach>
						or
					</if>
					join_model = 1
				)
			</if>
		</where>
		group by id
	</select>

	<select id="getEvalTaskPageList" resultType="com.light.course.task.entity.vo.TaskVo">
	    select pt.*,tt.cover_url
	    from p_task_eval_user pteu inner join p_task pt on pteu.task_id = pt.id and pt.is_delete = 0
	    inner join p_task_template tt on pt.task_temp_id = tt.id

	    <where>
             <if test="evalUserOid != null and evalUserOid != ''">
                 and pteu.user_oid = #{evalUserOid}
             </if>
             <if test="evalState != null">
                <if test="evalState == 0 ">
					and pt.eval_start_time &gt; now()
				</if>
				<if test="evalState == 1 ">
					and  now() between pt.eval_start_time and pt.eval_end_time
				</if>
				<if test="evalState == 2 ">
					and  pt.eval_end_time &lt; now()
				</if>
            </if>
			<if test="title != null and title != '' ">and pt.title like concat('%', #{title}, '%')</if>
        </where>

    </select>

    <select id="getMyTaskPageListByCondition" resultType="com.light.course.task.entity.vo.TaskVo">
		SELECT
			a.*,
			b.*,
			c.cover_url
		FROM
			( SELECT task_id FROM p_task_user_result
			  WHERE user_oid = #{userOid} AND is_delete = 0 GROUP BY task_id ) a
			LEFT JOIN p_task b ON a.task_id = b.id AND b.is_delete = 0
			left join p_task_template c on b.task_temp_id = c.id
    </select>

    <select id="getListByIdList" resultType="com.light.course.task.entity.vo.TaskVo">
        select * from p_task where is_delete = 0 and id in
        <foreach collection="taskIdList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
