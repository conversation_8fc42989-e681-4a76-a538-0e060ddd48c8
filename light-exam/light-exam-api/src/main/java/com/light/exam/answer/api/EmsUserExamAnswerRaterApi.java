package com.light.exam.answer.api;


import com.light.exam.answer.entity.bo.EmsUserExamAnswerRaterConditionBo;
import com.light.exam.answer.entity.bo.EmsUserExamAnswerRaterBo;
import com.light.exam.answer.entity.vo.EmsUserExamAnswerRaterVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 作答记录复核人表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-14 17:25:14
 */
public interface EmsUserExamAnswerRaterApi {

    /**
     * 查询作答记录复核人表分页列表
     * <AUTHOR>
     * @date 2023-09-14 17:25:14
     */
    @PostMapping("/ems/user/exam/answer/rater/page/list")
    public AjaxResult<PageInfo<EmsUserExamAnswerRaterVo>> getEmsUserExamAnswerRaterPageListByCondition(@RequestBody EmsUserExamAnswerRaterConditionBo condition);

    /**
     * 查询作答记录复核人表列表
     * <AUTHOR>
     * @date 2023-09-14 17:25:14
     */
    @PostMapping("/ems/user/exam/answer/rater/list")
    public AjaxResult<List<EmsUserExamAnswerRaterVo>> getEmsUserExamAnswerRaterListByCondition(@RequestBody EmsUserExamAnswerRaterConditionBo condition);


    /**
     * 新增作答记录复核人表
     * <AUTHOR>
     * @date 2023-09-14 17:25:14
     */
    @PostMapping("/ems/user/exam/answer/rater/add")
    public AjaxResult addEmsUserExamAnswerRater(@Validated @RequestBody EmsUserExamAnswerRaterBo emsUserExamAnswerRaterBo);

    /**
     * 修改作答记录复核人表
     * @param emsUserExamAnswerRaterBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-14 17:25:14
     */
    @PostMapping("/ems/user/exam/answer/rater/update")
    public AjaxResult updateEmsUserExamAnswerRater(@Validated @RequestBody EmsUserExamAnswerRaterBo emsUserExamAnswerRaterBo);

    /**
     * 查询作答记录复核人表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-14 17:25:14
     */
    @GetMapping("/ems/user/exam/answer/rater/detail")
    public AjaxResult<EmsUserExamAnswerRaterVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除作答记录复核人表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-14 17:25:14
     */
    @GetMapping("/ems/user/exam/answer/rater/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 批量添加时间复核人
     * @param emsUserExamAnswerRaterBos
     * @return
     */
    @PostMapping("/ems/user/exam/answer/addBatch")
    AjaxResult addEmsUserExamAnswerRaterBatch(@RequestBody List<EmsUserExamAnswerRaterBo> emsUserExamAnswerRaterBos);
}
