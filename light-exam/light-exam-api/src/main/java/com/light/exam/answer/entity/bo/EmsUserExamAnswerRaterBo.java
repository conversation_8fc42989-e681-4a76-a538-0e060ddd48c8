package com.light.exam.answer.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 作答记录复核人表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-14 17:25:14
 */
@Data
public class EmsUserExamAnswerRaterBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 作答ID
	 */
	@ApiModelProperty("作答ID")
	private Long answerId;

	/**
	 * 场次ID
	 */
	@ApiModelProperty("场次ID")
	private Long batchId;

	/**
	 * 场次名称
	 */
	@ApiModelProperty("场次名称")
	private String batchName;

	/**
	 * 考试ID
	 */
	@ApiModelProperty("考试ID")
	private Long examId;

	/**
	 * 考试名称
	 */
	@ApiModelProperty("考试名称")
	private String examName;

	/**
	 * 作答用户OID
	 */
	@ApiModelProperty("作答用户OID")
	private String userOid;

	/**
	 * 考试用时
	 */
	@ApiModelProperty("考试用时")
	private Long useTime;

	/**
	 * 复核人OID
	 */
	@ApiModelProperty("复核人OID")
	private String raterUserOid;

	/**
	 * 是否删除 0 否 1 是
	 */
	@ApiModelProperty("是否删除 0 否 1 是")
	private Integer isDelete;

	@ApiModelProperty("试卷ds")
	private List<String> answerCodes;

}
