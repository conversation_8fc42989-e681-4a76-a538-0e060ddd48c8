package com.light.exam.batch.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 平台考试场次信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-14 17:14:53
 */
@Data
public class EmsExamBatchConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 考试id
	 */
	@ApiModelProperty("考试id")
	private Long examId;

	/**
	 * 场次名称
	 */
	@ApiModelProperty("场次名称")
	private String batchName;

	/**
	 * 场次开始时间
	 */
	@ApiModelProperty("场次开始时间")
	private Date batchStartTime;

	/**
	 * 场次结束时间
	 */
	@ApiModelProperty("场次结束时间")
	private Date batchEndTime;

	/**
	 * 场次地点
	 */
	@ApiModelProperty("场次地点")
	private String batchLocation;

	/**
	 * 场次考试结束方式，1：答题时长，2：答题终止时间
	 */
	@ApiModelProperty("场次考试结束方式，1：答题时长，2：答题终止时间")
	private Integer examTimeWay;

	/**
	 * 答题时长，单位秒
	 */
	@ApiModelProperty("答题时长，单位秒")
	private Long examTime;

	/**
	 * 答题终止时间
	 */
	@ApiModelProperty("答题终止时间")
	private Date examFinishTime;

	/**
	 * 是否有场次考试码
	 */
	@ApiModelProperty("是否有场次考试码")
	private Integer isBatchCode;

	/**
	 * 场次考试码
	 */
	@ApiModelProperty("场次考试码")
	private String batchCode;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;


}
