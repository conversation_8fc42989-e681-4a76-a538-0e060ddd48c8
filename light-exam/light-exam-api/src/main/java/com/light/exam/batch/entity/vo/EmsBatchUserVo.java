package com.light.exam.batch.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 考试场次用户
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-14 17:16:03
 */
@Data
public class EmsBatchUserVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 考试id
     */
    @ApiModelProperty("考试id")
    private Long examId;

    /**
     * 考试场次id
     */
    @ApiModelProperty("考试场次id")
    private Long batchId;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String userOid;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 考试名称
     */
    @ApiModelProperty("考试名称")
    private String examName;

    /**
     * 账号
     */
    @ApiModelProperty("账号")
    private String accountName;

    /**
     * 报名信息
     */
    @ApiModelProperty("报名信息")
    private String infoContent;

    /**
     * 场次名称
     */
    @ApiModelProperty("场次名称")
    private String batchName;

    @ApiModelProperty("是否考试，0：否，1：是")
    private Integer isExam;

    /**
     * 方便steam流存入自身
     * */
    public EmsBatchUserVo returnOwn() {
        return this;
    }

}
