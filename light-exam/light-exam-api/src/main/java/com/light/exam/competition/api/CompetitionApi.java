package com.light.exam.competition.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.exam.competition.entity.bo.CompetitionBo;
import com.light.exam.competition.entity.bo.CompetitionConditionBo;
import com.light.exam.competition.entity.vo.CompetitionVo;
import com.light.exam.stage.entity.vo.ComStageUserResultVo;
import com.light.exam.user.entity.vo.ComUserVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

public interface CompetitionApi {

    /**
     * 查询竞赛列表
     * <AUTHOR>
     * @date 2022-03-28 16:06:40
     */
    @PostMapping("/competition/list")
    public AjaxResult<PageInfo<CompetitionVo>> getCompetitionListByCondition(@RequestBody CompetitionConditionBo condition);

    /**
     * 查询竞赛列表
     * <AUTHOR>
     * @date 2022-03-28 16:06:40
     */
    @PostMapping("/competition/all")
    public AjaxResult<List<CompetitionVo>> getCompetitionAllByCondition(@RequestBody CompetitionConditionBo condition);

    /**
     * 新增竞赛
     * <AUTHOR>
     * @date 2022-03-28 16:06:40
     */
    @PostMapping("/competition/add")
    public AjaxResult addCompetition(@Validated @RequestBody CompetitionBo competitionBo);

    /**
     * 修改竞赛
     * @param competitionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-28 16:06:40
     */
    @PostMapping("/competition/update")
    public AjaxResult updateCompetition(@Validated @RequestBody CompetitionBo competitionBo);

    /**
     * 查询竞赛详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-28 16:06:40
     */
    @GetMapping("/competition/detail")
    public AjaxResult<CompetitionVo> getDetail(@RequestParam("id") Long id);

    /**
     * 删除竞赛
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-28 16:06:40
     */
    @GetMapping("/competition/delete")
    public AjaxResult delete(@RequestParam("id") Long id);

    /**
     * 竞赛排行查询
     * @param id
     * @return
     */
    @GetMapping("/competition/rank")
    public List<ComUserVo> rank(@RequestParam("id") Long id);

    /**
     * 清空竞赛
     */
    @GetMapping("/competition/clear")
    public AjaxResult clear(@RequestParam("id") Long id);

    /**
     * 复制竞赛
     */
    @GetMapping("/competition/copy")
    public AjaxResult copy(@RequestParam("id") Long id);
}
