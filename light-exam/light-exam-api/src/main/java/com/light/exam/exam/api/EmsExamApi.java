package com.light.exam.exam.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.exam.answer.entity.vo.EmsUserExamAnswerVo;
import com.light.exam.exam.entity.bo.EmsExamBo;
import com.light.exam.exam.entity.bo.EmsExamConditionBo;
import com.light.exam.exam.entity.bo.EmsExamStartBo;
import com.light.exam.exam.entity.vo.EmsExamVo;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/1716:18
 */
public interface EmsExamApi {

    /**
     * 查询平台考试表列表
     * <AUTHOR>
     * @date 2022-05-17 14:29:58
     * @return
     */
    @PostMapping("/exam/list")
    public AjaxResult<List<EmsExamVo>> getEmsExamListByCondition(@RequestBody EmsExamConditionBo condition);

    /**
     * 查询平台考试表列表
     * <AUTHOR>
     * @date 2022-05-17 14:29:58
     */
    @PostMapping("/exam/pageList")
    public AjaxResult<PageInfo<EmsExamVo>> getEmsExamPageListByCondition(@RequestBody EmsExamConditionBo condition);



    /**
     * 新增平台考试表
     * <AUTHOR>
     * @date 2022-05-17 14:29:58
     */
    @PostMapping("/exam/add")
    public AjaxResult addEmsExam(@RequestBody EmsExamBo emsExamBo);

    /**
     * 修改平台考试表
     * @param emsExamBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 14:29:58
     */
    @PostMapping("/exam/update")
    public AjaxResult updateEmsExam(@RequestBody EmsExamBo emsExamBo);


    /**
     * 更新参与方式
     *
     * @param emsExamBo ems考试
     * @return {@link AjaxResult}
     */
    @PostMapping("/exam/updateJoinType")
    public AjaxResult updateJoinType(@RequestBody EmsExamBo emsExamBo);


    /**
     * 查询平台考试表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 14:29:58
     */
    @GetMapping("/exam/detail")
    public AjaxResult<EmsExamVo> getDetail(@RequestParam("id") Long id);


    /**
     * 删除平台考试表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 14:29:58
     */
    @GetMapping("/exam/delete")
    public AjaxResult delete(@RequestParam("id") Long id);

    /**
     * 更新考试状态
     * @param id
     * @param state
     * @return
     */
    @GetMapping("/exam/state")
    AjaxResult state(@RequestParam("id") Long id, @RequestParam("state") Integer state);

    /**
     *  更新考试模板
     *
     * @param id         考试ID the exam id
     * @param templateId 模板id the paper template id
     * @return {@link AjaxResult}
     */
    @PostMapping("/exam/updatePaperTempByExamId/{examId}/{templateId}")
    AjaxResult updatePaperTemp(@PathVariable("examId") Long id, @PathVariable("templateId") Long templateId);


    /**
     * 根据考试OID 获取考试信息
     *
     * @param examOid 考试oid
     * @return {@link AjaxResult}<{@link EmsExamVo}>
     */
    @GetMapping("/exam/detailByOid")
    AjaxResult<EmsExamVo> getDetailByOid(@RequestParam("examOid") String examOid);


    /**
     * 开始考试
     *
     * @param bo the ems exam start bo  开始考试参数
     * @return {@link AjaxResult}<{@link EmsUserExamAnswerVo}>
     */
    @PostMapping("/exam/startExamByExamKey")
    AjaxResult<EmsUserExamAnswerVo> startExamByExamKey(@RequestBody EmsExamStartBo bo);

    /**
     * 开始考试
     *
     * @param bo the ems exam start bo  开始考试参数
     * @return {@link AjaxResult}<{@link EmsUserExamAnswerVo}>
     */
    @PostMapping("/exam/startExamByExamId")
    AjaxResult<EmsUserExamAnswerVo> startExamByExamId(@RequestBody EmsExamStartBo bo);


    /**
     * 停止考试
     *
     * @param id 考试id
     * @return {@link AjaxResult}
     */
    @PostMapping("/exam/stop/{id}")
    AjaxResult stop(@PathVariable("id") Long id);

    /**
     * 根据用户考试OId 获取剩余考试次数
     *
     * @param examOid 考试oid
     * @param userOid 用户oid
     * @return {@link AjaxResult}
     */
    @GetMapping("/exam/getAnswerCountInfo")
    AjaxResult getAnswerCountInfo(@RequestParam("examOid") String examOid, @RequestParam("userOid")String userOid);

    /**
     * 修改考试多场次状态
     * @param id
     * @param isMultiBatch
     * @return
     */
    @GetMapping("/exam/multi")
    AjaxResult multi(@RequestParam("id") Long id, @RequestParam("isMultiBatch") Integer isMultiBatch);

    /**
     * 根据考试ID 获取数据
     * @param examIdList the exam id list
     * @return {@link AjaxResult}<{@link List}<{@link EmsExamVo}>>
     */
    @PostMapping("/exam/getByIdList")
    AjaxResult<List<EmsExamVo>> getByIdList(@RequestBody List<Long> examIdList);

    /**
     * 获取用户考试分页列表
     *
     * @param conditionBo
     * @return {@link AjaxResult}
     */
    @PostMapping("/exam/getUserExamPageList")
    AjaxResult<PageInfo<EmsExamVo>> getUserExamPageList(@RequestBody EmsExamConditionBo conditionBo);
}
