package com.light.exam.exam.api;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.exam.exam.entity.bo.EmsExamSettingBo;
import com.light.exam.exam.entity.bo.EmsExamSettingConditionBo;
import com.light.exam.exam.entity.vo.EmsExamSettingVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/2415:05
 */
public interface EmsExamSettingApi {

    /**
     * 新增平台考试设置表
     * <AUTHOR>
     * @date 2022-05-17 15:15:55
     */
    @PostMapping("/exam/setting/add")
    public AjaxResult addEmsExamSetting(@Validated @RequestBody EmsExamSettingBo emsExamSettingBo);

    /**
     * 修改平台考试设置表
     * @param emsExamSettingBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:15:55
     */
    @PostMapping("/exam/setting/update")
    public AjaxResult updateEmsExamSetting(@Validated @RequestBody EmsExamSettingBo emsExamSettingBo);

    /**
     * 查询平台考试设置表详情
     * @param examId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:15:55
     */
    @GetMapping("/exam/setting/detail")
    public AjaxResult getDetail(@RequestParam("examId") Long examId);


    /**
     * 保存ems考试设置
     *
     * @param emsExamSettingBo ems考试设置 BO
     * @return {@link AjaxResult}
     */
    @PostMapping("/exam/setting/saveByExamId")
    AjaxResult saveEmsExamSettingByExamId(@RequestBody EmsExamSettingBo emsExamSettingBo);

    /**
     * 根据考试id获取考试设置
     * @param examId
     * @return
     */
    @GetMapping("/exam/setting/getById")
    AjaxResult<EmsExamSettingVo> getEmsExamSettingByExamId(@NotNull(message = "请选择考试") @RequestParam("examId") Long examId);
}
