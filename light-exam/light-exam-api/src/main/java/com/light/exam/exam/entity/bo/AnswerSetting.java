package com.light.exam.exam.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *  答题设置
 *
 * <AUTHOR>
 * @since 2023/2/6
 */
@Data
public class AnswerSetting implements Serializable {

    /**
     * 答题时长（分钟）
     */
    @ApiModelProperty("答题时长（分钟）")
    private Long answerDuration;

    /**
     * 考试切屏次数限制，空和0为不限
     */
    @ApiModelProperty("考试切屏次数限制，0为不限")
    private Integer cutScreenNumLimit;
    /**
     * 是否限制答题次数 0 否 1 是
     */
    @ApiModelProperty("是否限制答题次数 0 否 1 是")
    private Integer isRestrictNum;

    /**
     * 限制次数方式 1 仅可 2 每天
     */
    @ApiModelProperty("限制次数方式 1 仅可 2 每天 3 每周 4 每月")
    private Integer restrictType;

    /**
     * 答题次数
     */
    @ApiModelProperty("答题次数")
    private Integer answerNum;

    /**
     * 是否展示剩余次数  0 否  1 是
     */
    @ApiModelProperty("是否展示剩余次数  0 否  1 是")
    private Integer isShowRemainNum;

    /**
     * 答题方式 1 整卷 2 逐题
     */
    @ApiModelProperty("答题方式 1 整卷 2 逐题")
    private Integer answerType;

    /**
     * 单题答题时长（秒）
     */
    @ApiModelProperty("单题答题时长（秒）")
    private Long itemAnswerDuration;

    /**
     * 是否开启闯关 0 否  1是
     */
    @ApiModelProperty("是否开启闯关 0 否  1是")
    private Integer isOpenLevel;

    /**
     * 每关多少题
     */
    @ApiModelProperty("每关多少题")
    private Integer levelItemNum;

    /**
     *  每关错误多少题闯关失败
     */
    @ApiModelProperty("每关错误多少题闯关失败")
    private Integer wrongLevelItemNum;
}
