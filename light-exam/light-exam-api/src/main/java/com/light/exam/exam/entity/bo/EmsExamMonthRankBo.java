package com.light.exam.exam.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 考试月排名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-13 13:44:42
 */
@Data
public class EmsExamMonthRankBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Long id;

	/**
	 * 月份 yyy-MM
	 */
	@ApiModelProperty("月份 yyy-MM")
	private String monthDate;

	/**
	 * 用户OID
	 */
	@ApiModelProperty("用户OID")
	private String userOid;

    /**
     * 城市ID
     */
    @ApiModelProperty("城市ID")
    private Long cityId;

	/**
	 * 考试ID
	 */
	@ApiModelProperty("考试ID")
	private Long examId;

	/**
	 * 作答ID
	 */
	@ApiModelProperty("作答ID")
	private Long answerId;

	/**
	 * 用时
	 */
	@ApiModelProperty("用时")
	private Long useTime;

	/**
	 * 分数
	 */
	@ApiModelProperty("分数")
	private Double score;

	/**
	 * 答对题数
	 */
	@ApiModelProperty("答对题数")
	private Long rightItemCount;

	/**
	 *
	 */
	@ApiModelProperty("")
	private Integer isDelete;



}
