package com.light.exam.exam.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 平台考试表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 14:29:58
 */
@Data
public class EmsExamStartBo implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * 考试ID
     */
    private Long examId;

    /**
     * 考试OID
     */
	private String examOid;

    /**
     * 考试用户
     */
    private String userOid;

    /**
     * 考试场次CODE码
     */
    private String batchCode;
}
