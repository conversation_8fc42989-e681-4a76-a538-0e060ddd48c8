package com.light.exam.exam.entity.vo;

import com.light.exam.exam.entity.bo.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 平台考试设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 15:15:55
 */
@Data
public class EmsExamSettingVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 考试ID
     */
    @ApiModelProperty("考试ID")
    private Long examId;

    /**
     * 是否设置完成 0 否 1 是
     */
    @ApiModelProperty("是否设置完成 0 否 1 是")
    private Integer isSetupFinish;


    /**
     * 显示设置
     */
    @ApiModelProperty("显示设置")
    private ShowSetting showSetting;

    /**
     * 用户完善设置
     */
    @ApiModelProperty("用户完善设置")
    private UserSetting userSetting;

    /**
     * 答题设置
     */
    @ApiModelProperty("答题设置")
    private AnswerSetting answerSetting;

    /**
     * 学习设置
     */
    @ApiModelProperty("学习设置")
    private StudySetting studySetting;

    /**
     * 成绩设置
     */
    @ApiModelProperty("成绩设置")
    private ScoreSetting scoreSetting;

    /**
     * 排名设置
     */
    @ApiModelProperty("排名设置")
    private RankSetting rankSetting;

    /**
     * 复核设置
     */
    @ApiModelProperty("复核设置")
    private RaterSetting raterSetting;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
}
