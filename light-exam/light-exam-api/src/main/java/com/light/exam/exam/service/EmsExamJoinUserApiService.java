package com.light.exam.exam.service;


import com.light.exam.exam.api.EmsExamJoinUserApi;
import com.light.exam.exam.entity.bo.EmsExamJoinUserBo;
import com.light.exam.exam.entity.bo.EmsExamJoinUserConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.exam.exam.entity.vo.EmsExamJoinUserVo;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 平台考试指定用户表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-07 11:00:20
 */
@FeignClient(contextId = "emsExamJoinUserApiService", value= "light-exam", configuration = FeignClientInterceptor.class, fallbackFactory = EmsExamJoinUserApiService.EmsExamJoinUserApiFallbackFactory.class)
@Component
public interface EmsExamJoinUserApiService extends EmsExamJoinUserApi {

    @Component
    class EmsExamJoinUserApiFallbackFactory implements FallbackFactory<EmsExamJoinUserApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(EmsExamJoinUserApiFallbackFactory.class);
        @Override
        public EmsExamJoinUserApiService create(Throwable cause) {
            EmsExamJoinUserApiFallbackFactory.LOGGER.error("平台考试指定参与用户服务调用失败:{}", cause.getMessage());
            return new EmsExamJoinUserApiService() {

                @Override
                public AjaxResult getEmsExamJoinUserPageListByCondition(EmsExamJoinUserConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                @Override
                public AjaxResult getEmsExamJoinUserListByCondition(EmsExamJoinUserConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                @Override
                public AjaxResult addEmsExamJoinUser(EmsExamJoinUserBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult updateEmsExamJoinUser(EmsExamJoinUserBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<EmsExamJoinUserVo> getByExamIdAndUserOid(Long examId, String userOid) {
                    return AjaxResult.fail("数据获取");
                }
            };
        }
    }
}
