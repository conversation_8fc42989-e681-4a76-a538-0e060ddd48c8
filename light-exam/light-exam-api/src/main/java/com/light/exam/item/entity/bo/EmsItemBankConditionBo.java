package com.light.exam.item.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 平台题库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-28 16:10:03
 */
@Data
public class EmsItemBankConditionBo extends PageLimitBo{

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 题库名称
	 */
	@ApiModelProperty("题库名称")
	private String bankName;

	/**
	 * 标签
	 */
	@ApiModelProperty("标签")
	private String tagIds;


    /**
     * 是否公开 0 否 1是
     */
    private Integer isPublic;


	/**
	 * 是否启用，0：否，1：是
	 */
	@ApiModelProperty("是否启用，0：否，1：是")
	private Integer state;

	/**
	 * 是否删除，0；否，1：是
	 */
	@ApiModelProperty("是否删除，0；否，1：是")
	private Integer isDelete;

	/**
	 * 组织机构id
	 */
	@ApiModelProperty("组织机构id")
	private Long organizationId;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

}
