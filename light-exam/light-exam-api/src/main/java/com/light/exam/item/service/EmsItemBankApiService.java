package com.light.exam.item.service;

import com.github.pagehelper.PageInfo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.exam.item.api.EmsItemBankApi;
import com.light.exam.item.entity.bo.EmsItemBankBo;
import com.light.exam.item.entity.bo.EmsItemBankConditionBo;
import com.light.exam.item.entity.vo.EmsItemBankVo;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(contextId = "emsItemBankApiService", value= ServiceNameConstants.LIGHT_EXAM, configuration = FeignClientInterceptor.class, fallbackFactory = EmsItemBankApiService.EmsItemBankApiFallbackFactory.class)
@Component
public interface EmsItemBankApiService extends EmsItemBankApi {

    @Component
    class EmsItemBankApiFallbackFactory implements FallbackFactory<EmsItemBankApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(EmsItemBankApiService.EmsItemBankApiFallbackFactory.class);

        @Override
        public EmsItemBankApiService create(Throwable cause) {
            EmsItemBankApiService.EmsItemBankApiFallbackFactory.LOGGER.error("考试服务调用失败:{}", cause.getMessage());
            return new EmsItemBankApiService() {
                @Override
                public AjaxResult getEmsItemBankListByCondition(EmsItemBankConditionBo condition) {
                    return AjaxResult.fail("查询题库列表失败");
                }

                @Override
                public AjaxResult addEmsItemBank(EmsItemBankBo emsItemBankBo) {
                    return AjaxResult.fail("新增题库失败");
                }

                @Override
                public AjaxResult updateEmsItemBank(EmsItemBankBo emsItemBankBo) {
                    return AjaxResult.fail("更新题库失败");
                }

                @Override
                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("获取题库详情失败");
                }

                @Override
                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除题库失败");
                }

                @Override
                public AjaxResult<PageInfo<EmsItemBankVo>> getEmsItemBankPageListByCondition(EmsItemBankConditionBo condition) {
                    return AjaxResult.fail("查询失败");
                }

                @Override
                public AjaxResult getChooseBankList(EmsItemBankConditionBo condition) {
                    return AjaxResult.fail("数据获取失败");
                }
            };
        }
    }
}
