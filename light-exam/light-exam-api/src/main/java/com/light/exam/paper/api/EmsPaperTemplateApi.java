package com.light.exam.paper.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.exam.paper.entity.bo.EmsPaperTemplateBo;
import com.light.exam.paper.entity.bo.EmsPaperTemplateConditionBo;
import com.light.exam.paper.entity.vo.EmsPaperTemplateVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/2419:50
 */
public interface EmsPaperTemplateApi {

    /**
     * 查询平台试卷模板表列表
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     * @return
     */
    @PostMapping("/paper/template/page-list")
    public AjaxResult<PageInfo<EmsPaperTemplateVo>> getEmsPaperTemplatePageListByCondition(@RequestBody EmsPaperTemplateConditionBo condition);

    /**
     * 查询平台试卷模板表列表
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     * @return
     */
    @PostMapping("/paper/template/list")
    public AjaxResult<List<EmsPaperTemplateVo>> getEmsPaperTemplateListByCondition(@RequestBody EmsPaperTemplateConditionBo condition);


    /**
     * 新增平台试卷模板表
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     */
    @PostMapping("/paper/template/add")
    public AjaxResult addEmsPaperTemplate(@Validated @RequestBody EmsPaperTemplateBo emsPaperTemplateBo);

    /**
     * 修改平台试卷模板表
     * @param emsPaperTemplateBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     */
    @PostMapping("/paper/template/update")
    public AjaxResult updateEmsPaperTemplate(@Validated @RequestBody EmsPaperTemplateBo emsPaperTemplateBo) ;

    /**
     * 查询平台试卷模板表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     */
    @GetMapping("/paper/template/detail")
    public AjaxResult getDetail(@RequestParam("id") Long id) ;

    /**
     * 删除平台试卷模板表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     */
    @GetMapping("/paper/template/delete")
    public AjaxResult delete(@RequestParam("id") Long id) ;


    /**
     * 更新分数配置
     *
     * @param bo
     * @return {@link AjaxResult}
     */
    @PostMapping("/paper/template/updateScoreConfig")
    AjaxResult updateScoreConfig(@RequestBody EmsPaperTemplateBo bo);
}
