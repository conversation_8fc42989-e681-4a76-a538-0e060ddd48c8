package com.light.exam.paper.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 平台试卷表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 15:16:39
 */
@Data
public class EmsPaperConditionBo extends PageLimitBo{

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 试题数量
	 */
	@ApiModelProperty("试题数量")
	private Integer itemCount;

	/**
	 * 试题等级
	 */
	@ApiModelProperty("试题等级")
	private Integer level;

	/**
	 * 试题总分
	 */
	@ApiModelProperty("试题总分")
	private Double score;

	/**
	 * 是否有主观题 - 0 否  1 是
	 */
	@ApiModelProperty("是否有主观题 - 0 否  1 是")
	private Integer isHasSubjective;

	/**
	 * 试卷内容
	 */
	@ApiModelProperty("试卷内容")
	private String paperContent;

	/**
	 * 考试ID
	 */
	@ApiModelProperty("考试ID")
	private Long examId;

	/**
	 * 试卷模板ID
	 */
	@ApiModelProperty("试卷模板ID")
	private Long paperTemplateId;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

}
