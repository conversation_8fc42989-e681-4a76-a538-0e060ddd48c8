package com.light.exam.relation.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 平台考试关联表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-23 09:31:15
 */
@Data
public class EmsRelationBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	@ApiModelProperty("ID")
	private Long id;
	/**
	 * 考试ID
	 */
	@ApiModelProperty("考试ID")
	private Long examId;
	/**
	 * 目标ID
	 */
	@ApiModelProperty("目标ID")
	private Long objectId;
	/**
	 * 类型（1、课程，2、测评）
	 */
	@ApiModelProperty("类型（1、课程，2、测评）")
	private Integer type;
	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Integer sequence;
	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

}
