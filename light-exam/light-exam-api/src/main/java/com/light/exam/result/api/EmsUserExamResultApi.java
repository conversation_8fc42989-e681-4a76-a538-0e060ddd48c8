package com.light.exam.result.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.exam.result.entity.bo.EmsUserExamResultConditionBo;
import com.light.exam.result.entity.vo.EmsUserExamResultVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/1718:33
 */
public interface EmsUserExamResultApi {

    /**
     * 查询平台用户考试结果表列表
     * <AUTHOR>
     * @date 2022-05-17 16:07:16
     */
    @PostMapping("/user/exam/result/list")
    public AjaxResult<PageInfo<EmsUserExamResultVo>> getEmsUserExamResultListByCondition(@RequestBody EmsUserExamResultConditionBo condition);

    /**
     * 查询平台用户考试结果表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 16:07:16
     */
    @GetMapping("/user/exam/result/detail")
    public AjaxResult getDetail(@RequestParam("id") Long id);

    /**
     * 删除平台用户考试结果表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 16:07:16
     */
    @GetMapping("/user/exam/result/delete")
    public AjaxResult delete(@RequestParam("id") Long id);
}
