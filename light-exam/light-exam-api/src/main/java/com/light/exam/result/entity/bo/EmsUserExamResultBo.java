package com.light.exam.result.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 平台用户考试结果表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 16:07:16
 */
@Data
public class EmsUserExamResultBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 平台用户考试结果表id
	 */
	@ApiModelProperty("平台用户考试结果表id")
	private Long emsUserExamResultId;
	
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 * 考试次数
	 */
	@ApiModelProperty("考试次数")
	private Integer examCount;
	/**
	 * 总分数
	 */
	@ApiModelProperty("总分数")
	private Double totalScore;
	/**
	 * 考试ID
	 */
	@ApiModelProperty("考试ID")
	private Long examId;

	/**
	 * 考试oid
	 */
	@ApiModelProperty("考试oid")
	private String examOid;
	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 考试累计总分
	 */
	@ApiModelProperty("考试累计总分")
	private Double accumulateScore;

	/**
	 * 考试累计使用时长
	 */
	@ApiModelProperty("考试累计使用时长")
	private Long accumulateUseTime;

	/**
	 * 最后一次考试的考试真实成绩
	 */
	@ApiModelProperty("最后一次考试的考试真实成绩")
	private Double score;

	/**
	 * 最后一次考试主观题得分
	 */
	@ApiModelProperty("最后一次考试主观题得分")
	private Double subjectiveScore;

	/**
	 * 最后一次考试客观题得分
	 */
	@ApiModelProperty("最后一次考试客观题得分")
	private Double objectiveScore;

	/**
	 * 首次成绩
	 */
	@ApiModelProperty("首次成绩")
	private Double firstScore;
	/**
	 * 是否通过
	 */
	@ApiModelProperty("是否通过")
	private Long pass;
	/**
	 * 最后一次考试试卷
	 */
	@ApiModelProperty("最后一次考试试卷")
	private Long paperId;
	/**
	 * 开始时间
	 */
	@ApiModelProperty("开始时间")
	private Date startTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty("结束时间")
	private Date endTime;
	/**
	 * 最后考试结果记录ID
	 */
	@ApiModelProperty("最后考试结果记录ID")
	private Long lastResultRecordId;
	/**
	 * 状态1、缺考，2、待评卷，3、已发布，4、待发布成绩
	 */
	@ApiModelProperty("状态1、缺考，2、待评卷，3、已发布，4、待发布成绩")
	private Long state;
	/**
	 * 考试使用时长
	 */
	@ApiModelProperty("考试使用时长")
	private Long useTime;
	/**
	 * 历史最高分
	 */
	@ApiModelProperty("历史最高分")
	private Double maxScore;
	/**
	 * 最高分数考试用时
	 */
	@ApiModelProperty("最高分数考试用时")
	private Long maxScoreUseTime;
	/**
	 * 最高分record记录Id
	 */
	@ApiModelProperty("最高分record记录Id")
	private Long maxScoreRecordId;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private String ticketImage;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

}
