package com.light.exam.user.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 竞赛用户结果
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-28 16:09:19
 */
@Data
public class ComUserResultBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 * 总分数
	 */
	@ApiModelProperty("总分数")
	private Double totalScore;
	/**
	 * 总用时(毫秒)
	 */
	@ApiModelProperty("总用时(毫秒)")
	private Long totalUseTime;
	/**
	 * 竞赛ID
	 */
	@ApiModelProperty("竞赛ID")
	private Long comId;
	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;
	/**
	 * 答题数
	 */
	@ApiModelProperty("答题数")
	private Long itemCount;
	/**
	 * 开始时间
	 */
	@ApiModelProperty("开始时间")
	private Date startTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty("结束时间")
	private Date endTime;
	/**
	 * 图片
	 */
	@ApiModelProperty("图片")
	private String ticketImage;

	/**
	 * 竞赛排名
	 */
	@ApiModelProperty("竞赛排名")
	private Integer comRank;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

}
