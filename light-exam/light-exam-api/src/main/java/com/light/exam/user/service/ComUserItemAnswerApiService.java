package com.light.exam.user.service;

import com.github.pagehelper.PageInfo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.exam.user.api.ComUserItemAnswerApi;
import com.light.exam.user.entity.bo.ComUserItemAnswerBo;
import com.light.exam.user.entity.bo.ComUserItemAnswerConditionBo;
import com.light.exam.user.entity.vo.ComUserItemAnswerVo;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

@FeignClient(contextId = "comUserItemAnswerApiService", value= ServiceNameConstants.LIGHT_EXAM, configuration = FeignClientInterceptor.class, fallbackFactory = ComUserItemAnswerApiService.ComUserItemAnswerApiFallbackFactory.class)
@Component
public interface ComUserItemAnswerApiService extends ComUserItemAnswerApi {

    @Component
    class ComUserItemAnswerApiFallbackFactory implements FallbackFactory<ComUserItemAnswerApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ComUserItemAnswerApiService.ComUserItemAnswerApiFallbackFactory.class);

        @Override
        public ComUserItemAnswerApiService create(Throwable cause) {
            ComUserItemAnswerApiService.ComUserItemAnswerApiFallbackFactory.LOGGER.error("考试服务调用失败:{}", cause.getMessage());
            return new ComUserItemAnswerApiService() {
                @Override
                public AjaxResult<PageInfo<ComUserItemAnswerVo>> getComUserItemAnswerListByCondition(ComUserItemAnswerConditionBo condition) {
                    return AjaxResult.fail("查询竞赛用户题目答案分页列表失败");
                }

                @Override
                public AjaxResult<List<ComUserItemAnswerVo>> getComUserItemAnswerAllListByCondition(ComUserItemAnswerConditionBo condition) {
                    return AjaxResult.fail("查询竞赛用户题目答案列表失败");
                }

                @Override
                public AjaxResult addComUserItemAnswer(ComUserItemAnswerBo comUserItemAnswerBo) {
                    return AjaxResult.fail("新增竞赛用户题目答案失败");
                }

                @Override
                public AjaxResult updateComUserItemAnswer(ComUserItemAnswerBo comUserItemAnswerBo) {
                    return AjaxResult.fail("更新竞赛用户题目答案失败");
                }

                @Override
                public AjaxResult editComUserItemAnswer(ComUserItemAnswerBo comUserItemAnswerBo) {
                    return AjaxResult.fail("修改竞赛用户题目答案失败");
                }

                @Override
                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("获取竞赛用户题目答案详情失败");
                }

                @Override
                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除竞赛用户题目答案失败");
                }

                @Override
                public AjaxResult updateComUserItemAnswerState(ComUserItemAnswerBo comUserItemAnswerBo) {
                    return AjaxResult.fail("更新答题权失败");
                }

                @Override
                public AjaxResult addComUserItemAnswerBatch(ComUserItemAnswerBo comUserItemAnswerBo) {
                    return AjaxResult.fail("批量新增竞赛用户题目失败");
                }

                @Override
                public AjaxResult updateComUserItemAnswerBatch(ComUserItemAnswerBo comUserItemAnswerBo) {
                    return AjaxResult.fail("批量更新答题结果失败");
                }

                @Override
                public AjaxResult updateComUserItemAnswerSubmitTime(Long id) {
                    return AjaxResult.fail("更新提交时间失败");
                }

                @Override
                public AjaxResult<List<ComUserItemAnswerVo>> updateComUserItemAnswerNoAnswer(ComUserItemAnswerConditionBo condition) {
                    return AjaxResult.fail("批量更新没有提交答案的答题结果失败");
                }
            };
        }
    }
}
