package com.light.exam.batch.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 平台考试场次信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-14 17:14:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("light_ems_exam_batch")
public class EmsExamBatchDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 考试id
	 */
	@TableField("exam_id")
	private Long examId;

	/**
	 * 场次名称
	 */
	@TableField("batch_name")
	private String batchName;

	/**
	 * 场次开始时间
	 */
	@TableField("batch_start_time")
	private Date batchStartTime;

	/**
	 * 场次结束时间
	 */
	@TableField("batch_end_time")
	private Date batchEndTime;

	/**
	 * 场次地点
	 */
	@TableField("batch_location")
	private String batchLocation;

	/**
	 * 场次考试结束方式，1：答题时长，2：答题终止时间
	 */
	@TableField("exam_time_way")
	private Integer examTimeWay;

	/**
	 * 答题时长，单位分钟
	 */
	@TableField("exam_time")
	private Long examTime;

	/**
	 * 答题终止时间
	 */
	@TableField("exam_finish_time")
	private Date examFinishTime;

	/**
	 * 是否有场次考试码
	 */
	@TableField("is_batch_code")
	private Integer isBatchCode;

	/**
	 * 场次考试码
	 */
	@TableField("batch_code")
	private String batchCode;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
