package com.light.exam.exam.controller;

import com.light.exam.exam.api.EmsExamInfoApi;
import com.light.exam.exam.entity.dto.EmsExamInfoDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.exam.exam.entity.bo.EmsExamInfoConditionBo;
import com.light.exam.exam.entity.bo.EmsExamInfoBo;
import com.light.exam.exam.entity.vo.EmsExamInfoVo;
import com.light.exam.exam.service.IEmsExamInfoService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 平台考试表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-10 10:25:11
 */
@RestController
@Validated
public class EmsExamInfoController implements EmsExamInfoApi{
	
    @Autowired
    private IEmsExamInfoService emsExamInfoService;

    /**
     * 查询平台考试表分页列表
     * <AUTHOR>
     * @date 2023-02-10 10:25:11
     */
    @Override
    public AjaxResult<PageInfo<EmsExamInfoVo>> getEmsExamInfoPageListByCondition(@RequestBody EmsExamInfoConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<EmsExamInfoVo> pageInfo = new PageInfo<>(emsExamInfoService.getEmsExamInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询平台考试表列表
	 * <AUTHOR>
	 * @date 2023-02-10 10:25:11
	 */
	@Override
	public AjaxResult<List<EmsExamInfoVo>> getEmsExamInfoListByCondition(@RequestBody EmsExamInfoConditionBo condition){
		List<EmsExamInfoVo> list = emsExamInfoService.getEmsExamInfoListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增平台考试表
     * <AUTHOR>
     * @date 2023-02-10 10:25:11
     */
	@Override
    public AjaxResult addEmsExamInfo(@Validated @RequestBody EmsExamInfoBo emsExamInfoBo){
		return emsExamInfoService.addEmsExamInfo(emsExamInfoBo);
    }

    /**
	 * 修改平台考试表
	 * @param emsExamInfoBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-10 10:25:11
	 */
	@Override
	public AjaxResult updateEmsExamInfo(@Validated @RequestBody EmsExamInfoBo emsExamInfoBo) {
		if(null == emsExamInfoBo.getId()) {
			return AjaxResult.fail("平台考试表id不能为空");
		}
		return emsExamInfoService.updateEmsExamInfo(emsExamInfoBo);
	}

	/**
	 * 查询平台考试表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-10 10:25:11
	 */
	@Override
	public AjaxResult<EmsExamInfoVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("平台考试表id不能为空");
		}
		EmsExamInfoVo vo = emsExamInfoService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除平台考试表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-10 10:25:11
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		EmsExamInfoDto emsExamInfoDto = new EmsExamInfoDto();
		emsExamInfoDto.setId(id);
		emsExamInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(emsExamInfoService.updateById(emsExamInfoDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
