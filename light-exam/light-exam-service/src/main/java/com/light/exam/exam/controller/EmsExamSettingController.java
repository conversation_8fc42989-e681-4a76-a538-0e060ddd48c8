package com.light.exam.exam.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.core.entity.AjaxResult;
import com.light.exam.exam.api.EmsExamSettingApi;
import com.light.exam.exam.entity.bo.*;
import com.light.exam.exam.entity.dto.EmsExamSettingDto;
import com.light.exam.exam.entity.vo.EmsExamSettingVo;
import com.light.exam.exam.service.IEmsExamSettingService;
import com.light.feign.annotation.FeignValidatorAnnotation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 平台考试设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 15:15:55
 */
@RestController
@Validated
public class EmsExamSettingController implements EmsExamSettingApi {

    @Autowired
    private IEmsExamSettingService emsExamSettingService;

    /**
     * 新增平台考试设置表
     * <AUTHOR>
     * @date 2022-05-17 15:15:55
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult addEmsExamSetting(@Validated @RequestBody EmsExamSettingBo emsExamSettingBo){
		return emsExamSettingService.addEmsExamSetting(emsExamSettingBo);
    }

    /**
	 * 修改平台考试设置表
	 * @param emsExamSettingBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:15:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateEmsExamSetting(@Validated @RequestBody EmsExamSettingBo emsExamSettingBo) {
		if(null == emsExamSettingBo.getId()) {
			return AjaxResult.fail("平台考试设置表id不能为空");
		}
		return emsExamSettingService.updateEmsExamSetting(emsExamSettingBo);
	}

	/**
	 * 查询平台考试设置表详情
	 * @param examId
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:15:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult getDetail(@RequestParam("examId") Long examId) {
		return AjaxResult.success( emsExamSettingService.getDetail(examId));
	}


    /**
     * 保存ems考试设置
     *
     * @param emsExamSettingBo ems考试设置
     * @return {@link AjaxResult}
     */
    @Override
    public AjaxResult saveEmsExamSettingByExamId(@RequestBody EmsExamSettingBo emsExamSettingBo) {
        // 根据考试获取 设置信息
        final Long examId = emsExamSettingBo.getExamId();
        EmsExamSettingDto examSettingDto = this.emsExamSettingService.getByExamId(examId);
        if(examSettingDto == null){
            examSettingDto = BeanUtil.toBean(emsExamSettingBo, EmsExamSettingDto.class);
            this.fillSetting(examSettingDto, emsExamSettingBo);
            examSettingDto.setExamId(examId);
        }else{
            final Long id = examSettingDto.getId();
            this.fillSetting(examSettingDto, emsExamSettingBo);
            examSettingDto.setId(id);
            examSettingDto.setExamId(examSettingDto.getExamId());
            examSettingDto.setIsSetupFinish(emsExamSettingBo.getIsSetupFinish());
        }
        this.emsExamSettingService.saveOrUpdate(examSettingDto);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult<EmsExamSettingVo> getEmsExamSettingByExamId(@NotNull(message = "请选择考试") Long examId) {
        //根据考试id查询数据
        return AjaxResult.success(emsExamSettingService.getDetail(examId));
    }

    /**
     * 填充考试设置
     *
     * @param examSettingDto   考试设置dto
     * @param emsExamSettingBo ems考试设置波
     */
    private void fillSetting(EmsExamSettingDto examSettingDto, EmsExamSettingBo emsExamSettingBo ){
        // 显示设置
        if (emsExamSettingBo.getShowSetting() != null) {
            examSettingDto.setShowSetting(JSON.toJSONString(emsExamSettingBo.getShowSetting()));
        }

        // 用户信息设置
        if (emsExamSettingBo.getUserSetting() != null) {
            examSettingDto.setUserSetting(JSON.toJSONString(emsExamSettingBo.getUserSetting()));
        }

        // 答题设置
        if (emsExamSettingBo.getAnswerSetting() != null) {
            examSettingDto.setAnswerSetting(JSON.toJSONString(emsExamSettingBo.getAnswerSetting()));
        }

        // 学习设置
        if (emsExamSettingBo.getStudySetting() != null) {
            examSettingDto.setStudySetting(JSON.toJSONString(emsExamSettingBo.getStudySetting()));
        }

        // 成绩设置
        if (emsExamSettingBo.getScoreSetting() != null) {
            examSettingDto.setScoreSetting(JSON.toJSONString(emsExamSettingBo.getScoreSetting()));
        }

        // 排行设置
        if (emsExamSettingBo.getRankSetting() != null) {
            examSettingDto.setRankSetting(JSON.toJSONString(emsExamSettingBo.getRankSetting()));
        }

        //复核设置
        if(emsExamSettingBo.getRaterSetting() != null) {
            examSettingDto.setRaterSetting(JSON.toJSONString(emsExamSettingBo.getRaterSetting()));
        }

    }
}
