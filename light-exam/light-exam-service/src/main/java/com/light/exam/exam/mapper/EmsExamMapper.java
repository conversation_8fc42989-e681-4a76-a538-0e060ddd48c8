package com.light.exam.exam.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.exam.exam.entity.dto.EmsExamDto;
import com.light.exam.exam.entity.bo.EmsExamConditionBo;
import com.light.exam.exam.entity.vo.EmsExamVo;
import org.apache.ibatis.annotations.Param;

/**
 * 平台考试表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 14:29:58
 */
public interface EmsExamMapper extends BaseMapper<EmsExamDto> {

	List<EmsExamVo> getEmsExamListByCondition(EmsExamConditionBo condition);

    /**
     * 添加ems考试试卷数量
     *
     * @param examId 考试id
     * @param num    试卷数量
     * @return boolean
     */
    int addEmsExamNum(@Param("examId") Long examId,@Param("num") Integer num);

    /**
     * 根据考试ID集合获取数据
     *
     * @param examIdList the exam id list 考试ID 集合
     * @return {@link List}<{@link EmsExamVo}>
     */
    List<EmsExamVo> getVoListByIdList(@Param("examIdList") List<Long> examIdList);

    List<EmsExamVo> getUserExamList(EmsExamConditionBo conditionBo);
}
