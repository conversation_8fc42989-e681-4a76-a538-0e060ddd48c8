package com.light.exam.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.exam.exam.entity.dto.EmsExamJoinUserDto;
import com.light.exam.exam.entity.bo.EmsExamJoinUserConditionBo;
import com.light.exam.exam.entity.bo.EmsExamJoinUserBo;
import com.light.exam.exam.entity.vo.EmsExamJoinUserVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 平台考试指定用户表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-07 11:00:20
 */
public interface IEmsExamJoinUserService extends IService<EmsExamJoinUserDto> {

    List<EmsExamJoinUserVo> getEmsExamJoinUserListByCondition(EmsExamJoinUserConditionBo condition);

	AjaxResult addEmsExamJoinUser(EmsExamJoinUserBo emsExamJoinUserBo);

	AjaxResult updateEmsExamJoinUser(EmsExamJoinUserBo emsExamJoinUserBo);

	EmsExamJoinUserVo getDetail(Long id);

    /**
     * 通过考试id删除
     *
     * @param id id
     * @return boolean
     */
    boolean deleteByExamId(Long id);

    /**
     * 通过考试id和用户oid获取参与用户信息
     *
     * @param examId  考试id
     * @param userOid 用户oid
     * @return {@link EmsExamJoinUserVo}
     */
    EmsExamJoinUserVo getByExamIdAndUserOid(Long examId, String userOid);

    /**
     * 通过考试ID 获取参与用户
     *
     * @param examId 考试id
     * @return {@link List}<{@link EmsExamJoinUserVo}>
     */
    List<EmsExamJoinUserVo> getByExamId(Long examId);

}

