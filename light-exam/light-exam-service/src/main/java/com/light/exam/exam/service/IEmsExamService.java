package com.light.exam.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.exam.answer.entity.vo.EmsUserExamAnswerVo;
import com.light.exam.exam.entity.bo.EmsExamStartBo;
import com.light.exam.exam.entity.dto.EmsExamDto;
import com.light.exam.exam.entity.bo.EmsExamConditionBo;
import com.light.exam.exam.entity.bo.EmsExamBo;
import com.light.exam.exam.entity.vo.EmsExamVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 平台考试表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 14:29:58
 */
public interface IEmsExamService extends IService<EmsExamDto> {

    /**
     * 被条件ems考试名单
     *
     * @param condition 条件
     * @return {@link List}<{@link EmsExamVo}>
     */
    List<EmsExamVo> getEmsExamListByCondition(EmsExamConditionBo condition);

    /**
     * 添加ems考试
     *
     * @param emsExamBo ems考试
     * @return {@link AjaxResult}
     */
    AjaxResult addEmsExam(EmsExamBo emsExamBo);

    /**
     * 更新ems考试
     *
     * @param emsExamBo ems考试
     * @return {@link AjaxResult}
     */
    AjaxResult updateEmsExam(EmsExamBo emsExamBo);

    /**
     * 得到细节
     *
     * @param emsExamId ems考试id
     * @return {@link EmsExamVo}
     */
    EmsExamVo getDetail(Long emsExamId);

    /**
     * 更新考试参与设置
     *
     * @param emsExamBo ems考试
     * @return {@link AjaxResult}
     */
    AjaxResult updateExamJoin(EmsExamBo emsExamBo);

    /**
     * 添加ems考试数量
     *
     * @param examId 考试id
     * @param num    试卷数量
     * @return boolean
     */
    boolean addEmsExamNum(Long examId, Integer num);

    /**
     * 根据考试ID更新模板ID
     *
     * @param examId     考试id
     * @param templateId 模板id
     * @return boolean
     */
    boolean updatePaperTempByExamId(Long examId, Long templateId);

    /**
     * 根据考试OID获取详情
     *
     * @param examOid 考试oid
     * @return {@link EmsExamVo}
     */
    EmsExamVo getDetailByOid(String examOid);

    /**
     * 开始考试
     *
     * @param bo the ems exam start bo 考试OID
     */
    AjaxResult startExam(EmsExamStartBo bo);


    /**
     * 停止
     *
     * @param id id
     * @return {@link AjaxResult}
     */
    AjaxResult stop(Long id);

    /**
     * 开始考试
     *
     * @param bo the ems exam start bo 考试OID
     */
    AjaxResult<EmsUserExamAnswerVo> startExamById(EmsExamStartBo bo);

    /**
     * 根据id集合获取考试列表
     *
     * @param examIdList
     * @return {@link List}<{@link EmsExamVo}>
     */
    List<EmsExamVo> getVoListByIdList(List<Long> examIdList);

    /**
     *  获取用户考试列表
     * @param conditionBo
     * @return {@link List}<{@link EmsExamVo}>
     */
    List<EmsExamVo> getUserExamList(EmsExamConditionBo conditionBo);
}

