package com.light.exam.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.exam.exam.entity.dto.EmsExamJoinUserDto;
import com.light.exam.exam.entity.bo.EmsExamJoinUserConditionBo;
import com.light.exam.exam.entity.bo.EmsExamJoinUserBo;
import com.light.exam.exam.entity.vo.EmsExamJoinUserVo;
import com.light.exam.exam.service.IEmsExamJoinUserService;
import com.light.exam.exam.mapper.EmsExamJoinUserMapper;
import com.light.core.entity.AjaxResult;
/**
 * 平台考试指定用户表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-07 11:00:20
 */
@Service
public class EmsExamJoinUserServiceImpl extends ServiceImpl<EmsExamJoinUserMapper, EmsExamJoinUserDto> implements IEmsExamJoinUserService {

	@Resource
	private EmsExamJoinUserMapper emsExamJoinUserMapper;

    @Override
	public List<EmsExamJoinUserVo> getEmsExamJoinUserListByCondition(EmsExamJoinUserConditionBo condition) {
		FuzzyQueryUtil.transferMeanBean(condition);
        return emsExamJoinUserMapper.getEmsExamJoinUserListByCondition(condition);
	}

	@Override
	public AjaxResult addEmsExamJoinUser(EmsExamJoinUserBo emsExamJoinUserBo) {
		EmsExamJoinUserDto emsExamJoinUser = new EmsExamJoinUserDto();
		BeanUtils.copyProperties(emsExamJoinUserBo, emsExamJoinUser);
		if(save(emsExamJoinUser)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateEmsExamJoinUser(EmsExamJoinUserBo emsExamJoinUserBo) {
		EmsExamJoinUserDto emsExamJoinUser = new EmsExamJoinUserDto();
		BeanUtils.copyProperties(emsExamJoinUserBo, emsExamJoinUser);
		if(updateById(emsExamJoinUser)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public EmsExamJoinUserVo getDetail(Long id) {
		EmsExamJoinUserConditionBo condition = new EmsExamJoinUserConditionBo();
		condition.setId(id);
		List<EmsExamJoinUserVo> list = emsExamJoinUserMapper.getEmsExamJoinUserListByCondition(condition);
		EmsExamJoinUserVo vo = new EmsExamJoinUserVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}


    @Override
    public boolean deleteByExamId(Long examId) {
        UpdateWrapper<EmsExamJoinUserDto> updateWrapper = new UpdateWrapper();
        updateWrapper.lambda().eq(EmsExamJoinUserDto::getExamId, examId);
        final int res = this.emsExamJoinUserMapper.delete(updateWrapper);
        return res > 0;
    }

    @Override
    public EmsExamJoinUserVo getByExamIdAndUserOid(Long examId, String userOid) {
        QueryWrapper<EmsExamJoinUserDto> updateWrapper = new QueryWrapper();
        updateWrapper.lambda().eq(EmsExamJoinUserDto::getExamId, examId)
                .eq(EmsExamJoinUserDto::getUserOid, userOid);
        final EmsExamJoinUserDto emsExamJoinUserDto = this.emsExamJoinUserMapper.selectOne(updateWrapper);
        if(emsExamJoinUserDto == null){
            return null;
        }
        final EmsExamJoinUserVo userVo = BeanUtil.toBean(emsExamJoinUserDto, EmsExamJoinUserVo.class);
        return userVo;
    }

    @Override
    public List<EmsExamJoinUserVo> getByExamId(Long examId) {
        return this.baseMapper.selectByExamId(examId);
    }
}
