package com.light.exam.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.exam.item.entity.bo.EmsItemModelBo;
import com.light.exam.item.entity.bo.EmsItemOldModelBo;
import com.light.exam.item.entity.dto.EmsItemDto;
import com.light.exam.item.entity.bo.EmsItemConditionBo;
import com.light.exam.item.entity.bo.EmsItemBo;
import com.light.exam.item.entity.vo.EmsItemVo;
import com.light.core.entity.AjaxResult;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 平台试题表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-28 16:09:45
 */
public interface IEmsItemService extends IService<EmsItemDto> {

    List<EmsItemVo> getEmsItemListByCondition(EmsItemConditionBo condition);

	AjaxResult addEmsItem(EmsItemBo emsItemBo);

	AjaxResult updateEmsItem(EmsItemBo emsItemBo);

	Map<String, Object> getDetail(Long emsItemId);

    AjaxResult importEmsItem(List<EmsItemModelBo> list);

	AjaxResult importOldEmsItem(List<EmsItemOldModelBo> list);

    /**
     * 根据试题ID 集合获取列表
     *
     * @param itemIds 试题ID列表  the ems item id list
     * @return {@link List}<{@link EmsItemVo}>
     */
    List<EmsItemVo> getListByItemIds(Collection<Long> itemIds);

    /**
     * 获取随机试题列表
     *
     * @param conditionBo 条件博
     * @return {@link List}<{@link EmsItemVo}>
     */
    List<EmsItemVo> getRandomItemListByCond(EmsItemConditionBo conditionBo);

    /**
     * 根据条件获取数量
     *
     * @param condition 条件
     * @return {@link Integer}
     */
    Integer getCountByCondition(EmsItemConditionBo condition);

    /**
     * 根据条件获取id
     * @param conditionBo
     * @return
     */
    List<Long> getIdByCondition(EmsItemConditionBo conditionBo);
}

