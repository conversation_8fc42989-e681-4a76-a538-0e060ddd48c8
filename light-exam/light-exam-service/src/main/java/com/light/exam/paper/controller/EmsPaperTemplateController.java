package com.light.exam.paper.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.exam.paper.api.EmsPaperTemplateApi;
import com.light.exam.paper.entity.bo.EmsPaperTemplateBo;
import com.light.exam.paper.entity.bo.EmsPaperTemplateConditionBo;
import com.light.exam.paper.entity.bo.EmsPaperTemplateScoreConfigBo;
import com.light.exam.paper.entity.dto.EmsPaperTemplateDto;
import com.light.exam.paper.entity.vo.EmsPaperTemplateVo;
import com.light.exam.paper.service.IEmsPaperTemplateService;
import com.light.feign.annotation.FeignValidatorAnnotation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 平台试卷模板表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 15:16:58
 */
@RestController
@Validated
public class EmsPaperTemplateController implements EmsPaperTemplateApi {

    @Autowired
    private IEmsPaperTemplateService emsPaperTemplateService;

    /**
     * 查询平台试卷模板表列表
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     * @return
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<EmsPaperTemplateVo>> getEmsPaperTemplatePageListByCondition(@RequestBody EmsPaperTemplateConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<EmsPaperTemplateVo> pageInfo = new PageInfo<>(emsPaperTemplateService.getEmsPaperTemplateListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    /**
     * 查询平台试卷模板表列表
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     * @return
     */
    @Override
    public AjaxResult<List<EmsPaperTemplateVo>> getEmsPaperTemplateListByCondition(EmsPaperTemplateConditionBo condition) {
        return AjaxResult.success(emsPaperTemplateService.getEmsPaperTemplateListByCondition(condition));
    }

    /**
     * 新增平台试卷模板表
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addEmsPaperTemplate(@Validated @RequestBody EmsPaperTemplateBo emsPaperTemplateBo){
		return emsPaperTemplateService.addEmsPaperTemplate(emsPaperTemplateBo);
    }

    /**
	 * 修改平台试卷模板表
	 * @param emsPaperTemplateBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateEmsPaperTemplate(@Validated @RequestBody EmsPaperTemplateBo emsPaperTemplateBo) {
		if(null == emsPaperTemplateBo.getId()) {
			return AjaxResult.fail("平台试卷模板表id不能为空");
		}
		return emsPaperTemplateService.updateEmsPaperTemplate(emsPaperTemplateBo);
	}

	/**
	 * 查询平台试卷模板表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult getDetail(@RequestParam("id") Long id) {
        return AjaxResult.success(this.emsPaperTemplateService.getDetail(id));
	}

    /**
	 * 删除平台试卷模板表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-17 15:16:58
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		LambdaUpdateWrapper<EmsPaperTemplateDto> luw = new LambdaUpdateWrapper<EmsPaperTemplateDto>();
		luw.eq(EmsPaperTemplateDto::getId, id);
		luw.set(EmsPaperTemplateDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		if(emsPaperTemplateService.update(luw)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

    /**
     * 更新分数配置
     *
     * @param bo
     * @return {@link AjaxResult}
     */
    @Override
    public AjaxResult updateScoreConfig(@RequestBody EmsPaperTemplateBo bo) {

        List<EmsPaperTemplateScoreConfigBo> scoreConfigList = bo.getScoreConfigList();

        if(CollUtil.isEmpty(scoreConfigList)){
            scoreConfigList = Lists.newArrayList();
        }
        EmsPaperTemplateDto dto = new EmsPaperTemplateDto();
        dto.setId(bo.getId());
        dto.setScoreConfig(JSON.toJSONString(scoreConfigList));
        this.emsPaperTemplateService.updateById(dto);
        return AjaxResult.success();
    }
}
