package com.light.exam.relation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.exam.relation.entity.dto.EmsRelationDto;
import com.light.exam.relation.entity.bo.EmsRelationConditionBo;
import com.light.exam.relation.entity.vo.EmsRelationVo;

/**
 * 平台考试关联表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-23 09:27:33
 */
public interface EmsRelationMapper extends BaseMapper<EmsRelationDto> {

	List<EmsRelationVo> getEmsRelationListByCondition(EmsRelationConditionBo condition);

}
