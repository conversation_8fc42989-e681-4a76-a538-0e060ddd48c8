package com.light.exam.relation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.exam.relation.entity.dto.EmsRelationDto;
import com.light.exam.relation.entity.bo.EmsRelationConditionBo;
import com.light.exam.relation.entity.bo.EmsRelationBo;
import com.light.exam.relation.entity.vo.EmsRelationVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 平台考试关联表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-23 09:27:33
 */
public interface IEmsRelationService extends IService<EmsRelationDto> {

    List<EmsRelationVo> getEmsRelationListByCondition(EmsRelationConditionBo condition);

	AjaxResult addEmsRelation(EmsRelationBo emsRelationBo);

	AjaxResult updateEmsRelation(EmsRelationBo emsRelationBo);

	Map<String, Object> getDetail(Long emsRelationId);

}

