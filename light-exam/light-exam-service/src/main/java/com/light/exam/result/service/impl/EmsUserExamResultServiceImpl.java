package com.light.exam.result.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.exam.result.entity.dto.EmsUserExamResultDto;
import com.light.exam.result.entity.bo.EmsUserExamResultConditionBo;
import com.light.exam.result.entity.vo.EmsUserExamResultVo;
import com.light.exam.result.service.IEmsUserExamResultService;
import com.light.exam.result.mapper.EmsUserExamResultMapper;

/**
 * 平台用户考试结果表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-17 16:07:16
 */
@Service
public class EmsUserExamResultServiceImpl extends ServiceImpl<EmsUserExamResultMapper, EmsUserExamResultDto> implements IEmsUserExamResultService {

	@Resource
	private EmsUserExamResultMapper emsUserExamResultMapper;

    @Override
	public List<EmsUserExamResultVo> getEmsUserExamResultListByCondition(EmsUserExamResultConditionBo condition) {
        return emsUserExamResultMapper.getEmsUserExamResultListByCondition(condition);
	}

	@Override
	public Map<String, Object> getDetail(Long emsUserExamResultId) {
		LambdaQueryWrapper<EmsUserExamResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(EmsUserExamResultDto::getId, emsUserExamResultId);
		EmsUserExamResultDto emsUserExamResult = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("emsUserExamResultVo", emsUserExamResult==null?new EmsUserExamResultVo():emsUserExamResult);
		return reuslt;
	}

    @Override
    public EmsUserExamResultDto getByExamIdAndUserOid(Long examId, String userOid) {
        QueryWrapper<EmsUserExamResultDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(EmsUserExamResultDto::getExamId, examId)
                .eq(EmsUserExamResultDto::getUserOid, userOid);
        return this.emsUserExamResultMapper.selectOne(queryWrapper);
    }

    @Override
    public EmsUserExamResultDto getByExamOidAndUserOid(String examOid, String userOid) {
        QueryWrapper<EmsUserExamResultDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(EmsUserExamResultDto::getExamOid, examOid)
                .eq(EmsUserExamResultDto::getUserOid, userOid);
        return this.emsUserExamResultMapper.selectOne(queryWrapper);
    }

    @Override
    public EmsUserExamResultDto getByLastAnswerId(Long answerId) {
        QueryWrapper<EmsUserExamResultDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(EmsUserExamResultDto::getLastResultRecordId, answerId);
        return this.emsUserExamResultMapper.selectOne(queryWrapper);
    }
}
