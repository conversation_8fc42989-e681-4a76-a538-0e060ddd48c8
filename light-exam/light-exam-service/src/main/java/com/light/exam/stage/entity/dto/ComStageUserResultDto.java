package com.light.exam.stage.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 竞赛环节用户结果
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-28 16:07:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("light_com_stage_user_result")
public class ComStageUserResultDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 竞赛ID
	 */
	@TableField("com_id")
	private Long comId;

	/**
	 * 环节ID
	 */
	@TableField("stage_id")
	private Long stageId;

	/**
	 * 用户oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 分数
	 */
	@TableField("score")
	private Double score;

	/**
	 * 正确率
	 */
	@TableField("rights_percent")
	private Double rightsPercent;

	/**
	 * 答题用时（毫秒）
	 */
	@TableField("use_time")
	private Long useTime;

	/**
	 * 答题数
	 */
	@TableField("item_count")
	private Long itemCount;

	/**
	 * 描述
	 */
	@TableField("description")
	private String description;

	/**
	 * 顺序
	 */
	@TableField("sequence")
	private Integer sequence;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}
