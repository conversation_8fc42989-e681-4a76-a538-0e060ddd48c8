package com.light.exam.stage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.exam.stage.entity.dto.ComStageItemDto;
import com.light.exam.stage.entity.bo.ComStageItemConditionBo;
import com.light.exam.stage.entity.bo.ComStageItemBo;
import com.light.exam.stage.entity.vo.ComStageItemVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 竞赛环节试题接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-28 16:07:16
 */
public interface IComStageItemService extends IService<ComStageItemDto> {

    List<ComStageItemVo> getComStageItemListByCondition(ComStageItemConditionBo condition);

	AjaxResult addComStageItem(ComStageItemBo comStageItemBo);

	AjaxResult updateComStageItem(ComStageItemBo comStageItemBo);

	ComStageItemVo getDetail(Long comStageItemId);

	List<ComStageItemVo> getSelectedStageItemList(ComStageItemConditionBo condition);

	List<ComStageItemVo> getUnSelectStageItemList(ComStageItemConditionBo condition);

    ComStageItemVo getStageItemByCondition(ComStageItemConditionBo condition);

    List<ComStageItemVo> getStageUserItemCategory(Long stageId, Integer step, String userOids);

	Map<String, Integer> getStageItemProgress(ComStageItemConditionBo condition);

	int selectMaxAnswerSequence(ComStageItemConditionBo condition);
}

