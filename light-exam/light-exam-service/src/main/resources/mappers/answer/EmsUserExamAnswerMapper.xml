<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.exam.answer.mapper.EmsUserExamAnswerMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.exam.answer.entity.dto.EmsUserExamAnswerDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="examCount" column="exam_count"/>
        <result property="score" column="score"/>
        <result property="totalScore" column="total_score"/>
        <result property="subjectiveScore" column="subjective_score"/>
        <result property="objectiveScore" column="objective_score"/>
        <result property="batchId" column="batch_id"/>
        <result property="examId" column="exam_id"/>
        <result property="paperId" column="paper_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="userRealName" column="user_real_name"/>
        <result property="userAccountName" column="user_account_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="useTime" column="use_time"/>
		<result property="rateWay" column="rate_way"/>
        <result property="status" column="status"/>
        <result property="answerDetailId" column="answer_detail_id"/>
        <result property="isCommitted" column="is_committed"/>
        <result property="points" column="points"/>
        <result property="pass" column="pass"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null">and id = #{id}</if>
			<if test="examCount != null and examCount != ''">and exam_count = #{examCount}</if>
			<if test="score != null and score != ''">and score = #{score}</if>
			<if test="totalScore != null and totalScore != ''">and total_score = #{totalScore}</if>
			<if test="batchId != null">and batch_id = #{batchId}</if>
			<if test="answerDetailId != null">and answer_detail_id = #{answerDetailId}</if>
			<if test="isCommitted != null">and is_committed = #{isCommitted}</if>
			<if test="rateWay != null">and rate_way = #{rateWay}</if>
			<if test="status != null">and status = #{status}</if>
			<if test="examId != null and examId != ''">and exam_id = #{examId}</if>
			<if test="paperId != null ">and paper_id = #{paperId}</if>
			<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
			<if test="endTime != null and endTime != ''">and end_time = #{endTime}</if>
			<if test="pass != null ">and pass = #{pass}</if>
			<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
			<if test="endTimeStart != null ">and end_time &gt;= #{endTimeStart}</if>
			<if test="endTimeEnd != null ">and end_time &lt;= #{endTimeEnd}</if>
			<if test="code != null and code != ''">and `code` = #{code}</if>
			<if test="examOid != null and examOid != ''">and exam_oid = #{examOid}</if>
			<if test="userRealName != null and userRealName != ''">and user_real_name like concat('%', #{userRealName}, '%')</if>
			<if test="userAccountName != null and userAccountName != ''">and user_account_name like concat('%', #{userAccountName}, '%')</if>
		</where>
	</sql>

	<select id="getEmsUserExamAnswerListByCondition" resultType="com.light.exam.answer.entity.vo.EmsUserExamAnswerVo">
		select t.* from (
			select a.*,
				  lee.oid as  exam_oid,
				  ra.rater_user_oid
			from light_ems_user_exam_answer a
			inner join light_ems_exam lee on a.exam_id = lee.id
		    left join light_ems_user_exam_answer_rater ra on a.id = ra.answer_id
		) t
		<include refid="common_where">
		</include>
	</select>

    <select id="getRankByTime" resultType="com.light.exam.answer.entity.vo.EmsUserExamAnswerVo">
		SELECT
			sum( right_item_count ) AS right_item_count,
			sum( use_time ) as use_time,
			sec_to_time(sum( use_time )) AS use_time_total,
			user_oid
		FROM
			light_ems_user_exam_answer
		<include refid="common_where">
		</include>
		GROUP BY
			user_oid
		order by right_item_count desc, use_time asc
	</select>
	<select id="getDaySumPointsByUserOid" resultType="java.lang.Double">
        select ifnull(sum(ifnull(leuea.points,0)),0) from light_ems_user_exam_answer leuea
        where leuea.user_oid = #{userOid} and leuea.end_time is not null
        and DATE_FORMAT(leuea.end_time,'%Y-%m-%d') = #{dayDate} and leuea.exam_id = #{examId}
    </select>

    <select id="getMonthSumPointsByUserOid" resultType="java.lang.Double">
        select ifnull(sum(ifnull(leuea.points,0)),0) from light_ems_user_exam_answer leuea
        where leuea.user_oid = #{userOid} and leuea.end_time is not null
        and DATE_FORMAT(leuea.end_time,'%Y-%m') = #{monthDate}
    </select>

    <select id="getEmsUserExamAnswerCommittedUser" resultType="com.light.exam.answer.entity.vo.EmsUserExamAnswerVo">
		select user_oid, max(is_committed) as is_committed
		from (
			SELECT
				user_oid,
				is_committed
			FROM
				light_ems_user_exam_answer
				<where>
					<if test="batchId != null">and batch_id = #{batchId}</if>
					<if test="examId != null and examId != ''">and exam_id = #{examId}</if>
					<if test="userOids != null and userOid.size() > 0">
						and user_oid in
						<foreach collection="userOids" item="item" open="(" close=")" separator=",">
							#{item}
						</foreach>
					</if>
					<if test="isCommitted != null">and is_committed = #{isCommitted}</if>
				</where>
		) t
		group by user_oid
    </select>
</mapper>
