package com.light.gateway.filters;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.light.core.constants.MessageConstants;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.inspect.InspectService;
import com.light.core.inspect.config.InspectProperties;
import com.light.core.inspect.thread.ThreadLocalBodyFactory;
import com.light.core.utils.XSSUtil;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 网关鉴权
 *
 */
//@Component
//@RefreshScope
public class InspectFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(InspectFilter.class);

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Autowired
    private InspectService inspectService;

    @Autowired
    private InspectProperties inspectProperties;

    @Resource
    private RedisComponent redisComponent;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        try {
            String uri = exchange.getRequest().getURI().getPath();
            final String body = ThreadLocalBodyFactory.get();
            // 判断是否需要校验
            Mono<Void> filter = chain.filter(exchange);
            if(StrUtil.isNotEmpty(body)) {
                //判断接口地址是不是不需要xss过滤
                if(!isNoXssFilter(uri)) {//需要xss过滤
                    //判断内容是否包含尖括号
                    if(XSSUtil.hasXSSContent(body)) {
                        return setUnauthorizedResponse(exchange, MessageConstants.WRONG_XSS_CONTENT, cn.hutool.http.HttpStatus.HTTP_INTERNAL_ERROR);
                    }
                }
                Route route = (Route)exchange.getAttributes().get(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
                Map<String, Object> map = route.getMetadata();
                if(null == map || null == map.get(SystemConstants.ROUTE_KEY) || StringUtils.isEmpty(map.get(SystemConstants.ROUTE_KEY).toString())) {
                    return setUnauthorizedResponse(exchange, MessageConstants.WRONG_GATEWAY_ROUTE, cn.hutool.http.HttpStatus.HTTP_INTERNAL_ERROR);
                }
                if(!isInspect(uri, map.get(SystemConstants.ROUTE_KEY).toString())) {
                    return filter;
                }
                if(inspectProperties.isOpen()){
                    final boolean b = this.inspectService.checkSingleText(body);
                    if (!b) {
                        return setUnauthorizedResponse(exchange, MessageConstants.WRONG_INSPECT, cn.hutool.http.HttpStatus.HTTP_INTERNAL_ERROR);
                    }
                }
            }
            return chain.filter(exchange);
        } catch (Exception e) {
            return setUnauthorizedResponse(exchange, MessageConstants.WRONG_GATEWAY_ROUTE, cn.hutool.http.HttpStatus.HTTP_INTERNAL_ERROR);
        }finally {
            ThreadLocalBodyFactory.clear();
        }
    }

    private Mono<Void> setUnauthorizedResponse(ServerWebExchange exchange, String msg, Integer code){
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        response.setStatusCode(HttpStatus.OK);

        log.error("[内容检查异常]请求路径:{}", exchange.getRequest().getPath());

        return response.writeWith(Mono.fromSupplier(() -> {
            DataBufferFactory bufferFactory = response.bufferFactory();
            return bufferFactory.wrap(JSON.toJSONBytes(AjaxResult.fail(code, msg)));
        }));
    }

    private boolean isInspect(String uri, String gatewayRoute) {
        String inspectPath = inspectProperties.getPath();
        String[] uris = null;
        String configValue = redisComponent.hgetObjectValue(RedisKeyEnum.CONFIG_KEY.getValue(), SystemConstants.INSPECT_PATH, SystemConstants.CONFIG_VALUE);
        if(!StringUtils.isEmpty(configValue)){
            uris = ArrayUtils.addAll(inspectPath.split(SystemConstants.SEPERATOR_COMMA), configValue.split(SystemConstants.SEPERATOR_COMMA));
        }else{
            uris = inspectPath.split(SystemConstants.SEPERATOR_COMMA);
        }
        for (String path : uris) {
            if (!StringUtils.isEmpty(path)) {
                if (antPathMatcher.match(gatewayRoute + path, uri)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isNoXssFilter(String uri) {
        String[] uris = null;
        String configValue = redisComponent.hgetObjectValue(RedisKeyEnum.CONFIG_KEY.getValue()
                , SystemConstants.NO_XSS_FILTER_PATH, SystemConstants.CONFIG_VALUE);
        if(!StringUtils.isEmpty(configValue)){
            uris = configValue.split(SystemConstants.SEPERATOR_COMMA);
            for (String path : uris) {
                if (!StringUtils.isEmpty(path)) {
                    if (antPathMatcher.match(path, uri)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public int getOrder(){
        return -100;
    }
}