package com.light.gateway.service;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.gateway.bean.PermissionAuthConfig;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class AuthService {

    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Value("${unauthor.path}")
    private String UNAUTHOR_PATH;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private ConfigDomainService configService;



    /**
     * 判断接口路径是否白名单地址
     * @param uri the uri 地址
     * @param gatewayRoute the gateway route 网管路由
     */
    public boolean isTokenWhite(String uri, String gatewayRoute) {
        String[] uris = null;
        String configValue = redisComponent.hgetObjectValue(RedisKeyEnum.CONFIG_KEY.getValue(), SystemConstants.UNAUTHOR_PATH, SystemConstants.CONFIG_VALUE);
        if(!StringUtils.isEmpty(configValue)){
            uris = ArrayUtils.addAll(UNAUTHOR_PATH.split(SystemConstants.SEPERATOR_COMMA), configValue.split(SystemConstants.SEPERATOR_COMMA));
        }else{
            uris = UNAUTHOR_PATH.split(SystemConstants.SEPERATOR_COMMA);
        }
        for (String path : uris) {
            if (!StringUtils.isEmpty(path)) {
                if (antPathMatcher.match(gatewayRoute + path, uri)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     *  是否权限鉴权
     * @param uri the uri 地址
     * @param gatewayRoute the gateway route 网管路由
     * @return boolean
     */
    public boolean isPermissionAuth(String uri, String gatewayRoute) {
        PermissionAuthConfig config = this.configService.getPermissionAuthConfig();
        // 没有进行配置
        if(config == null){
            return false;
        }
        // 没有开启
        if(!config.isOpen()){
            return false;
        }
        // 没有设置需要鉴权的路由
        if (CollUtil.isEmpty(config.getRoutes())) {
            return false;
        }
        // 没有匹配的路由
        boolean b = config.getRoutes().stream().anyMatch(x -> x.equals(gatewayRoute));
        if(!b){
            return false;
        }

        List<String> ignoreUrls = Optional.ofNullable(config.getIgnoreUrl()).orElse(Lists.newArrayList());
        return ignoreUrls.stream().noneMatch(x -> antPathMatcher.match(x, uri));

    }


    /**
     *  匹配权限码
     * @param userPermissions the user permissions 用户权限码
     * @param permissionsList the permission list 权限吗集合
     * @return boolean
     */
    public boolean matchPermission(List<String> userPermissions, List<String> permissionsList) {
        if(CollUtil.isEmpty(permissionsList)){
            return true;
        }

        if(CollUtil.isEmpty(userPermissions)){
            return false;
        }

        return  userPermissions.stream().anyMatch(permissionsList::contains);
    }



}
