package com.light.gateway.service;


import cn.hutool.core.collection.CollectionUtil;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import com.light.user.permission.api.PermisstionApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PermissionsDomainService {



    @Resource
    private PermisstionApi permisstionApi;

    @Resource
    private RedisComponent redisComponent;


    /**
     * 根据 URL 获取权限码
     * @param url the url
     * @return {@link List }<{@link String }>
     */
    public List<String> getPermissionsByUrl(String url) {

        // redis 缓存获取
        List<String> permissions = this.getCachePathPermission(url);
        if(CollectionUtil.isNotEmpty(permissions)) {
            return permissions;
        }

        // 服务查询
        AjaxResult<List<String>> ajaxResult = permisstionApi.queryPermissionByPath(url);
        if (!ajaxResult.isSuccess()) {
            return Collections.emptyList();
        }
        return ajaxResult.getData();
    }

    /**
     *  缓存内根据地址获取权限码
     * @param path the path
     * @return {@link List }<{@link String }>
     */
    public List<String> getCachePathPermission(String path) {
        String redisKey = RedisKeyEnum.URL_PERMISSION.getValue();
        Object obj = this.redisComponent.hget(redisKey, path);
        if(obj == null) {
            return null;
        }
        if(obj instanceof List) {
            return (List<String>) obj;
        }
        return null;
    }



}
