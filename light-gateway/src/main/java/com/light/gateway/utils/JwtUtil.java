package com.light.gateway.utils;

import cn.hutool.http.HttpStatus;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.light.core.constants.MessageConstants;
import com.light.gateway.exception.AuthenticationException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2021/7/610:08
 */
@Slf4j
@Component
public class JwtUtil {

    @Getter
    private static Long EXPIRE_TIME;

    @Value("${shiro.jwt.expireTime}")
    public void setProfile(Long time) {
        JwtUtil.EXPIRE_TIME = time;
    }

    /**
     * 校验token是否正确
     *
     * @param token  密钥
     * @param secret 用户的密码
     * @return 是否正确
     */
    public static boolean verify(String token, String account, String secret) {

        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).withClaim("account", account).build();
            verifier.verify(token);
            return true;
        } catch (RuntimeException e) {
            throw new AuthenticationException(HttpStatus.HTTP_NOT_ACCEPTABLE, MessageConstants.TOKEN_EXPIRE);
        }
    }

    /**
     * 获得token中的信息无需secret解密也能获得
     *
     * @return token中包含的用户id
     */
    public static String getAccount(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("account").asString();
        } catch (RuntimeException e) {
            return null;
        }
    }

    /**
     * 生成签名
     *
     * @param account 用户用户名
     * @param secret   用户的密码
     * @return 加密的token
     */
    public static String sign(String account, String secret) {
        //默认7天时间
        long expireTime = 7 * 24 * 60 * 60 * 1000;

        if (EXPIRE_TIME != null) {
            expireTime = EXPIRE_TIME;
        } else {
            log.warn("配置文件读取token过期时间错误,请检查配置是否正确");
        }

        try {
            Date date = new Date(System.currentTimeMillis() + expireTime);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            // 附带id信息
            return JWT.create().withClaim("account", account).withExpiresAt(date).sign(algorithm);
        } catch (RuntimeException e) {
            return null;
        }
    }
}
