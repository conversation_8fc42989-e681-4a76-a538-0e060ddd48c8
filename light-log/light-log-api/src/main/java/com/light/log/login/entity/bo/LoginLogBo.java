package com.light.log.login.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 登录日志表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 11:41:16
 */
@Data
public class LoginLogBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 日志id
	 */
	@ApiModelProperty("日志id")
	private Long id;
	/**
	 * 模块名称
	 */
	@ApiModelProperty("模块名称")
	private String moduleName;
	/**
	 * 方法名称
	 */
	@ApiModelProperty("方法名称")
	private String methodName;
	/**
	 * 请求URL
	 */
	@ApiModelProperty("请求URL")
	private String requestUrl;
	/**
	 * 请求方式
	 */
	@ApiModelProperty("请求方式")
	private String requestType;
	/**
	 * 登录终端
	 */
	@ApiModelProperty("登录终端")
	private String loginTerminal;
	/**
	 * 登录账号
	 */
	@ApiModelProperty("登录账号")
	private String loginAccount;
	/**
	 * 登录账号oid
	 */
	@ApiModelProperty("登录账号oid")
	private String loginAccountOid;

	/**
	 * 登录用户oid
	 */
	@ApiModelProperty("登录用户oid")
	private String loginUserOid;

	/**
	 * 登录用户姓名
	 */
	@ApiModelProperty("登录用户姓名")
	private String loginUserName;

	/**
	 * ip地址
	 */
	@ApiModelProperty("ip地址")
	private String loginIp;
	/**
	 * 登录地点
	 */
	@ApiModelProperty("登录地点")
	private String loginLocation;
	/**
	 * 组织机构id
	 */
	@ApiModelProperty("组织机构id")
	private Long loginOrganizationId;
	/**
	 * 组织机构名称
	 */
	@ApiModelProperty("组织机构名称")
	private String loginOrganizationName;
	/**
	 * 请求参数
	 */
	@ApiModelProperty("请求参数")
	private String requestParams;
	/**
	 * 返回结果
	 */
	@ApiModelProperty("返回结果")
	private String requestResult;
	/**
	 * 请求状态（1:正确，0:错误）
	 */
	@ApiModelProperty("请求状态（1:正确，0:错误）")
	private String requestStatus;
	/**
	 * 错误消息
	 */
	@ApiModelProperty("错误消息")
	private String requestErrorMessage;
	/**
	 * 用时
	 */
	@ApiModelProperty("用时")
	private Long useTime;
	/**
	 * 登录时间
	 */
	@ApiModelProperty("登录时间")
	private Date loginTime;

}
