package com.light.log.common.listener;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.core.constants.SystemConstants;
import com.light.log.operation.entity.dto.OperationLogDto;
import com.light.log.operation.service.IOperationLogService;
import com.light.mq.constants.RabbitMqConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/1216:55
 */
@Component
@RabbitListener(queues = RabbitMqConstants.OPERATION_LOG_QUEUE)
public class OperationLogListener {

    private static Logger logger = LoggerFactory.getLogger(LoginLogListener.class);

    @Autowired
    private IOperationLogService operationLogService;

    @Value("${operation.log.seperator:false}")
    private boolean OPERATION_LOG_SEPERATOR;

    @Value("${table.schema:light_log}")
    private String TABLE_SCHEMAS;

    private static final String BASE_TABLE = "p_operation_log";

    @RabbitHandler
    public void process(String message){
        try {
            logger.info("=============处理操作日志消息================");
            logger.info("receive: " + message);
            if(!StringUtils.isEmpty(message)) {
                OperationLogDto operation = JSON.parseObject(message, OperationLogDto.class);
                if(OPERATION_LOG_SEPERATOR) {//如果是分表
                    //获取当前日期
                    String month = DateUtil.format((new Date()), "yyyyMM");
                    String tableName = BASE_TABLE + SystemConstants.UNDERLINE + month;
                    if(operationLogService.checkTableExist(tableName, TABLE_SCHEMAS)) {//如果表存在，则直接保存，否则创建新表
                        operationLogService.insertByTableName(operation, tableName);
                    }else{
                        //创建表
                        operationLogService.createTable(tableName);
                        operationLogService.insertByTableName(operation, tableName);
                    }
                }else{
                    operationLogService.save(operation);
                }
            }
        } catch (Exception e) {
            logger.error("操作日志消息队列处理错误{}" + e);
        }
    }
}
