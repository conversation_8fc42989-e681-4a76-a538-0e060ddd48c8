package com.light.log.operation.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-12 15:49:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_operation_log")
public class OperationLogDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 日志id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 模块名称
	 */
	@TableField("module_name")
	private String moduleName;

	/**
	 * 操作类型
	 */
	@TableField("operation_type")
	private String operationType;

	/**
	 * 方法名称
	 */
	@TableField("method_name")
	private String methodName;

	/**
	 * 请求URL
	 */
	@TableField("request_url")
	private String requestUrl;

	/**
	 * 请求方式
	 */
	@TableField("request_type")
	private String requestType;

	/**
	 * 操作终端
	 */
	@TableField("operation_terminal")
	private String operationTerminal;

	/**
	 * 操作账号
	 */
	@TableField("operation_account_name")
	private String operationAccountName;

	/**
	 * 操作账号oid
	 */
	@TableField("operation_account_oid")
	private String operationAccountOid;

	/**
	 * 操作用户oid
	 */
	@TableField("operation_user_oid")
	private String operationUserOid;

	/**
	 * 操作用户姓名
	 */
	@TableField("operation_user_name")
	private String operationUserName;

	/**
	 * 操作角色id
	 */
	@TableField("operation_role_id")
	private Long operationRoleId;

	/**
	 * 操作角色名称
	 */
	@TableField("operation_role_name")
	private String operationRoleName;

	/**
	 * ip地址
	 */
	@TableField("operation_ip")
	private String operationIp;

	/**
	 * 操作地点
	 */
	@TableField("operation_location")
	private String operationLocation;

	/**
	 * 组织机构id
	 */
	@TableField("operation_organization_id")
	private Long operationOrganizationId;

	/**
	 * 组织机构名称
	 */
	@TableField("operation_organization_name")
	private String operationOrganizationName;

	/**
	 * 请求参数
	 */
	@TableField("request_params")
	private String requestParams;

	/**
	 * 返回结果
	 */
	@TableField("request_result")
	private String requestResult;

	/**
	 * 请求状态（1:正确，0:错误）
	 */
	@TableField("request_status")
	private String requestStatus;

	/**
	 * 错误消息
	 */
	@TableField("request_error_message")
	private String requestErrorMessage;

	/**
	 * 操作时间
	 */
	@TableField("operation_time")
	private Date operationTime;

	/**
	 * 用时
	 */
	@TableField("use_time")
	private Long useTime;

}
