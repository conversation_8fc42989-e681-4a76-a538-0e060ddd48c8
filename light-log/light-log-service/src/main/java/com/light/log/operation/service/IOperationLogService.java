package com.light.log.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.log.operation.entity.dto.OperationLogDto;
import com.light.log.operation.entity.bo.OperationLogConditionBo;
import com.light.log.operation.entity.bo.OperationLogBo;
import com.light.log.operation.entity.vo.OperationLogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 操作日志表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-12 15:49:37
 */
public interface IOperationLogService extends IService<OperationLogDto> {

    List<OperationLogVo> getOperationLogListByCondition(OperationLogConditionBo condition);

	Map<String, Object> getDetail(Long operationLogId);

    boolean checkTableExist(String tableName, String tableSchema);

    void createTable(String tableName);

    void insertByTableName(OperationLogDto operation, String tableName);

    List<OperationLogVo> getOperationMonthListByCondition(OperationLogConditionBo condition, String month);
}

