package com.light.manage.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ecloud.config")
public class EcloudProperties {

    /**
     * 移动云ak
     */
    private String path = "file/%s/other/";
    /**
     * 移动云ak
     */
    private String ak = "A3ILMZICQCNMSDSWZNH5";

    /**
     * 移动云sk
     */
    private String sk = "iCVRo6B5n18Gj7eumbsCnKugQEFNnb8YGXcJbZhv";

    /**
     * 移动云endpointId
     */
    private String endpointId = "cidc-rp-12";

    /**
     * endpoint
     */
    private String endpoint = "obs.cidc-rp-12.joint.cmecloud.cn";

    /**
     * 移动云桶名
     */
    private String bucket = "xinxikeji";

    /**
     * token 有限期
     */
    private String duration = "900";

}
