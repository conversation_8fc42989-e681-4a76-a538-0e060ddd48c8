package com.light.manage.controller.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import com.light.base.article.entity.bo.ArticleBo;
import com.light.base.article.entity.bo.ArticleConditionBo;
import com.light.base.article.entity.vo.ArticleVo;
import com.light.base.article.service.ArticleApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.manage.service.category.CategoryService;
import com.light.manage.service.user.AdminService;
import com.light.manage.service.user.OrganizationService;
import com.light.security.service.CurrentAdminService;
import com.light.swagger.constants.SwaggerConstant;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文章表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-31 13:50:54
 */
@RestController
@Validated
@RequestMapping("article")
@Api(value = "", tags = "文章表接口" )
public class ArticleApiController {

    @Autowired
    private ArticleApiService articleApiService;

    @Resource
    private CurrentAdminService currentAdminService;

    @Resource
    private AdminService adminService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private CategoryService categoryService;

    /**
     * 查询文章表分页列表
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询文章表列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getArticlePageListByCondition(@RequestBody ArticleConditionBo condition){
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        AjaxResult<PageInfo<ArticleVo>> resp = articleApiService.getArticlePageListByCondition(condition);

        if(resp.isFail()){
            return resp;
        }
        final PageInfo<ArticleVo> data = resp.getData();
        if(data == null){
            return resp;
        }
        List<ArticleVo> list = data.getList();
        if(CollUtil.isEmpty(list)){
            return resp;
        }
        //转换分类
        this.covertCategoryInfo(list);
        //转换管理员姓名
        this.convertAdminName(list);

        this.convertOrganizationName(list);

        //转换创建人
        data.setList(list);
        return AjaxResult.success(data);
    }

    /**
     * 查询文章表列表
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询文章表列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getArticleListByCondition(@RequestBody ArticleConditionBo condition){
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ArticleVo> list = articleApiService.getArticleListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }

    /**
     * 我的分页列表
     * <AUTHOR>
     * @date 2023-03-31 13:49:17
     */
    @PostMapping("/myPageList")
    @ApiOperation(value = "我的分页列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult myPageList(@RequestBody ArticleConditionBo condition){
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setCreateBy(this.currentAdminService.getCurrentAdmin().getOid());
        return articleApiService.getArticleListByCondition(condition);
    }

    /**
     * 组织机构分页列表
     * <AUTHOR>
     * @date 2023-03-31 13:49:17
     */
    @PostMapping("/orgArticelPageList")
    @ApiOperation(value = "组织机构分页列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult orgArticelPageList(@RequestBody ArticleConditionBo condition){
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrganizationId(this.currentAdminService.getCurrentAdmin().getOrganizationId());
        AjaxResult<PageInfo<ArticleVo>> resp = articleApiService.getArticlePageListByCondition(condition);

        if(resp.isFail()){
            return resp;
        }
        final PageInfo<ArticleVo> data = resp.getData();
        if(data == null){
            return resp;
        }
        List<ArticleVo> list = data.getList();
        if(CollUtil.isEmpty(list)){
            return resp;
        }
        //转换管理员姓名
        list = this.convertAdminName(list);

        //转换创建人
        data.setList(list);
        return AjaxResult.success(data);
    }


    /**
     * 转换管理员姓名
     *
     * @param list 列表
     * @return {@link List}<{@link ArticleVo}>
     */
    private List<ArticleVo> convertAdminName(List<ArticleVo> list){
        final Set<String> adminOids = this.fetchAdminOid(list);

        final Map<String, AdminVo> adminNameMap = this.adminService.fetchAdminVoMapByOids(adminOids);

        list.stream().filter(x-> StrUtil.isNotEmpty(x.getCreateBy())).forEach(x->{
            final AdminVo adminVo = adminNameMap.get(x.getCreateBy());
            if(adminVo != null){
                x.setCreateAdminName(adminVo.getAdminName());
            }
        });

        return list;
    }


    /**
     * 获取管理员OID
     *
     * @param list 列表
     * @return {@link Set}<{@link String}>
     */
    private Set<String> fetchAdminOid(List<ArticleVo> list){
        final Set<String> adminOids = list.stream().map(ArticleVo::getCreateBy)
                .filter(StrUtil::isNotEmpty).collect(Collectors.toSet());

        return adminOids;
    }


    /**
     * 转换分类
     * @param list
     * @return
     */
    private void covertCategoryInfo(List<ArticleVo> list){
        // 获取所有分类信息
        final Set<Long> categoryIdList = this.fetchCategoryIdList(list);
        final Map<String, String> stringStringMap = categoryService.fetchCategoryMap(categoryIdList);

        list.stream().filter(x-> StrUtil.isNotEmpty(x.getCategoryIds())).forEach(x->{
            final String categoryIds = x.getCategoryIds();
            final String[] categoryArray = categoryIds.split(",");
            List<String> categoryName = new ArrayList<>();
            for (String categoryId : categoryArray){
                categoryName.add(stringStringMap.getOrDefault(categoryId,""));
            }
            x.setCategoryNames(categoryName);
        });
    }

    /**
     * 获取所有分类ID
     * @param list the article vo list
     * @return
     */
    private Set<Long> fetchCategoryIdList(List<ArticleVo> list) {
        Set<Long> categoryIdList = Sets.newConcurrentHashSet();
        list.stream().filter(x-> StrUtil.isNotEmpty(x.getCategoryIds())).forEach(x->{
            final String categoryIds = x.getCategoryIds();
            final String[] categoryIdArray = categoryIds.split(",");
            final Set<Long> categoryIdSet = Arrays.asList(categoryIdArray).stream()
                    .map(Long::parseLong).collect(Collectors.toSet());
            categoryIdList.addAll(categoryIdSet);
        });
        return categoryIdList;
    }



    /**
     * 将组织名称
     *
     * @param list 列表
     * @return {@link List}<{@link ArticleVo}>
     */
    private List<ArticleVo> convertOrganizationName(List<ArticleVo> list){
        // 处理添加人 和所属机构
        final Set<Long> orgIdList = list.stream().map(ArticleVo::getOrganizationId).collect(Collectors.toSet());

        // 获取组织机构名称
        final Map<Long, OrganizationVo> organizationNameMap = this.organizationService.fetchOrganizationMap(orgIdList);
        list.stream().filter(x-> x.getOrganizationId() != null).forEach(x-> {
            final OrganizationVo organizationVo = organizationNameMap.get(x.getOrganizationId());
            if(organizationVo != null){
                x.setOrganizationName(organizationVo.getName());
            }
        });

        return list;
    }


    /**
     * 新增文章表
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增文章表",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增文章表", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addArticle(@RequestBody ArticleBo articleBo){
        articleBo.setOrganizationId(this.currentAdminService.getCurrentAdmin().getOrganizationId());
        return articleApiService.addArticle(articleBo);
    }

    /**
     * 发布
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @PostMapping("/publishChange")
    @ApiOperation(value = "更改文章发布状态",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "文章发布", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult publishChange(@RequestBody ArticleBo articleBo){
        final Long id = articleBo.getId();
        if(id == null){
            return AjaxResult.fail("ID不能为空");
        }
        final Integer isPublish = articleBo.getIsPublish();
        if(isPublish == null){
            return AjaxResult.fail("发布状态不能为空");
        }

        ArticleBo bo = new ArticleBo();
        bo.setId(id);
        bo.setIsPublish(isPublish);
        if(isPublish.equals(StatusEnum.YES.getCode())){
            bo.setPublishTime(DateUtil.date());
        }
        return articleApiService.updateArticle(bo);
    }


    /**
     * 修改文章表
     * @param articleBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新文章表",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "更新文章表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateArticle(@RequestBody ArticleBo articleBo) {
        if(null == articleBo.getId()) {
            return AjaxResult.fail("文章表id不能为空");
        }
        return articleApiService.updateArticle(articleBo);
    }

    /**
     * 查询文章表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询文章表详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "文章表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ArticleVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("文章表id不能为空");
        }
        ArticleVo vo = articleApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("articleVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除文章表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-31 13:50:54
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除文章表",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "文章表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    @OperationLogAnnotation(moduleName = "删除文章表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return articleApiService.delete(id);
    }
}
