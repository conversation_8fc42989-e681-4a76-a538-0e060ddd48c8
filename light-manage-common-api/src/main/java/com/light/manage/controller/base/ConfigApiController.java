package com.light.manage.controller.base;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.base.config.entity.bo.ConfigBo;
import com.light.base.config.entity.bo.ConfigConditionBo;
import com.light.base.config.entity.vo.ConfigVo;
import com.light.base.config.service.ConfigApiService;
import com.light.log.constants.OperationLogConstants;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/1616:34
 */
@RestController
@Validated
@Api(value = "", tags = "系统参数接口")
@RequestMapping("/config")
public class ConfigApiController {

    @Autowired
    private ConfigApiService configApiService;

    /**
     * 查询列表
     * <AUTHOR>
     * @date 2021-12-08 10:29:32
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询系统参数",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getConfigListByCondition(@RequestBody ConfigConditionBo condition){
        return configApiService.getConfigListByCondition(condition);
    }


    /**
     * 新增
     * <AUTHOR>
     * @date 2021-12-08 10:29:32
     */
    @PostMapping("/add")
    @OperationLogAnnotation(moduleName = "新增", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    @ApiOperation(value = "新增系统参数",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addConfig(@Validated @RequestBody ConfigBo configBo){
        return configApiService.addConfig(configBo);
    }

    /**
     * 修改
     * @param configBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2021-12-08 10:29:32
     */
    @PostMapping("/update")
    @OperationLogAnnotation(moduleName = "修改", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    @ApiOperation(value = "更新系统参数",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateConfig(@Validated @RequestBody ConfigBo configBo) {
        return configApiService.updateConfig(configBo);
    }

    /**
     * 查询详情
     * @param configId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2021-12-08 10:29:32
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询系统参数详情",httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long configId) {
        return configApiService.getDetail(configId);
    }

    /**
     * 根据参数key查询系统参数详情
     * @param key
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2021-12-08 10:29:32
     */
    @GetMapping("/key")
    @ApiOperation(value = "根据参数key查询系统参数详情",httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getDetail(@NotNull(message = "请选择参数Key") String key) {
        ConfigVo config = configApiService.getConfigByKey(key).getData();
        return AjaxResult.success(config);
    }

    /**
     * 刷新配置信息缓存
     * @return
     */
    @GetMapping("/refresh")
    @ApiOperation(value = "刷新配置信息缓存", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult refresh() {
        return configApiService.refresh();
    }
}
