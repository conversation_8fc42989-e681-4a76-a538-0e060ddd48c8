package com.light.manage.controller.base;

import com.light.base.statistics.service.DayStatisticsApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.light.base.statistics.entity.bo.DayStatisticsConditionBo;
import com.light.base.statistics.entity.bo.DayStatisticsBo;
import com.light.base.statistics.entity.vo.DayStatisticsVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 每日PV UV统计信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-16 10:21:49
 */
@RestController
@Validated
@RequestMapping("day/statistics")
@Api(value = "", tags = "每日PV UV统计信息接口" )
public class DayStatisticsApiController {

    @Autowired
    private DayStatisticsApiService dayStatisticsApiService;

    /**
     * 查询每日PV UV统计信息分页列表
     * <AUTHOR>
     * @date 2022-12-16 10:21:49
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询每日PV UV统计信息列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getDayStatisticsPageListByCondition(@RequestBody DayStatisticsConditionBo condition){
        PageInfo<DayStatisticsVo> page = dayStatisticsApiService.getDayStatisticsPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询每日PV UV统计信息列表
     * <AUTHOR>
     * @date 2022-12-16 10:21:49
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询每日PV UV统计信息列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getDayStatisticsListByCondition(@RequestBody DayStatisticsConditionBo condition){
        List<DayStatisticsVo> list = dayStatisticsApiService.getDayStatisticsListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增每日PV UV统计信息
     * <AUTHOR>
     * @date 2022-12-16 10:21:49
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增每日PV UV统计信息",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增每日PV UV统计信息", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addDayStatistics(@RequestBody DayStatisticsBo dayStatisticsBo){
        return dayStatisticsApiService.addDayStatistics(dayStatisticsBo);
    }

    /**
     * 修改每日PV UV统计信息
     * @param dayStatisticsBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-16 10:21:49
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新每日PV UV统计信息",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "更新每日PV UV统计信息", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateDayStatistics(@RequestBody DayStatisticsBo dayStatisticsBo) {
        if(null == dayStatisticsBo.getId()) {
            return AjaxResult.fail("每日PV UV统计信息id不能为空");
        }
        return dayStatisticsApiService.updateDayStatistics(dayStatisticsBo);
    }

    /**
     * 查询每日PV UV统计信息详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-16 10:21:49
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询每日PV UV统计信息详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "每日PV UV统计信息id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<DayStatisticsVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("每日PV UV统计信息id不能为空");
        }
        DayStatisticsVo vo = dayStatisticsApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("dayStatisticsVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除每日PV UV统计信息
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-16 10:21:49
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除每日PV UV统计信息",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "每日PV UV统计信息id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    @OperationLogAnnotation(moduleName = "删除每日PV UV统计信息", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return dayStatisticsApiService.delete(id);
    }
}
