package com.light.manage.controller.base;

import com.light.base.module.service.ModuleApiService;
import com.light.core.constants.SystemConstants;
import io.swagger.annotations.Api;

import com.light.log.constants.OperationLogConstants;
import com.light.log.annotation.OperationLogAnnotation;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.base.module.entity.bo.ModuleConditionBo;
import com.light.base.module.entity.bo.ModuleBo;
import com.light.base.module.api.ModuleApi;

import com.light.core.entity.AjaxResult;
import javax.validation.constraints.NotNull;

/**
 * 模块
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 17:43:03
 */
@RestController
@Validated
@Api(value = "", tags = "模块接口")
public class ModuleApiController  {

    @Autowired
    private ModuleApiService moduleApiService;

    @PostMapping("/module/list")
    @ApiOperation(value = "分页查询模块", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getModuleListByCondition(@RequestBody ModuleConditionBo condition) {
        return moduleApiService.getModuleListByCondition(condition);
    }

    @PostMapping("/module/add")
    @ApiOperation(value = "新增模块", httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增模块", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addModule(@Validated @RequestBody ModuleBo moduleBo) {
        return moduleApiService.addModule(moduleBo);
    }

    @PostMapping("/module/update")
    @ApiOperation(value = "修改模块", httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "修改模块", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateModule(@Validated @RequestBody ModuleBo moduleBo) {
        return moduleApiService.updateModule(moduleBo);
    }

    @GetMapping("/module/detail")
    @ApiOperation(value = "查询模块详情", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id) {
        return moduleApiService.getDetail(id);
    }

    @GetMapping("/module/delete")
    @ApiOperation(value = "删除模块", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "模块id", required = true, dataType = "Long", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除模块", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id) {
        return moduleApiService.delete(id);
    }
}
