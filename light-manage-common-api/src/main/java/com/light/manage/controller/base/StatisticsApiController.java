package com.light.manage.controller.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.light.base.area.entity.bo.AreaConditionBo;
import com.light.base.area.entity.vo.AreaVo;
import com.light.base.area.service.AreaApiService;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.base.dictionary.service.DictionaryDataApiService;
import com.light.base.statistics.entity.bo.*;
import com.light.base.statistics.entity.vo.*;
import com.light.base.statistics.service.*;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.DatePeriodUtil;
import com.light.base.enums.ClientEnum;
import com.light.base.enums.PlatformEnum;
import com.light.manage.job.statistics.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计相关接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@RestController
@RequestMapping("/statistics")
@Api(value = "", tags = "统计相关接口" )
public class StatisticsApiController {

    @Autowired
    private DayTotalStatisticsApiService dayTotalStatisticsApiService;

    @Autowired
    private DayStatisticsApiService dayStatisticsApiService;

    @Autowired
    private MonthStatisticsApiService monthStatisticsApiService;

    @Autowired
    private CityDayStatisticsApiService cityDayStatisticsApiService;

    @Autowired
    private SectionDayStatisticsApiService sectionDayStatisticsApiService;

    @Autowired
    private AreaApiService areaApiService;

    @Autowired
    private DictionaryDataApiService dictionaryDataApiService;

    private static final String sp = "_";

    /**
     * 获取总统计信息
     *
     * @param period 期
     * @return {@link AjaxResult}
     */
    @PostMapping("getSumTotalStatistics")
    @ApiOperation(value = "总量统计",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getSumTotalByCond(@RequestBody DatePeriodUtil.DatePeriod period){
        DatePeriodUtil.getDatePeriod(period);
        AjaxResult<DayTotalStatisticsVo> ajaxResult = this.dayTotalStatisticsApiService.getSumTotalByCond(period);
        return ajaxResult;
    }

    /**
     * 各端日活统计信息
     *
     * @param period 期
     * @return {@link AjaxResult}
     */
    @PostMapping("getClientDailyActiveStatistics")
    @ApiOperation(value = "各端日活统计",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getClientDailyActiveStatistics(@RequestBody DatePeriodUtil.DatePeriod period){
        DatePeriodUtil.getDatePeriod(period);
        DayStatisticsConditionBo conditionBo = new DayStatisticsConditionBo();
        final Date startTime = period.getStartTime();
        conditionBo.setStartDate(startTime);
        final Date endTime = period.getEndTime();
        conditionBo.setEndDate(endTime);

        final List<Date> dates = DatePeriodUtil.betweenDayDateList(startTime, endTime);


        AjaxResult<List<DayStatisticsVo>> ajaxResult = this.dayStatisticsApiService.getDayStatisticsListByCondition(conditionBo);
        if (ajaxResult.isSuccess()) {
            Map<String, Object> result = Maps.newHashMap();
            result.put("date", dates);
            final List<DayStatisticsVo> data = ajaxResult.getData();
            // 填充数据
            final List<DayStatisticsVo> dayStatisticsVos = this.fillDayStatisticsData(data, dates);
            result.put("data", dayStatisticsVos);
            return AjaxResult.success(result);
        }

        return ajaxResult;
    }

    /**
     * 填充每日各端统计数据
     *
     * @param data  数据
     * @param dates 日期
     * @return {@link List}<{@link DayStatisticsVo}>
     */
    private List<DayStatisticsVo> fillDayStatisticsData(List<DayStatisticsVo> data, List<Date> dates){
        // 按照平台+ 端口进行分组
        final Map<String, DayStatisticsVo> clientDateDataMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(data)) {
            Map<String, DayStatisticsVo> map = data.stream()
                    .collect(Collectors.toMap(x ->
                            this.buildGroupDayKey(x.getPlatform(), x.getClient(), x.getDateDay()), x -> x
                    ));
            clientDateDataMap.putAll(map);
        }

        final ClientEnum[] clientEnums = ClientEnum.values();
        final PlatformEnum[] platformEnums = PlatformEnum.values();

        final List<DayStatisticsVo> list = Arrays.stream(platformEnums).map(platformEnum -> {
            // 获取当天统计数据
            return Arrays.stream(clientEnums).map(clientEnum -> {
                final List<DayStatisticsVo> dayStatisticsVos = dates.stream().map(date -> {
                    // 获取map中是否存在 不存在即创建
                    String key = this.buildGroupDayKey(platformEnum.getVal(), clientEnum.getVal(), date);
                    DayStatisticsVo vo = clientDateDataMap.getOrDefault(key, initDayStatisticsVo(date, clientEnum.getVal(), platformEnum.getVal()));
                    return vo;
                }).collect(Collectors.toList());
                return dayStatisticsVos;
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());

        return list;

    }

    /**
     * 初始化每日统计
     *
     * @param date     日期
     * @param client   客户端
     * @param platform 平台
     * @return {@link DayStatisticsVo}
     */
    private DayStatisticsVo initDayStatisticsVo(Date date, Integer client, Integer platform){
        DayStatisticsVo vo = new DayStatisticsVo();
        vo.setDateDay(date);
        vo.setClient(client);
        vo.setPlatform(platform);
        vo.setPvNum(0L);
        vo.setUvNum(0L);
        vo.setViewDuration(0L);
        return vo;
    }


    /**
     * 平台 端口组成key
     * @return
     */
    private String buildGroupDayKey(Integer platform, Integer client, Date date){
        return platform + sp + client + sp + DateUtil.format(date, "yyyyMMdd");
    }

    /**
     * 日活统计信息
     *
     * @param period 期
     * @return {@link AjaxResult}
     */
    @PostMapping("getDailyActiveStatistics")
    @ApiOperation(value = "日活统计",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getDailyActiveStatistics(@RequestBody DatePeriodUtil.DatePeriod period){
        DatePeriodUtil.getDatePeriod(period);

        // 查询数据
        DayStatisticsConditionBo conditionBo = new DayStatisticsConditionBo();
        final Date startTime = period.getStartTime();
        conditionBo.setStartDate(startTime);
        final Date endTime = period.getEndTime();
        conditionBo.setEndDate(endTime);
        AjaxResult<List<DayStatisticsVo>> ajaxResult = this.dayStatisticsApiService.getDayStatisticsByCondForGroupDate(conditionBo);

        //数据填充开始
        if (ajaxResult.isSuccess()) {
            Map<String, Object> result = Maps.newHashMap();
            final List<Date> dates = DatePeriodUtil.betweenDayDateList(startTime, endTime);
            result.put("date", dates);
            final List<DayStatisticsVo> data = ajaxResult.getData();
            final List<DayStatisticsVo> list = this.fillDayStatisticsDataGroupDate(data, dates);
            result.put("data", list);
            return AjaxResult.success(result);
        }

        return ajaxResult;
    }

    /**
     * 填充日活统计数据
     *
     * @param data  数据
     * @param dates 日期
     * @return {@link List}<{@link DayStatisticsVo}>
     */
    private List<DayStatisticsVo> fillDayStatisticsDataGroupDate(List<DayStatisticsVo> data, List<Date> dates) {
        final Map<String, DayStatisticsVo> dateGroupMap = Maps.newHashMap();

        // 按照日期分组
        if(CollUtil.isNotEmpty(data)) {
            final Map<String, DayStatisticsVo> map = data.stream()
                    .collect(Collectors.toMap(x -> buildDailyActiveGroupKey(x.getDateDay()), x -> x));
            dateGroupMap.putAll(map);
        }
        // 填充日期
        final List<DayStatisticsVo> list = dates.stream().map(date -> {
            final String key = this.buildDailyActiveGroupKey(date);
            final DayStatisticsVo vo = dateGroupMap.getOrDefault(key, initDayStatisticsVo(date, null, null));
            return vo;
        }).collect(Collectors.toList());
        return list;
    }


    /**
     * 处理每日key
     *
     * @param date 日期
     * @return {@link String}
     */
    private String buildDailyActiveGroupKey(Date date){
        return DateUtil.format(date, "yyyyMMdd");
    }

    /**
     * 各端月活统计信息
     *
     * @param period 期
     * @return {@link AjaxResult}
     */
    @PostMapping("getClientMonthlyActiveStatistics")
    @ApiOperation(value = "各端月活统计",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getClientMonthlyActiveStatistics(@RequestBody DatePeriodUtil.DatePeriod period){
        DatePeriodUtil.getDatePeriod(period);
        MonthStatisticsConditionBo conditionBo = new MonthStatisticsConditionBo();
        final Date startTime = period.getStartTime();
        conditionBo.setStartMonth(DateUtil.format(startTime, "yyyy-MM"));
        final Date endTime = period.getEndTime();
        conditionBo.setEndMonth(DateUtil.format(endTime, "yyyy-MM"));
        AjaxResult<List<MonthStatisticsVo>> ajaxResult = this.monthStatisticsApiService.getMonthStatisticsListByCondition(conditionBo);

        if(ajaxResult.isSuccess()) {

            final List<MonthStatisticsVo> data = ajaxResult.getData();
            final List<Date> dates = DatePeriodUtil.betweenMonthDateList(startTime, endTime);
            Map<String, Object> result = Maps.newHashMap();
            result.put("date", dates);
            // 填充数据
            final List<MonthStatisticsVo> monthStatisticsVos = this.fillMonthStatisticsData(data, dates);
            result.put("data", monthStatisticsVos);
            return AjaxResult.success(result);
        }

        return ajaxResult;
    }

    /**
     * 填充每月各端统计数据
     *
     * @param data  数据
     * @param dates 日期
     * @return {@link List}<{@link MonthStatisticsVo}>
     */
    private List<MonthStatisticsVo> fillMonthStatisticsData(List<MonthStatisticsVo> data, List<Date> dates){
        // 按照平台+ 端口进行分组
        final Map<String, MonthStatisticsVo> clientDateDataMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(data)) {
            Map<String, MonthStatisticsVo> map = data.stream()
                    .collect(Collectors.toMap(x ->
                            this.buildGroupMonthKey(x.getPlatform(), x.getClient(), x.getDateMonth()), x -> x
                    ));
            clientDateDataMap.putAll(map);
        }

        final ClientEnum[] clientEnums = ClientEnum.values();
        final PlatformEnum[] platformEnums = PlatformEnum.values();

        final List<MonthStatisticsVo> list = Arrays.stream(platformEnums).map(platformEnum -> {
            // 获取当天统计数据
            return Arrays.stream(clientEnums).map(clientEnum -> {
                final List<MonthStatisticsVo> dayStatisticsVos = dates.stream().map(date -> {
                    // 获取map中是否存在 不存在即创建
                    final String month = DateUtil.format(date, "yyyy-MM");
                    String key = this.buildGroupMonthKey(platformEnum.getVal(), clientEnum.getVal(), month);
                    MonthStatisticsVo vo = clientDateDataMap.getOrDefault(key, initMonthStatisticsVo(month, clientEnum.getVal(), platformEnum.getVal()));
                    return vo;
                }).collect(Collectors.toList());
                return dayStatisticsVos;
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());

        return list;

    }

    /**
     * 平台 端口组成key
     * @return
     */
    private String buildGroupMonthKey(Integer platform, Integer client, String month){
        return platform + sp + client + sp + month;
    }

    /**
     * 初始化每日统计
     *
     * @param month     日期
     * @param client   客户端
     * @param platform 平台
     * @return {@link MonthStatisticsVo}
     */
    private MonthStatisticsVo initMonthStatisticsVo(String month, Integer client, Integer platform){
        MonthStatisticsVo vo = new MonthStatisticsVo();
        vo.setDateMonth(month);
        vo.setClient(client);
        vo.setPlatform(platform);
        vo.setPvNum(0L);
        vo.setUvNum(0L);
        vo.setViewDuration(0L);
        return vo;
    }


    /**
     * 月活统计信息
     *
     * @param period 期
     * @return {@link AjaxResult}
     */
    @PostMapping("getMonthlyActiveStatistics")
    @ApiOperation(value = "月活统计信息",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getMonthlyActiveStatistics(@RequestBody DatePeriodUtil.DatePeriod period){
        DatePeriodUtil.getDatePeriod(period);
        MonthStatisticsConditionBo conditionBo = new MonthStatisticsConditionBo();
        final Date startTime = period.getStartTime();
        conditionBo.setStartMonth(DateUtil.format(startTime, "yyyy-MM"));
        final Date endTime = period.getEndTime();
        conditionBo.setEndMonth(DateUtil.format(endTime, "yyyy-MM"));
        AjaxResult<List<MonthStatisticsVo>> ajaxResult = this.monthStatisticsApiService.getMonthStatisticsByCondForGroupDate(conditionBo);

        //数据填充开始
        if (ajaxResult.isSuccess()) {
            final List<Date> dates = DatePeriodUtil.betweenMonthDateList(startTime, endTime);
            Map<String, Object> result = Maps.newHashMap();
            result.put("date", dates);
            final List<MonthStatisticsVo> data = ajaxResult.getData();
            final List<MonthStatisticsVo> list = this.fillMonthStatisticsDataGroupDate(data, dates);
            result.put("data", list);
            return AjaxResult.success(result);
        }
        return ajaxResult;
    }

    /**
     * 填充月活统计数据
     *
     * @param data  数据
     * @param dates 日期
     * @return {@link List}<{@link MonthStatisticsVo}>
     */
    private List<MonthStatisticsVo> fillMonthStatisticsDataGroupDate(List<MonthStatisticsVo> data, List<Date> dates) {
        final Map<String, MonthStatisticsVo> dateGroupMap = Maps.newHashMap();

        // 按照日期分组
        if(CollUtil.isNotEmpty(data)) {
            final Map<String, MonthStatisticsVo> map = data.stream()
                    .collect(Collectors.toMap(x -> x.getDateMonth(), x -> x));
            dateGroupMap.putAll(map);
        }
        // 填充日期
        final List<MonthStatisticsVo> list = dates.stream().map(date -> {
            final String key = DateUtil.format(date, "yyyy-MM");
            final MonthStatisticsVo vo = dateGroupMap.getOrDefault(key, initMonthStatisticsVo(key, null, null));
            return vo;
        }).collect(Collectors.toList());
        return list;
    }


    /**
     * 获取城市统计数据
     *
     * @param bo 薄
     * @return {@link AjaxResult}
     */
    @PostMapping("getCityStatistics")
    @ApiOperation(value = "城市统计",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getCityStatistics(@RequestBody CityDayStatisticsConditionBo bo){
        final Long provinceId = bo.getProvinceId();
        if(provinceId == null){
            return AjaxResult.fail("参数错误");
        }
        DatePeriodUtil.DatePeriod period = new DatePeriodUtil.DatePeriod();
        period.setOffset(bo.getOffset());
        period.setStartTime(bo.getStartTime());
        period.setEndTime(bo.getEndTime());
        DatePeriodUtil.getDatePeriod(period);

        bo.setStartTime(period.getStartTime());
        bo.setEndTime(period.getEndTime());


        final AjaxResult<List<CityDayStatisticsVo>> cityListDataReps = this.cityDayStatisticsApiService.getListByCondForGroupCity(bo);

        if(cityListDataReps.isSuccess()){

            final List<AreaVo> areaVos = fetchAreaByPid(provinceId);
            Map<String, Object> result = Maps.newHashMap();
            result.put("areaVoList", areaVos);
            final List<CityDayStatisticsVo> data = cityListDataReps.getData();
            // 填充各城市数据
            final List<CityDayStatisticsVo> list = this.fillCityStatisticsData(areaVos, data);
            result.put("data", list);
            return AjaxResult.success(result);
        }

        return cityListDataReps;
    }

    /**
     * 填补城市统计数据
     *
     * @param areaVos 区域vos
     * @param data    数据
     * @return {@link List}<{@link CityDayStatisticsVo}>
     */
    private List<CityDayStatisticsVo> fillCityStatisticsData(List<AreaVo> areaVos, List<CityDayStatisticsVo> data) {
        // 现有数据 根据城市ID 转map
        Map<Long, CityDayStatisticsVo> cityIdDataMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(data)){
            final Map<Long, CityDayStatisticsVo> cityIdMap
                    = data.stream().collect(Collectors.toMap(x -> x.getCityId(), x -> x));
            cityIdDataMap.putAll(cityIdMap);
        }

        // 循环城市信息  填充 各城市数据
        final List<CityDayStatisticsVo> list = areaVos.stream().map(city -> {
            final Long id = city.getId();

            final CityDayStatisticsVo vo = cityIdDataMap.getOrDefault(id,
                    this.initCityDayStatisticsVo(id, city.getAreaName()));

            return vo;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 初始化城市VO
     *
     * @param cityId   城市标识
     * @param cityName 城市名字
     * @return {@link CityDayStatisticsVo}
     */
    private CityDayStatisticsVo initCityDayStatisticsVo(Long cityId,String cityName){
        CityDayStatisticsVo vo = new CityDayStatisticsVo();
        vo.setCityId(cityId);
        vo.setCityName(cityName);
        vo.setPvNum(0L);
        vo.setUvNum(0L);
        vo.setViewDuration(0L);
        return vo;
    }

    /**
     * 获取城市ID数据
     *
     * @param provinceId 省id
     * @return {@link List}<{@link AreaVo}>
     */
    private List<AreaVo> fetchAreaByPid(Long provinceId) {
        AreaConditionBo areaBo = new AreaConditionBo();
        areaBo.setParentAreaId(provinceId);
        final AjaxResult areaResp = this.areaApiService.getAreaListByCondition(areaBo);
        final Object areaObjectData = areaResp.getData();
        if(ObjectUtil.isNotEmpty(areaObjectData)) {
            final Map<String, Object> areaData = (Map<String, Object>) areaObjectData;
            final Object list = areaData.get("list");
            if(ObjectUtil.isNotEmpty(list)) {
                return JSONUtil.toList(JSONUtil.toJsonStr(list), AreaVo.class);
            }
        }
        return Lists.newArrayList();
    }


    /**
     * 获取学段统计数据
     *
     * @param bo 薄
     * @return {@link AjaxResult}
     */
    @PostMapping("getSectionStatistics")
    @ApiOperation(value = "学段统计",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getSectionStatistics(@RequestBody SectionDayStatisticsConditionBo bo){

        final Integer userIdentityType = bo.getUserIdentityType();
        if (userIdentityType == null) {
            return AjaxResult.fail("身份不能为空");
        }

        DatePeriodUtil.DatePeriod period = new DatePeriodUtil.DatePeriod();
        period.setOffset(bo.getOffset());
        period.setStartTime(bo.getStartTime());
        period.setEndTime(bo.getEndTime());
        DatePeriodUtil.getDatePeriod(period);

        bo.setStartTime(period.getStartTime());
        bo.setEndTime(period.getEndTime());

        final AjaxResult<List<SectionDayStatisticsVo>> ajaxResult = this.sectionDayStatisticsApiService.getListByCondForGroupSection(bo);

        if (ajaxResult.isSuccess()) {

            final List<DictionaryDataVo> dictionaryDataVos = this.fetchSectionData();
            Map<String, Object> result = Maps.newHashMap();
            result.put("section", dictionaryDataVos);

            // 填充学段数据
            final List<SectionDayStatisticsVo> data = ajaxResult.getData();
            final List<SectionDayStatisticsVo> list = this.fetchSectionStatisticsData(userIdentityType, dictionaryDataVos, data);
            result.put("data", list);

            return AjaxResult.success(result);
        }

        return ajaxResult;
    }

    /**
     * 填充 学段统计数据
     *
     * @param userIdentityType  用户身份类型
     * @param dictionaryDataVos 字典数据vos
     * @param data              数据
     * @return {@link List}<{@link SectionDayStatisticsVo}>
     */
    private List<SectionDayStatisticsVo> fetchSectionStatisticsData(Integer userIdentityType,
                                                                         List<DictionaryDataVo> dictionaryDataVos,
                                                                         List<SectionDayStatisticsVo> data) {
        // 转sectionMap
        final Map<String, SectionDayStatisticsVo> sectionDataMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty(data)) {
            final Map<String, SectionDayStatisticsVo> dataMap = data.stream().collect(Collectors.toMap(x -> x.getSection(), x -> x));
            sectionDataMap.putAll(dataMap);
        }

        // 循环比对填充
        List<SectionDayStatisticsVo> list = dictionaryDataVos.stream().map(x-> {
            final String section = x.getDictValue();
            final String sectionName = x.getDictLabel();
            final SectionDayStatisticsVo vo = sectionDataMap.getOrDefault(section,
                    this.initSectionStatisticsVo(section, sectionName, userIdentityType));
            return vo;
        }).collect(Collectors.toList());

        return list;
    }

    /**
     * 初始化学段统计VO
     *
     * @param section      部分
     * @param sectionName  部分名字
     * @param identityType 身份类型
     * @return {@link SectionDayStatisticsVo}
     */
    private  SectionDayStatisticsVo initSectionStatisticsVo(String section, String sectionName, Integer identityType){
        SectionDayStatisticsVo vo = new SectionDayStatisticsVo();
        vo.setSection(section);
        vo.setSectionName(sectionName);
        vo.setUserIdentityType(identityType);
        vo.setPvNum(0L);
        vo.setUvNum(0L);
        vo.setViewDuration(0L);
        return vo;
    }


    /**
     * 获取学段信息
     *
     * @return {@link List}<{@link DictionaryDataVo}>
     */
    private List<DictionaryDataVo> fetchSectionData() {
        DictionaryDataListConditionBo dictDataBo = new DictionaryDataListConditionBo();
        dictDataBo.setDictType("section");
        final AjaxResult<List<DictionaryDataVo>> dictResp = this.dictionaryDataApiService.getAvailableList(dictDataBo);
        final List<DictionaryDataVo> data = dictResp.getData();
        return data;
    }


    /**
     * 手动分析统计数据
     *
     * @return {@link AjaxResult}
     */
    @GetMapping("analysisStatistics")
    public AjaxResult analysisStatistics(){
        final DateTime yesterday = DateUtil.date();
        // 非用户相关维度统计
        DayStatisticsHandler dayStatisticsHandler = new DayStatisticsHandler();
        DayTotalStatisticsHandler dayTotalStatisticsHandler = new DayTotalStatisticsHandler();
        MonthDayStatisticsHandler monthDayStatisticsHandler = new MonthDayStatisticsHandler();
        dayTotalStatisticsHandler.setNext(monthDayStatisticsHandler);
        dayStatisticsHandler.setNext(dayTotalStatisticsHandler);
        dayStatisticsHandler.handleRequest(DateUtil.date());

        final DayStatisticsRedisClearHandler redisClearHandler = new DayStatisticsRedisClearHandler();
        redisClearHandler.handleRequest(yesterday);

        // 用户相关维度统计

        UserDayStatisticsHandler userDayStatisticsHandler = new UserDayStatisticsHandler();
        CityDayStatisticsHandler cityDayStatisticsHandler = new CityDayStatisticsHandler();
        SectionStatisticsHandler sectionStatisticsHandler = new SectionStatisticsHandler();
        sectionStatisticsHandler.setNext(cityDayStatisticsHandler);
        userDayStatisticsHandler.setNext(sectionStatisticsHandler);
        userDayStatisticsHandler.handleRequest(yesterday);

        final UserDayStatisticsRedisClearHandler redisClear = new UserDayStatisticsRedisClearHandler();
        redisClear.handleRequest(yesterday);

        return AjaxResult.success();
    }
}
