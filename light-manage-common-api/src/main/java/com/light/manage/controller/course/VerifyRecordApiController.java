package com.light.manage.controller.course;

import com.light.course.verify.service.VerifyRecordApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.light.course.verify.entity.bo.VerifyRecordConditionBo;
import com.light.course.verify.entity.bo.VerifyRecordBo;
import com.light.course.verify.entity.vo.VerifyRecordVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 审核记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-27 09:28:37
 */
@RestController
@RequestMapping("/verify/record")
@Validated
@Api(value = "", tags = "审核记录接口" )
public class VerifyRecordApiController {

    @Autowired
    private VerifyRecordApiService verifyRecordApiService;

    /**
     * 查询审核记录分页列表
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询审核记录列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getVerifyRecordPageListByCondition(@RequestBody VerifyRecordConditionBo condition){
        PageInfo<VerifyRecordVo> page = verifyRecordApiService.getVerifyRecordPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询审核记录列表
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询审核记录列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult<List<VerifyRecordVo>> getVerifyRecordListByCondition(@RequestBody VerifyRecordConditionBo condition){
        List<VerifyRecordVo> list = verifyRecordApiService.getVerifyRecordListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增审核记录
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增审核记录",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增审核记录", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addVerifyRecord(@Validated @RequestBody VerifyRecordBo verifyRecordBo){
        return verifyRecordApiService.addVerifyRecord(verifyRecordBo);
    }

    /**
     * 修改审核记录
     * @param verifyRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新审核记录",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "更新审核记录", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateVerifyRecord(@Validated @RequestBody VerifyRecordBo verifyRecordBo) {
        if(null == verifyRecordBo.getId()) {
            return AjaxResult.fail("审核记录id不能为空");
        }
        return verifyRecordApiService.updateVerifyRecord(verifyRecordBo);
    }

    /**
     * 查询审核记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询审核记录详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "审核记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<VerifyRecordVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("审核记录id不能为空");
        }
        VerifyRecordVo vo = verifyRecordApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("verifyRecordVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除审核记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-27 09:28:37
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除审核记录",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "审核记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    @OperationLogAnnotation(moduleName = "删除审核记录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        return verifyRecordApiService.delete(id);
    }
}
