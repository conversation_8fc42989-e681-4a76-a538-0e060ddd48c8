package com.light.manage.controller.invoice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.base.enums.InvoiceEnums;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordBo;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordConditionBo;
import com.light.base.invoice.entity.vo.InvoiceObsoleteRecordExcelVo;
import com.light.base.invoice.entity.vo.InvoiceObsoleteRecordVo;
import com.light.base.invoice.entity.vo.InvoiceRecordExcelVo;
import com.light.base.invoice.service.InvoiceObsoleteRecordApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.redis.utils.ExcelUtils;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.light.core.text.CharsetKit.UTF_8;

/**
 * 发票申请作废记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-23 14:10:39
 */
@RestController
@Validated
@RequestMapping("invoice/obsolete/record")
@Api(value = "", tags = "发票申请作废记录接口" )
public class InvoiceObsoleteRecordApiController {

    @Autowired
    private InvoiceObsoleteRecordApiService invoiceObsoleteRecordApiService;

    /**
     * 查询发票申请作废记录分页列表
     * <AUTHOR>
     * @date 2023-11-23 14:10:39
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询发票申请作废记录列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getInvoiceObsoleteRecordPageListByCondition(@RequestBody InvoiceObsoleteRecordConditionBo condition){
        PageInfo<InvoiceObsoleteRecordVo> page = invoiceObsoleteRecordApiService.getInvoiceObsoleteRecordPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询发票申请作废记录列表
     * <AUTHOR>
     * @date 2023-11-23 14:10:39
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询发票申请作废记录列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getInvoiceObsoleteRecordListByCondition(@RequestBody InvoiceObsoleteRecordConditionBo condition){
        List<InvoiceObsoleteRecordVo> list = invoiceObsoleteRecordApiService.getInvoiceObsoleteRecordListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增发票申请作废记录
     * <AUTHOR>
     * @date 2023-11-23 14:10:39
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增发票申请作废记录",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增发票申请作废记录", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addInvoiceObsoleteRecord(@RequestBody InvoiceObsoleteRecordBo invoiceObsoleteRecordBo){
        return invoiceObsoleteRecordApiService.addInvoiceObsoleteRecord(invoiceObsoleteRecordBo);
    }

    /**
     * 修改发票申请作废记录
     * @param invoiceObsoleteRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-23 14:10:39
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新发票申请作废记录",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "更新发票申请作废记录", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateInvoiceObsoleteRecord(@RequestBody InvoiceObsoleteRecordBo invoiceObsoleteRecordBo) {
        if(null == invoiceObsoleteRecordBo.getId()) {
            return AjaxResult.fail("发票申请作废记录id不能为空");
        }
        return invoiceObsoleteRecordApiService.updateInvoiceObsoleteRecord(invoiceObsoleteRecordBo);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出",httpMethod = SystemConstants.POST_REQUEST)
    public void export(@RequestBody InvoiceObsoleteRecordConditionBo condition, HttpServletResponse response)  {

        List<InvoiceObsoleteRecordVo> list = invoiceObsoleteRecordApiService.getInvoiceObsoleteRecordListByCondition(condition).getData();
        if(CollUtil.isEmpty(list)){
            list = Lists.newArrayList();
        }
        final List<InvoiceObsoleteRecordExcelVo> recordExcelVoList = list.stream().map(x -> {
            final InvoiceObsoleteRecordExcelVo bean = BeanUtil.toBean(x, InvoiceObsoleteRecordExcelVo.class);

            // 申请时间
            String applyTime = "";
            if(x.getApplyTime() != null){
                applyTime = DateUtil.formatDateTime( x.getApplyTime());
            }
            bean.setApplyTime(applyTime);

            return bean;

        }).collect(Collectors.toList());
        try {
            String filename = "发票作废记录";
            ExcelUtils.exportExcel(recordExcelVoList,"发票作废记录","发票作废记录", InvoiceObsoleteRecordExcelVo.class, filename,response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询发票申请作废记录详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-23 14:10:39
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询发票申请作废记录详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "发票申请作废记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<InvoiceObsoleteRecordVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("发票申请作废记录id不能为空");
        }
        InvoiceObsoleteRecordVo vo = invoiceObsoleteRecordApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("invoiceObsoleteRecordVo", vo);
        return AjaxResult.success(map);
    }


    /**
     * 删除发票申请作废记录
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-23 14:10:39
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除发票申请作废记录",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "发票申请作废记录id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    @OperationLogAnnotation(moduleName = "删除发票申请作废记录", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return invoiceObsoleteRecordApiService.delete(id);
    }

}
