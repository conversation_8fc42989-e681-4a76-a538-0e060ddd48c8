package com.light.manage.controller.log;

import com.github.pagehelper.IPage;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.StringUtils;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.log.entity.OperationLog;
import com.light.log.operation.entity.bo.OperationLogConditionBo;
import com.light.log.operation.entity.vo.OperationLogVo;
import com.light.log.operation.service.OperationLogApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/5/1216:05
 */
@RestController
@RequestMapping("/operation/log")
@Validated
@Api(value = "", tags = "操作日志接口")
public class OperationLogApiController {

    @Autowired
    private OperationLogApiService operationLogApiService;

    /**
     * 查询操作日志表列表
     * <AUTHOR>
     * @date 2022-05-12 15:49:37
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询操作日志表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getOperationLogListByCondition(@RequestBody OperationLogConditionBo condition){
        return operationLogApiService.getOperationLogListByCondition(condition);
    }

    /**
     * 分页按月查询操作日志列表
     * @param condition
     * @return
     */
    @PostMapping("/month/list")
    @ApiOperation(value = "分页按月查询操作日志表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getOperationMonthListByCondition(@RequestBody OperationLogConditionBo condition) {
        if(StringUtils.isEmpty(condition.getMonth())) {
            return operationLogApiService.getOperationLogListByCondition(condition);
        }else{
            PageInfo<OperationLogVo> page = operationLogApiService.getOperationMonthListByCondition(condition).getData();
            return AjaxResult.success(page.getList(), page.getTotal(), page.getPageNum(), page.getPageSize());
        }
    }

    /**
     * 查询操作日志表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-12 15:49:37
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询操作日志表详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "操作日志表id", required = true, dataType = "Long", paramType = "query")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long id) {
        return operationLogApiService.getDetail(id);
    }

    /**
     * 删除操作日志表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-12 15:49:37
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除操作日志表",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "操作日志表id", required = true, dataType = "Long", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除操作日志", operationType = OperationLogConstants.OP_TYPE_DELETE)
    public AjaxResult delete(@NotNull(message = "请选择数据") Long id) {
        return operationLogApiService.delete(id);
    }
}
