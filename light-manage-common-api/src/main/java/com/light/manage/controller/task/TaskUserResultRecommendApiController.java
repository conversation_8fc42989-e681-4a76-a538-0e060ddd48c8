package com.light.manage.controller.task;

import com.light.course.task.entity.bo.TaskUserResultRecommendBatchBo;
import com.light.course.task.service.TaskUserUserResultRecommendApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.light.course.task.entity.bo.TaskUserResultRecommendConditionBo;
import com.light.course.task.entity.bo.TaskUserResultRecommendBo;
import com.light.course.task.entity.vo.TaskUserResultRecommendVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 作业作品推荐
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-30 10:10:39
 */
@RestController
@Validated
@RequestMapping("/task/user/result/recommend")
@Api(value = "", tags = "作业案例推荐接口" )
public class TaskUserResultRecommendApiController {

    @Autowired
    private TaskUserUserResultRecommendApiService taskResultRecommendApiService;

    /**
     * 查询作业作品推荐分页列表
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getTaskResultUserRecommendPageListByCondition(@RequestBody TaskUserResultRecommendConditionBo condition){
        PageInfo<TaskUserResultRecommendVo> page = taskResultRecommendApiService.getTaskUserResultRecommendPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询作业作品推荐列表
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getTaskUserResultRecommendListByCondition(@RequestBody TaskUserResultRecommendConditionBo condition){
        List<TaskUserResultRecommendVo> list = taskResultRecommendApiService.getTaskUserResultRecommendListByCondition(condition).getData();
        return AjaxResult.success(list);
    }


    /**
     * 删除并批量新增
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/delAndSaveBatch")
    @ApiOperation(value = "删除并批量新增",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "删除并批量新增", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delAndSaveBatch(@RequestBody TaskUserResultRecommendBatchBo resultRecommendBatchBo){
        return this.taskResultRecommendApiService.delAndSaveBatch(resultRecommendBatchBo);
    }

    /**
     * 新增作业作品推荐
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增作业案例推荐",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增作业案例推荐", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addTaskUserResultRecommend(@RequestBody TaskUserResultRecommendBo taskUserResultRecommendBo){
        return taskResultRecommendApiService.addTaskUserResultRecommend(taskUserResultRecommendBo);
    }


    /**
     * 更改序号
     * <AUTHOR>
     * @param changeId 被更换ID
     * @param id 需要更换的ID
     * @date 2022-06-30 10:10:39
     */
    @GetMapping("/changeSort")
    @ApiOperation(value = "更改序号",httpMethod = SystemConstants.GET_REQUEST)
    @OperationLogAnnotation(moduleName = "更改序号", operationType = OperationLogConstants.OP_TYPE_INSERT, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult changeSort(@RequestParam("id") Long id, @RequestParam("changeId") Long changeId){
        return taskResultRecommendApiService.changeSort(id,changeId);
    }

    /**
     * 修改作业作品推荐
     * @param taskUserResultRecommendBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新推荐",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "更新推荐", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateTaskUserResultRecommend(@RequestBody TaskUserResultRecommendBo taskUserResultRecommendBo) {
        if(null == taskUserResultRecommendBo.getId()) {
            return AjaxResult.fail("推荐id不能为空");
        }
        return taskResultRecommendApiService.updateTaskUserResultRecommend(taskUserResultRecommendBo);
    }

    /**
     * 查询作业作品推荐详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询推荐详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "作业案例推荐id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<TaskUserResultRecommendVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("作业案例推荐id不能为空");
        }
        TaskUserResultRecommendVo vo = taskResultRecommendApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("taskResultRecommendVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除作业作品推荐
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-06-30 10:10:39
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除推荐",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "作业作品推荐id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    @OperationLogAnnotation(moduleName = "删除推荐", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return taskResultRecommendApiService.delete(id);
    }
}
