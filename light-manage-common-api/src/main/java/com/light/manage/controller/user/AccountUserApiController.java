package com.light.manage.controller.user;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.log.constants.OperationLogConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.user.account.api.AccountUserApi;
import com.light.user.account.entity.bo.AccountUserBo;
import com.light.user.account.entity.bo.AccountUserConditionBo;
import com.light.user.account.entity.vo.AccountUserVo;
import com.light.user.account.service.AccountUserApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/179:32
 */
@RestController
@RequestMapping("/account/user")
@Validated
@Api(value = "", tags = "账号用户关系接口" )
public class AccountUserApiController {

    @Autowired
    private AccountUserApiService accountUserApiService;

    /**
     * 查询账号用户关系列表
     * <AUTHOR>
     * @date 2022-03-02 11:16:28
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询账号用户关系",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getAccountUserListByCondition(@RequestBody AccountUserConditionBo condition){
        return accountUserApiService.getAccountUserListByCondition(condition);
    }


    /**
     * 新增账号用户关系
     * <AUTHOR>
     * @date 2022-03-02 11:16:28
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增账号用户关系",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "新增账号用户关系", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addAccountUser(@Validated @RequestBody AccountUserBo accountUserBo){
        return accountUserApiService.addAccountUser(accountUserBo);
    }

    /**
     * 修改账号用户关系
     * @param accountUserBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-02 11:16:28
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改账号用户关系",httpMethod = SystemConstants.POST_REQUEST)
    @OperationLogAnnotation(moduleName = "修改账号用户关系", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateAccountUser(@Validated @RequestBody AccountUserBo accountUserBo) {
        return accountUserApiService.updateAccountUser(accountUserBo);
    }

    /**
     * 查询账号用户关系详情
     * @param accountUserId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-02 11:16:28
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询账号用户关系详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "accountUserId", value = "账号用户关系id", required = true, dataType = "Long", paramType = "query")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long accountUserId) {
        return accountUserApiService.getDetail(accountUserId);
    }

    /**
     * 删除账号用户关系
     * @param accountUserId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-02 11:16:28
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除账号用户关系",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "accountUserId", value = "账号用户关系id", required = true, dataType = "Long", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除账号用户关系", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long accountUserId) {
        return accountUserApiService.delete(accountUserId);
    }
}
