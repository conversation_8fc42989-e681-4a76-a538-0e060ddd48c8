package com.light.manage.job.statistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.light.base.statistics.entity.bo.DayStatisticsBo;
import com.light.base.statistics.entity.bo.MonthStatisticsBo;
import com.light.base.statistics.entity.bo.MonthStatisticsConditionBo;
import com.light.base.statistics.entity.vo.MonthStatisticsVo;
import com.light.base.statistics.service.MonthStatisticsApiService;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.SpringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/12/28
 */
@Slf4j
public class MonthDayStatisticsHandler extends StatisticsHandler<List<DayStatisticsBo>>{

    private final  MonthStatisticsApiService monthStatisticsApiService;

    {
        monthStatisticsApiService = SpringUtils.getBean(MonthStatisticsApiService.class);
    }

    @Override
    public void handleRequest(List<DayStatisticsBo> dayStatisticsBos) {

        log.info(" ================= 非用户统计=》 每月统计 开始处理 =========================");
        // 处理月份统计数据
        final List<MonthStatisticsBo> monthStatisticsBoList = dayStatisticsBos.stream().map(this::buildMonthStatistics).collect(Collectors.toList());

        if(CollUtil.isNotEmpty(monthStatisticsBoList)){
            log.info(" ================= 非用户统计=》 每月统计： 入库处理 =========================");

            this.monthStatisticsApiService.batchSaveOrUpdate(monthStatisticsBoList);
        }
        log.info(" ================= 非用户统计=》 每月统计 结束处理 =========================");

        this.after(dayStatisticsBos);
    }


    /**
     * 构建处理月统计数据
     *
     * @param dayStatisticsBo 天统计
     * @return {@link MonthStatisticsBo}
     */
    private MonthStatisticsBo buildMonthStatistics( DayStatisticsBo dayStatisticsBo) {

        // 查询月份数据
        MonthStatisticsConditionBo bo = new MonthStatisticsConditionBo();
        bo.setClient(dayStatisticsBo.getClient());
        bo.setPlatform(dayStatisticsBo.getPlatform());
        bo.setDateMonth(dayStatisticsBo.getDateMonth());
        final AjaxResult<List<MonthStatisticsVo>> monthReps = this.monthStatisticsApiService.getMonthStatisticsListByCondition(bo);
        final List<MonthStatisticsVo> monthList = monthReps.getData();
        MonthStatisticsVo monthStatisticsVo = null;
        if(CollUtil.isNotEmpty(monthList)){
            monthStatisticsVo = monthList.get(0);
        }

        // 不存在 初始化
        if(monthStatisticsVo == null){
            monthStatisticsVo = new MonthStatisticsVo();
            monthStatisticsVo.setPvNum(0L);
            monthStatisticsVo.setUvNum(0L);
            monthStatisticsVo.setViewDuration(0L);
        }
        final MonthStatisticsBo monthStatisticsBo = BeanUtil.toBean(monthStatisticsVo, MonthStatisticsBo.class);
        monthStatisticsBo.setDateMonth(dayStatisticsBo.getDateMonth());
        monthStatisticsBo.setPlatform(dayStatisticsBo.getPlatform());
        monthStatisticsBo.setClient(dayStatisticsBo.getClient());
        // 叠加统计数据
        monthStatisticsBo.setPvNum(monthStatisticsBo.getPvNum() + dayStatisticsBo.getPvNum());
        monthStatisticsBo.setUvNum(monthStatisticsBo.getUvNum() + dayStatisticsBo.getUvNum());
        monthStatisticsBo.setViewDuration(monthStatisticsBo.getViewDuration() + dayStatisticsBo.getViewDuration());

        return monthStatisticsBo;
    }
}
