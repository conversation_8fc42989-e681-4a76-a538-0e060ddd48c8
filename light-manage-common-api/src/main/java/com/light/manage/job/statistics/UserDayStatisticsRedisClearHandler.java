package com.light.manage.job.statistics;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Maps;
import com.light.core.utils.SpringUtils;
import com.light.base.enums.ClientEnum;
import com.light.base.enums.PlatformEnum;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/12/28
 */
@Slf4j
public class UserDayStatisticsRedisClearHandler extends StatisticsHandler<DateTime> {

    private final RedisComponent redisComponent;

    {
        redisComponent = SpringUtils.getBean(RedisComponent.class);
    }


    @Override
    public void handleRequest(DateTime dateTime) {
        log.info("============= 用户统计 缓存清除 =====================");
        final ClientEnum[] clientEnums = ClientEnum.values();
        final PlatformEnum[] platformEnums = PlatformEnum.values();
        final String dateStr = dateTime.toString("yyyyMMdd");
        Map<String, String> param = Maps.newHashMap();
        param.put("date", dateStr);
        Arrays.stream(platformEnums).forEach(platformEnum -> {
            final int platform = platformEnum.getVal();
            param.put("platform", platform + "");

            Arrays.stream(clientEnums).forEach(x -> {
                final int client = x.getVal();
                param.put("client", client + "");
                String userCountKey = RedisKeyEnum.USER_COUNT_DAY_PLATFORM_CLIENT_CACHE.getKey(param);
                final Set<Object> userOidObjSet = this.redisComponent.sGet(userCountKey);
                if (CollUtil.isNotEmpty(userOidObjSet)) {
                    userOidObjSet.stream().filter(userOidObj-> ObjectUtil.isNotEmpty(userOidObj)).forEach(userOidObj-> {
                        // 删除具体某个用户的缓存
                        param.put("userOid", userOidObj.toString());
                        String uvKey = RedisKeyEnum.UV_DAY_PLATFORM_CLIENT_CACHE.getKey(param);
                        String duKey = RedisKeyEnum.DURATION_USER_DAY_PLATFORM_CLIENT_CACHE.getKey(param);
                        this.redisComponent.del(uvKey, duKey);
                    });
                    // 删除某个平台用户统计数据key
                    this.redisComponent.del(userCountKey);
                }
            });
        });
        log.info("============= 用户统计 缓存清除结束 =====================");
    }


}
