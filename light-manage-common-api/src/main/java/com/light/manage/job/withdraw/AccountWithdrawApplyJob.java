package com.light.manage.job.withdraw;

import com.light.core.entity.AjaxResult;
import com.light.user.withdraw.service.AccountWithdrawApplyApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/6/7
 */
@Slf4j
@Component
public class AccountWithdrawApplyJob {

    @Resource
    private AccountWithdrawApplyApiService accountWithdrawApplyApiService;


    /**
     *  每天凌晨 待审核账号注销申请 超过失效实现 更改为已失效
     *
     */
    @Scheduled(cron = "01 00 00 * * * ")
    public void applyExpired(){
        AjaxResult<Integer> ajaxResult = this.accountWithdrawApplyApiService.expiredApply();
        log.debug("账号注销定时任务，执行数量：" + ajaxResult.getData());
    }
}
