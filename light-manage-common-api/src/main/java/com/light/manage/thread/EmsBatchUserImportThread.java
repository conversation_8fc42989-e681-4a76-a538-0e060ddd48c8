package com.light.manage.thread;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.light.activity.activity.service.ActivityApiService;
import com.light.activity.constants.ActivityConstants;
import com.light.activity.signupInfo.entity.bo.SignupInfoBo;
import com.light.activity.signupInfo.entity.bo.SignupInfoConditionBo;
import com.light.activity.signupInfo.entity.vo.SignupInfoVo;
import com.light.activity.signupInfo.service.SignupInfoApiService;
import com.light.core.entity.AjaxResult;
import com.light.core.entity.Progress;
import com.light.exam.batch.entity.bo.EmsBatchUserBo;
import com.light.exam.batch.service.EmsBatchUserApiService;
import com.light.redis.component.RedisComponent;
import com.light.user.account.entity.vo.AccountVo;
import com.light.user.account.service.AccountApiService;
import com.light.user.user.entity.bo.UserBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/9/21
 */
@Slf4j
public class EmsBatchUserImportThread extends Thread {

    private RedisComponent redisComponent = SpringUtil.getBean(RedisComponent.class);

    private SignupInfoApiService signupInfoApiService = SpringUtil.getBean(SignupInfoApiService.class);

    private EmsBatchUserApiService emsBatchUserApiService = SpringUtil.getBean(EmsBatchUserApiService.class);

    private AccountApiService accountApiService = SpringUtil.getBean(AccountApiService.class);

    private String redisKey;

    List<EmsBatchUserBo> users;

    private Long activityId;

    public EmsBatchUserImportThread(String redisKey, List<EmsBatchUserBo> users, Long activityId) {
        this.redisKey = redisKey;
        this.users = users;
        this.activityId = activityId;
    }

    @Override
    @Async
    public void run() {
        Object object = redisComponent.get(redisKey);
        Progress progress = JSON.parseObject(object.toString(), Progress.class);
        Integer total = progress.getTotal();
        Integer successCount = progress.getSuccess();
        Integer failureCount = progress.getFail();
        StringBuilder errorMessage = new StringBuilder("");
        SignupInfoConditionBo condition = new SignupInfoConditionBo();
        condition.setRelationType(ActivityConstants.SINGUP_FORM_TYPE_ACTIVITY);
        condition.setState(ActivityConstants.SINGUP_INFO_NORMAL);
        condition.setRelationId(activityId);
        for(EmsBatchUserBo user : users) {
            try{
                //查询用户账号
                List<AccountVo> accounts = accountApiService.getAccountByUserOid(user.getUserOid()).getList(AccountVo.class);
                if(!CollectionUtils.isEmpty(accounts)) {
                    user.setAccountName(accounts.get(0).getAccountName());
                }else{
                    failureCount++;
                    errorMessage.append("第"+ (successCount + failureCount) +"条数据分配场次失败，原因是：未查到该用户的账号信息\n");
                    updateProgress(progress, successCount, failureCount, total);
                    continue;
                }
                //根据用户和活动查询报名表单信息
                condition.setUserOid(user.getUserOid());
                SignupInfoVo signupInfoVo = signupInfoApiService.getSignupInfoByCondition(condition).getData();
                if(signupInfoVo != null) {
                    user.setInfoContent(signupInfoVo.getInfoContent());
                }else{
                    failureCount++;
                    errorMessage.append("第"+ (successCount + failureCount) +"条数据分配场次失败，原因是：未查到该用户的活动报名信息\n");
                    updateProgress(progress, successCount, failureCount, total);
                    continue;
                }
                //更新或者保存用户场次信息
                AjaxResult result = emsBatchUserApiService.saveBatchUser(user);
                if(result.isSuccess()) {
                    successCount++;
                    updateProgress(progress, successCount, failureCount, total);
                }else{
                    failureCount++;
                    errorMessage.append("第"+ (successCount + failureCount) +"条数据分配场次失败，原因是："+result.getMsg()+"\n");
                    updateProgress(progress, successCount, failureCount, total);
                }
            }catch (Exception e) {
                log.error("考试场次分配人员错误：{}", getStackTrace(e));
                failureCount++;
                errorMessage.append("第"+ (successCount + failureCount) +"条数据分配场次失败，原因是：未知错误\n");
                updateProgress(progress, successCount, failureCount, total);
            }
        }
        progress.setProgress(100d);
        progress.setErrorMessage(errorMessage.toString());
        updateProgress(progress, successCount, failureCount, total);
    }

    private void updateProgress(Progress progress, Integer successCount, Integer failureCount, Integer total) {
        progress.setFail( failureCount);
        progress.setSuccess( successCount);
        progress.setProgress( ((successCount + failureCount) * 100d) / total);
        redisComponent.set(redisKey, JSON.toJSONString(progress), 500);
    }

    /**
     * 获取堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
