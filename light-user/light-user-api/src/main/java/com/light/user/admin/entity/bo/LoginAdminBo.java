package com.light.user.admin.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/713:59
 */
@Data
public class LoginAdminBo {

    @NotBlank(message = "请输入账号")
    private String accountName;

    @NotBlank(message = "请输入密码")
    private String password;

    private Boolean deanline;

    private Boolean kick;
}
