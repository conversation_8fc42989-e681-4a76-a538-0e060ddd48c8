package com.light.user.campus.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 校区表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-18 14:57:11
 */
@Data
public class CampusBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 校区表id
	 */
	@ApiModelProperty("校区表id")
	private Long campusId;
	
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 * 校区名称
	 */
	@ApiModelProperty("校区名称")
	private String name;
	/**
	 * 地址
	 */
	@ApiModelProperty("地址")
	private String address;
	/**
	 * 联系信息
	 */
	@ApiModelProperty("联系信息")
	private String contactInfo;
	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Long sequence;
	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

}
