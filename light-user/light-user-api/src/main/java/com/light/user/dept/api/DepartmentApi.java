package com.light.user.dept.api;

import com.light.core.entity.AjaxResult;
import com.light.user.dept.entity.bo.DepartmentBo;
import com.light.user.dept.entity.bo.DepartmentConditionBo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/3/31 4:00 下午
 * @description：
 */
public interface DepartmentApi {

    /**
     * 部门分页查询
     * @param bo
     * @return
     */
    @PostMapping("/dept/list")
    AjaxResult list(@RequestBody DepartmentConditionBo bo);

    /**
     * 部门新增
     * @param bo
     * @return
     */
    @PostMapping("/dept/add")
    AjaxResult add(@RequestBody DepartmentBo bo);

    /**
     * 部门更新
     * @param bo
     * @return
     */
    @PostMapping("/dept/update")
    AjaxResult update( @RequestBody DepartmentBo bo);

    /**
     * 部门详情
     * @param id
     * @return
     */
    @GetMapping("/dept/detail/{id}")
    AjaxResult detail(@PathVariable("id") Long id);

    /**
     * 部门删除
     * @param id
     * @return
     */
    @PostMapping("/dept/delete/{id}")
    AjaxResult delete(@PathVariable("id") Long id);
}
