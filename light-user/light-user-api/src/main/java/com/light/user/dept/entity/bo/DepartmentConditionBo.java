package com.light.user.dept.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

/**
 *
 * 部门查询条件
 * <AUTHOR>
 * @description：
 */
@Data
public class DepartmentConditionBo extends PageLimitBo {


    /**
     * 部门名称
     */
    private String name;

    /**
     *  部门介绍
     */
    private String introduction;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 状态 是否启用，0：否，1：是
     */
    private Integer state;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 上级部门ids
     */
    private String superiorsIds;

    /**
     * 所属组织机构ID
     */
    private Long organizationId;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer sequence;

    private Integer isDelete;
}
