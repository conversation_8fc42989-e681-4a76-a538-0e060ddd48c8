package com.light.user.dept.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/3/31 2:59 下午
 * @description：
 */
@Data
public class DepartmentVo implements Serializable {

    /**
     * 部门ID
     */
    private Long id;

    /**
     * 部门名称
     */
    private String name;

    /**
     *  部门介绍
     */
    private String introduction;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 状态 是否启用，0：否，1：是
     */
    private Integer state;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 上级部门ids
     */
    private String superiorsIds;

    /**
     * 所属组织机构ID
     */
    private Long organizationId;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 是否删除 0 为删除 1 已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     *  更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;


}
