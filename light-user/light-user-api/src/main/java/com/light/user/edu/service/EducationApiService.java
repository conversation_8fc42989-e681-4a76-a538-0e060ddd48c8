package com.light.user.edu.service;

import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.edu.api.EducationApi;
import com.light.user.edu.entity.bo.EducationBo;
import com.light.user.edu.entity.bo.EducationListConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.Collection;

@FeignClient(contextId = "educationApiService", value = ServiceNameConstants.LIGHT_USER, configuration = FeignClientInterceptor.class, fallbackFactory = EducationApiService.EducationApiFallbackFactory.class)
@Component
public interface EducationApiService extends EducationApi {

    @Component
    class EducationApiFallbackFactory implements FallbackFactory<EducationApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(EducationApiFallbackFactory.class);

        @Override
        public EducationApiService create(Throwable cause) {
            EducationApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
            return new EducationApiService() {


                @Override
                public AjaxResult getEducationListByCondition(EducationListConditionBo bo) {
                    return AjaxResult.fail("查询教育信息失败");
                }

                @Override
                public AjaxResult getByUserOidList(Collection<String> userOidList) {
                    return AjaxResult.fail("获取教育信息失败");
                }

                @Override
                public AjaxResult addEducation(EducationBo bo) {
                    return AjaxResult.fail("添加教育信息失败");
                }

                @Override
                public AjaxResult updateEducation(EducationBo bo) {
                    return AjaxResult.fail("更新教育信息失败");
                }

                @Override
                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("获取教育信息详情失败");
                }

                @Override
                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除教育信息失败");
                }
            };
        }
    }
}
