package com.light.user.organization.entity.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *  组织机构学期信息配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-14 15:06:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrganizationTermBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	private Long id;

	/**
	 * 学期序号
	 */
	private Integer indexNo;

	/**
	 * 组织ID
	 */
	private Long organizationId;

	/**
	 * 学年
	 */
	private String studyYear;

	/**
     *
	 */
	private Date startTime;

	/**
     *
	 */
	private Date endTime;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 更新人
	 */
	private String updateBy;

}
