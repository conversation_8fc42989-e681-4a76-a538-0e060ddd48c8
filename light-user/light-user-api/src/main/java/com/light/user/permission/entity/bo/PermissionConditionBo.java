package com.light.user.permission.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 平台权限表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-04 10:59:39
 */
@Data
public class PermissionConditionBo extends PageLimitBo{

	/**
	 * 权限ID
	 */
	@ApiModelProperty("权限ID")
	private Long id;

	/**
	 * 父级id
	 */
	@ApiModelProperty("父级id")
	private Long parentId;

	/**
	 * 菜单名称
	 */
	@ApiModelProperty("菜单名称")
	private String name;

	/**
	 * 菜单标题
	 */
	@ApiModelProperty("菜单标题")
	private String title;

	/**
	 * 菜单类型：1:目录;2:菜单:3:按钮
	 */
	@ApiModelProperty("菜单类型：1:目录;2:菜单:3:按钮")
	private Long menuType;

	/**
	 * 菜单类型：1:目录;2:菜单:3:按钮
	 */
	@ApiModelProperty("多个菜单类型：1:目录;2:菜单:3:按钮，逗号分割")
	private String menuTypes;

	/**
	 * 权限标识
	 */
	@ApiModelProperty("权限标识")
	private String permission;

	/**
	 * 菜单路由
	 */
	@ApiModelProperty("菜单路由")
	private String menuRoute;

	/**
	 * 活动页
	 */
	@ApiModelProperty("活动页")
	private String activeMenu;

	/**
	 * 菜单页面组件
	 */
	@ApiModelProperty("菜单页面组件")
	private String menuPage;

	/**
	 * 图标
	 */
	@ApiModelProperty("图标")
	private String icon;

	/**
	 *redirect
	 */
	@ApiModelProperty("redirect")
	private String redirect;

	/**
	 * layout
	 */
	@ApiModelProperty("layout")
	private String layout;

	/**
	 * 顺序
	 */
	@ApiModelProperty("顺序")
	private Long sequence;

	/**
	 * 描述
	 */
	@ApiModelProperty("描述")
	private String description;

	/**
	 * 是否外链：0：否，1：是
	 */
	@ApiModelProperty("是否外链：0：否，1：是")
	private Integer isLink;

	/**
	 * 是否隐藏：0：否，1：是
	 */
	@ApiModelProperty("是否隐藏：0：否，1：是")
	private Integer isHide;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 是否删除： 0：否，1：是
	 */
	@ApiModelProperty("是否删除： 0：否，1：是")
	private Integer isDelete;

	/**
	 * 状态：0：禁用，1：正常
	 */
	@ApiModelProperty("状态：0：禁用，1：正常")
	private Integer state;

	/**
	 * 前后台标志（1、后台，2、前台）
	 */
	@ApiModelProperty("前后台标志（1、后台，2、前台）")
	private Integer position;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

}
