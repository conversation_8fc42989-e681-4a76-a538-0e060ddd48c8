package com.light.user.register.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 注册表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-15 10:10:54
 */
@Data
public class RegisterConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * 手机号码
	 */
	@ApiModelProperty("手机号码")
	private String phone;

	/**
	 * 密码
	 */
	@ApiModelProperty("密码")
	private String password;

	/**
	 * 注册类型，1：admin，2：user
	 */
	@ApiModelProperty("注册类型，1：admin，2：user")
	private Integer registerType;

	/**
	 * 组织机构名称
	 */
	@ApiModelProperty("组织机构名称")
	private String organizationName;

	/**
	 * 状态，-1：不通过，0：注册中，99：已完成
	 */
	@ApiModelProperty("状态，-1：不通过，0：注册中，99：已完成")
	private Integer status;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

}
