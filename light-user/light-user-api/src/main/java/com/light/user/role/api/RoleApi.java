package com.light.user.role.api;

import com.light.core.entity.AjaxResult;
import com.light.user.role.entity.bo.RoleBo;
import com.light.user.role.entity.bo.RoleConditionBo;
import com.light.user.role.entity.vo.RoleVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/179:21
 */
public interface RoleApi {

    /**
     * 查询平台角色表列表
     * <AUTHOR>
     * @date 2022-03-04 10:57:05
     */
    @PostMapping("/role/list")
    public AjaxResult getRoleListByCondition(@RequestBody RoleConditionBo condition);


    /**
     * 新增平台角色表
     * <AUTHOR>
     * @date 2022-03-04 10:57:05
     */
    @PostMapping("/role/add")
    public AjaxResult addRole(@Validated @RequestBody RoleBo roleBo);

    /**
     * 修改平台角色表
     * @param roleBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-04 10:57:05
     */
    @PostMapping("/role/update")
    public AjaxResult updateRole(@Validated @RequestBody RoleBo roleBo);

    /**
     *  根据admin oid 获取角色列表
     * @param adminOid
     * @return
     */
    @GetMapping("/role/admin-oid/{adminOid}")
    public AjaxResult<List<RoleVo>> getByAdminOid(@PathVariable("adminOid") String adminOid);

    /**
     * 查询平台角色表详情
     * @param roleId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-04 10:57:05
     */
    @GetMapping("/role/detail")
    public AjaxResult getDetail(@NotNull(message = "请选择角色") @RequestParam("roleId") Long roleId);

    /**
     * 删除平台角色表
     * @param roleId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-04 10:57:05
     */
    @GetMapping("/role/delete")
    public AjaxResult delete(@NotNull(message = "请选择角色") @RequestParam("roleId") Long roleId);

    /**
     　* @Description: 禁用启用角色
     　* @param
     　* @return
     　* @throws
     　* <AUTHOR>
     　* @date 2022/3/14 14:58
     　*/
    @GetMapping("/role/state")
    public AjaxResult updateRoleState(@NotNull(message = "请选择角色") @RequestParam("roleId") Long roleId,
                                      @NotNull(message = "请选择状态") @RequestParam("state") Integer state);
}
