package com.light.user.role.api;

import com.light.core.entity.AjaxResult;
import com.light.user.role.entity.bo.RolePermissionBo;
import com.light.user.role.entity.bo.RolePermissionConditionBo;
import com.light.user.role.entity.vo.RolePermissionVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/179:21
 */
public interface RolePermissionApi {

    /**
     * 查询平台角色菜单权限表列表
     * <AUTHOR>
     * @date 2022-03-04 10:58:07
     */
    @PostMapping("/role/permission/list")
    public AjaxResult getRolePermissionListByCondition(@RequestBody RolePermissionConditionBo condition);

    /**
     * 根据组织机构ID 获取角色权限数量
     * @param orgId the organization id 组织机构ID
     * @return
     */
    @GetMapping("/role/permission/org-id-count/{orgId}")
    public AjaxResult<Integer> getCountByOrgId(@PathVariable("orgId") Long orgId);

    /**
     * 根据权限标识获取角色集合
     * @param permission 权限标识
     * @return
     */
    @GetMapping("/role/permission/roleIds/{permission}")
    public AjaxResult<String> getRoleIdsByPermission(@PathVariable("permission") String permission);
    /**
     * 根据组织机构ID 获取角色权限信息
     * @param orgId the organization id 组织机构ID
     * @return
     */
    @GetMapping("/role/permission/org-id/{orgId}")
    public AjaxResult<List<RolePermissionVo>> getListByOrgId(@PathVariable("orgId") Long orgId);

    /**
     * 批量保存
     * @param boList the role permission bo list 角色权限参数列表
     * @return
     */
    @PostMapping("/role/permission/saveBatch")
    public AjaxResult saveBatch(@RequestBody List<RolePermissionBo> boList);

    /**
     * 保存角色权限
     * <AUTHOR>
     * @date 2022-03-04 10:58:07
     */
    @PostMapping("/role/permission/save")
    public AjaxResult addRolePermission(@Validated @RequestBody RolePermissionBo rolePermissionBo);

    /**
     * 修改平台角色菜单权限表
     * @param rolePermissionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-04 10:58:07
     */
    @PostMapping("/role/permission/update")
    public AjaxResult updateRolePermission(@Validated @RequestBody RolePermissionBo rolePermissionBo);

    /**
     * 查询平台角色菜单权限表详情
     * @param rolePermissionId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-04 10:58:07
     */
    @GetMapping("/role/permission/detail")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("rolePermissionId") Long rolePermissionId) ;

    /**
     * 删除平台角色菜单权限表
     * @param rolePermissionId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-04 10:58:07
     */
    @GetMapping("/role/permission/delete")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("rolePermissionId") Long rolePermissionId) ;


    /**
     * 根据组织机构类型获取 角色权限 组织机构ID
     *
     * @param orgType 组织类型
     * @return {@link AjaxResult}<{@link List}<{@link Long}>>
     */
    @GetMapping("/role/permission/getOrgIdListByOrgType")
    public AjaxResult<List<Long>> getOrgIdListByOrgType(@RequestParam("orgType") Integer orgType);
}
