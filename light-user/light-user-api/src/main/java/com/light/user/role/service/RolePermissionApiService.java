package com.light.user.role.service;

import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.role.api.RolePermissionApi;
import com.light.user.role.entity.bo.RolePermissionBo;
import com.light.user.role.entity.bo.RolePermissionConditionBo;
import com.light.user.role.entity.vo.RolePermissionVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/179:21
 */
@FeignClient(contextId = "rolePermissionApiService", value= ServiceNameConstants.LIGHT_USER, configuration = FeignClientInterceptor.class, fallbackFactory = RolePermissionApiService.RolePermissionApiFallbackFactory.class)
@Component
public interface RolePermissionApiService extends RolePermissionApi {

    @Component
    class RolePermissionApiFallbackFactory implements FallbackFactory<RolePermissionApiService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(RolePermissionApiService.RolePermissionApiFallbackFactory.class);

        @Override
        public RolePermissionApiService create(Throwable cause) {
            RolePermissionApiService.RolePermissionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", RolePermissionApiService.getStackTrace(cause));
            return new RolePermissionApiService() {
                @Override
                public AjaxResult getRolePermissionListByCondition(RolePermissionConditionBo condition) {
                    return AjaxResult.fail("查询角色权限分页列表失败");
                }

                @Override
                public AjaxResult<Integer> getCountByOrgId(Long orgId) {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult<String> getRoleIdsByPermission(String permission) {
                    return AjaxResult.fail("角色获取失败");
                }

                @Override
                public AjaxResult<List<RolePermissionVo>> getListByOrgId(Long orgId) {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult saveBatch(List<RolePermissionBo> boList) {
                    return AjaxResult.fail("数据保存失败");
                }

                @Override
                public AjaxResult addRolePermission(RolePermissionBo rolePermissionBo) {
                    return AjaxResult.fail("新增角色权限失败");
                }

                @Override
                public AjaxResult updateRolePermission(RolePermissionBo rolePermissionBo) {
                    return AjaxResult.fail("更新角色权限失败");
                }

                @Override
                public AjaxResult getDetail(Long rolePermissionId) {
                    return AjaxResult.fail("获取角色权限详情失败");
                }

                @Override
                public AjaxResult delete(Long rolePermissionId) {
                    return AjaxResult.fail("删除角色权限失败");
                }

                @Override
                public AjaxResult<List<Long>> getOrgIdListByOrgType(Integer orgType) {
                    return AjaxResult.fail("获取失败");
                }
            };
        }
    }

    /**
     * 获取堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
