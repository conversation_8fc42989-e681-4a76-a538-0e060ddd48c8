package com.light.user.student.api;


import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.user.student.entity.bo.StudentTransferBo;
import com.light.user.student.entity.bo.StudentTransferConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 学生转校转班表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 17:50:38
 */
public interface StudentTransferApi  {

	/**
	 * 查询学生转校转班表列表
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/studentTransfer/list")
	AjaxResult getStudentTransferListByCondition(@RequestBody StudentTransferConditionBo condition);


	/**
	 * 新增学生转校转班表
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/studentTransfer/add")
	AjaxResult addStudentTransfer(@Validated @RequestBody StudentTransferBo studentTransferBo);

	/**
	 * 批量新增学生转校转班表
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/studentTransfer/addBatch")
	AjaxResult addStudentTransferBatch(@Validated @RequestBody List<StudentTransferBo> studentTransferBoList);

	/**
	 * 修改学生转校转班表
	 * @param studentTransferBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/studentTransfer/update")
	AjaxResult updateStudentTransfer(@Validated @RequestBody StudentTransferBo studentTransferBo);

	/**
	 * 查询学生转校转班表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@GetMapping("/studentTransfer/detail")
	AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

	/**
	 * 删除学生转校转班表
	 * @param id
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@GetMapping("/studentTransfer/delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据")  @RequestParam("id") Long id);



}

