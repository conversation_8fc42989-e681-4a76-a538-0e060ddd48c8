package com.light.user.student.service;


import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.student.api.StudentTransferApi;
import com.light.user.student.entity.bo.StudentTransferBo;
import com.light.user.student.entity.bo.StudentTransferConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 学生转校转班表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 17:50:38
 */
@FeignClient(contextId = "studentTransferApiService", value=ServiceNameConstants.LIGHT_USER, configuration = FeignClientInterceptor.class, fallbackFactory = StudentTransferApiService.StudentTransferApiFallbackFactory.class)
@Component
public interface StudentTransferApiService  extends StudentTransferApi {

	@Component
	class StudentTransferApiFallbackFactory implements FallbackFactory<StudentTransferApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(StudentTransferApiFallbackFactory.class);
		@Override
		public StudentTransferApiService create(Throwable cause) {
			StudentTransferApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new StudentTransferApiService() {
				public AjaxResult getStudentTransferListByCondition(StudentTransferConditionBo condition){
					return AjaxResult.fail("查询调班记录失败");
				}

				public AjaxResult addStudentTransfer(StudentTransferBo studentTransferBo){
					return AjaxResult.fail("添加调班记录失败");
				}

				public AjaxResult addStudentTransferBatch(List<StudentTransferBo> studentTransferBoList){
					return AjaxResult.fail("批量添加调班记录失败");
				}

				public AjaxResult updateStudentTransfer(StudentTransferBo studentTransferBo){
					return AjaxResult.fail("更新调班记录失败");
				}

				public AjaxResult getDetail(Long id){
					return AjaxResult.fail("查询调班记录详情失败");
				}

				public AjaxResult delete(Long id){
					return AjaxResult.fail("删除调班记录失败");
				}
			};
		}
	}
}