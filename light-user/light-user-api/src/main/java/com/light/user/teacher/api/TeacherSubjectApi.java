package com.light.user.teacher.api;


import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherSubjectBo;
import com.light.user.teacher.entity.bo.TeacherSubjectConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;


/**
 * 教师任教科目接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 17:50:38
 */
public interface TeacherSubjectApi  {

	/**
	 * 查询教师任教科目列表
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/teacherSubject/list")
	AjaxResult getTeacherSubjectListByCondition(@RequestBody TeacherSubjectConditionBo condition);


	/**
	 * 新增教师任教科目
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/teacherSubject/add")
	AjaxResult addTeacherSubject(@Validated @RequestBody TeacherSubjectBo teacherSubjectBo);

	/**
	 * 修改教师任教科目
	 * @param teacherSubjectBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@PostMapping("/teacherSubject/update")
	AjaxResult updateTeacherSubject(@Validated @RequestBody TeacherSubjectBo teacherSubjectBo);

	/**
	 * 查询教师任教科目详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@GetMapping("/teacherSubject/detail")
	AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

	/**
	 * 删除教师任教科目
	 * @param id
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2022-03-14 17:50:38
	 */
	@GetMapping("/teacherSubject/delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("id") Long id);

}

