package com.light.user.teacher.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 教师班级科目
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 11:09:37
 */
@Data
public class TeacherClassesSubjectBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 教师班级科目id
	 */
	@ApiModelProperty("教师班级科目id")
	private Long teacherClassesSubjectId;
	
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;
	/**
	 * 学科
	 */
	@ApiModelProperty("学科")
	private String subjectCode;
	/**
	 * 教师用户oid
	 */
	@ApiModelProperty("教师用户oid")
	private String teacherUserOid;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;
	/**
	 * 
	 */
	@ApiModelProperty("")
	private Integer isDelete;

}
