package com.light.user.user.api;

import com.light.core.entity.AjaxResult;
import com.light.user.user.entity.bo.UserIdentityBo;
import com.light.user.user.entity.bo.UserIdentityConditionBo;
import com.light.user.user.entity.vo.UserIdentityVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/179:23
 */
public interface UserIdentityApi {

    /**
     * 查询用户身份表列表
     * <AUTHOR>
     * @date 2022-03-02 14:02:20
     */
    @PostMapping("/user/identity/list")
    public AjaxResult getUserIdentityListByCondition(@RequestBody UserIdentityConditionBo condition);

    /**
     * 通过用户oid获取身份信息
     *
     * @param userOid 用户oid
     * @return {@link AjaxResult}<{@link List}<{@link UserIdentityVo}>>
     */
    @GetMapping("/user/identity/getByUserOid")
    public AjaxResult<List<UserIdentityVo>> getByUserOid(@RequestParam("userOid") String userOid);

    /**
     * 新增用户身份表
     * <AUTHOR>
     * @date 2022-03-02 14:02:20
     */
    @PostMapping("/user/identity/add")
    public AjaxResult addUserIdentity(@Validated @RequestBody UserIdentityBo userIdentityBo);

    /**
     * 修改用户身份表
     * @param userIdentityBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-02 14:02:20
     */
    @PostMapping("/user/identity/update")
    public AjaxResult updateUserIdentity(@Validated @RequestBody UserIdentityBo userIdentityBo);

    /**
     * 查询用户身份表详情
     * @param userIdentityId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-02 14:02:20
     */
    @GetMapping("/user/identity/detail")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("userIdentityId") Long userIdentityId) ;

    /**
     * 删除用户身份表
     * @param userIdentityId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-02 14:02:20
     */
    @GetMapping("/user/identity/delete")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("userIdentityId") Long userIdentityId) ;


    /**
     * 根据账号手机号获取身份列表
     * @param accountPhone 账号手机号
     * @return
     */
    @GetMapping("/user/identity/getByAccountPhone")
    public AjaxResult<List<UserIdentityVo>> getByAccountPhone(@RequestParam("accountPhone") String accountPhone);
}
