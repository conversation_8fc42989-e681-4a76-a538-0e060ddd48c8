package com.light.user.user.api;


import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.user.user.entity.bo.UserPlatformInfoBo;
import com.light.user.user.entity.bo.UserPlatformInfoConditionBo;
import com.light.user.user.entity.vo.UserPlatformInfoVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 用户平台信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-24 09:22:55
 */
public interface UserPlatformInfoApi {

    /**
     * 查询用户平台信息表分页列表
     * <AUTHOR>
     * @date 2024-09-24 09:22:55
     */
    @PostMapping("/user/platform/info/page/list")
    public AjaxResult<PageInfo<UserPlatformInfoVo>> getUserPlatformInfoPageListByCondition(@RequestBody UserPlatformInfoConditionBo condition);

    /**
     * 查询用户平台信息表列表
     * <AUTHOR>
     * @date 2024-09-24 09:22:55
     */
    @PostMapping("/user/platform/info/list")
    public AjaxResult<List<UserPlatformInfoVo>> getUserPlatformInfoListByCondition(@RequestBody UserPlatformInfoConditionBo condition);


    /**
     * 新增用户平台信息表
     * <AUTHOR>
     * @date 2024-09-24 09:22:55
     */
    @PostMapping("/user/platform/info/add")
    public AjaxResult addUserPlatformInfo(@Validated @RequestBody UserPlatformInfoBo userPlatformInfoBo);

    /**
     * 修改用户平台信息表
     * @param userPlatformInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-09-24 09:22:55
     */
    @PostMapping("/user/platform/info/update")
    public AjaxResult updateUserPlatformInfo(@Validated @RequestBody UserPlatformInfoBo userPlatformInfoBo);

    /**
     * 查询用户平台信息表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-09-24 09:22:55
     */
    @GetMapping("/user/platform/info/detail")
    public AjaxResult<UserPlatformInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除用户平台信息表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-09-24 09:22:55
     */
    @GetMapping("/user/platform/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
