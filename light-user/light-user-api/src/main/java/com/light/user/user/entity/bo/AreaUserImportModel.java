package com.light.user.user.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/1814:53
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AreaUserImportModel {

    @Excel(name = "账号", width = 20.0, fixedIndex = 0)
    @NotBlank(message = "账号不能为空")
    private String accountName;

    @Excel(name = "角色", width = 20.0, fixedIndex = 1)
    @NotBlank(message = "角色不能为空")
    private String roleName;

    @Excel(name = "手机号", width = 20.0, fixedIndex = 2)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Excel(name = "姓名", width = 20.0, fixedIndex = 3)
    @NotBlank(message = "姓名不能为空")
    private String realName;

    @Excel(name = "省", width = 20.0, fixedIndex = 4)
    @NotBlank(message = "省不能为空")
    private String provinceName;

    @Excel(name = "市", width = 20.0, fixedIndex = 5)
    @NotBlank(message = "是不能为空")
    private String cityName;

    @Excel(name = "县区", width = 20.0, fixedIndex = 6)
    @NotBlank(message = "县区不能为空")
    private String areaName;

    @ExcelIgnore
    private Long organizationId;

    @ExcelIgnore
    private String password;
}
