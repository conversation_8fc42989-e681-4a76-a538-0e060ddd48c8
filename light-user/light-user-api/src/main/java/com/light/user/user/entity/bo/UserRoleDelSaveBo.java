package com.light.user.user.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 平台用户角色表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 14:01:49
 */
@Data
public class UserRoleDelSaveBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 *
	 */
	@ApiModelProperty("角色ID集合")
	private List<Long> roleIds;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * userRole关联信息
	 */
	private List<UserRoleBo> userRoleBos;


}
