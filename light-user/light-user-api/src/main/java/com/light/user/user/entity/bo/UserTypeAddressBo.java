package com.light.user.user.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 09:24
 */
@Data
public class UserTypeAddressBo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("身份类型(1：学生，2：教师，3、家长)")
    private Integer identityType;

    @ApiModelProperty("不同纬度集合")
    private List<AddressBo> addressBos;
}
