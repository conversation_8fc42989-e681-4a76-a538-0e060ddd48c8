package com.light.user.user.entity.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/1714:48
 */
@Data
public class UserExportModel {


    @Excel(name = "ID", width = 15.0, fixedIndex = 0)
    private String id;


    @Excel(name = "姓名", width = 15.0, fixedIndex = 0)
    private String realName;

    @Excel(name = "账号", width = 15.0, fixedIndex = 0)
    private String accountName;

    @Excel(name = "手机", width = 15.0, fixedIndex = 0)
    private String phone;

    @Excel(name = "身份", width = 15.0, fixedIndex = 0)
    private String identityName;

    @Excel(name = "账号状态", width = 15.0, fixedIndex = 0)
    private String accountStatus;
}
