package com.light.user.user.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户修改次数限制表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-07 17:43:11
 */
@Data
public class UserUpdateLimitVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty("")
    private Long id;

    /**
     * 用户OID
     */
    @ApiModelProperty("用户OID")
    private String userOid;

    /**
     * 总次数
     */
    @ApiModelProperty("总次数")
    private Long total;

    /**
     * 修改次数
     */
    @ApiModelProperty("修改次数")
    private Long updateNum;


    /**
     * 修改次数
     */
    @ApiModelProperty("最后一次修改时间")
    private Date lastUpdateTime;

    /**
     *
     */
    @ApiModelProperty("")
    private Date createTime;

    /**
     *
     */
    @ApiModelProperty("")
    private String createBy;

    /**
     *
     */
    @ApiModelProperty("")
    private Date updateTime;

    /**
     *
     */
    @ApiModelProperty("")
    private String updateBy;

    /**
     *
     */
    @ApiModelProperty("")
    private Integer isDelete;

}
