package com.light.user.user.service;

import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.user.api.UserRoleApi;
import com.light.user.user.entity.bo.UserRoleBo;
import com.light.user.user.entity.bo.UserRoleConditionBo;
import com.light.user.user.entity.bo.UserRoleDelSaveBo;
import com.light.user.user.entity.vo.UserRoleVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

/**
 * <AUTHOR>
 * @Title:
 * @Package
 * @Description:
 * @date 2022/3/179:25
 */
@FeignClient(contextId = "userRoleService", value= ServiceNameConstants.LIGHT_USER, configuration = FeignClientInterceptor.class, fallbackFactory = UserRoleApiService.UserRoleApiFallbackFactory.class)
@Component
public interface UserRoleApiService extends UserRoleApi {

    @Component
    class UserRoleApiFallbackFactory implements FallbackFactory<UserRoleApiService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(UserRoleApiService.UserRoleApiFallbackFactory.class);

        @Override
        public UserRoleApiService create(Throwable cause) {
            UserRoleApiService.UserRoleApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", UserRoleApiService.getStackTrace(cause));
            return new UserRoleApiService() {
                @Override
                public AjaxResult getUserRoleListByCondition(UserRoleConditionBo condition) {
                    return AjaxResult.fail("查询用户角色分页列表失败");
                }

                @Override
                public AjaxResult<List<UserRoleVo>> getListByUserOid(String userOid) {
                    return AjaxResult.fail("获取数据失败");
                }

                @Override
                public AjaxResult deleteByCondition(UserRoleBo userRoleBo) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult addUserRole(UserRoleBo userRoleBo) {
                    return AjaxResult.fail("新增用户角色失败");
                }

                @Override
                public AjaxResult saveBatchUserRole(List<UserRoleBo> userRoleBoList) {
                    return AjaxResult.fail("新增用户角色失败");
                }

                @Override
                public AjaxResult delAndSaveByRoleId(Long roleId, Long orgId, List<UserRoleBo> userRoleBos) {
                    return AjaxResult.fail("保存失败");
                }

                @Override
                public AjaxResult delAndSaveByUserOid(String userOid, Long orgId, List<UserRoleBo> userRoleBos) {
                    return AjaxResult.fail("保存失败");
                }

                @Override
                public AjaxResult delAndSaveByUserOidAndRoleIds(@RequestBody UserRoleDelSaveBo userRoleDelSaveBo){
                    return AjaxResult.fail("保存失败");
                }

                @Override
                public AjaxResult updateUserRole(UserRoleBo userRoleBo) {
                    return AjaxResult.fail("更新用户角色失败");
                }

                @Override
                public AjaxResult getDetail(Long userRoleId) {
                    return AjaxResult.fail("获取用户角色详情失败");
                }

                @Override
                public AjaxResult delete(Long userRoleId) {
                    return AjaxResult.fail("删除用户角色失败");
                }
            };
        }
    }

    /**
     * 获取堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
