package com.light.user.userThirdRelationship.api;


import com.light.core.entity.AjaxResult;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipBo;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipConditionBo;
import com.light.user.userThirdRelationship.entity.vo.UserThirdRelationshipVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;


/**
 * 用户与第三方关联表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-10 17:08:45
 */
public interface UserThirdRelationshipApi  {

	/**
	 * 查询用户与第三方关联表列表
	 * <AUTHOR>
	 * @date 2025-04-10 17:08:45
	 */
	@PostMapping("/userThirdRelationship/list")
	@ApiOperation(value = "分页查询用户与第三方关联表",httpMethod = "POST")
	AjaxResult getUserThirdRelationshipListByCondition(@RequestBody UserThirdRelationshipConditionBo condition);


	@GetMapping("/userThirdRelationship/queryByThirdOidAndAppCode")
	AjaxResult<UserThirdRelationshipVo> queryByThirdOidAndAppCode(@RequestParam("thirdOid") String thirdOid,@RequestParam("appCode") String appCode);

	/**
	 * 新增用户与第三方关联表
	 * <AUTHOR>
	 * @date 2025-04-10 17:08:45
	 */
	@PostMapping("/userThirdRelationship/add")
	@ApiOperation(value = "新增用户与第三方关联表",httpMethod = "POST")
	AjaxResult addUserThirdRelationship(@Validated @RequestBody UserThirdRelationshipBo userThirdRelationshipBo);


	@GetMapping("/userThirdRelationship/queryByUserOidAndAppCode")
	AjaxResult<UserThirdRelationshipVo> queryByUserOidAndAppCode(@RequestParam("userOid") String userOid,@RequestParam("appCode") String appCode);

	/**
	 * 新增或者更新用户与第三方关联表
	 * <AUTHOR>
	 * @date 2025-04-10 17:08:45
	 */
	@PostMapping("/userThirdRelationship/addOrUpdate")
	@ApiOperation(value = "新增或者更新用户与第三方关联表",httpMethod = "POST")
	AjaxResult addOrUpdateThirdRelationship(@Validated @RequestBody UserThirdRelationshipBo userThirdRelationshipBo);

	/**
	 * 修改用户与第三方关联表
	 * @param userThirdRelationshipBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-04-10 17:08:45
	 */
	@PostMapping("/userThirdRelationship/update")
	@ApiOperation(value = "修改用户与第三方关联表",httpMethod = "POST")
	AjaxResult updateUserThirdRelationship(@Validated @RequestBody UserThirdRelationshipBo userThirdRelationshipBo);

	/**
	 * 查询用户与第三方关联表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-04-10 17:08:45
	 */
	@GetMapping("/userThirdRelationship/detail")
	@ApiOperation(value = "查询用户与第三方关联表详情",httpMethod = "GET")
	AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除用户与第三方关联表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-04-10 17:08:45
	 */
	@GetMapping("/userThirdRelationship/delete")
	@ApiOperation(value = "删除用户与第三方关联表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	@GetMapping("/userThirdRelationship/deleteByUserOid")
	@ApiOperation(value = "删除用户与第三方关联表",httpMethod = "GET")
	@ApiImplicitParam(name = "userOid", value = "userOid", required = true, dataType = "String", paramType = "delete")
	AjaxResult deleteByUserOid(@NotNull(message = "请选择需要删除的数据") @RequestParam("userOid") String userOid);


}

