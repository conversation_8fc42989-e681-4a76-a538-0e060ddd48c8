package com.light.user.withdraw.service;


import com.github.pagehelper.PageInfo;
import com.light.user.withdraw.api.AccountWithdrawApplyApi;
import com.light.user.withdraw.entity.bo.AccountWithdrawApplyBatchBo;
import com.light.user.withdraw.entity.bo.AccountWithdrawApplyBo;
import com.light.user.withdraw.entity.bo.AccountWithdrawApplyConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.withdraw.entity.vo.AccountWithdrawApplyVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 账号注销申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-22 11:46:06
 */
@FeignClient(contextId = "accountWithdrawApplyApiService", value= "light-user", configuration = FeignClientInterceptor.class, fallbackFactory = AccountWithdrawApplyApiService.AccountWithdrawApplyApiFallbackFactory.class)
@Component
public interface AccountWithdrawApplyApiService extends AccountWithdrawApplyApi {

    @Component
    class AccountWithdrawApplyApiFallbackFactory implements FallbackFactory<AccountWithdrawApplyApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(AccountWithdrawApplyApiFallbackFactory.class);
        @Override
        public AccountWithdrawApplyApiService create(Throwable cause) {
            AccountWithdrawApplyApiFallbackFactory.LOGGER.error("账号注销申请服务调用失败:{}", cause.getMessage());
            return new AccountWithdrawApplyApiService() {
                public AjaxResult getAccountWithdrawApplyPageListByCondition(AccountWithdrawApplyConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                @Override
                public AjaxResult<PageInfo<AccountWithdrawApplyVo>> getStudentApplyListByCondition(AccountWithdrawApplyConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getAccountWithdrawApplyListByCondition(AccountWithdrawApplyConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                @Override
                public AjaxResult<List<AccountWithdrawApplyVo>> getLastByUserOidList(List<String> userOidList) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addAccountWithdrawApply(AccountWithdrawApplyBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateAccountWithdrawApply(AccountWithdrawApplyBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult revoke(AccountWithdrawApplyBo accountWithdrawApplyBo) {
                    return AjaxResult.fail("操作失败");
                }

                @Override
                public AjaxResult<AccountWithdrawApplyVo> getLastByUserOid(String userOid) {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult audit(AccountWithdrawApplyBo condition) {
                    return AjaxResult.fail("审核失败");
                }

                @Override
                public AjaxResult<AccountWithdrawApplyVo> getVoById(Long id) {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult<List<AccountWithdrawApplyVo>> getVoByIdList(List<Long> idList) {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult<Integer> expiredApply() {
                    return AjaxResult.fail("数据处理失败");
                }

                @Override
                public AjaxResult<Integer> expiredApplyByUserOid(String userOid) {
                    return AjaxResult.fail("数据处理失败");
                }

                @Override
                public AjaxResult<Integer> getWaitAuditCountByOrgId(Long organizationId) {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult<Integer> getWaitAuditCount() {
                    return AjaxResult.fail("数据获取失败");
                }

                @Override
                public AjaxResult batchAudit(AccountWithdrawApplyBatchBo bo) {
                    return AjaxResult.fail("数据操作失败");
                }

                @Override
                public AjaxResult batchWithdraw(List<AccountWithdrawApplyBo> boList) {
                    return AjaxResult.fail("批量注销失败");
                }
            };
        }
    }
}
