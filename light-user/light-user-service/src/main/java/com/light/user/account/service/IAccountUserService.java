package com.light.user.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.user.account.entity.dto.AccountUserDto;
import com.light.user.account.entity.bo.AccountUserConditionBo;
import com.light.user.account.entity.bo.AccountUserBo;
import com.light.user.account.entity.vo.AccountUserVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 账号用户关系接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 11:16:28
 */
public interface IAccountUserService extends IService<AccountUserDto> {

    List<AccountUserVo> getAccountUserListByCondition(AccountUserConditionBo condition);

	AjaxResult addAccountUser(AccountUserBo accountUserBo);

	AjaxResult updateAccountUser(AccountUserBo accountUserBo);

	AjaxResult delByUserOid(String userOid);

	Map<String, Object> getDetail(Long accountUserId);

    AccountUserDto getByUserOid(String userOid);

    AjaxResult batchDelByUserOid(List<String> userOidList);
}

