package com.light.user.account.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.light.core.utils.BeanUtils;
import com.light.user.organization.entity.dto.OrganizationDto;
import com.light.user.organization.service.IOrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.user.account.entity.dto.AccountUserDto;
import com.light.user.account.entity.bo.AccountUserConditionBo;
import com.light.user.account.entity.bo.AccountUserBo;
import com.light.user.account.entity.vo.AccountUserVo;
import com.light.user.account.service.IAccountUserService;
import com.light.user.account.mapper.AccountUserMapper;
import com.light.core.entity.AjaxResult;
/**
 * 账号用户关系接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 11:16:28
 */
@Service
public class AccountUserServiceImpl extends ServiceImpl<AccountUserMapper, AccountUserDto> implements IAccountUserService {

	@Resource
	private AccountUserMapper accountUserMapper;

	@Autowired
	private IOrganizationService iOrganizationService;

    @Override
	public List<AccountUserVo> getAccountUserListByCondition(AccountUserConditionBo condition) {
		final List<AccountUserVo> accountUserList = accountUserMapper.getAccountUserListByCondition(condition);
		if(CollectionUtil.isNotEmpty(accountUserList)){
			return accountUserList.stream().map(this::buildSchoolAreaInfo).collect(Collectors.toList());
		}
		return accountUserList;
	}

	/**
	 *  构建行政省市区信息
	 * @param accountUserVo
	 */
	private AccountUserVo buildSchoolAreaInfo(AccountUserVo accountUserVo){
		final Integer orgType = accountUserVo.getOrgType();
		if(orgType != null && orgType != 2){
			return accountUserVo;
		}

		final List<Long> superiors = this.trimSuperId(accountUserVo.getOrgSuperiorsIds());
		if(CollectionUtil.isEmpty(superiors)){
			return accountUserVo;
		}

		// 省份
		int size = superiors.size();
		final Long provinceId = superiors.get(0);
		final OrganizationDto province = this.iOrganizationService.getById(provinceId);
		accountUserVo.setProvinceName(province.getName());

		if(size > 1) {
			// 市
			final Long cityId = superiors.get(1);
			final OrganizationDto city = this.iOrganizationService.getById(cityId);
			accountUserVo.setCityName(city.getName());
		}

		return accountUserVo;

	}

	/**
	 * 去除0 并转换 Long
	 * @param superiorsIds
	 * @return
	 */
	private List<Long> trimSuperId(String superiorsIds){

		if(StrUtil.isEmpty(superiorsIds)){
			return null;
		}
		final String[] superIds = superiorsIds.split(",");
		if(superIds == null || superIds.length == 0){
			return null;
		}
		return Arrays.stream(superIds)
				.filter(x-> StrUtil.isNotEmpty(x) && !x.trim().equals("0"))
				.map(x->Long.parseLong(x)).collect(Collectors.toList());
	}

	@Override
	public AjaxResult addAccountUser(AccountUserBo accountUserBo) {
		AccountUserDto accountUser = new AccountUserDto();
		BeanUtils.copyProperties(accountUserBo, accountUser);
		if(save(accountUser)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateAccountUser(AccountUserBo accountUserBo) {
		AccountUserDto accountUser = new AccountUserDto();
		BeanUtils.copyProperties(accountUserBo, accountUser);
		if(updateById(accountUser)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult delByUserOid(String userOid) {
		UpdateWrapper<AccountUserDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(AccountUserDto::getUserOid,userOid);
		this.baseMapper.delete(updateWrapper);
		return AjaxResult.success();
	}

	@Override
	public Map<String, Object> getDetail(Long accountUserId) {
		LambdaQueryWrapper<AccountUserDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(AccountUserDto::getId, accountUserId);
		AccountUserDto accountUser = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("accountUserVo", accountUser==null?new AccountUserVo():accountUser);
		return reuslt;
	}

	@Override
	public AccountUserDto getByUserOid(String userOid) {
		QueryWrapper<AccountUserDto> queryWrapper = new QueryWrapper();
		queryWrapper.lambda().eq(AccountUserDto::getUserOid,userOid);
		return this.baseMapper.selectOne(queryWrapper);
	}

    @Override
    public AjaxResult batchDelByUserOid(List<String> userOidList) {
        UpdateWrapper<AccountUserDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(AccountUserDto::getUserOid,userOidList);
        this.baseMapper.delete(updateWrapper);
        return AjaxResult.success();
    }
}
