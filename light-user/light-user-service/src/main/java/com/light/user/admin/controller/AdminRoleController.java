package com.light.user.admin.controller;

import java.util.Map;

import javax.validation.constraints.NotNull;

import com.light.core.constants.SystemConstants;
import com.light.user.admin.api.AdminRoleApi;
import com.light.user.admin.entity.bo.AdminRoleBo;
import com.light.user.admin.entity.bo.AdminRoleConditionBo;
import com.light.user.admin.entity.vo.AdminRoleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.user.admin.service.IAdminRoleService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

/**
 * 平台用户角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-07 11:28:55
 */
@RestController
@Validated
@Api(value = "", tags = "管理员角色表接口" )
public class AdminRoleController implements AdminRoleApi {
	
    @Autowired
    private IAdminRoleService adminRoleService;

    /**
     * 查询平台用户角色表列表
     * <AUTHOR>
     * @date 2022-03-07 11:28:55
     */
	@Override
	@ApiOperation(value = "分页查询平台用户角色表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getAdminRoleListByCondition(@RequestBody AdminRoleConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<AdminRoleVo> pageInfo = new PageInfo<>(adminRoleService.getAdminRoleListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }


    /**
     * 新增平台用户角色表
     * <AUTHOR>
     * @date 2022-03-07 11:28:55
     */
	@Override
	@ApiOperation(value = "新增平台用户角色表")
    public AjaxResult addAdminRole(@Validated @RequestBody AdminRoleBo adminRoleBo){
		return adminRoleService.addAdminRole(adminRoleBo);
    }

    /**
	 * 修改平台用户角色表
	 * @param adminRoleBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-07 11:28:55
	 */
	@Override
	@ApiOperation(value = "修改平台用户角色表")
	public AjaxResult updateAdminRole(@Validated @RequestBody AdminRoleBo adminRoleBo) {
		if(null == adminRoleBo.getAdminRoleId()) {
			return AjaxResult.fail("平台用户角色表id不能为空");
		}
		return adminRoleService.updateAdminRole(adminRoleBo);
	}

	/**
	 * 查询平台用户角色表详情
	 * @param adminRoleId
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-07 11:28:55
	 */
	@Override
	@ApiOperation(value = "查询平台用户角色表详情")
	public AjaxResult getDetail(@PathVariable("adminRoleId") Long adminRoleId) {
		Map<String, Object> map = adminRoleService.getDetail(adminRoleId);
		return AjaxResult.success(map);
	}
    
    /**
	 * 删除平台用户角色表
	 * @param adminRoleId
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-03-07 11:28:55
	 */
	@Override
	@ApiOperation(value = "删除平台用户角色表")
	public AjaxResult delete(@PathVariable("adminRoleId") Long adminRoleId) {
		AdminRoleBo adminRoleBo = new AdminRoleBo();
		adminRoleBo.setAdminRoleId(adminRoleId);
		return adminRoleService.updateAdminRole(adminRoleBo);
	}
}
