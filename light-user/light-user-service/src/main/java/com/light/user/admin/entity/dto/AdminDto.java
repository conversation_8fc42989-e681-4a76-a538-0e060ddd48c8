package com.light.user.admin.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 平台用户表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-07 11:27:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_admin")
public class AdminDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 账户名
	 */
	@TableField("account_name")
	private String accountName;

	/**
	 * 密码
	 */
	@TableField("password")
	private String password;

	/**
	 * 手机号
	 */
	@TableField("phone")
	private String phone;

	/**
	 * 管理员姓名
	 */
	@TableField("admin_name")
	private String adminName;

	/**
	 * 微信unionid
	 */
	@TableField("weixin_unionid")
	private String weixinUnionid;

	/**
	 * 联系电话
	 */
	@TableField("contact_number")
	private String contactNumber;

	/**
	 * 邮箱
	 */
	@TableField("email")
	private String email;

	/**
	 * 性别
	 */
	@TableField("sex")
	private Integer sex;

	/**
	 * 生日
	 */
	@TableField("birthday")
	private Date birthday;

	/**
	 * 最后登录时间
	 */
	@TableField("last_login_time")
	private Date lastLoginTime;

	/**
	 * 头像
	 */
	@TableField("picture")
	private String picture;

	/**
	 * 是否锁定：0：否，1：是
	 */
	@TableField("is_locked")
	private Integer isLocked;

	/**
	 * 介绍
	 */
	@TableField("introduction")
	private String introduction;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 是否激活：0：否，1：是
	 */
	@TableField("is_activation")
	private Integer isActivation;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

}
