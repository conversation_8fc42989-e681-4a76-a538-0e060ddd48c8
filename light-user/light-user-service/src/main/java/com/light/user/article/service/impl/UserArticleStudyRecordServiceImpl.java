package com.light.user.article.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.security.service.CurrentUserService;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.points.entity.dto.UserPointsDetailDto;
import com.light.user.points.service.IUserPointsDetailService;
import com.light.user.user.entity.vo.LoginUserVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.user.article.entity.dto.UserArticleStudyRecordDto;
import com.light.user.article.entity.bo.UserArticleStudyRecordConditionBo;
import com.light.user.article.entity.bo.UserArticleStudyRecordBo;
import com.light.user.article.entity.vo.UserArticleStudyRecordVo;
import com.light.user.article.service.IUserArticleStudyRecordService;
import com.light.user.article.mapper.UserArticleStudyRecordMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文章学习记录接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-11 15:28:09
 */
@Service
public class UserArticleStudyRecordServiceImpl extends ServiceImpl<UserArticleStudyRecordMapper, UserArticleStudyRecordDto> implements IUserArticleStudyRecordService {

	@Resource
	private UserArticleStudyRecordMapper userArticleStudyRecordMapper;

	@Autowired
	private CurrentUserService currentUserService;

	@Autowired
	private IUserPointsDetailService userPointsDetailService;

    @Override
	public List<UserArticleStudyRecordVo> getUserArticleStudyRecordListByCondition(UserArticleStudyRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return userArticleStudyRecordMapper.getUserArticleStudyRecordListByCondition(condition);
	}

	@Override
	@Transactional
	public AjaxResult addUserArticleStudyRecord(UserArticleStudyRecordBo userArticleStudyRecordBo) {
    	//获取当前用户
		String userOid = currentUserService.getCurrentOid();
		if(StringUtils.isEmpty(userOid)) {
			return AjaxResult.fail("当前用户不存在");
		}
		userArticleStudyRecordBo.setUserOid(userOid);
		UserArticleStudyRecordDto userArticleStudyRecord = new UserArticleStudyRecordDto();
		BeanUtils.copyProperties(userArticleStudyRecordBo, userArticleStudyRecord);
		userArticleStudyRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		Date date = new Date();
		if(save(userArticleStudyRecord)){
			//查询阅读文章总数，30秒
            UserArticleStudyRecordConditionBo bo = new UserArticleStudyRecordConditionBo();
            bo.setArticleId(userArticleStudyRecord.getArticleId());
            final Integer type = userArticleStudyRecord.getType();
            bo.setType(type);
            bo.setUserOid(userArticleStudyRecord.getUserOid());
            bo.setStartTime(date);
            bo.setEndTime(date);
            Integer articleCount = this.getCountByCondition(bo);
            if(articleCount > 1){
                return AjaxResult.success(HttpStatus.HTTP_OK, "保存成功", null);
            }

            Integer count = this.baseMapper.getArticleCountByUserOidAndType(userOid, type, DateUtil.today());

			if (count <= 6) { //小于六次才会添加计分
				UserPointsDetailDto userPointsDetailDto = new UserPointsDetailDto();
				userPointsDetailDto.setOid(RandomUtil.randomString(SystemConstants.OID_LENGTH));
				userPointsDetailDto.setUserOid(userOid);
				userPointsDetailDto.setPointsVal(1d);
				userPointsDetailDto.setTitle("阅读文章积分");
				userPointsDetailService.addUserPointsDetailAndUpdateTotal(userPointsDetailDto);
				return AjaxResult.success(HttpStatus.HTTP_OK, "保存成功", 1);
			}else{
				return AjaxResult.success(HttpStatus.HTTP_OK, "保存成功", null);
			}
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

    private Integer getCountByCondition(UserArticleStudyRecordConditionBo bo){
        LambdaQueryWrapper<UserArticleStudyRecordDto> lqw = new LambdaQueryWrapper<UserArticleStudyRecordDto>();
        lqw.eq(StrUtil.isNotEmpty(bo.getUserOid()), UserArticleStudyRecordDto::getUserOid, bo.getUserOid());
        lqw.eq(bo.getArticleId() != null, UserArticleStudyRecordDto::getArticleId, bo.getArticleId());
        lqw.eq(bo.getType() != null, UserArticleStudyRecordDto::getType, bo.getType());
        lqw.eq(UserArticleStudyRecordDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.ge(bo.getStartTime() != null,UserArticleStudyRecordDto::getCreateTime, DateUtil.beginOfDay(bo.getStartTime()));
        lqw.le(bo.getEndTime() != null, UserArticleStudyRecordDto::getCreateTime, DateUtil.endOfDay(DateUtil.beginOfDay(bo.getEndTime())));
        return this.baseMapper.selectCount(lqw);
    }

	@Override
	public AjaxResult updateUserArticleStudyRecord(UserArticleStudyRecordBo userArticleStudyRecordBo) {
		UserArticleStudyRecordDto userArticleStudyRecord = new UserArticleStudyRecordDto();
		BeanUtils.copyProperties(userArticleStudyRecordBo, userArticleStudyRecord);
		if(updateById(userArticleStudyRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public UserArticleStudyRecordVo getDetail(Long id) {
		UserArticleStudyRecordConditionBo condition = new UserArticleStudyRecordConditionBo();
		condition.setId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<UserArticleStudyRecordVo> list = userArticleStudyRecordMapper.getUserArticleStudyRecordListByCondition(condition);
		UserArticleStudyRecordVo vo = new UserArticleStudyRecordVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}
