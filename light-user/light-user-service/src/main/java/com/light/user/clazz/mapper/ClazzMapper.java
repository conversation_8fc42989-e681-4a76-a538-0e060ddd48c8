package com.light.user.clazz.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.clazz.entity.dto.ClazzDto;
import com.light.user.clazz.entity.bo.ClazzConditionBo;
import com.light.user.clazz.entity.vo.ClazzVo;
import org.apache.ibatis.annotations.Param;

/**
 * 班级信息Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 10:43:37
 */
public interface ClazzMapper extends BaseMapper<ClazzDto> {

	List<ClazzVo> getClazzListByCondition(ClazzConditionBo condition);

    int addNum(@Param("clazzId") Long clazzId,@Param("num") int num);
}
