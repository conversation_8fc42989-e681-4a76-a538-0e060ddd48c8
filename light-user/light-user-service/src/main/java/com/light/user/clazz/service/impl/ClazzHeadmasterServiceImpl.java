package com.light.user.clazz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.light.user.clazz.mapper.ClazzHeadmasterMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.user.clazz.entity.dto.ClazzHeadmasterDto;
import com.light.user.clazz.entity.bo.ClazzHeadmasterConditionBo;
import com.light.user.clazz.entity.bo.ClazzHeadmasterBo;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.clazz.service.IClassesHeadmasterService;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 班级班主任接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 10:43:37
 */
@Service
public class ClazzHeadmasterServiceImpl extends ServiceImpl<ClazzHeadmasterMapper, ClazzHeadmasterDto> implements IClassesHeadmasterService {

	@Resource
	private ClazzHeadmasterMapper classesHeadmasterMapper;
	
    @Override
	public List<ClazzHeadmasterVo> getClassesHeadmasterListByCondition(ClazzHeadmasterConditionBo condition) {
        return classesHeadmasterMapper.getClazzHeadmasterListByCondition(condition);
	}

	@Override
	public AjaxResult addClassesHeadmaster(ClazzHeadmasterBo classesHeadmasterBo) {
		ClazzHeadmasterDto classesHeadmaster = new ClazzHeadmasterDto();
		BeanUtils.copyProperties(classesHeadmasterBo, classesHeadmaster);
		classesHeadmaster.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(classesHeadmaster)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateClassesHeadmaster(ClazzHeadmasterBo classesHeadmasterBo) {
		ClazzHeadmasterDto classesHeadmaster = new ClazzHeadmasterDto();
		BeanUtils.copyProperties(classesHeadmasterBo, classesHeadmaster);
		if(updateById(classesHeadmaster)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public Map<String, Object> getDetail(Long classesHeadmasterId) {
		LambdaQueryWrapper<ClazzHeadmasterDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(ClazzHeadmasterDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(ClazzHeadmasterDto::getId, classesHeadmasterId);
		ClazzHeadmasterDto classesHeadmaster = getOne(lqw);
		Map<String, Object> reuslt = new HashMap<String, Object>(4);
		reuslt.put("classesHeadmasterVo", classesHeadmaster==null?new ClazzHeadmasterVo():classesHeadmaster);
		return reuslt;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult delAndSaveBatch(Long teacherId, Long orgId, List<ClazzHeadmasterBo> boList) {
		//
		if(CollectionUtil.isEmpty(boList)){
			return AjaxResult.fail("任教信息不能为空");
		}
		//删除该学校原有 班主任记录
		this.deleteByTeacherIdAndOrgId(teacherId,orgId);

		// 新增班主任信息
		final List<ClazzHeadmasterDto> list = boList.stream().map(val -> {
			ClazzHeadmasterDto dto = new ClazzHeadmasterDto();
			dto.setIsDelete(StatusEnum.NOTDELETE.getCode());
			dto.setClassesId(val.getClassesId());
			dto.setTeacherId(teacherId);
			dto.setType(val.getType());
			return dto;
		}).collect(Collectors.toList());

		// 存储入库
		this.saveBatch(list);

		return AjaxResult.success();
	}

	@Override
	public int delByTeacherIdAndOrgId(Long teacherId, Long orgId) {
		return this.baseMapper.deleteByTeacherIdAndOrgId(teacherId,orgId);
	}

	@Override
	public List<ClazzHeadmasterVo> getByUserOid(String userOid) {
		return this.baseMapper.selectByUserOid(userOid);
	}

	public int  deleteByTeacherIdAndOrgId(Long teacherId, Long orgId){
		return this.baseMapper.deleteByTeacherIdAndOrgId(teacherId,orgId);
	}


}