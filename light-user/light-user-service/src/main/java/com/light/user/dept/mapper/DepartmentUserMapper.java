package com.light.user.dept.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.dept.entity.bo.DepartmentUserListConditionBo;
import com.light.user.dept.entity.dto.DepartmentUserDto;
import com.light.user.dept.entity.vo.DepartmentUserVo;

import java.util.List;

/**
 * 用户部门表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-12 18:09:51
 */
public interface DepartmentUserMapper extends BaseMapper<DepartmentUserDto> {

	List<DepartmentUserVo> getDepartmentUserListByCondition(DepartmentUserListConditionBo condition);

}
