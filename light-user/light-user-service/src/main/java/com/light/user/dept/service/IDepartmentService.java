package com.light.user.dept.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.core.entity.AjaxResult;
import com.light.user.dept.entity.bo.DepartmentBo;
import com.light.user.dept.entity.bo.DepartmentConditionBo;
import com.light.user.dept.entity.dto.DepartmentDto;
import com.light.user.dept.entity.vo.DepartmentVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 部门
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-04 10:59:09
 */
public interface IDepartmentService extends IService<DepartmentDto> {

    AjaxResult addDept(DepartmentBo bo);

    AjaxResult updateDept(Long id,DepartmentBo bo);

    AjaxResult deleteById(Long id);

    List<DepartmentVo> getDeptListByCondition(DepartmentConditionBo condition);
}
