package com.light.user.edu.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.edu.entity.bo.EducationListConditionBo;
import com.light.user.edu.entity.dto.EducationDto;
import com.light.user.edu.entity.vo.EducationVo;

/**
 * 教育信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-08 10:05:42
 */
public interface EducationMapper extends BaseMapper<EducationDto> {

	List<EducationVo> getEducationListByCondition(EducationListConditionBo condition);

}
