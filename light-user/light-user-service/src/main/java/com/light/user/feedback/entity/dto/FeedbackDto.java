package com.light.user.feedback.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 反馈信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-20 09:40:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_feedback")
public class FeedbackDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 反馈内容
	 */
	@TableField("feedback_content")
	private String feedbackContent;

	/**
	 * 联系电话
	 */
	@TableField("phone_number")
	private String phoneNumber;

	/**
	 * 版本ID
	 */
	@TableField("version_id")
	private Long versionId;

	/**
	 * 反馈状态 1：未读 2：已读
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
