package com.light.user.organization.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.StringUtils;
import com.light.user.organization.api.OrganizationSetupApi;
import com.light.user.organization.entity.bo.OrganizationSetupBo;
import com.light.user.organization.entity.bo.OrganizationSetupConditionBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.user.organization.service.IOrganizationSetupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组织设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-14 15:06:02
 */
@RestController
@Validated
public class OrganizationSetupController implements OrganizationSetupApi{
	
    @Autowired
    private IOrganizationSetupService organizationSetupService;

    /**
     * 查询组织设置表分页列表
     * <AUTHOR>
     * @date 2022-07-14 15:06:02
     */
    @Override
    public AjaxResult<PageInfo<OrganizationSetupVo>> getOrganizationSetupPageListByCondition(@RequestBody OrganizationSetupConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<OrganizationSetupVo> pageInfo = new PageInfo<>(organizationSetupService.getOrganizationSetupListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询组织设置表列表
	 * <AUTHOR>
	 * @date 2022-07-14 15:06:02
	 */
	@Override
	public AjaxResult<List<OrganizationSetupVo>> getOrganizationSetupListByCondition(@RequestBody OrganizationSetupConditionBo condition){
		List<OrganizationSetupVo> list = organizationSetupService.getOrganizationSetupListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增组织设置表
     * <AUTHOR>
     * @date 2022-07-14 15:06:02
     */
	@Override
    public AjaxResult addOrganizationSetup(@Validated @RequestBody OrganizationSetupBo organizationSetupBo){
		return organizationSetupService.addOrganizationSetup(organizationSetupBo);
    }

    /**
	 * 修改组织设置表
	 * @param organizationSetupBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-07-14 15:06:02
	 */
	@Override
	public AjaxResult updateOrganizationSetup(@Validated @RequestBody OrganizationSetupBo organizationSetupBo) {
		if(null == organizationSetupBo.getId()) {
			return AjaxResult.fail("组织设置表id不能为空");
		}
		return organizationSetupService.updateOrganizationSetup(organizationSetupBo);
	}

	/**
	 * 查询组织设置表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-07-14 15:06:02
	 */
	@Override
	public AjaxResult<OrganizationSetupVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("组织设置表id不能为空");
		}
		OrganizationSetupVo vo = organizationSetupService.getDetail(id);
		return AjaxResult.success(vo);
	}

	@Override
	public AjaxResult<OrganizationSetupVo> getByOrgId(@PathVariable("orgId") Long orgId) {
		return AjaxResult.success(this.organizationSetupService.getVoByOrgId(orgId));
	}

	/**
	 * 删除组织设置表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-07-14 15:06:02
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		if(organizationSetupService.removeById(id)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}


	@Override
	public AjaxResult saveOrganizationSetup(@RequestBody OrganizationSetupBo organizationSetupBo) {
		return this.organizationSetupService.saveOrganizationSetup(organizationSetupBo);
	}

	/**
	 * 根据域名获取组织机构设置
	 * @param setupDomainName
	 * @return
	 */
	@Override
	public AjaxResult getOrganizationBySetupDomain(@RequestParam("setupDomainName") String setupDomainName) {
		if(StringUtils.isEmpty(setupDomainName)) {
			return AjaxResult.fail("请选择setup域名");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("setup", organizationSetupService.getOrganizationBySetupDomain(setupDomainName));
		return AjaxResult.success(map);
	}
}
