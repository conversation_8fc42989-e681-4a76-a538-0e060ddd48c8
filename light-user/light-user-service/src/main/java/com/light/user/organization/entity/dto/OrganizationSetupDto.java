package com.light.user.organization.entity.dto;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织设置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-14 15:06:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_organization_setup")
public class OrganizationSetupDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 平台名称
	 */
	@TableField("web_name")
	private String webName;

	/**
	 * 是否需要资源审核：0：否，1：是
	 */
	@TableField("is_resource_check")
	private Integer isResourceCheck;

	/**
	 * 注册方式(1、自注册，2、邀请制，3、管理员制)
	 */
	@TableField("register_type")
	private Integer registerType;

	/**
	 * logo
	 */
	@TableField("logo")
	private String logo;

	/**
	 * 
	 */
	@TableField("sign")
	private String sign;

	/**
	 * 登录图片
	 */
	@TableField("login_picture")
	private String loginPicture;

	/**
	 * 是否强制登陆：0：否，1：是
	 */
	@TableField("is_redirect_login")
	private Integer isRedirectLogin;

	/**
	 * 二维码
	 */
	@TableField("qrcode")
	private String qrcode;

	/**
	 * 是否使用数联的版权信息：0：否，1：是
	 */
	@TableField("is_use_copyright")
	private Integer isUseCopyright;

	/**
	 * 版全信息
	 */
	@TableField(value = "copyright_info" , strategy = FieldStrategy.IGNORED)
	private String copyrightInfo;

	/**
	 * icon
	 */
	@TableField("icon")
	private String icon;

	/**
	 * icon标签
	 */
	@TableField("icon_tag")
	private String iconTag;

	/**
	 * 联系信息
	 */
	@TableField("contact_info")
	private String contactInfo;

	/**
	 * QQ号
	 */
	@TableField("qq_number")
	private String qqNumber;

	/**
	 * 其他配置
	 */
	@TableField(value = "other_config", strategy = FieldStrategy.IGNORED)
	private String otherConfig;

	/**
	 * setup域名
	 */
	@TableField("setup_domain_name")
	private String setupDomainName;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
