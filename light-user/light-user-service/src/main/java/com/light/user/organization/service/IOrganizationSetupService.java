package com.light.user.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.user.organization.entity.dto.OrganizationSetupDto;
import com.light.user.organization.entity.bo.OrganizationSetupConditionBo;
import com.light.user.organization.entity.bo.OrganizationSetupBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 组织设置表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-14 15:06:02
 */
public interface IOrganizationSetupService extends IService<OrganizationSetupDto> {

    List<OrganizationSetupVo> getOrganizationSetupListByCondition(OrganizationSetupConditionBo condition);

	AjaxResult addOrganizationSetup(OrganizationSetupBo organizationSetupBo);

	AjaxResult updateOrganizationSetup(OrganizationSetupBo organizationSetupBo);

	OrganizationSetupVo getDetail(Long id);

    AjaxResult saveOrganizationSetup(OrganizationSetupBo organizationSetupBo);

	OrganizationSetupVo getVoByOrgId(Long orgId);

	OrganizationSetupDto getByOrgId(Long orgId);

	OrganizationSetupVo getOrganizationBySetupDomain(String setupDomainName);
}

