package com.light.user.points.controller;

import com.light.user.points.api.UserPointsApi;
import com.light.user.points.entity.dto.UserPointsDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.user.points.entity.bo.UserPointsConditionBo;
import com.light.user.points.entity.bo.UserPointsBo;
import com.light.user.points.entity.vo.UserPointsVo;
import com.light.user.points.service.IUserPointsService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 用户总积分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-11 15:15:16
 */
@RestController
@Validated
public class UserPointsController implements UserPointsApi{
	
    @Autowired
    private IUserPointsService userPointsService;

    /**
     * 查询用户总积分表分页列表
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
     */
    @Override
    public AjaxResult<PageInfo<UserPointsVo>> getUserPointsPageListByCondition(@RequestBody UserPointsConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<UserPointsVo> pageInfo = new PageInfo<>(userPointsService.getUserPointsListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	@Override
	public AjaxResult<PageInfo<UserPointsVo>> getOrganizationPointsPageListByCondition(@RequestBody UserPointsConditionBo condition) {
		PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
		PageInfo<UserPointsVo> pageInfo = new PageInfo<>(userPointsService.getOrganizationPointsPageListByCondition(condition));
		return AjaxResult.success(pageInfo);
	}

	/**
	 * 查询用户总积分表列表
	 * <AUTHOR>
	 * @date 2023-04-11 15:15:16
	 */
	@Override
	public AjaxResult<List<UserPointsVo>> getUserPointsListByCondition(@RequestBody UserPointsConditionBo condition){
		List<UserPointsVo> list = userPointsService.getUserPointsListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增用户总积分表
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
     */
	@Override
    public AjaxResult addUserPoints(@Validated @RequestBody UserPointsBo userPointsBo){
		return userPointsService.addUserPoints(userPointsBo);
    }

    /**
	 * 修改用户总积分表
	 * @param userPointsBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
	 */
	@Override
	public AjaxResult updateUserPoints(@Validated @RequestBody UserPointsBo userPointsBo) {
		if(null == userPointsBo.getId()) {
			return AjaxResult.fail("用户总积分表id不能为空");
		}
		return userPointsService.updateUserPoints(userPointsBo);
	}

	/**
	 * 查询用户总积分表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
	 */
	@Override
	public AjaxResult<UserPointsVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("用户总积分表id不能为空");
		}
		UserPointsVo vo = userPointsService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除用户总积分表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		UserPointsDto userPointsDto = new UserPointsDto();
		userPointsDto.setId(id);
		userPointsDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(userPointsService.updateById(userPointsDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	@Override
	public AjaxResult<UserPointsVo> getUserPointsRankByUserOid(@RequestParam("userOid") String userOid) {
		return AjaxResult.success(userPointsService.getUserPointsRankByUserOid(userOid));
	}
}
