package com.light.user.points.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.user.points.entity.dto.UserPointsDetailDto;
import com.light.user.points.entity.bo.UserPointsDetailConditionBo;
import com.light.user.points.entity.bo.UserPointsDetailBo;
import com.light.user.points.entity.vo.UserPointsDetailVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 用户积分明细表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-11 15:15:16
 */
public interface IUserPointsDetailService extends IService<UserPointsDetailDto> {

    List<UserPointsDetailVo> getUserPointsDetailListByCondition(UserPointsDetailConditionBo condition);

	AjaxResult addUserPointsDetail(UserPointsDetailBo userPointsDetailBo);

	AjaxResult updateUserPointsDetail(UserPointsDetailBo userPointsDetailBo);

	UserPointsDetailVo getDetail(Long id);

    AjaxResult addUserPointsDetailAndUpdateTotal(UserPointsDetailDto userPointsDetailDto);
}

