package com.light.user.register.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.user.admin.entity.dto.AdminDto;
import com.light.user.admin.service.IAdminService;
import com.light.user.organization.entity.dto.OrganizationDto;
import com.light.user.organization.service.IOrganizationService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Random;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.user.register.entity.dto.RegisterDto;
import com.light.user.register.entity.bo.RegisterConditionBo;
import com.light.user.register.entity.bo.RegisterBo;
import com.light.user.register.entity.vo.RegisterVo;
import com.light.user.register.service.IRegisterService;
import com.light.user.register.mapper.RegisterMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 注册表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-15 10:10:54
 */
@Service
public class RegisterServiceImpl extends ServiceImpl<RegisterMapper, RegisterDto> implements IRegisterService {

	@Resource
	private RegisterMapper registerMapper;

	@Resource
	private IAdminService adminService;

	@Resource
	private IOrganizationService organizationService;
	
    @Override
	public List<RegisterVo> getRegisterListByCondition(RegisterConditionBo condition) {
		FuzzyQueryUtil.transferMeanBean(condition);
        return registerMapper.getRegisterListByCondition(condition);
	}

	@Override
	public AjaxResult addRegister(RegisterBo registerBo) {
		RegisterDto register = new RegisterDto();
		BeanUtils.copyProperties(registerBo, register);
		if(save(register)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateRegister(RegisterBo registerBo) {
		RegisterDto register = new RegisterDto();
		BeanUtils.copyProperties(registerBo, register);
		if(updateById(register)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public RegisterVo getDetail(Long id) {
		RegisterConditionBo condition = new RegisterConditionBo();
		condition.setId(id);
		List<RegisterVo> list = registerMapper.getRegisterListByCondition(condition);
		RegisterVo vo = new RegisterVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

	@Override
	@Transactional
	public AjaxResult pass(Long id) {
		RegisterDto dto = registerMapper.selectById(id);
		if(null == dto) {
			return AjaxResult.fail("注册申请不存在");
		}else{
			if(dto.getStatus().intValue() != 0) {
				return AjaxResult.fail("该注册申请已被审核");
			}
		}
		//再次确认手机号是否已经注册
		if(!CollectionUtils.isEmpty(adminService.list((new LambdaQueryWrapper<AdminDto>()
				.eq(AdminDto::getPhone, dto.getPhone())
				.eq(AdminDto::getIsDelete, StatusEnum.NOTDELETE.getCode()))))) {
			return AjaxResult.fail("该手机号已经注册");
		}
		if(!CollectionUtils.isEmpty(adminService.list((new LambdaQueryWrapper<AdminDto>()
				.eq(AdminDto::getAccountName, dto.getPhone())
				.eq(AdminDto::getIsDelete, StatusEnum.NOTDELETE.getCode()))))) {
			return AjaxResult.fail("该手机号已经注册");
		}
		//创建组织机构
		OrganizationDto org = new OrganizationDto();
		org.setCode(RandomUtil.randomString(32));
		org.setName(dto.getOrganizationName());
		org.setIsDelete(StatusEnum.NOTDELETE.getCode());
		org.setParentId(0l);
		org.setLoginType(0);
		org.setIsUsed(StatusEnum.YES.getCode());
		org.setType(1l);
		if(organizationService.save(org)) {
			//创建管理员账号
			AdminDto admin = new AdminDto();
            admin.setAccountName(dto.getPhone());
            admin.setOid(RandomUtil.randomString(64));
            admin.setPhone(dto.getPhone());
            admin.setPassword(dto.getPassword());
            admin.setOrganizationId(org.getId());
            admin.setIsDelete(StatusEnum.NOTDELETE.getCode());
            admin.setIsDelete(StatusEnum.NO.getCode());
            admin.setIsActivation(StatusEnum.YES.getCode());
            if(adminService.save(admin)) {
                dto.setStatus(99);
                if(updateById(dto)) {
                    return AjaxResult.success("审核通过");
                }else{
                    return AjaxResult.fail("审核失败");
                }
            }else{
                return AjaxResult.fail("审核失败");
            }
		}
		return AjaxResult.fail("审核失败");
	}

}