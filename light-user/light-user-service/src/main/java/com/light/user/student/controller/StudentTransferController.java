package com.light.user.student.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.light.feign.annotation.FeignValidatorAnnotation;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.user.student.entity.bo.StudentTransferConditionBo;
import com.light.user.student.entity.bo.StudentTransferBo;
import com.light.user.student.entity.vo.StudentTransferVo;
import com.light.user.student.service.IStudentTransferService;
import com.light.user.student.api.StudentTransferApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;
/**
 * 学生转校转班表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-15 18:56:56
 */
@RestController
@Validated
@Api(value = "", tags = "学生转校转班表接口" )
public class StudentTransferController implements StudentTransferApi{
	
    @Autowired
    private IStudentTransferService studentTransferService;

	@FeignValidatorAnnotation
    public AjaxResult getStudentTransferListByCondition(@RequestBody StudentTransferConditionBo condition){
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(SystemConstants.NO_PAGE.equals(condition.getPageNo())){
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("list",studentTransferService.getStudentTransferListByCondition(condition));
			return AjaxResult.success(map);
		}else {
			PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
			PageInfo<StudentTransferVo> pageInfo = new PageInfo<>(studentTransferService.getStudentTransferListByCondition(condition));
			return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
		}
    }

	@FeignValidatorAnnotation
    public AjaxResult addStudentTransfer(@Validated @RequestBody StudentTransferBo studentTransferBo){
		return studentTransferService.addStudentTransfer(studentTransferBo);
    }

	@FeignValidatorAnnotation
	public AjaxResult addStudentTransferBatch(@Validated @RequestBody List<StudentTransferBo> studentTransferBoList){
		return studentTransferService.addStudentTransferBatch(studentTransferBoList);
	}

	@FeignValidatorAnnotation
	public AjaxResult updateStudentTransfer(@Validated @RequestBody StudentTransferBo studentTransferBo) {
		if(null == studentTransferBo.getId()) {
			return AjaxResult.fail("id不能为空");
		}
		return studentTransferService.updateStudentTransfer(studentTransferBo);
	}

	@FeignValidatorAnnotation
	public AjaxResult getDetail(@NotNull(message = "请选择数据") Long id) {
		Map<String, Object> map = studentTransferService.getDetail(id);
		return AjaxResult.success(map);
	}

	@FeignValidatorAnnotation
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long id) {
		StudentTransferBo studentTransferBo = new StudentTransferBo();
		studentTransferBo.setId(id);
		studentTransferBo.setIsDelete(StatusEnum.ISDELETE.getCode());
		return studentTransferService.updateStudentTransfer(studentTransferBo);
	}
}
