package com.light.user.student.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.student.entity.bo.StudentTransferConditionBo;
import com.light.user.student.entity.dto.StudentTransferDto;
import com.light.user.student.entity.vo.StudentTransferVo;

/**
 * 学生转校转班表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 09:34:10
 */
public interface StudentTransferMapper extends BaseMapper<StudentTransferDto> {

	List<StudentTransferVo> getStudentTransferListByCondition(StudentTransferConditionBo condition);

}
