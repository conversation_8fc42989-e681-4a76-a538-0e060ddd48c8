package com.light.user.teacher.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.core.utils.DatePeriodUtil;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.dto.TeacherDto;
import com.light.user.teacher.entity.vo.TeacherVo;
import org.apache.ibatis.annotations.Param;

/**
 * 教师信息表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 09:38:46
 */
public interface TeacherMapper extends BaseMapper<TeacherDto> {

	List<TeacherVo> getTeacherListByCondition(TeacherConditionBo condition);

    List<TeacherVo> selectByPhone(@Param("phone") String phone);

    Map<String, Object> getTeacherTotal(DatePeriodUtil.DatePeriod period);

    List<Map<String, Object>> getTeacherSectionList(DatePeriodUtil.DatePeriod period);

    List<TeacherVo> getOrgStudentListByRealNames(@Param("orgId") Long orgId, @Param("realNames") List<String> realNames);

    List<TeacherVo> getTeacherInfoListByUserOids(@Param("userOids") List<String> userOids);

}
