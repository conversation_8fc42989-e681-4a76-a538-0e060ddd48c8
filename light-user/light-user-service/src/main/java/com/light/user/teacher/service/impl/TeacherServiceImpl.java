package com.light.user.teacher.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.light.core.constants.SystemConstants;
import com.light.core.exception.UnifiedException;
import com.light.core.utils.DatePeriodUtil;
import com.light.user.account.entity.dto.AccountDto;
import com.light.user.account.entity.dto.AccountUserDto;
import com.light.user.account.entity.vo.AccountVo;
import com.light.user.account.service.IAccountService;
import com.light.user.account.service.IAccountUserService;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.clazz.service.IClassesService;
import com.light.user.edu.entity.bo.EducationBo;
import com.light.user.edu.entity.dto.EducationDto;
import com.light.user.edu.entity.vo.EducationVo;
import com.light.user.edu.service.IEducationService;
import com.light.user.organization.entity.dto.OrganizationDto;
import com.light.user.organization.service.IOrganizationService;
import com.light.user.teacher.entity.bo.TeacherSubjectBo;
import com.light.user.teacher.entity.dto.TeacherClassesSubjectDto;
import com.light.user.teacher.entity.dto.TeacherDto;
import com.light.user.teacher.service.ITeacherClassesSubjectService;
import com.light.user.teacher.service.ITeacherSubjectService;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.dto.UserDto;
import com.light.user.user.entity.dto.UserIdentityDto;
import com.light.user.user.entity.dto.UserOrgDto;
import com.light.user.user.entity.dto.UserRoleDto;
import com.light.user.user.entity.vo.UserOrgVo;
import com.light.user.user.entity.vo.UserVo;
import com.light.user.user.service.IUserIdentityService;
import com.light.user.user.service.IUserOrgService;
import com.light.user.user.service.IUserRoleService;
import com.light.user.user.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.teacher.service.ITeacherService;
import com.light.user.teacher.mapper.TeacherMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 教师信息表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-03-14 09:38:46
 */
@Service
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, TeacherDto> implements ITeacherService {

	@Resource
	private TeacherMapper teacherMapper;

	@Lazy
	@Autowired
	private IUserService iUserService;

	@Lazy
	@Autowired
	private IAccountService iAccountService;

	@Lazy
	@Autowired
	private IAccountUserService iAccountUserService;

	@Lazy
	@Autowired
	private IEducationService iEducationService;

	@Lazy
	@Autowired
	private IUserIdentityService iUserIdentityService;

	@Lazy
	@Autowired
	private IUserOrgService iUserOrgService;

    @Resource
    private IOrganizationService iOrganizationService;

	@Lazy
	@Autowired
	private ITeacherSubjectService iTeacherSubjectService;

    @Resource
    private ITeacherClassesSubjectService iTeacherClassesSubjectService;

    @Resource
    private IClassesService iClassesService;

    @Resource
    private IUserRoleService iUserRoleService;


	@Value("${email.default.suffix:''}")
	private String emailDefaultSuffix;


    @Override
	public List<TeacherVo> getTeacherListByCondition(TeacherConditionBo condition) {
        return teacherMapper.getTeacherListByCondition(condition);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult addBatchTeacher(List<TeacherBo> teacherBos){

		if(CollectionUtil.isEmpty(teacherBos)){
			throw new UnifiedException("教师数据不能为空");
		}

		for (TeacherBo teacherBo : teacherBos){
			this.addTeacher(teacherBo);
		}

		return AjaxResult.success();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult<TeacherVo> addTeacher(TeacherBo teacherBo) {

		final UserBo userBo = teacherBo.getUser();
		final String phone = userBo.getPhone();
		//校验手机号 是否重复
		if(StrUtil.isNotEmpty(phone)){
			final int count = this.iAccountService.getCountByPhone(phone);
			if(count > 0){
				throw new UnifiedException("["+phone + "]手机号已存在");
			}
		}

		//创建用户信息
		UserDto user = BeanUtil.toBean(userBo,UserDto.class);
		String userOid = RandomUtil.randomString(SystemConstants.OID_LENGTH);
        if (userBo.getUserId() != null) {
            UserDto byUserId = this.iUserService.getByUserId(userBo.getUserId());
            user.setId(byUserId.getId());
            userOid = byUserId.getOid();
        }
        user.setOid(userOid);
        String finalUserOid = userOid;


		//老师任教学科信息
		final List<String> subjectList = teacherBo.getSubjectList();
		if(CollUtil.isNotEmpty(subjectList)){
			final List<TeacherSubjectBo> teacherSubjectBoList = subjectList.stream().map(x -> {
				TeacherSubjectBo teacherSubjectBo = new TeacherSubjectBo();
				teacherSubjectBo.setSubjectCode(x);
				teacherSubjectBo.setTeacherUserOid(finalUserOid);
				teacherSubjectBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
				return teacherSubjectBo;
			}).collect(Collectors.toList());
			this.iTeacherSubjectService.addBatchTeacherSubject(teacherSubjectBoList);
		}

		//保存身份
		UserIdentityDto userIdentityDto = new UserIdentityDto();
		userIdentityDto.setIdentityType(SystemConstants.TEACHER_IDENTITY);
		userIdentityDto.setUserOid(userOid);
		this.iUserIdentityService.save(userIdentityDto);

        user.setUserIdentities(String.valueOf(SystemConstants.TEACHER_IDENTITY));




        if(userBo.getOrganizationId() != null){

            //保存组织机构
            UserOrgDto userOrgDto = new UserOrgDto();
            userOrgDto.setUserOid(userOid);
            userOrgDto.setOrganizationId(userBo.getOrganizationId());
            this.iUserOrgService.save(userOrgDto);

            final OrganizationDto organizationDto = this.iOrganizationService.getById(userBo.getOrganizationId());
            Optional.ofNullable(organizationDto).ifPresent(o->{
                user.setOrganizationIds(o.getId().toString());
                user.setOrganizationNames(o.getName());
                user.setOrgProvinceIds(Optional.ofNullable(o.getProvinceId()).map(Object::toString).orElse(StringUtil.EMPTY));
                user.setOrgCityIds(Optional.ofNullable(o.getCityId()).map(Object::toString).orElse(StringUtil.EMPTY));
                user.setOrgAreaIds(Optional.ofNullable(o.getAreaId()).map(Object::toString).orElse(StringUtil.EMPTY));
                user.setOrgProvinceNames(o.getProvinceName());
                user.setOrgCityNames(o.getCityName());
                user.setOrgAreaNames(o.getAreaName());
            });

        }


        // 用户角色
        final List<Long> roleIds = userBo.getRoleIds();
        if(CollectionUtil.isNotEmpty(roleIds)){
            final List<UserRoleDto> userRoleDtos = roleIds.stream().map(val -> {
                UserRoleDto userRoleDto = new UserRoleDto();
                userRoleDto.setUserOid(finalUserOid);
                userRoleDto.setRoleId(val);
                userRoleDto.setOrganizationId(userBo.getOrganizationId());
                return userRoleDto;
            }).collect(Collectors.toList());
            this.iUserRoleService.saveBatch(userRoleDtos);
        }

        //生成账号
		String accountName = null, accountOid = null;
		if(teacherBo.isGeneratorAccount()){
			AccountDto accountDto = null;
			// 手动输入账号
			final String userAccountName = userBo.getAccountName();
			if(StrUtil.isNotEmpty(userAccountName)){
				final int count = this.iAccountService.getCountByAccountName(userAccountName);
				if(count > 0){
					throw new UnifiedException("账号已存在");
				}
				accountDto = this.iAccountService.generator(phone, userAccountName, userBo.getAccountSource(),userBo.getPassword());
			}else {
				// 自动生成账号
				accountDto = this.iAccountService.generator(phone, userBo.getAccountSource(), userBo.getPassword());
			}
            accountOid = accountDto.getOid();
            accountDto.setRemark(userBo.getRemark());
            user.setAccountOid(accountDto.getOid());
            user.setAccountId(accountDto.getId());
            user.setAccountName(accountDto.getAccountName());

            // 插入第三方ID
            final String accountThirdId = userBo.getAccountThirdId();
            if(StrUtil.isNotEmpty(accountThirdId)){
                accountDto.setThirdId(accountThirdId);
                this.iAccountService.updateById(accountDto);
            }

			accountName = accountDto.getAccountName();
			//创建账号用户关联
			AccountUserDto accountUserDto = new AccountUserDto();
			accountUserDto.setAccountOid(accountDto.getOid());
			accountUserDto.setUserOid(userOid);
			this.iAccountUserService.save(accountUserDto);

			// 是否设置默认邮箱
			if(teacherBo.isSetDefaultEmail() && StrUtil.isEmpty(user.getEmail())){
				user.setEmail(accountDto.getAccountName() + emailDefaultSuffix);
			}
		}

        this.iUserService.saveOrUpdate(user);

		// 添加老师
		TeacherDto teacher = new TeacherDto();
		BeanUtils.copyProperties(teacherBo, teacher);
		teacher.setUserOid(userOid);
		teacher.setIsDelete(StatusEnum.NOTDELETE.getCode());
		this.baseMapper.insert(teacher);

		//添加教育信息
		final List<EducationBo> educationBoList = teacherBo.getEducationList();
		if(CollectionUtil.isNotEmpty(educationBoList)){
			final List<EducationDto> educationDtoList = educationBoList.stream().map(val -> {
				final EducationDto educationDto = BeanUtil.toBean(val, EducationDto.class);
				educationDto.setUserOid(finalUserOid);
				return educationDto;
			}).collect(Collectors.toList());
			this.iEducationService.saveBatch(educationDtoList);
		}

		TeacherVo teacherVo = BeanUtil.toBean(teacher,TeacherVo.class);
		teacherVo.setAccountName(accountName);
        teacherVo.setAccountOid(accountOid);
		return AjaxResult.success(teacherVo);
	}


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateBatchTeacher(List<TeacherBo> teacherBos) {
        if(CollectionUtil.isEmpty(teacherBos)){
            throw new UnifiedException("教师数据不能为空");
        }

        for (TeacherBo teacherBo : teacherBos){
            final Long id = teacherBo.getId();
            if(id == null){
                teacherBo.setId(teacherBo.getTeacherId());
            }
            this.updateTeacher(teacherBo);
        }
        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveOrUpdateBatchTeacher(List<TeacherBo> teacherBos) {
        if(CollectionUtil.isEmpty(teacherBos)){
            throw new UnifiedException("教师数据不能为空");
        }
        final List<TeacherBo> waitAddList = teacherBos.stream().filter(x -> x.getId() == null && x.getTeacherId() == null).collect(Collectors.toList());
        final List<TeacherBo> waitUpdateList = teacherBos.stream().filter(x -> x.getId() != null || x.getTeacherId() != null).collect(Collectors.toList());

        // 新增
        if(CollUtil.isNotEmpty(waitAddList)){
            this.addBatchTeacher(waitAddList);
        }
        // 更新
        if(CollUtil.isNotEmpty(waitUpdateList)){
            this.updateBatchTeacher(waitUpdateList);
        }

        return AjaxResult.success();
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult updateTeacher(TeacherBo teacherBo) {

		Long teacherId = teacherBo.getTeacherId();
		if(teacherId == null) teacherId = teacherBo.getId();
		TeacherDto teacherDto = this.getByTeacherId(teacherId);
		if(teacherDto == null){
			throw new UnifiedException("教师信息不存在");
		}

        this.updateTeacherInfo(teacherBo, teacherDto);

        return AjaxResult.success();
	}

    private void updateTeacherInfo(TeacherBo teacherBo, TeacherDto teacherDto) {
        //获取用户信息
        final String userOid = teacherDto.getUserOid();
        final UserDto userDto = this.iUserService.getByUserOid(userOid);
        final Long userId = userDto.getId();

        //
        UserBo userBo = teacherBo.getUser();
        if(userBo == null){
            userBo = new UserBo();
        }
        if(StrUtil.isNotEmpty(teacherBo.getUserRealName())){
            userBo.setRealName(teacherBo.getUserRealName());
        }

        //校验手机号
        final String phone = userBo.getPhone();
        final String oldPhone = userDto.getPhone();
        //校验手机号 是否重复
        if(StrUtil.isNotEmpty(phone) && StrUtil.isNotEmpty(oldPhone) && !phone.equals(oldPhone)){
            final int count = this.iAccountService.getCountByPhone(phone);
            if(count > 0){
                throw new UnifiedException("手机号已存在");
            }
        }

        //校验 证件号
        final String identityType = userBo.getIdentityType();
        final String identityCardNumber = userBo.getIdentityCardNumber();
        // 原证件号信息
        final String oldIdentityType = userDto.getIdentityType();
        final String oldIdentityCardNumber = userDto.getIdentityCardNumber();
        //原证件类型 和证件号 与原有证件号不同 进行校验
        if(StrUtil.isNotEmpty(identityType) && StrUtil.isNotEmpty(identityCardNumber)

             && StrUtil.isNotEmpty(oldIdentityType) && StrUtil.isNotEmpty(oldIdentityCardNumber)
                && !identityType.equals(oldIdentityType) && identityCardNumber.equals(oldIdentityCardNumber)
        ){
            final Integer count = this.iUserService.getCountByIdentityTypeAndCard(identityType, identityCardNumber);
            if(count > 0){
                throw new UnifiedException("该证件号已存在");
            }
        }


        //更新用户信息
        UserDto user = BeanUtil.toBean(userBo,UserDto.class);
        user.setId(userId);
        user.setOid(userOid);
        this.iUserService.updateById(user);

        //老师任教学科信息
        final List<String> subjectList = teacherBo.getSubjectList();
        if(CollUtil.isNotEmpty(subjectList)){

            // 删除之前学科信息
            this.iTeacherSubjectService.delByUserOid(userOid);
            // 添加新学科信息
            final List<TeacherSubjectBo> teacherSubjectBoList = subjectList.stream().map(x -> {
                TeacherSubjectBo teacherSubjectBo = new TeacherSubjectBo();
                teacherSubjectBo.setSubjectCode(x);
                teacherSubjectBo.setTeacherUserOid(userOid);
                teacherSubjectBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
                return teacherSubjectBo;
            }).collect(Collectors.toList());
            this.iTeacherSubjectService.addBatchTeacherSubject(teacherSubjectBoList);
        }

        if(teacherBo.getIsUpdateClassInfos() != null && teacherBo.getIsUpdateClassInfos().equals(StatusEnum.YES.getCode())){
            // 删除原有任教班级
            this.iTeacherClassesSubjectService.delByUserOid(userOid);

            // 任教班级
            final List<TeacherBo.ClassInfo> classInfos = teacherBo.getClassInfos();
            if(CollUtil.isNotEmpty(classInfos)){

                // 过滤 班级ID不为空 或 班级名称、班级年级 、班级学校 不为空数据
                final List<TeacherBo.ClassInfo> classInfoList = classInfos.stream().filter(x ->
                        x.getClassId() != null
                                ||
                                (StrUtil.isNotEmpty(x.getClassName()) && StrUtil.isNotEmpty(x.getGrade()) && x.getOrganizationId() != null)
                ).collect(Collectors.toList());

                if(CollUtil.isNotEmpty(classInfoList)) {
                    // 删除原有任教班级
                    this.iTeacherClassesSubjectService.delByUserOid(userOid);

                    // 构建老师任教班级
                    final List<TeacherClassesSubjectDto> teacherClassesSubjectDtos = classInfoList.stream().map(x -> {
                        TeacherClassesSubjectDto dto = new TeacherClassesSubjectDto();
                        final String grade = x.getGrade();
                        final Long organizationId = x.getOrganizationId();
                        final String className = x.getClassName();

                        Long classId = x.getClassId();
                        if (classId == null) {
                            final ClazzVo clazzVO = this.iClassesService.getOrSaveByCondition(className, grade, organizationId);
                            classId = clazzVO.getId();
                        }

                        dto.setTeacherUserOid(userOid);
                        dto.setClassesId(classId);
                        return dto;
                    }).collect(Collectors.toList());

                    // 批量保存老师任教班级
                    this.iTeacherClassesSubjectService.saveBatch(teacherClassesSubjectDtos);
                }
            }

        }

        //更新账号
        final String remark = userBo.getRemark();
        final List<AccountVo> accountVoList = this.iAccountService.getByUserOid(userOid);
        if(CollectionUtil.isNotEmpty(accountVoList)){
            //重新组装
            final List<AccountDto> accountList = accountVoList.stream().map(val -> {
                AccountDto accountDto = new AccountDto();
                accountDto.setId(val.getId());
                accountDto.setPhone(phone);
                accountDto.setRemark(remark);
                return accountDto;
            }).collect(Collectors.toList());
            this.iAccountService.updateBatchById(accountList);
        }


        // 更新老师信息
        TeacherDto teacher = new TeacherDto();
        BeanUtils.copyProperties(teacherBo, teacher);
        teacher.setId(teacherDto.getId());
        teacher.setUserOid(userOid);
        teacher.setIsDelete(StatusEnum.NOTDELETE.getCode());
        this.baseMapper.updateById(teacher);

        //修改教育信息
        final List<EducationBo> educationBoList = teacherBo.getEducationList();
        if(CollectionUtil.isNotEmpty(educationBoList)){
            //删除之前的教育信息
            this.iEducationService.delByUserOid(userOid);
            //添加现有的教育信息
            final List<EducationDto> educationDtoList = educationBoList.stream().map(val -> {
                final EducationDto educationDto = BeanUtil.toBean(val, EducationDto.class);
                educationDto.setUserOid(userOid);
                return educationDto;
            }).collect(Collectors.toList());
            this.iEducationService.saveBatch(educationDtoList);
        }
    }

    public TeacherDto getByTeacherId(Long teacherId) {
		QueryWrapper<TeacherDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(TeacherDto::getIsDelete,StatusEnum.NOTDELETE.getCode())
				.eq(TeacherDto::getId,teacherId);

		return this.baseMapper.selectOne(queryWrapper);
	}

	@Override
	public TeacherVo getDetail(Long teacherId) {
		final TeacherDto teacherDto = this.getByTeacherId(teacherId);
		if(teacherDto == null){
			return null;
		}
        return this.buildTeacherVo(teacherDto);
	}

    private TeacherVo buildTeacherVo(TeacherDto teacherDto) {
        TeacherVo teacherVo = BeanUtil.toBean(teacherDto,TeacherVo.class);

        //设置获取用户信息
        final String userOid = teacherVo.getUserOid();
        final UserDto userDto = this.iUserService.getByUserOid(userOid);
        if(userDto == null ){
            return null;
        }
        //用户基础信息
        final UserVo userVo = BeanUtil.toBean(userDto, UserVo.class);

        // 账号信息
        final List<AccountVo> accountVos = this.iAccountService.getByUserOid(userOid);
        if(CollUtil.isNotEmpty(accountVos)){
            userVo.setAccountName(accountVos.get(0).getAccountName());
            userVo.setAccountPhone(accountVos.get(0).getPhone());
            teacherVo.setAccountName(accountVos.get(0).getAccountName());
        }

        // 获取任教科目信息
        List<String> subjectList = this.iTeacherSubjectService.getSubjectListByUserOid(userOid);
        teacherVo.setSubjectList(subjectList);


        //获取组织机构
        final List<UserOrgDto> userOrgList = this.iUserOrgService.getListByUserOid(userOid);
        if(CollectionUtil.isNotEmpty(userOrgList)){
            final List<UserOrgVo> userOrgVos = JSONUtil.toList(JSONUtil.parseArray(userOrgList), UserOrgVo.class);
            userVo.setUserOrgList(userOrgVos);
        }

        //身份信息
        userVo.setUserIdentityType(SystemConstants.TEACHER_IDENTITY);
        final List<UserIdentityDto> userIdentityDtoList = this.iUserIdentityService.getByUserOid(userOid);
        if(CollectionUtil.isNotEmpty(userIdentityDtoList)){
            final List<Integer> userIdentityList = userIdentityDtoList.stream().map(UserIdentityDto::getIdentityType)
                    .collect(Collectors.toList());
            userVo.setUserIdentityTypeList(userIdentityList);
        }
        teacherVo.setUserVo(userVo);

        //教育信息
        List<EducationDto> educationDtoList = this.iEducationService.getByUserOid(userOid);
        if(CollectionUtil.isNotEmpty(educationDtoList)) {
            final List<EducationVo> educationVos = JSONUtil.toList(JSONUtil.parseArray(educationDtoList), EducationVo.class);
            teacherVo.setEducationList(educationVos);
        }
        return teacherVo;
    }

    @Override
	public TeacherVo getByUserOid(String userOid) {
		LambdaQueryWrapper<TeacherDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(TeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(TeacherDto::getUserOid, userOid);
		List<TeacherDto> teacherDtos = this.baseMapper.selectList(lqw);
		if(CollectionUtil.isNotEmpty(teacherDtos)){
			return BeanUtil.toBean(teacherDtos.get(0),TeacherVo.class);
		}
		return null;
	}

    public TeacherDto getDtoByUserOid(String userOid) {
        LambdaQueryWrapper<TeacherDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(TeacherDto::getUserOid, userOid);
        return this.baseMapper.selectOne(lqw);
    }

    public List<TeacherDto> getDtoByUserOidList(List<String> userOidList) {
        LambdaQueryWrapper<TeacherDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.in(TeacherDto::getUserOid, userOidList);
        return this.baseMapper.selectList(lqw);
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult delete(Long teacherId, boolean delAccount) {
		//删除老师
		TeacherDto teacherDto = this.baseMapper.selectById(teacherId);
		if(teacherDto == null){
			return AjaxResult.fail("老师不存在");
		}
        deleteOtherInfo( delAccount, teacherDto);

        return AjaxResult.success();
	}

    private void deleteOtherInfo( boolean delAccount, TeacherDto teacherDto) {
        final Long teacherId = teacherDto.getId();
        //删除账号
        final String userOid = teacherDto.getUserOid();
        if(delAccount){


            //用户OID获取 账号信息 只会有一个
            final List<AccountVo> accountVoList = this.iAccountService.getByUserOid(userOid);
            if(CollectionUtil.isNotEmpty(accountVoList)){
                String date = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
                // 删除账号
                final List<AccountDto> accountList = accountVoList.stream().map(val -> {
                    AccountDto accountDto = new AccountDto();
                    accountDto.setId(val.getId());
                    accountDto.setAccountName(val.getAccountName().concat(SystemConstants.LINE).concat(date));
                    if(StrUtil.isNotEmpty(val.getPhone())) {
                        accountDto.setPhone(val.getPhone().concat(SystemConstants.LINE).concat(date));
                    }
                    accountDto.setIsDelete(StatusEnum.ISDELETE.getCode());
                    return accountDto;
                }).collect(Collectors.toList());

                this.iAccountService.updateBatchById(accountList);
            }
        }

        if(StrUtil.isNotEmpty(userOid)){
            //删除用户基础信息
            UserDto userDto = this.iUserService.getByUserOid(userOid);
            if(userDto != null) {
                final Long userId = userDto.getId();
                userDto = new UserDto();
                userDto.setId(userId);
                userDto.setIsDelete(StatusEnum.ISDELETE.getCode());
                this.iUserService.updateById(userDto);
            }

            // 删除学科信息
            this.iTeacherSubjectService.delByUserOid(userOid);

            //删除教育信息
            this.iEducationService.delByUserOid(userOid);

            // 删除账号用户关系
            this.iAccountUserService.delByUserOid(userOid);
        }


        //删除老师
        teacherDto = new TeacherDto();
        teacherDto.setId(teacherId);
        teacherDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        this.baseMapper.updateById(teacherDto);
    }

    @Override
    public AjaxResult deleteByUserOid(String userOid, boolean delAccount) {
        //删除老师
        TeacherDto teacherDto = this.getDtoByUserOid(userOid);
        if(teacherDto == null){
            return AjaxResult.fail("老师不存在");
        }
        deleteOtherInfo( delAccount, teacherDto);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteByUserOidList(List<String> teacherUserOidList, boolean delAccount) {

        if(CollUtil.isEmpty(teacherUserOidList)){
            return AjaxResult.fail("用户OID不能为空");
        }

        final List<TeacherDto> teacherDtoList = this.getDtoByUserOidList(teacherUserOidList);
        if(CollUtil.isEmpty(teacherDtoList)){
            return AjaxResult.fail("数据不存在");
        }

        //删除老师信息
        final List<TeacherDto> waitDeleteList = teacherDtoList.stream().map(x -> {
            TeacherDto dto = new TeacherDto();
            dto.setId(x.getId());
            dto.setIsDelete(StatusEnum.ISDELETE.getCode());
            return dto;
        }).collect(Collectors.toList());
        this.updateBatchById(waitDeleteList);


        //删除账号
        if(delAccount){
            //用户OID获取 账号信息 只会有一个
            final List<AccountVo> accountVoList = this.iAccountService.getByUserOidList(teacherUserOidList);
            if(CollectionUtil.isNotEmpty(accountVoList)){
                String date = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
                // 删除账号
                final List<AccountDto> accountList = accountVoList.stream().map(val -> {
                    AccountDto accountDto = new AccountDto();
                    accountDto.setId(val.getId());
                    accountDto.setAccountName(val.getAccountName().concat(SystemConstants.LINE).concat(date));
                    if(StrUtil.isNotEmpty(val.getPhone())) {
                        accountDto.setPhone(val.getPhone().concat(SystemConstants.LINE).concat(date));
                    }
                    accountDto.setIsDelete(StatusEnum.ISDELETE.getCode());
                    return accountDto;
                }).collect(Collectors.toList());

                this.iAccountService.updateBatchById(accountList);
            }
        }

        //删除用户基础信息
        this.iUserService.deleteBatchByUserOid(teacherUserOidList);

        // 删除学科信息
        this.iTeacherSubjectService.delByUserOidList(teacherUserOidList);

        // 删除原有任教班级
        this.iTeacherClassesSubjectService.delByUserOidList(teacherUserOidList);

        //删除教育信息
        this.iEducationService.delByUserOidList(teacherUserOidList);

        // 删除账号用户关系
        this.iAccountUserService.batchDelByUserOid(teacherUserOidList);

        return AjaxResult.success();
    }

    @Override
	public List<TeacherVo> getByPhone(String phone) {
		return this.baseMapper.selectByPhone(phone);
	}

	@Override
	public Map<String, Object> getTeacherTotal(DatePeriodUtil.DatePeriod period) {
		return this.baseMapper.getTeacherTotal(period);
	}

	@Override
	public List<Map<String, Object>> getTeacherSectionList(DatePeriodUtil.DatePeriod period) {
		return this.baseMapper.getTeacherSectionList(period);
	}

    @Override
    public List<TeacherVo> getOrgStudentListByRealNames(Long orgId, List<String> realNames) {
        return this.baseMapper.getOrgStudentListByRealNames(orgId, realNames);
    }

    @Override
    public List<TeacherVo> getTeacherInfoListByUserOids(List<String> userOids) {
        return this.baseMapper.getTeacherInfoListByUserOids(userOids);
    }

	@Override
	public TeacherVo selectTeacherByUserOid(String userOid) {
    	TeacherConditionBo condition = new TeacherConditionBo();
    	condition.setUserOid(userOid);
    	condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<TeacherVo> list = teacherMapper.getTeacherListByCondition(condition);
		if(!CollectionUtil.isEmpty(list)) {
			return list.get(0);
		}
		return null;
	}

    @Override
    public AjaxResult updateTeacherByUserOid(TeacherBo teacherBo) {
        String userOid = teacherBo.getUserOid();
        if(StrUtil.isEmpty(userOid)){
            return AjaxResult.fail("用户OID不能为空");
        }
        TeacherDto teacherDto = this.getDtoByUserOid(userOid);
        if(teacherDto == null){
            throw new UnifiedException("教师信息不存在");
        }

        this.updateTeacherInfo(teacherBo, teacherDto);

        return AjaxResult.success();
    }


    @Override
    public TeacherVo getDetailByCode(String code) {
        final TeacherDto dto = this.getByCode(code);
        if(dto == null){
            return null;
        }
        return this.buildTeacherVo(dto);
    }

    private TeacherDto getByCode(String code){
        QueryWrapper<TeacherDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TeacherDto::getCode, code)
                .eq(TeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }
}
