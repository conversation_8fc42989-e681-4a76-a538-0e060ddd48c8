package com.light.user.user.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.light.core.entity.AjaxResult;
import com.light.feign.annotation.FeignValidatorAnnotation;
import com.light.user.user.api.UserTransferApi;
import com.light.user.user.entity.bo.UserTransferBo;
import com.light.user.user.entity.bo.UserTransferListConditionBo;
import com.light.user.user.service.IUserTransferService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户转出表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-11 14:45:27
 */
@RestController
@Api(tags = "用户转出管理")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class UserTransferController implements UserTransferApi {
	
    private final IUserTransferService userTransferService;

    /**
     * 查询用户转出表列表
     * <AUTHOR>
     * @date 2022-04-11 14:45:27
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult getUserTransferListByCondition(@RequestBody UserTransferListConditionBo condition){
		final Page<Object> page = PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
		return AjaxResult.success(page.toPageInfo());
    }


    /**
     * 新增用户转出表
     * <AUTHOR>
     * @date 2022-04-11 14:45:27
     */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult addUserTransfer(@RequestBody UserTransferBo userTransferBo){
		boolean save = userTransferService.addUserTransfer(userTransferBo);
		if(save) {
			return AjaxResult.success();
		}
		return AjaxResult.fail();
    }

    /**
	 * 修改用户转出表
	 * @param userTransferBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-11 14:45:27
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateUserTransfer( @RequestBody UserTransferBo userTransferBo) {
		if(null == userTransferBo.getUserTransferId()) {
			return AjaxResult.fail("用户转出表id不能为空");
		}
		boolean update = userTransferService.updateUserTransfer(userTransferBo);
		if(update) {
			return AjaxResult.success("修改成功");
		}
		return AjaxResult.fail();
	}

	/**
	 * 查询用户转出表详情
	 * @param userTransferId
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-11 14:45:27
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult getDetail(@PathVariable("id") Long userTransferId) {
		return AjaxResult.success( userTransferService.getDetail(userTransferId));
	}
    
    /**
	 * 删除用户转出表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-11 14:45:27
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		this.userTransferService.removeById(id);
		return AjaxResult.success();
	}
}
