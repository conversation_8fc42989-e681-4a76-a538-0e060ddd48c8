package com.light.user.user.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 14:01:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user")
public class UserDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 用户oid
	 */
	@TableField("oid")
	private String oid;

    /**
     * 账号ID
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 账号OID
     */
    @TableField("account_oid")
    private String accountOid;

    /**
     * 账号名称
     */
    @TableField("account_name")
    private String accountName;

	/**
	 * 昵称
	 */
	@TableField("nick_name")
	private String nickName;

	/**
	 * 真实姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 联系电话
	 */
	@TableField("phone")
	private String phone;

    /**
     * 微信OPEN_ID
     */
    @TableField("wx_open_id")
    private String wxOpenId;

    /**
     * 联系方式
     */
    @TableField("contact_info")
    private String contactInfo;

	/**
	 * 头像
	 */
	@TableField("picture")
	private String picture;

	/**
	 * 证件类型
	 */
	@TableField("identity_type")
	private String identityType;

	/**
	 * 证件号
	 */
	@TableField("identity_card_number")
	private String identityCardNumber;

	/**
	 * 用户性别字典
	 */
	@TableField("sex")
	private Integer sex;

	/**
	 * 邮箱
	 */
	@TableField("email")
	private String email;

	/**
	 * 出生日期
	 */
	@TableField("birthday")
	private Date birthday;

	/**
	 * 籍贯(市)
	 */
	@TableField("native_place_id")
	private String nativePlaceId;

	/**
	 * 健康状态字典
	 */
	@TableField("health_status")
	private Integer healthStatus;

	/**
	 * 政治面貌字典
	 */
	@TableField("political_outlook")
	private Integer politicalOutlook;

	/**
	 * 婚姻状态字典
	 */
	@TableField("marital_status")
	private Integer maritalStatus;

	/**
	 * 是否港澳台侨外，0：否，1：是
	 */
	@TableField("overseas_chinese")
	private Integer overseasChinese;

	/**
	 * 户口性质字典
	 */
	@TableField("registration_type")
	private Integer registrationType;

	/**
	 * 邮编
	 */
	@TableField("postal_code")
	private String postalCode;

	/**
	 * 家庭住址省份
	 */
	@TableField("home_address_province")
	private Long homeAddressProvince;

    /**
     * 家庭住址省份
     */
    @TableField("home_address_province_name")
    private String homeAddressProvinceName;

	/**
	 * 家庭住址市
	 */
	@TableField("home_address_city")
	private Long homeAddressCity;

    /**
     * 家庭住址市
     */
    @TableField("home_address_city_name")
    private String homeAddressCityName;

	/**
	 * 家庭住址县区
	 */
	@TableField("home_address_area")
	private Long homeAddressArea;

    /**
     * 家庭住址县区 名称
     */
    @TableField("home_address_area_name")
    private String homeAddressAreaName;

	/**
	 * 家庭地址详情
	 */
	@TableField("home_address")
	private String homeAddress;

	/**
	 * 户口所在地身份
	 */
	@TableField("registered_residence_province")
	private Long registeredResidenceProvince;

	/**
	 * 户口所在地市
	 */
	@TableField("registered_residence_city")
	private Long registeredResidenceCity;

	/**
	 * 户口所在地县区
	 */
	@TableField("registered_residence_area")
	private Long registeredResidenceArea;

	/**
	 * 户口所在地详细
	 */
	@TableField("registered_residence")
	private String registeredResidence;

	/**
	 * 宗教信仰字典
	 */
	@TableField("religious_belief_id")
	private String religiousBeliefId;

	/**
	 * 国籍
	 */
	@TableField("nationality_id")
	private Long nationalityId;

	/**
	 * 民族字典
	 */
	@TableField("nation")
	private Integer nation;

	/**
	 * 职位
	 */
	@TableField("position")
	private String position;

	/**
	 * 介绍
	 */
	@TableField("introduction")
	private String introduction;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 工作单位
	 */
	@TableField("work")
	private String work;

	/**
	 * 注册时间
	 */
	@TableField("register_time")
	private Date registerTime;

	/**
	 * 上次登陆时间
	 */
	@TableField("last_login_time")
	private Date lastLoginTime;

	/**
	 * 学段
	 */
	@TableField("section")
	private String section;

	/**
	 * 是否删除：0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

    /**
     * 是否完善用户 0 否 1 是
     */
    @TableField("is_completed")
    private Integer isCompleted;

	/**
	 * 是否锁定：0：否，1：是
	 */
	@TableField("is_locked")
	private Integer isLocked;

	/**
	 * 是否激活：0：否，1：是
	 */
	@TableField("is_activation")
	private Integer isActivation;

    /**
     *  标签
     */
    @TableField("tags")
    private String tags;

    /**
     * 用户身份 多个逗号分隔
     */
    @TableField("user_identities")
    private String userIdentities;

    /**
     * 组织机构ID 多个逗号分隔
     */
    @TableField("organization_ids")
    private String organizationIds;

    /**
     * 组织机构名称 多个逗号分隔
     */
    @TableField("organization_names")
    private String organizationNames;


    /**
     * 组织机构省份ID 多个逗号分割
     */
    @TableField("org_province_ids")
    private String orgProvinceIds;

    /**
     * 组织机构省份ID 多个逗号分割
     */
    @TableField("org_province_names")
    private String orgProvinceNames;


    /**
     * 组织机构省份ID 多个逗号分割
     */
    @TableField("org_city_ids")
    private String orgCityIds;

    /**
     * 组织机构省份ID 多个逗号分割
     */
    @TableField("org_city_names")
    private String orgCityNames;


    /**
     * 组织机构省市区ID 多个|分隔 和组织机构名称下标对应
     */
    @TableField("org_area_ids")
    private String orgAreaIds;

    /**
     * 组织机构省市区名称 多个|分隔 和组织机构名称下标对应
     */
    @TableField("org_area_names")
    private String orgAreaNames;

    /**
     * 最后一次选择的组织机构ID
     */
    @TableField("last_choose_org_id")
    private Long lastChooseOrgId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

}
