package com.light.user.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.user.entity.bo.KeeperRelationListConditionBo;
import com.light.user.user.entity.dto.KeeperRelationDto;
import com.light.user.user.entity.vo.KeeperRelationVo;

import java.util.List;

/**
 * 学生守护者关系Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-12 18:10:41
 */
public interface KeeperRelationMapper extends BaseMapper<KeeperRelationDto> {

	List<KeeperRelationVo> getKeeperRelationListByCondition(KeeperRelationListConditionBo condition);

    List<KeeperRelationVo> getListByCondition(KeeperRelationListConditionBo bo);
}
