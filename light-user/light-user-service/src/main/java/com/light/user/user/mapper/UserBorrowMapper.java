package com.light.user.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.user.entity.bo.UserBorrowListConditionBo;
import com.light.user.user.entity.dto.UserBorrowDto;
import com.light.user.user.entity.vo.UserBorrowVo;

import java.util.List;

/**
 * 用户借调表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-11 16:46:03
 */
public interface UserBorrowMapper extends BaseMapper<UserBorrowDto> {

	List<UserBorrowVo> getUserBorrowListByCondition(UserBorrowListConditionBo condition);

}
