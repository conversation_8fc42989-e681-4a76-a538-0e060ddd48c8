package com.light.user.user.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.core.entity.AjaxResult;
import com.light.user.user.entity.bo.UserRoleBo;
import com.light.user.user.entity.dto.UserRoleDto;
import com.light.user.user.entity.bo.UserRoleConditionBo;
import com.light.user.user.entity.vo.UserRoleVo;
import org.apache.ibatis.annotations.Param;

/**
 * 平台用户角色表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-03-02 14:01:49
 */
public interface UserRoleMapper extends BaseMapper<UserRoleDto> {

	List<UserRoleVo> getUserRoleListByCondition(UserRoleConditionBo condition);

    List<UserRoleVo> getUserRolesByAccountOid(@Param("accountOid") String oid);

    List<UserRoleVo> getByUserOid(@Param("userOid") String userOid);

    AjaxResult deleteByCondtion(UserRoleBo userRoleBo);
}
