package com.light.user.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.user.user.entity.bo.UserTransferListConditionBo;
import com.light.user.user.entity.dto.UserTransferDto;
import com.light.user.user.entity.vo.UserTransferVo;

import java.util.List;

/**
 * 用户转出表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-11 14:45:27
 */
public interface UserTransferMapper extends BaseMapper<UserTransferDto> {

	List<UserTransferVo> getUserTransferListByCondition(UserTransferListConditionBo condition);

}
