package com.light.user.userThirdRelationship.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户与第三方关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-10 17:08:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("p_user_third_relationship")
public class UserThirdRelationshipDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 表唯一oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 用户oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 来源类型,1接口对接 ，2导入
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 对接应用code
	 */
	@TableField("app_code")
	private String appCode;

	/**
	 * 第三方用户账号oid
	 */
	@TableField("third_oid")
	private String thirdOid;

	/**
	 * 备注
	 */
	@TableField("note")
	private String note;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
