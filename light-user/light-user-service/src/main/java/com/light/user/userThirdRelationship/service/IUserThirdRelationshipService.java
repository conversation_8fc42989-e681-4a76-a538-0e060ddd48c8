package com.light.user.userThirdRelationship.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.user.userThirdRelationship.entity.dto.UserThirdRelationshipDto;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipConditionBo;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipBo;
import com.light.user.userThirdRelationship.entity.vo.UserThirdRelationshipVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 用户与第三方关联表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-04-10 17:08:45
 */
public interface IUserThirdRelationshipService extends IService<UserThirdRelationshipDto> {

    List<UserThirdRelationshipVo> getUserThirdRelationshipListByCondition(UserThirdRelationshipConditionBo condition);

	AjaxResult addUserThirdRelationship(UserThirdRelationshipBo userThirdRelationshipBo);

	AjaxResult addOrUpdateThirdRelationship(UserThirdRelationshipBo userThirdRelationshipBo);

	AjaxResult updateUserThirdRelationship(UserThirdRelationshipBo userThirdRelationshipBo);

	Map<String, Object> getDetail(String oid);

	AjaxResult deleteByUserOid(String userOid);

    UserThirdRelationshipVo queryByUserOidAndAppCode(String userOid, String appCode);
}

