<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.account.mapper.AccountUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.account.entity.dto.AccountUserDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountOid" column="account_oid"/>
        <result property="userOid" column="user_oid"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

	<select id="getAccountUserListByCondition" resultType="com.light.user.account.entity.vo.AccountUserVo">
		select t.* from (
			SELECT
				a.*,
				b.real_name,
				b.sex,
				b.identity_card_number,
				d.name as organization_name,
				d.id as organization_id,
			    d.type as org_type,
			    d.superiors_ids as org_superiors_ids,
				f.`name` as role_name,
				h.classes_name,
				i.area_name as city_name,
				i.parent_area_name as province_name,
				j.identity_type  as user_identity_type
			FROM
				p_account_user a
				LEFT JOIN p_user b ON a.user_oid = b.oid
				left join p_user_org c on b.oid = c.user_oid
				left join p_organization d on c.organization_id = d.id
				left join p_user_role e on b.oid = e.user_oid
				left join p_role f on e.role_id = f.id
				left join p_student g on b.oid = g.user_oid
				left join p_classes h on g.classes_id = h.id
				left join p_area i on d.city_id = i.id
				left join p_user_identity j on b.oid = j.user_oid
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
			<if test="accountOid != null and accountOid != ''">and account_oid = #{accountOid}</if>
			<if test="userOid != null and userOid != ''">and user_oid = #{userOid}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
			<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
			<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
	    </where>
	</select>

    <select id="getUserListByAccountOid" resultType="com.light.user.user.entity.vo.LoginUserVo">
		select t.* from (
			SELECT
				a.id as account_user_id,
<!--				a.account_oid,-->
				a.user_oid,
				b.*,
			    b.id as userId
			FROM
				(select * from p_account_user where account_oid = #{accountOid}) a
				LEFT JOIN p_user b ON a.user_oid = b.oid
		) t where is_delete = 0
		order by create_time desc
    </select>

    <select id="getUserListByAccountOidAndIdentity" resultType="com.light.user.user.entity.vo.LoginUserVo">
		select t.* from (
			SELECT
				a.id as account_user_id,
				a.account_oid,
				a.user_oid,
                b.id
                ,b.oid
                ,b.account_name
                ,b.account_id
                ,b.nick_name
                ,b.real_name
                ,b.phone
                ,b.wx_open_id
                ,b.picture
                ,b.identity_type
                ,b.identity_card_number
                ,b.sex
                ,b.email
                ,b.birthday
                ,b.native_place_id
                ,b.health_status
                ,b.political_outlook
                ,b.marital_status
                ,b.overseas_chinese
                ,b.registration_type
                ,b.postal_code
                ,b.contact_info
                ,b.home_address_province
                ,b.home_address_province_name
                ,b.home_address_city
                ,b.home_address_city_name
                ,b.home_address_area
                ,b.home_address_area_name
                ,b.home_address
                ,b.registered_residence_province
                ,b.registered_residence_city
                ,b.registered_residence_area
                ,b.registered_residence
                ,b.religious_belief_id
                ,b.nationality_id
                ,b.nation
                ,b.position
                ,b.introduction
                ,b.remark
                ,b.work
                ,b.section
                ,b.tags
                ,b.register_time
                ,b.last_login_time
                ,b.is_completed
                ,b.is_delete
                ,b.is_locked
                ,b.is_activation
                ,b.user_identities
                ,b.organization_ids
                ,b.organization_names
                ,b.org_province_ids
                ,b.org_province_names
                ,b.org_city_ids
                ,b.org_city_names
                ,b.org_area_ids
                ,b.org_area_names
                ,b.last_choose_org_id
                ,b.create_time
                ,b.create_by
                ,b.update_time
                ,b.update_by
				,b.id as userId,
				c.identity_type as userIdentityType
			FROM
				(select * from p_account_user where account_oid = #{accountOid}) a
				LEFT JOIN p_user b ON a.user_oid = b.oid
				left join p_user_identity c on a.user_oid = c.user_oid
			) t where is_delete = 0 and userIdentityType = #{identity}
	</select>
</mapper>
