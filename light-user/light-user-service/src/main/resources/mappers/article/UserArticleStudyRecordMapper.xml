<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.article.mapper.UserArticleStudyRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.article.entity.dto.UserArticleStudyRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="articleId" column="article_id"/>
        <result property="studyDuration" column="study_duration"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="articleId != null ">and article_id = #{articleId}</if>
			<if test="studyDuration != null ">and study_duration = #{studyDuration}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.user_oid
	 		,t.article_id
	 		,t.study_duration
	 		,t.create_time
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from p_user_article_study_record a
		 ) t

	</sql>

	<select id="getUserArticleStudyRecordListByCondition" resultType="com.light.user.article.entity.vo.UserArticleStudyRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
	<select id="getArticleCountByUserOidAndType" resultType="java.lang.Integer">
        select count(distinct article_id) from p_user_article_study_record
        where is_delete = 0 and user_oid = #{userOid} and type =#{type}
        and DATE_FORMAT(create_time,'%Y-%m-%d') = #{dayDate}
    </select>
</mapper>
