<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.edu.mapper.EducationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.edu.entity.dto.EducationDto" id="BaseResultMap">
	        <result property="id" column="id"/>
	        <result property="userOid" column="user_oid"/>
	        <result property="educationSystem" column="education_system"/>
	        <result property="educationBackground" column="education_background"/>
	        <result property="graduateSchool" column="graduate_school"/>
	        <result property="joinDate" column="join_date"/>
	        <result property="graduationDate" column="graduation_date"/>
	        <result property="major" column="major"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	    </resultMap>

	<select id="getEducationListByCondition" resultType="com.light.user.edu.entity.vo.EducationVo">
		select a.* from p_education a
	    <where>
	    				    <if test="id != null and id != ''">and a.id = #{id}</if>
						    <if test="userOid != null and userOid != ''">and a.user_oid = #{userOid}</if>
						    <if test="educationSystem != null and educationSystem != ''">and a.education_system = #{educationSystem}</if>
						    <if test="educationBackground != null and educationBackground != ''">and a.education_background = #{educationBackground}</if>
						    <if test="graduateSchool != null and graduateSchool != ''">and a.graduate_school = #{graduateSchool}</if>
						    <if test="joinDate != null and joinDate != ''">and a.join_date = #{joinDate}</if>
						    <if test="graduationDate != null and graduationDate != ''">and a.graduation_date = #{graduationDate}</if>
						    <if test="major != null and major != ''">and a.major = #{major}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
							<if test="userOidList != null and userOidList.size() != 0">
								a.user_oid in
								<foreach item="item" index="index" collection="userOidList"  open="(" separator="," close=")">
									#{item}
								</foreach>
							</if>
				    </where>
	</select>
</mapper>