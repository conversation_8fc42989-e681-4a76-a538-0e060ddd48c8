<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.role.mapper.RoleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.role.entity.dto.RoleDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="state" column="state"/>
        <result property="description" column="description"/>
        <result property="organizationId" column="organization_id"/>
        <result property="position" column="position"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<select id="getRoleListByCondition" resultType="com.light.user.role.entity.vo.RoleVo">
		select t.* from (
			select a.* from p_role a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
			<if test="name != null and name != ''">and name = #{name}</if>
			<if test="code != null and code != ''">and code = #{code}</if>
			<if test="state != null and state != ''">and state = #{state}</if>
			<if test="description != null and description != ''">and description = #{description}</if>
			<if test="organizationId != null and organizationId != ''">and organization_id = #{organizationId}</if>
			<if test="position != null and position != ''">and position = #{position}</if>
			<if test="ids != null and ids.size() != 0">
				and id in
				<foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
	    </where>
	</select>
    <select id="selectByAdminOid" resultType="com.light.user.role.entity.vo.RoleVo">
		select r.*
		from p_role r inner join p_admin_role ar on r.id = ar.role_id
		where ar.admin_oid = #{adminOid}
	</select>
</mapper>