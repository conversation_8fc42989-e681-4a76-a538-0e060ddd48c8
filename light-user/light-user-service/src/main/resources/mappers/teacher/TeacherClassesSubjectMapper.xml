<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.teacher.mapper.TeacherClassesSubjectMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.teacher.entity.dto.TeacherClassesSubjectDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="classesId" column="classes_id"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="teacherUserOid" column="teacher_user_oid"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>
	<update id="delByUserOidAndOrgId">
		update p_teacher_classes_subject tcs,p_classes c set tcs.is_delete = 1
		where tcs.classes_id = c.id and c.organization_id = #{orgId} and tcs.teacher_user_oid = #{userOid}
	</update>

	<select id="getTeacherClassesSubjectListByCondition" resultType="com.light.user.teacher.entity.vo.TeacherClassesSubjectVo">
		select t.* from (
			select a.*,c.organization_id,
			c.grade ,
			c.section ,
			c.id as 'clazzVo.id',
			c.class_code as 'clazzVo.classCode',
			c.classes_name,
			c.classes_name as 'clazzVo.classesName',
			c.grade as 'clazzVo.grade',
			c.section as 'clazzVo.section',
			c.is_graduate as 'clazzVo.isGraduate',
			c.is_graduate,
			u.real_name,
			u.real_name as 'userVo.realName'

			from p_teacher_classes_subject a
			inner join p_user u on a.teacher_user_oid = u.oid
		    inner join p_classes as c on a.classes_id = c.id and c.is_delete = 0
		) t
	    <where>

			<if test="id != null and id != ''">and id = #{id}</if>
			<if test="classesId != null and classesId != ''">and classes_id = #{classesId}</if>
			<if test="subjectCode != null and subjectCode != ''">and subject_code = #{subjectCode}</if>
			<if test="teacherUserOid != null and teacherUserOid != ''">and teacher_user_oid = #{teacherUserOid}</if>
			<if test="isDelete != null">and is_delete = #{isDelete}</if>
			<if test="organizationId != null">and organization_id = #{organizationId}</if>
			<if test="grade != null and grade != ''">and grade = #{grade}</if>
			<if test="realName != null and realName != ''">and real_name like concat('%',#{realName},'%')</if>
			<if test="section != null and section != ''">and section = #{section}</if>

			<if test="userOidList != null and userOidList.size() > 0">
				and teacher_user_oid  in
				<foreach collection="userOidList" index="index" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>

            <if test="isGraduate != null">
                and is_graduate  = #{isGraduate}
            </if>

			<if test="startUpdateTime != null">
                 and update_time <![CDATA[  >= ]]> #{startUpdateTime}
            </if>

            <if test="endUpdateTime != null">
                 and update_time <![CDATA[  <= ]]> #{endUpdateTime}
            </if>
	    </where>
		<if test="pageNo == -1 and orderBy != null and  orderBy != '' " > order by ${orderBy}</if>
	</select>
    <select id="selectByUserOid" resultType="com.light.user.teacher.entity.vo.TeacherClassesSubjectVo">
		select a.*,b.grade as `clazzVo.grade`, b.classes_name as `clazzVo.classesName`
		from p_teacher_classes_subject a
		left join p_classes b on a.classes_id = b.id
		where a.is_delete = 0 and a.teacher_user_oid = #{userOid}
	</select>
	<delete id="delByUserOidAndOrgIdList">
        update p_teacher_classes_subject tcs,p_classes c set tcs.is_delete = 1
		where tcs.classes_id = c.id   and tcs.teacher_user_oid = #{userOid}
		and c.organization_id in
		<foreach collection="orgIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </delete>

    <select id="getByUserOidAndOrgId" resultType="com.light.user.teacher.entity.vo.TeacherClassesSubjectVo">
        select a.*,b.grade as `clazzVo.grade`, b.classes_name as `clazzVo.classesName`,b.is_graduate as 'clazzVo.isGraduate'
        from p_teacher_classes_subject a
                 left join p_classes b on a.classes_id = b.id
        where a.is_delete = 0 and a.teacher_user_oid = #{userOid} and  b.organization_id = #{orgId}
    </select>
</mapper>
