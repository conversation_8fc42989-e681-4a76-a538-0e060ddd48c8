<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.teacher.mapper.TeacherSubjectMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.teacher.entity.dto.TeacherSubjectDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="teacherUserOid" column="teacher_user_oid"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getTeacherSubjectListByCondition" resultType="com.light.user.teacher.entity.vo.TeacherSubjectVo">
		select t.* from (
			select a.* from p_teacher_subject a
		) t
	    <where>
			<if test="id != null and id != ''">and id = #{id}</if>
			<if test="teacherUserOid != null and teacherUserOid != ''">and teacher_user_oid = #{teacherUserOid}</if>
			<if test="subjectCode != null and subjectCode != ''">and subject_code = #{subjectCode}</if>
<!--			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>-->
<!--			<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>-->
<!--			<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>-->
<!--			<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>-->
			<if test="(isDelete != null and isDelete != '') or isDelete == 0">and is_delete = #{isDelete}</if>
	    </where>
	</select>
</mapper>