<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.user.withdraw.mapper.AccountWithdrawApplyMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.user.withdraw.entity.dto.AccountWithdrawApplyDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="accountOid" column="account_oid"/>
        <result property="userOid" column="user_oid"/>
        <result property="userType" column="user_type"/>
        <result property="status" column="status"/>
        <result property="applyUserOid" column="apply_user_oid"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="auditTime" column="audit_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="accountOid != null and accountOid != '' ">and account_oid = #{accountOid}</if>
            <if test="accountName != null and accountName != '' ">and account_name like concat('%', #{accountName},'%' ) </if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
            <if test="userRealName != null and userRealName != '' ">and user_real_name like concat('%', #{userRealName},'%' )</if>
			<if test="userType != null ">and user_type = #{userType}</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="applyUserOid != null and applyUserOid != '' ">and apply_user_oid = #{applyUserOid}</if>
			<if test="expiredTime != null ">and expired_time = #{expiredTime}</if>
			<if test="auditRemark != null and auditRemark != '' ">and audit_remark like concat('%', #{auditRemark}, '%')</if>
			<if test="auditTime != null ">and audit_time = #{auditTime}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="accountOid != null and accountOid != '' ">and account_oid = #{accountOid}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="userType != null ">and user_type = #{userType}</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="applyUserOid != null and applyUserOid != '' ">and apply_user_oid = #{applyUserOid}</if>
			<if test="expiredTime != null ">and expired_time = #{expiredTime}</if>
			<if test="auditRemark != null and auditRemark != '' ">and audit_remark like concat('%', #{auditRemark}, '%')</if>
			<if test="auditTime != null ">and audit_time = #{auditTime}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.account_oid
	 		,t.user_oid
	 		,t.user_type
	 		,t.status
	 		,t.apply_user_oid
	 		,t.expired_time
	 		,t.audit_remark
	 		,t.audit_time
	 		,t.create_time
	 		,t.update_by
	 		,t.update_time
	 		,t.is_delete
		from (
			select a.* from p_account_withdraw_apply a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getAccountWithdrawApplyListByCondition" resultType="com.light.user.withdraw.entity.vo.AccountWithdrawApplyVo">
        select a.*
        from p_account_withdraw_apply a
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="accountOid != null and accountOid != '' ">and account_oid = #{accountOid}</if>
            <if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
            <if test="userRealName != null and userRealName != '' ">and user_real_name like concat('%', #{userRealName},'%' )</if>
            <if test="userType != null ">and user_type = #{userType}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="applyUserOid != null and applyUserOid != '' ">and apply_user_oid = #{applyUserOid}</if>
            <if test="expiredTime != null ">and expired_time = #{expiredTime}</if>
            <if test="auditRemark != null and auditRemark != '' ">and audit_remark like concat('%', #{auditRemark}, '%')</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>

            <if test="accountName != null and accountName != ''">
                and account_name like concat('%', #{accountName},'%' )
            </if>

            <if test="userRealName != null and userRealName != ''">
                and user_real_name like concat('%',#{userRealName},'%')
            </if>
            <if test="className != null and className != ''">
                and find_in_set(#{className},class_names)
            </if>
            <if test="grade != null and grade != ''">
                and find_in_set(#{grade}, grades)
            </if>
            <if test="organizationId != null">
                and a.organization_id = #{organizationId}
            </if>
            <if test="organizationName != null and organizationName != ''">
                and a.organization_name like concat('%',#{organizationName},'%')
            </if>
            <if test="withdrawType != null">
                and a.withdraw_type = #{withdrawType}
            </if>
        </where>
        -- 待审核、已驳回、已撤销、已失效、已注销
        ORDER BY FIELD(status,1,5,2,3,4),
        CASE WHEN status in(1,5,2,3) THEN create_time END DESC,
        CASE WHEN status = 4 THEN  audit_time END DESC

	</select>

	<select id="getAccountWithdrawApplyByCondition" resultType="com.light.user.withdraw.entity.vo.AccountWithdrawApplyVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

    <select id="getStudentApplyListByCondition" resultType="com.light.user.withdraw.entity.vo.AccountWithdrawApplyVo">
        select pawa.* ,pa.account_name, u.real_name as user_real_name,c.grade,c.classes_name as className
        from p_account_withdraw_apply pawa
        inner join p_account pa on pawa.account_oid = pa.oid
        inner join p_user u on pawa.user_oid = u.oid
        inner join p_student s on u.oid = s.user_oid
        left join p_classes c on s.classes_id = c.id
        <where>
             and pawa.user_type = 1
            <if test="id != null ">and pawa.id = #{id}</if>
            <if test="accountOid != null and accountOid != '' ">and pawa.account_oid = #{accountOid}</if>
            <if test="userOid != null and userOid != '' ">and pawa.user_oid = #{userOid}</if>
            <if test="userType != null ">and pawa.user_type = #{userType}</if>
            <if test="status != null ">and pawa.status = #{status}</if>
            <if test="applyUserOid != null and applyUserOid != '' ">and pawa.apply_user_oid = #{applyUserOid}</if>
            <if test="expiredTime != null ">and pawa.expired_time = #{expiredTime}</if>
            <if test="auditRemark != null and auditRemark != '' ">and pawa.audit_remark like concat('%', #{auditRemark}, '%')</if>
            <if test="auditTime != null ">and pawa.audit_time = #{auditTime}</if>
            <if test="isDelete != null ">and pawa.is_delete = #{isDelete}</if>

            <if test="accountName != null and accountName != ''">
                and pa.account_name like concat('%',#{accountName},'%')
            </if>

            <if test="userRealName != null and userRealName != ''">
                and u.real_name like concat('%',#{userRealName},'%')
            </if>
            <if test="className != null and className != ''">
                and c.classes_name = #{className}
            </if>
            <if test="grade != null and grade != ''">
                and c.grade = #{grade}
            </if>
        </where>
    </select>

    <update id="expiredApply">
        update p_account_withdraw_apply set status = 3 where status = 1 and expired_time &lt;= now()
    </update>

    <select id="getLastByUserOidList" resultType="com.light.user.withdraw.entity.vo.AccountWithdrawApplyVo">
        select * from p_account_withdraw_apply
        where is_last = 1 and  user_oid in
        <foreach collection="userOidList" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
    </select>

    <update id="expiredApplyByUserOid">
        update p_account_withdraw_apply set status = 3 where status = 1 and user_oid = #{userOid}
    </update>

    <select id="getVoByIdList" resultType="com.light.user.withdraw.entity.vo.AccountWithdrawApplyVo">
        select * from p_account_withdraw_apply where is_delete = 0 and
        id in
        <foreach collection="idList" open="(" close=")" item="item" separator="," >
            #{item}
        </foreach>
    </select>
</mapper>
