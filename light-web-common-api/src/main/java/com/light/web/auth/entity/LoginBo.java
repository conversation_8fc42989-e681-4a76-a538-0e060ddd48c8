package com.light.web.auth.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  登录参数
 * <AUTHOR>
 */
@Data
public class LoginBo {

    /**
     * 登录模式
     * @see com.light.web.auth.enums.LoginTypeEnum
     */
    private String model;

    private Integer client;

    /**
     * <AUTHOR>
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class AccountLoginBo extends  LoginBo {

        /**
         * 用户名密码
         */
        private String accountName;

        /**
         * 密码
         */
        private String password;

    }
}
