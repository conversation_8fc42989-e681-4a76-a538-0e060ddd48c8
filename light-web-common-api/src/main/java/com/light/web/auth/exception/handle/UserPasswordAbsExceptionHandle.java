package com.light.web.auth.exception.handle;

import com.light.redis.component.RedisComponent;
import com.light.web.auth.entity.LoginBo;
import com.light.web.auth.exception.LoginException;
import com.light.web.auth.exception.UsernamePasswordException;
import com.light.web.auth.exception.handle.base.LoginAbsExceptionHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用户名密码异常
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserPasswordAbsExceptionHandle extends LoginAbsExceptionHandle {

    @Resource
    private RedisComponent redisComponent;

    @Override
    public void handle(LoginBo bo, LoginException exception) {

        LoginBo.AccountLoginBo accountLoginBo = (LoginBo.AccountLoginBo) bo;
        log.warn("【登录异常】 用户名或密码错误 ， 用户名: {}", accountLoginBo.getAccountName());

    }

    @Override
    public Class exceptionClass() {
        return UsernamePasswordException.class;
    }
}