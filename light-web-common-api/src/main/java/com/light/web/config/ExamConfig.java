package com.light.web.config;

import com.light.web.observer.exam.ExamAnswerCommitSubject;
import com.light.web.observer.exam.ExamPointsObserver;
import com.light.web.service.points.PointsDefaultServiceImpl;
import com.light.web.service.points.PointsLocalServiceImpl;
import com.light.web.service.points.PointsRabbitServiceImpl;
import com.light.web.service.points.PointsService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *  考试配置
 * <AUTHOR>
 * @since 2023/4/11
 */
@Configuration
public class ExamConfig {

    @Autowired
    private ExamProperties examProperties;

    @Autowired(required = false)
    private RabbitTemplate rabbitTemplate;


    /**
     *  积分处理方式 注入
     * @return
     */
    @Bean
    public PointsService pointsService(){
        PointsService pointsService = new PointsDefaultServiceImpl();
        if(examProperties.getPoints().getHandleType().trim().equalsIgnoreCase(ExamProperties.ExamAfterHandlerEnum.MQ.toString())){
            if(rabbitTemplate == null){
                throw new RuntimeException("rabbit service is not defined -------");
            }
            pointsService = new PointsRabbitServiceImpl(rabbitTemplate);
        }
        if(examProperties.getPoints().getHandleType().trim().equalsIgnoreCase(ExamProperties.ExamAfterHandlerEnum.LOCAL.toString())){
            pointsService = new PointsLocalServiceImpl();
        }
        return pointsService;
    }


    /**
     * 声明 答案提交订阅
     * @return
     */
    @Bean
    public ExamAnswerCommitSubject answerCommitAnswer(PointsService pointsService){
        ExamAnswerCommitSubject subject = new ExamAnswerCommitSubject();

        // 开启积分
        if(examProperties.getPoints().isEnabled()){
            if(pointsService == null){
                throw new RuntimeException("points service is not defined ----------");
            }
            subject.registerObserver(new ExamPointsObserver(pointsService));
        }
        return subject;
    }



}
