package com.light.web.config;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/4/11
 */
@Data
@Configuration
@ConfigurationProperties(prefix = ExamProperties.prefix)
public class ExamProperties {

    public static final String prefix = "light.exam";

    /**
     * 积分配置
     */
    private Points points = new Points();

    @Data
    public static class Points {

        /**
         * 是否开启
         */
        private boolean enabled = false;

        /**
         * 处理方式
         */
        private String handleType = ExamAfterHandlerEnum.DEFAULT.toString();
    }

    @Getter
    public static enum ExamAfterHandlerEnum {

        /**
         * 默认 不处理
         */
        DEFAULT,

        /**
         * MQ
         */
        MQ,

        /**
         * 本地调用 ，可使用API调用
         */
        LOCAL,
    }

}
