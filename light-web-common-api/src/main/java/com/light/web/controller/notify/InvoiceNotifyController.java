package com.light.web.controller.notify;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.light.activity.enums.SignupInfoEnums;
import com.light.activity.signupInfo.entity.bo.SignupInfoBo;
import com.light.activity.signupInfo.entity.vo.SignupInfoVo;
import com.light.activity.signupInfo.service.SignupInfoApiService;
import com.light.base.config.service.ConfigApiService;
import com.light.base.enums.InvoiceEnums;
import com.light.base.invoice.entity.bo.InvoiceObsoleteRecordBo;
import com.light.base.invoice.entity.bo.InvoiceRecordBo;
import com.light.base.invoice.entity.vo.InvoiceObsoleteRecordVo;
import com.light.base.invoice.entity.vo.InvoiceRecordVo;
import com.light.base.invoice.service.InvoiceObsoleteRecordApiService;
import com.light.base.invoice.service.InvoiceRecordApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.invoice.constants.NuoNuoInvoiceConstants;
import com.light.core.invoice.constants.NuonuoInvoiceResponseConstants;
import com.light.core.invoice.model.NuonuoInvoiceFastRedRequest;
import com.light.core.invoice.model.NuonuoInvoiceOpenNotifyRequest;
import com.light.core.invoice.model.NuonuoInvoiceQueryRedConfirmResponse;
import com.light.core.invoice.service.InvoiceClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Slf4j
@RestController
@RequestMapping("invoice-notify")
public class InvoiceNotifyController {


    @Autowired
    private SignupInfoApiService signupInfoApiService;

    @Autowired
    private InvoiceRecordApiService invoiceRecordApiService;

    @Autowired
    private InvoiceObsoleteRecordApiService invoiceObsoleteRecordApiService;

    @Autowired
    private ConfigApiService configApiService;

    @Autowired
    private InvoiceClientService invoiceClientService;

    /**
     *  诺诺开票接口回调
     *
     * @param request the nuo nuo invoice open notify request 诺诺开票回调
     * @return {@link String}
     */
    @RequestMapping(value = "callback/nuonuoOpenNotify", method = {RequestMethod.GET, RequestMethod.POST})
    public String nuonuoInvoiceCallback(HttpServletRequest request){
        final String content = request.getParameter("content");
        log.info("【诺诺开票结果回调】回调参数:{}", content);
        final String sno = request.getParameter("orderno");
        log.info("【诺诺开票结果回调】回调发票申请编号:{}", sno);

        final NuonuoInvoiceOpenNotifyRequest.NotifyContent notifyContent = JSON.parseObject(content, NuonuoInvoiceOpenNotifyRequest.NotifyContent.class);

        int signInvoiceStatus = SignupInfoEnums.InvoiceStatusEnums.FAIL.getVal();
        int invoiceRecordStatus = InvoiceEnums.RecordState.FAIL.getVal();
        if(notifyContent.getStatus().equals(NuoNuoInvoiceConstants.OpenStatus.SUCCESS)){
            signInvoiceStatus = SignupInfoEnums.InvoiceStatusEnums.SUCCESS.getVal();;
            invoiceRecordStatus = InvoiceEnums.RecordState.SUCCESS.getVal();
        }



        // 发票申请记录
        final AjaxResult<InvoiceRecordVo> invoiceRecordResp = this.invoiceRecordApiService.getBySno(sno);
        final InvoiceRecordVo invoiceRecordVo = invoiceRecordResp.getData();
        if(invoiceRecordVo == null){
            log.warn("【诺诺开票结果回调】 开票记录不存在, 申请编号: {} ",sno);
            return NuonuoInvoiceResponseConstants.NotifyResponse.fail();
        }

        String orderNo = invoiceRecordVo.getOrderNo();
        final AjaxResult<SignupInfoVo> byOrderNo = this.signupInfoApiService.getByOrderNo(orderNo);
        final SignupInfoVo data = byOrderNo.getData();
        if(data == null){
            log.warn("【诺诺开票结果回调】 订单号 : {} , 不存在",orderNo);
            return NuonuoInvoiceResponseConstants.NotifyResponse.fail();
        }

        // 更新报名信息内 开票状态
        SignupInfoBo signupInfoBo = new SignupInfoBo();
        signupInfoBo.setId(data.getId());
        signupInfoBo.setInvoiceStatus(signInvoiceStatus);
        this.signupInfoApiService.updateSignupInfo(signupInfoBo);


        // 更新开票申请表状态
        InvoiceRecordBo bo = new InvoiceRecordBo();
        bo.setId(invoiceRecordVo.getId());
        bo.setStatus(invoiceRecordStatus);
        bo.setInvoiceCode(notifyContent.getInvoiceCode());
        bo.setInvoiceNum(notifyContent.getInvoiceNum());
        bo.setInvoiceUrl(notifyContent.getPdfUrl());
        final String invoiceTime = notifyContent.getInvoiceTime();
        if(StrUtil.isNotEmpty(invoiceTime)) {
            bo.setInvoiceTime(new Date(Long.parseLong(invoiceTime)));
        }
        bo.setReason(notifyContent.getErrMessage());
        this.invoiceRecordApiService.updateInvoiceRecord(bo);

        return NuonuoInvoiceResponseConstants.NotifyResponse.success();
    }



    /**
     *  诺诺红字确认单申请结果回调
     *
     * @param request the nuo nuo invoice open notify request 诺诺红字确认单申请结果回调
     * @return {@link String}
     */
    @RequestMapping(value = "callback/nuonuoRedConfirmApplyNotify", method = {RequestMethod.GET, RequestMethod.POST})
    public String nuonuoRedConfirmApplyNotify(HttpServletRequest request){

        final String operater = request.getParameter("operater");
        if(operater.equals("confirmCallback")) {


            final String billId = request.getParameter("billId");
            InvoiceObsoleteRecordVo invoiceObsoleteRecordVo = this.invoiceObsoleteRecordApiService.getByBillId(billId).getData();
            if (invoiceObsoleteRecordVo == null) {
                log.error("【诺诺红字确认单申请结果回调】 未获取到作废申请 billid: {}", billId);
                return NuonuoInvoiceResponseConstants.NotifyResponse.fail();
            }
            final String content = request.getParameter("content");
            log.info("【诺诺红字确认单申请结果回调】 内容信息 content: {}", content);
            final NuonuoInvoiceQueryRedConfirmResponse.RedConfirm redConfirm = JSON.parseObject(content, NuonuoInvoiceQueryRedConfirmResponse.RedConfirm.class);
            final String billStatus = redConfirm.getBillStatus();
            String serialNo = null;
            if (billStatus.equals(NuoNuoInvoiceConstants.RedConfirmStatus.UN_NEED_ENTER) || billStatus.equals(NuoNuoInvoiceConstants.RedConfirmStatus.ENTER)) {
                log.info("【诺诺红字确认单申请结果回调】进行快捷冲红开票， 订单号：{}", redConfirm.getOrderNo());
                // 调用快捷冲红
                NuonuoInvoiceFastRedRequest fastRedRequest = new NuonuoInvoiceFastRedRequest();
                fastRedRequest.setBillUuid(redConfirm.getBillUuid());
                fastRedRequest.setOrderNo(redConfirm.getOrderNo());
                fastRedRequest.setBillNo(redConfirm.getBillNo());
                fastRedRequest.setBillId(billId);
                fastRedRequest.setCallBackUrl(this.configApiService.getConfigValue(SystemConstants.NUONUO_INVOICE_WRITE_BACK_NOTIFY_URL).getData());
                serialNo = this.invoiceClientService.fastInvoiceRed(fastRedRequest);
            }

            InvoiceObsoleteRecordBo bo = new InvoiceObsoleteRecordBo();
            bo.setId(invoiceObsoleteRecordVo.getId());
            bo.setBillsId(billId);
            bo.setBillsUuid(redConfirm.getBillUuid());
            bo.setBillsNo(redConfirm.getBillNo());
            bo.setThirdSerialNum(serialNo);
            this.invoiceObsoleteRecordApiService.updateInvoiceObsoleteRecord(bo);
        } else if (operater.equals("callback")) {
            final String content = request.getParameter("content");
            final String sno = request.getParameter("orderno");
            return handleInvoiceObsoleteNotify(content, sno);
        }

        return NuonuoInvoiceResponseConstants.NotifyResponse.success();
    }


    /**
     *  诺诺冲红接口回调
     *
     * @param request the nuo nuo invoice open notify request 诺诺冲红回调
     * @return {@link String}
     */
    @RequestMapping(value = "callback/nuonuoWriteBackNotify", method = {RequestMethod.GET, RequestMethod.POST})
    public String nuonuoWriteBackNotify(HttpServletRequest request){
        final String content = request.getParameter("content");
        log.info("【诺诺冲红结果回调】回调参数:{}", content);

        final String sno = request.getParameter("orderno");
        log.info("【诺诺冲红结果回调】回调发票申请编号:{}", sno);

        return handleInvoiceObsoleteNotify(content, sno);
    }

    private String handleInvoiceObsoleteNotify(String content, String sno) {
        final NuonuoInvoiceOpenNotifyRequest.NotifyContent notifyContent = JSON.parseObject(content, NuonuoInvoiceOpenNotifyRequest.NotifyContent.class);
        // 此处为发票申请记录编号

        // 发票申请记录
        final AjaxResult<InvoiceRecordVo> invoiceRecordResp = this.invoiceRecordApiService.getBySno(sno);
        final InvoiceRecordVo invoiceRecordVo = invoiceRecordResp.getData();
        if(invoiceRecordVo == null){
            log.warn("【诺诺冲红结果回调】 开票记录不存在, 申请编号: {} ", sno);
            return NuonuoInvoiceResponseConstants.NotifyResponse.fail();
        }

        String orderNo = invoiceRecordVo.getOrderNo();
        final AjaxResult<SignupInfoVo> byOrderNo = this.signupInfoApiService.getByOrderNo(orderNo);
        final SignupInfoVo data = byOrderNo.getData();
        if(data == null){
            log.warn("【诺诺冲红结果回调】 订单号 : {} , 不存在",orderNo);
            return NuonuoInvoiceResponseConstants.NotifyResponse.fail();
        }

        int signInvoiceStatus = SignupInfoEnums.InvoiceStatusEnums.OBSOLETE_FAIL.getVal();
        int invoiceRecordStatus = InvoiceEnums.RecordState.OBSOLETE_FAIL.getVal();
        int obsoleteStatus = InvoiceEnums.ObsoleteStatus.OBSOLETE_FAIL.getVal();
        if(notifyContent.getStatus().equals(NuoNuoInvoiceConstants.OpenStatus.SUCCESS)){
            signInvoiceStatus = SignupInfoEnums.InvoiceStatusEnums.OBSOLETE.getVal();;
            invoiceRecordStatus = InvoiceEnums.RecordState.OBSOLETE.getVal();
            obsoleteStatus = InvoiceEnums.ObsoleteStatus.OBSOLETE.getVal();
        }

        // 更新报名信息内 开票状态
        SignupInfoBo signupInfoBo = new SignupInfoBo();
        signupInfoBo.setId(data.getId());
        signupInfoBo.setInvoiceStatus(signInvoiceStatus);
        this.signupInfoApiService.updateSignupInfo(signupInfoBo);


        // 更新开票申请表状态
        InvoiceRecordBo bo = new InvoiceRecordBo();
        bo.setId(invoiceRecordVo.getId());
        bo.setStatus(invoiceRecordStatus);
        bo.setReason(notifyContent.getErrMessage());
        this.invoiceRecordApiService.updateInvoiceRecord(bo);

        // 更新作废记录
        final Long lastObsoleteRecordId = invoiceRecordVo.getLastObsoleteRecordId();
        if(lastObsoleteRecordId != null){
            InvoiceObsoleteRecordBo invoiceObsoleteRecordBo = new InvoiceObsoleteRecordBo();
            invoiceObsoleteRecordBo.setId(lastObsoleteRecordId);
            invoiceObsoleteRecordBo.setStatus(obsoleteStatus);
            invoiceObsoleteRecordBo.setInvoiceCode(notifyContent.getInvoiceCode());
            invoiceObsoleteRecordBo.setInvoiceNum(notifyContent.getInvoiceNum());
            invoiceObsoleteRecordBo.setInvoiceUrl(notifyContent.getPdfUrl());
            final String invoiceTime = notifyContent.getInvoiceTime();
            if(StrUtil.isNotEmpty(invoiceTime)) {
                invoiceObsoleteRecordBo.setObsoleteTime(new Date(Long.parseLong(invoiceTime)));
            }
            invoiceObsoleteRecordBo.setRemark(notifyContent.getErrMessage());

            this.invoiceObsoleteRecordApiService.updateInvoiceObsoleteRecord(invoiceObsoleteRecordBo);
        }

        return NuonuoInvoiceResponseConstants.NotifyResponse.success();
    }


}
