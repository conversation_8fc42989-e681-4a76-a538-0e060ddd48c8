package com.light.web.controller.user;

import cn.hutool.core.util.StrUtil;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import com.light.user.points.service.UserPointsApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.light.user.points.entity.bo.UserPointsConditionBo;
import com.light.user.points.entity.bo.UserPointsBo;
import com.light.user.points.entity.vo.UserPointsVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 用户总积分表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-11 15:15:16
 */
@RestController
@Validated
@RequestMapping("user/points")
@Api(value = "", tags = "用户总积分表接口" )
public class UserPointsApiController {

    @Autowired
    private UserPointsApiService userPointsApiService;

    @Autowired
    private RedisComponent redisComponent;

    /**
     * 查询用户总积分表分页列表
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询用户总积分表列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getUserPointsPageListByCondition(@RequestBody UserPointsConditionBo condition){
        final String rankPointsValLimit = this.getConfig(SystemConstants.USER_POINTS_RANK_VALUE_LIMIT);
        if(StrUtil.isNotEmpty(rankPointsValLimit)){
            condition.setGePointsLimit(Double.parseDouble(rankPointsValLimit));
        }

        PageInfo<UserPointsVo> page = userPointsApiService.getUserPointsPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    private String getConfig(String key){
        String value = redisComponent.hgetObjectValue(RedisKeyEnum.CONFIG_KEY.getValue(), key, SystemConstants.CONFIG_VALUE);
        return value;
    }

    /**
     * 查询组织机构积分排行
     * @param condition
     * @return
     */
    @PostMapping("/organization/page/list")
    @ApiOperation(value = "分页查询组织机构总积分列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getOrganizationPointsPageListByCondition(@RequestBody UserPointsConditionBo condition) {
        final String rankPointsValLimit = this.getConfig(SystemConstants.ORGANIZATION_POINTS_RANK_VALUE_LIMIT);
        if(StrUtil.isNotEmpty(rankPointsValLimit)){
            condition.setGePointsLimit(Double.parseDouble(rankPointsValLimit));
        }
        PageInfo<UserPointsVo> page = userPointsApiService.getOrganizationPointsPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询用户总积分表列表
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询用户总积分表列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getUserPointsListByCondition(@RequestBody UserPointsConditionBo condition){
        List<UserPointsVo> list = userPointsApiService.getUserPointsListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 查询用户总积分表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-11 15:15:16
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询用户总积分表详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "用户总积分表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<UserPointsVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("用户总积分表id不能为空");
        }
        UserPointsVo vo = userPointsApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("userPointsVo", vo);
        return AjaxResult.success(map);
    }


}
