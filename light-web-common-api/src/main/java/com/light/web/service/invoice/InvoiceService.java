package com.light.web.service.invoice;

import com.alibaba.fastjson.JSON;
import com.light.base.enums.InvoiceEnums;
import com.light.base.invoice.entity.bo.InvoiceRecordBo;
import com.light.base.invoice.entity.vo.InvoiceRecordVo;
import com.light.base.invoice.service.InvoiceRecordApiService;
import com.light.core.entity.AjaxResult;
import com.light.security.service.CurrentUserService;
import com.light.user.user.entity.vo.UserVo;
import com.light.web.rabbitmq.constant.InvoiceRabbitConstant;
import com.light.web.service.user.UserService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/11/17
 */
@Component
public class InvoiceService {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private InvoiceRecordApiService invoiceRecordApiService;

    @Resource
    private UserService userService;

    public void invoiceApplyMessageHandle(InvoiceRecordBo invoiceRecordBo){

        // 发送申请 请求
        if(invoiceRecordBo.getStatus().equals(InvoiceEnums.RecordState.ING.getVal())){
            this.rabbitTemplate.convertAndSend(InvoiceRabbitConstant.INVOICE_EXCHANGE,InvoiceRabbitConstant.INVOICE_APPLY_QUEUE, JSON.toJSONString(invoiceRecordBo));
        }

    }

    private void invoiceWriteBackMessageHandle(InvoiceRecordBo invoiceRecordBo){

        // 发送申请 请求
        this.rabbitTemplate.convertAndSend(InvoiceRabbitConstant.INVOICE_EXCHANGE,InvoiceRabbitConstant.INVOICE_WRITE_BACK_QUEUE, JSON.toJSONString(invoiceRecordBo));

    }

    public void obsolete(Long invoiceRecordId){

        InvoiceRecordBo recordBo = new InvoiceRecordBo();
        recordBo.setId(invoiceRecordId);
        recordBo.setObsoleteUserType(InvoiceEnums.ObsoleteUserType.USER.getVal());
        String currentOid = this.currentUserService.getCurrentOid();
        recordBo.setObsoleteUserOid(currentOid);

        UserVo userVo = this.userService.fetchUserVoByOid(currentOid);
        recordBo.setObsoleteUserName(userVo.getRealName());
        final AjaxResult<InvoiceRecordVo> obsoleteResp = this.invoiceRecordApiService.obsolete(recordBo);
        if(obsoleteResp.isSuccess()){
            this.invoiceWriteBackMessageHandle(recordBo);
        }

    }
}
