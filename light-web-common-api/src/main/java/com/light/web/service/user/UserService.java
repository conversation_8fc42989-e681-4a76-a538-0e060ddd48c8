package com.light.web.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.core.entity.AjaxResult;
import com.light.user.account.entity.vo.AccountVo;
import com.light.user.account.service.AccountApiService;
import com.light.user.admin.entity.vo.AdminVo;
import com.light.user.user.entity.vo.UserVo;
import com.light.user.user.service.UserApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  主要用于 提供其他模块方法抽取
 * <AUTHOR>
 * @since 2023/2/1
 */
@Component
public class UserService {

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private AccountApiService accountApiService;

    /**
     * 根据获取用户信息
     *
     * @param userOidList 用户oid
     * @return {@link Map}<{@link String}, {@link AdminVo}>
     */
    public Map<String, UserVo> fetchUserVoMapByOids(List<String> userOidList){
        Map<String, UserVo> map = Maps.newHashMap();
        final List<UserVo> data = this.fetchUserVoByOids(userOidList);
        return data.stream().collect(Collectors.toMap(x -> x.getOid(), x -> x, (k1, k2) -> k2));
    }

    /**
     * 根据oid获取用户列表
     *
     * @param userOids 用户oid
     * @return {@link List}<{@link AdminVo}>
     */
    public List<UserVo> fetchUserVoByOids(List<String> userOids){
        List<UserVo> list = Lists.newArrayList();
        // 查询admin
        final AjaxResult<List<UserVo>> userAjax = this.userApiService.getByOidList(userOids);
        if(userAjax.isFail()){
            return list;
        }
        final List<UserVo> data = userAjax.getData();
        if(CollUtil.isEmpty(data)){
            return list;
        }
        return data;
    }

    /**
     * 根据oid获取用户
     *
     * @param userOid 用户oid
     * @return {@link List}<{@link AdminVo}>
     */
    public UserVo fetchUserVoByOid(String userOid){
        final List<UserVo> userVoList = fetchUserVoByOids(Arrays.asList(userOid));
        if(CollUtil.isEmpty(userVoList)){
            return null;
        }
        return userVoList.get(0);
    }

    /**
     * 根据oid获取用户
     *
     * @param userOid 用户oid
     * @return {@link List}<{@link AdminVo}>
     */
    public UserVo fetchAccountUserVoByOid(String userOid){
        final List<UserVo> userVoList = fetchUserVoByOids(Arrays.asList(userOid));
        if(CollUtil.isEmpty(userVoList)){
            return null;
        }
        final UserVo userVo = userVoList.get(0);

        if(userVo != null) {
            // 获取账号
            final List<AccountVo> accountVoList = this.accountApiService.getAccountListByUserOid(userOid).getData();
            if(CollUtil.isNotEmpty(accountVoList)){
                userVo.setAccountName(accountVoList.get(0).getAccountName());
            }
        }


        return userVo;
    }


}
