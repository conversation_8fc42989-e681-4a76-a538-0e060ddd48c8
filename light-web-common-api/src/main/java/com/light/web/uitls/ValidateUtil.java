package com.light.web.uitls;

import com.alibaba.fastjson.JSON;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import com.light.base.config.service.ConfigApiService;
import com.light.core.constants.SystemConstants;
import com.light.web.controller.valid.entity.ValidateConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Properties;

import static com.google.code.kaptcha.Constants.*;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/23
 */
@Component
@Slf4j
public class ValidateUtil {

    public static ConfigApiService configApiService;

    @Resource
    public void setConfigApiService(ConfigApiService configApiService) {
        this.configApiService = configApiService;
    }

    /**
     * 查询参数配置中的验证码类型
     * @return
     */
    public static ValidateConfig getValidateConfig() {
        ValidateConfig validateConfig = new ValidateConfig();
        String configValue = configApiService.getConfigValue(SystemConstants.VALIDATION_CODE_PARAMS).getData();
        if(!StringUtils.isEmpty(configValue)) {
            validateConfig = JSON.parseObject(configValue, ValidateConfig.class);
        }
        return validateConfig;
    }

    public static DefaultKaptcha getCharKaptcha(ValidateConfig validateConfig) {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        // 验证码文本内容的选择范围
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_STRING, validateConfig.getContent());
        // 是否有边框 默认为true 我们可以自己设置yes，no
        properties.setProperty(KAPTCHA_BORDER, validateConfig.getBorder());
        // 验证码文本字符颜色 默认为Color.BLACK
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_COLOR, validateConfig.getTextColor());
        // 验证码干扰内容的颜色
        properties.setProperty(KAPTCHA_NOISE_COLOR, validateConfig.getNoiseColor());
        // 验证码图片宽度 默认为200
        properties.setProperty(KAPTCHA_IMAGE_WIDTH, validateConfig.getWidth());
        // 验证码图片高度 默认为50
        properties.setProperty(KAPTCHA_IMAGE_HEIGHT, validateConfig.getHeight());
        // 验证码文本字符大小 默认为40
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_SIZE, validateConfig.getTextSize());
        // KAPTCHA_SESSION_KEY
        properties.setProperty(KAPTCHA_SESSION_CONFIG_KEY, "kaptchaCode");
        // 验证码文本字符长度 默认为5
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_LENGTH, validateConfig.getLength());
        // 验证码文本字体样式 默认为new Font("Arial", 1, fontSize), new Font("Courier", 1, fontSize)
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_NAMES, "Arial,Courier");
        // 图片样式 水纹com.google.code.kaptcha.impl.WaterRipple 鱼眼com.google.code.kaptcha.impl.FishEyeGimpy 阴影com.google.code.kaptcha.impl.ShadowGimpy
        properties.setProperty(KAPTCHA_OBSCURIFICATOR_IMPL, "");
        // 背景颜色开始值
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_FROM, validateConfig.getBackgroundColorFrom());
        // 背景颜色结束值
        properties.setProperty(KAPTCHA_BACKGROUND_CLR_TO, validateConfig.getBackgroundColorTo());
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }

    public static DefaultKaptcha getMathKaptcha(ValidateConfig validateConfig) {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        // 是否有边框 默认为true 我们可以自己设置yes，no
        properties.setProperty(KAPTCHA_BORDER, validateConfig.getBorder());
        // 边框颜色 默认为Color.BLACK
        properties.setProperty(KAPTCHA_BORDER_COLOR, "105,179,90");
        // 验证码文本字符颜色 默认为Color.BLACK
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_COLOR, validateConfig.getTextColor());
        // 验证码图片宽度 默认为200
        properties.setProperty(KAPTCHA_IMAGE_WIDTH, validateConfig.getWidth());
        // 验证码图片高度 默认为50
        properties.setProperty(KAPTCHA_IMAGE_HEIGHT, validateConfig.getHeight());
        // 验证码文本字符大小 默认为40
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_SIZE, validateConfig.getTextSize());
        // KAPTCHA_SESSION_KEY
        properties.setProperty(KAPTCHA_SESSION_CONFIG_KEY, "kaptchaCodeMath");
        // 验证码文本生成器
        properties.setProperty(KAPTCHA_TEXTPRODUCER_IMPL, "com.light.web.config.KaptchaTextCreator");
        // 验证码文本字符间距 默认为2
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_SPACE, "3");
        // 验证码文本字符长度 默认为5
        properties.setProperty(KAPTCHA_TEXTPRODUCER_CHAR_LENGTH, validateConfig.getLength());
        // 验证码文本字体样式 默认为new Font("Arial", 1, fontSize), new Font("Courier", 1, fontSize)
        properties.setProperty(KAPTCHA_TEXTPRODUCER_FONT_NAMES, "Arial,Courier");
        // 验证码噪点颜色 默认为Color.BLACK
        properties.setProperty(KAPTCHA_NOISE_COLOR, validateConfig.getNoiseColor());
        // 干扰实现类
        properties.setProperty(KAPTCHA_NOISE_IMPL, "com.google.code.kaptcha.impl.NoNoise");
        // 图片样式 水纹com.google.code.kaptcha.impl.WaterRipple 鱼眼com.google.code.kaptcha.impl.FishEyeGimpy 阴影com.google.code.kaptcha.impl.ShadowGimpy
        properties.setProperty(KAPTCHA_OBSCURIFICATOR_IMPL, "com.google.code.kaptcha.impl.ShadowGimpy");
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }

}
