::light-common
@echo build light-common...
cd ../../light-common
call mvn clean install

::light-gateway
@echo build light-gateway
cd ../light-gateway
call mvn clean install

::light-authority
#@echo build light-authority
#cd ../light-authority-api
#call mvn clean install

::light-base
@echo build light-base
cd ../light-base
call mvn clean install

::light-user
@echo build light-user
cd ../light-user
call mvn clean install

::light-exam
@echo build light-exam
cd ../light-exam
call mvn clean install

::light-log
@echo build light-log
cd ../light-log
call mvn clean install

::light-course
#@echo build light-course
#cd ../light-course
#call mvn clean install

::light-manage-common-api
@echo build light-manage-common-api
cd ../light-manage-common-api
call mvn clean install

::light-web-common-api
@echo build light-web-common-api
cd ../light-web-common-api
call mvn clean install


